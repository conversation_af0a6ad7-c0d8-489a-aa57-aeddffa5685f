<Project Sdk="Microsoft.NET.Sdk.Web">
  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="ByteAnt.DigitalAgencyStarterKit" Version="1.0.25" />
    <PackageReference Include="Umbraco.Cms" Version="13.9.2" />
  </ItemGroup>

  <ItemGroup>
    <!-- Opt-in to app-local ICU to ensure consistent globalization APIs across different platforms -->
    <PackageReference Include="Microsoft.ICU.ICU4C.Runtime" Version="********" />
    <RuntimeHostConfigurationOption Include="System.Globalization.AppLocalIcu" Value="********" Condition="$(RuntimeIdentifier.StartsWith('linux')) or $(RuntimeIdentifier.StartsWith('win')) or ('$(RuntimeIdentifier)' == '' and !$([MSBuild]::IsOSPlatform('osx')))" />
  </ItemGroup>

  <PropertyGroup>
    <!-- Razor files are needed for the backoffice to work correctly -->
    <CopyRazorGenerateFilesToPublishDirectory>true</CopyRazorGenerateFilesToPublishDirectory>
  </PropertyGroup>

  <PropertyGroup>
    <!-- Remove RazorCompileOnBuild and RazorCompileOnPublish when not using ModelsMode InMemoryAuto -->
    <RazorCompileOnBuild>false</RazorCompileOnBuild>
    <RazorCompileOnPublish>false</RazorCompileOnPublish>
  </PropertyGroup>

</Project>
