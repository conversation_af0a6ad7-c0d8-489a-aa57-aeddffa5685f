﻿@inherits Umbraco.Web.Mvc.UmbracoViewPage<ContentModels.ContentPage>
@using ContentModels = Umbraco.Web.PublishedModels;
@{
    Layout = "Master.cshtml";
}

@Html.Partial("~/Views/Partials/SectionHeader.cshtml")

<section class="section">

    <div class="container">

        <div class="col-md-3">
            <nav class="nav-bar nav-bar--list">
                @Html.Partial("~/Views/Partials/Navigation/SubNavigation.cshtml")
            </nav>
        </div>

        <div class="col-md-9">
            <article>
                @Html.GetGridHtml(Model, "bodyText", "bootstrap3-fluid")
            </article>
        </div>
    </div>

</section>