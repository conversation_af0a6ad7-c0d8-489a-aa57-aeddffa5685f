﻿@inherits Umbraco.Web.Macros.PartialViewMacroPage


@*
    This snippet lists the items from a Multinode tree picker, using the pickers default settings.
    Content Values stored as xml.

    To get it working with any site's data structure, set the selection equal to the property which has the
    multinode treepicker (so: replace "PropertyWithPicker" with the alias of your property).
*@

@{ var selection = CurrentPage.PropertyWithPicker.Split(','); }

<ul>
    @foreach (var id in selection)
    {
        var item = Umbraco.Content(id);
        <li>
            <a href="@item.Url">@item.Name</a>
        </li>
    }
</ul>
