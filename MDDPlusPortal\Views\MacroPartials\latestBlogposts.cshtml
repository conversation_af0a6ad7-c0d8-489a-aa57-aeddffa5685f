﻿@using ContentModels = Umbraco.Web.PublishedModels;
@using Umbraco.Web;
@inherits Umbraco.Web.Macros.PartialViewMacroPage
@{
    var startNodeId = Model.MacroParameters["startNodeId"] != null ? Model.MacroParameters["startNodeId"] : Model.Content.Id;
    var numberOfPosts = 3;
    if (Model.MacroParameters["numberOfPosts"] != null)
    {
        int.TryParse((string)Model.MacroParameters["numberOfPosts"], out numberOfPosts);
    }
}
@if (startNodeId != null)
{
    @* Get the starting page *@
    var startNode = Umbraco.Content(startNodeId);
    var blogposts = startNode.Children.OrderByDescending(x => x.CreateDate).Take(numberOfPosts);

    if (blogposts.Any())
    {
        <div class="blogposts">

            @foreach (ContentModels.Blogpost post in blogposts)
            {
                <a href="@post.Url" class="blogpost">
                    <div class="blogpost-meta">
                        <small class="blogpost-date">@post.PublishDateTime.ToShortDateString()</small>
                        <small class="blogpost-cat">
                            @Html.Partial("~/Views/Partials/CategoryLinks.cshtml", post.Categories)
                        </small>
                    </div>
                    <h3 class="blogpost-title">@post.PageTitle</h3>
                    <div class="blogpost-excerpt">@post.Excerpt</div>
                </a>
            }
        </div>
    }
}
