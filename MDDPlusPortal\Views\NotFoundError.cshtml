﻿@inherits UmbracoViewPage
@{
    Layout = null;
    var home = Umbraco.ContentAtRoot().FirstOrDefault() as Home;
}
<html lang="en">

<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=Edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>Page Not Found | @home?.WebsiteName</title>
    <link rel="stylesheet" href="~/bundles/styles.min.css" asp-append-version="true" />
    <link rel="stylesheet" href="~/bundles/error-pages.css" asp-append-version="true" />
    @if (home != null)
    {
        @await Html.CachedPartialAsync("Favicons", home, TimeSpan.FromHours(1), true, false)
        ;
    }
</head>

<body>
    <div class="err-page">
        <div class="err-page-wrp">
            <div class="err-text-wrp">
                <div class="img-wrp img-wrp-404">
                </div>
                <div class="err-descr">
                    Page not found
                </div>
                <div class="err-title">
                    Oops! Something went wrong
                </div>
                <a href="/" class="btn btn-red">
                    <div class="btn-text">Go Back</div>
                </a>
            </div>
        </div>
    </div>
</body>

</html>