﻿@inherits Umbraco.Cms.Web.Common.Views.UmbracoViewPage;
@using Umbraco.Cms.Core.Models;
@{
    var home = Model as Home;
    var icon = home?.FaviconAndTouchIcon;

    if (icon == null) { return; }

    var type = @icon.GetProperty("umbracoExtension")?.GetValue();

    <link rel="shortcut icon" href="@icon.Url()?width=32&height=32" type="image/@type" />
    <link rel="apple-touch-icon" sizes="57x57" href="@icon.Url()?width=57&height=57" />
    <link rel="apple-touch-icon" sizes="60x60" href="@icon.Url()?width=60&height=60" />
    <link rel="apple-touch-icon" sizes="72x72" href="@icon.Url()?width=72&height=72" />
    <link rel="apple-touch-icon" sizes="76x76" href="@icon.Url()?width=76&height=76" />
    <link rel="apple-touch-icon" sizes="114x114" href="@icon.Url()?width=114&height=114" />
    <link rel="apple-touch-icon" sizes="120x120" href="@icon.Url()?width=120&height=120" />
    <link rel="apple-touch-icon" sizes="144x144" href="@icon.Url()?width=144&height=144" />
    <link rel="apple-touch-icon" sizes="152x152" href="@icon.Url()?width=152&height=152" />
    <link rel="apple-touch-icon" sizes="180x180" href="@icon.Url()?width=180&height=180" />
    <link rel="icon" type="image/@type" href="@icon.Url()?width=16&height=16" sizes="16x16" />
    <link rel="icon" type="image/@type" href="@icon.Url()?width=32&height=32" sizes="32x32" />
    <link rel="icon" type="image/@type" href="@icon.Url()?width=96&height=96" sizes="96x96" />
    <link rel="icon" type="image/@type" href="@icon.Url()?width=192&height=192" sizes="192x192" />
    <meta name="msapplication-square70x70logo" content="@icon.Url()?width=70&height=70" />
    <meta name="msapplication-square150x150logo" content="@icon.Url()?width=150&height=150" />
    <meta name="msapplication-square310x310logo" content="@icon.Url()?width=310&height=310" />
}