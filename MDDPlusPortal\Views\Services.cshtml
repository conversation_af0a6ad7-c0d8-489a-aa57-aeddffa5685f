﻿@inherits UmbracoViewPage<ContentModels.Services>
@using ContentModels = Umbraco.Cms.Web.Common.PublishedModels;
@{
    Layout = "Master.cshtml";
}
<div class="services">
    <input id="active-section" type="hidden" value="tab1" />
    <section class="section-1">
        <div class="title-padding container">
            <h3 class="title">@Model.Name</h3>
            <div class="subtitle">@Model.Header</div>
            @{
                var yearFounded = Model?.Parent<Home>()?.YearFounded;
                if (!string.IsNullOrWhiteSpace(yearFounded))
                {
                    <div class="since">
                        <span>since</span>
                        <br>@yearFounded
                    </div>
                }
            }
        </div>
    </section>
    <section class="section-2" id="services">
        <div class="container">
            <div class="row">
                <div class="col-xs-12 col-sm-4 zero-padding">
                    <ul class="nav nav-pills services-list">
                        @{
                            int k = 1;
                        }
                        @foreach (var service in Model?.Children ?? Enumerable.Empty<IPublishedContent>())
                        {
                            var serviceContent = service as Service;
                            <li>
                                <a href="#" data-target="#tab@(k)" data-toggle="pill">
                                    <div>
                                        <div class="service-icon @serviceContent?.ServicesClass"></div>
                                        <div class="service-text">@serviceContent?.ServiceName</div>
                                    </div>
                                </a>
                            </li>
                            k++;
                        }
                    </ul>
                </div>
                <div class="col-xs-12 col-sm-8">
                    <div class="tab-content">
                        @{
                            int j = 1;
                        }
                        @foreach (var service in Model?.Children ?? Enumerable.Empty<IPublishedContent>())
                        {
                            var serviceContent = service as Service;
                            <div id="tab@(j)" class="tab-pane fade">
                                <div class="title">@serviceContent?.ServiceName</div>
                                <div class="summary">@serviceContent?.DetailedDescription</div>
                            </div>
                            j++;
                        }
                    </div>
                </div>
            </div>
        </div>
    </section>
    <section class="section-3">
        <div class="technologis-background">
            <h2 class="header ">@Model?.CompetencyText</h2>
            <div class="technologis-area-background ">
                <div class="row no-gutters">
                    @{
                        int i = 1;
                    }
                    @foreach (var competency in Model?.TechnicalAreas ?? Enumerable.Empty<Competency>())
                    {
                        var competencyContent = competency as ContentModels.Competency;
                        string itemClass = string.Empty;
                        if (i == 1)
                        {
                            itemClass = "first-item";
                        }
                        else if (i == 2)
                        {
                            itemClass = "left-item first-item";
                        }
                        else if (i == Model?.TechnicalAreas?.Count() - 1)
                        {
                            itemClass = "last-item";
                        }
                        else if (i == Model?.TechnicalAreas?.Count())
                        {
                            itemClass = "left-item last-item";
                        }
                        else if (i % 2 == 0)
                        {
                            itemClass = "left-item";
                        }
                        <div class="col-xs-12 col-md-6 ">
                            <div class="relative-position @itemClass">
                                <div class="technical-areas-icons @competencyContent?.IconCssclass icon"></div>
                                <div class="margin-info">
                                    <div class="info-header">@competencyContent?.CompetencyTitle </div>
                                    <div class="info">
                                        @competencyContent?.CompetencyDetails
                                    </div>
                                </div>
                            </div>
                        </div>
                        i++;
                    }
                </div>
                <div class="border-line"></div>
            </div>
        </div>
    </section>
    <section class="section-4 background">
        @Model.TechnologiesIcons
    </section>
</div>