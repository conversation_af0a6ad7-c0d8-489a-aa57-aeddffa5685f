{"$schema": "appsettings-schema.json", "Serilog": {"MinimumLevel": {"Default": "Information"}, "WriteTo": [{"Name": "Async", "Args": {"configure": [{"Name": "<PERSON><PERSON><PERSON>"}]}}]}, "ConnectionStrings": {"umbracoDbDSN": "Data Source=|DataDirectory|/Umbraco.sqlite.db;Cache=Shared;Foreign Keys=True;Pooling=True", "umbracoDbDSN_ProviderName": "Microsoft.Data.Sqlite"}, "Umbraco": {"CMS": {"Unattended": {"InstallUnattended": true, "UnattendedUserName": "Admin", "UnattendedUserEmail": "<EMAIL>", "UnattendedUserPassword": "Mm@12345678"}, "Content": {"MacroErrors": "<PERSON>hrow"}, "Hosting": {"Debug": true}, "RuntimeMinification": {"UseInMemoryCache": true, "CacheBuster": "Timestamp"}}}}