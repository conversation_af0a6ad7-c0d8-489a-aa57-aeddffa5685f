{"$schema": "appsettings-schema.json", "Serilog": {"MinimumLevel": {"Default": "Information", "Override": {"Microsoft": "Warning", "Microsoft.Hosting.Lifetime": "Information", "System": "Warning"}}}, "Umbraco": {"CMS": {"Global": {"Id": "cbb63686-927d-4b23-baab-bb186cb5ddfa", "SanitizeTinyMce": true}, "Content": {"AllowEditInvariantFromNonDefault": true, "ContentVersionCleanupPolicy": {"EnableCleanup": true}}, "Unattended": {"UpgradeUnattended": true}, "Security": {"AllowConcurrentLogins": false}}}}