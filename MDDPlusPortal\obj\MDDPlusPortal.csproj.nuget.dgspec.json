{"format": 1, "restore": {"D:\\MDDPlusPortal\\MDDPlusPortal\\MDDPlusPortal.csproj": {}}, "projects": {"D:\\MDDPlusPortal\\MDDPlusPortal\\MDDPlusPortal.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\MDDPlusPortal\\MDDPlusPortal\\MDDPlusPortal.csproj", "projectName": "MDDPlusPortal", "projectPath": "D:\\MDDPlusPortal\\MDDPlusPortal\\MDDPlusPortal.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\MDDPlusPortal\\MDDPlusPortal\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "all"}, "SdkAnalysisLevel": "9.0.100"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"ByteAnt.DigitalAgencyStarterKit": {"target": "Package", "version": "[1.0.25, )"}, "Microsoft.ICU.ICU4C.Runtime": {"target": "Package", "version": "[72.1.0.3, )"}, "Umbraco.Cms": {"target": "Package", "version": "[13.*, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.AspNetCore.App": {"privateAssets": "none"}, "Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.100/PortableRuntimeIdentifierGraph.json"}}}}}