{"@t":"2025-07-17T13:35:08.3005311Z","@mt":"Acquiring MainDom.","SourceContext":"Umbraco.Cms.Core.Runtime.MainDom","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:08.3152790Z","@mt":"Acquired MainDom.","SourceContext":"Umbraco.Cms.Core.Runtime.MainDom","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:08.4534000Z","@mt":"Configured database is reporting as not being available.","@l":"Warning","@x":"Microsoft.Data.Sqlite.SqliteException (0x80004005): SQLite Error 14: 'unable to open database file'.\r\n   at Microsoft.Data.Sqlite.SqliteException.ThrowExceptionForRC(Int32 rc, sqlite3 db)\r\n   at Microsoft.Data.Sqlite.SqliteConnectionInternal..ctor(SqliteConnectionStringBuilder connectionOptions, SqliteConnectionPool pool)\r\n   at Microsoft.Data.Sqlite.SqliteConnectionPool.GetConnection()\r\n   at Microsoft.Data.Sqlite.SqliteConnectionFactory.GetConnection(SqliteConnection outerConnection)\r\n   at Microsoft.Data.Sqlite.SqliteConnection.Open()\r\n   at Umbraco.Extensions.DbConnectionExtensions.IsAvailable(IDbConnection connection)","SourceContext":"object","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"WARN "}
{"@t":"2025-07-17T13:35:09.4679375Z","@mt":"Configured database is reporting as not being available.","@l":"Warning","@x":"Microsoft.Data.Sqlite.SqliteException (0x80004005): SQLite Error 14: 'unable to open database file'.\r\n   at Microsoft.Data.Sqlite.SqliteException.ThrowExceptionForRC(Int32 rc, sqlite3 db)\r\n   at Microsoft.Data.Sqlite.SqliteConnectionInternal..ctor(SqliteConnectionStringBuilder connectionOptions, SqliteConnectionPool pool)\r\n   at Microsoft.Data.Sqlite.SqliteConnectionPool.GetConnection()\r\n   at Microsoft.Data.Sqlite.SqliteConnectionFactory.GetConnection(SqliteConnection outerConnection)\r\n   at Microsoft.Data.Sqlite.SqliteConnection.Open()\r\n   at Umbraco.Extensions.DbConnectionExtensions.IsAvailable(IDbConnection connection)","SourceContext":"object","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"WARN "}
{"@t":"2025-07-17T13:35:10.4811474Z","@mt":"Configured database is reporting as not being available.","@l":"Warning","@x":"Microsoft.Data.Sqlite.SqliteException (0x80004005): SQLite Error 14: 'unable to open database file'.\r\n   at Microsoft.Data.Sqlite.SqliteException.ThrowExceptionForRC(Int32 rc, sqlite3 db)\r\n   at Microsoft.Data.Sqlite.SqliteConnectionInternal..ctor(SqliteConnectionStringBuilder connectionOptions, SqliteConnectionPool pool)\r\n   at Microsoft.Data.Sqlite.SqliteConnectionPool.GetConnection()\r\n   at Microsoft.Data.Sqlite.SqliteConnectionFactory.GetConnection(SqliteConnection outerConnection)\r\n   at Microsoft.Data.Sqlite.SqliteConnection.Open()\r\n   at Umbraco.Extensions.DbConnectionExtensions.IsAvailable(IDbConnection connection)","SourceContext":"object","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"WARN "}
{"@t":"2025-07-17T13:35:11.4939987Z","@mt":"Configured database is reporting as not being available.","@l":"Warning","@x":"Microsoft.Data.Sqlite.SqliteException (0x80004005): SQLite Error 14: 'unable to open database file'.\r\n   at Microsoft.Data.Sqlite.SqliteException.ThrowExceptionForRC(Int32 rc, sqlite3 db)\r\n   at Microsoft.Data.Sqlite.SqliteConnectionInternal..ctor(SqliteConnectionStringBuilder connectionOptions, SqliteConnectionPool pool)\r\n   at Microsoft.Data.Sqlite.SqliteConnectionPool.GetConnection()\r\n   at Microsoft.Data.Sqlite.SqliteConnectionFactory.GetConnection(SqliteConnection outerConnection)\r\n   at Microsoft.Data.Sqlite.SqliteConnection.Open()\r\n   at Umbraco.Extensions.DbConnectionExtensions.IsAvailable(IDbConnection connection)","SourceContext":"object","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"WARN "}
{"@t":"2025-07-17T13:35:12.4957102Z","@mt":"Configured database is reporting as not being available.","@l":"Warning","@x":"Microsoft.Data.Sqlite.SqliteException (0x80004005): SQLite Error 14: 'unable to open database file'.\r\n   at Microsoft.Data.Sqlite.SqliteException.ThrowExceptionForRC(Int32 rc, sqlite3 db)\r\n   at Microsoft.Data.Sqlite.SqliteConnectionInternal..ctor(SqliteConnectionStringBuilder connectionOptions, SqliteConnectionPool pool)\r\n   at Microsoft.Data.Sqlite.SqliteConnectionPool.GetConnection()\r\n   at Microsoft.Data.Sqlite.SqliteConnectionFactory.GetConnection(SqliteConnection outerConnection)\r\n   at Microsoft.Data.Sqlite.SqliteConnection.Open()\r\n   at Umbraco.Extensions.DbConnectionExtensions.IsAvailable(IDbConnection connection)","SourceContext":"object","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"WARN "}
{"@t":"2025-07-17T13:35:12.6865672Z","@mt":"Starting unattended install.","SourceContext":"Umbraco.Cms.Infrastructure.Install.UnattendedInstaller","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:12.7071462Z","@mt":"Create table:\n {Sql}","Sql":"CREATE TABLE umbracoUser\r\n(\r\n \"id\" INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT\r\n, \"userDisabled\" INTEGER NOT NULL CONSTRAINT \"DF_umbracoUser_userDisabled\" DEFAULT ('0')\r\n, \"userNoConsole\" INTEGER NOT NULL CONSTRAINT \"DF_umbracoUser_userNoConsole\" DEFAULT ('0')\r\n, \"userName\" TEXT COLLATE NOCASE NOT NULL\r\n, \"userLogin\" TEXT COLLATE NOCASE NOT NULL\r\n, \"userPassword\" TEXT COLLATE NOCASE NOT NULL\r\n, \"passwordConfig\" TEXT COLLATE NOCASE NULL\r\n, \"userEmail\" TEXT COLLATE NOCASE NOT NULL\r\n, \"userLanguage\" TEXT COLLATE NOCASE NULL\r\n, \"securityStampToken\" TEXT COLLATE NOCASE NULL\r\n, \"failedLoginAttempts\" INTEGER NULL\r\n, \"lastLockoutDate\" TEXT NULL\r\n, \"lastPasswordChangeDate\" TEXT NULL\r\n, \"lastLoginDate\" TEXT NULL\r\n, \"emailConfirmedDate\" TEXT NULL\r\n, \"invitedDate\" TEXT NULL\r\n, \"createDate\" TEXT NOT NULL CONSTRAINT \"DF_umbracoUser_createDate\" DEFAULT (DATE())\r\n, \"updateDate\" TEXT NOT NULL CONSTRAINT \"DF_umbracoUser_updateDate\" DEFAULT (DATE())\r\n, \"avatar\" TEXT COLLATE NOCASE NULL\r\n, \"tourData\" TEXT COLLATE NOCASE NULL\r\n, CONSTRAINT PK_user UNIQUE (\"id\")\r\n)\r\n","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:12.7106870Z","@mt":"Create Index:\n {Sql}","Sql":"CREATE  INDEX \"IX_umbracoUser_userLogin\" ON \"umbracoUser\" (\"userLogin\")","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:12.7119487Z","@mt":"Creating data in {TableName}","TableName":"umbracoUser","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:12.7530298Z","@mt":"Completed creating data in {TableName}","TableName":"umbracoUser","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:12.7533642Z","@mt":"New table {TableName} was created","TableName":"umbracoUser","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseSchemaCreator","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:12.7572612Z","@mt":"Create table:\n {Sql}","Sql":"CREATE TABLE umbracoNode\r\n(\r\n \"id\" INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT\r\n, \"uniqueId\" TEXT NOT NULL CONSTRAINT \"DF_umbracoNode_uniqueId\" DEFAULT (NEWID())\r\n, \"parentId\" INTEGER NOT NULL\r\n, \"level\" INTEGER NOT NULL\r\n, \"path\" TEXT COLLATE NOCASE NOT NULL\r\n, \"sortOrder\" INTEGER NOT NULL\r\n, \"trashed\" INTEGER NOT NULL CONSTRAINT \"DF_umbracoNode_trashed\" DEFAULT ('0')\r\n, \"nodeUser\" INTEGER NULL\r\n, \"text\" TEXT COLLATE NOCASE NULL\r\n, \"nodeObjectType\" TEXT NULL\r\n, \"createDate\" TEXT NOT NULL CONSTRAINT \"DF_umbracoNode_createDate\" DEFAULT (DATE())\r\n, CONSTRAINT PK_umbracoNode UNIQUE (\"id\")\r\n, CONSTRAINT FK_umbracoNode_umbracoNode_id FOREIGN KEY (\"parentId\") REFERENCES \"umbracoNode\" (\"id\")  \r\n, CONSTRAINT FK_umbracoNode_umbracoUser_id FOREIGN KEY (\"nodeUser\") REFERENCES \"umbracoUser\" (\"id\")  \r\n)\r\n","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:12.7579110Z","@mt":"Create Index:\n {Sql}","Sql":"CREATE UNIQUE INDEX \"IX_umbracoNode_UniqueId\" ON \"umbracoNode\" (\"uniqueId\")","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:12.7584512Z","@mt":"Create Index:\n {Sql}","Sql":"CREATE  INDEX \"IX_umbracoNode_parentId_nodeObjectType\" ON \"umbracoNode\" (\"parentID\",\"nodeObjectType\")","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:12.7588574Z","@mt":"Create Index:\n {Sql}","Sql":"CREATE  INDEX \"IX_umbracoNode_Level\" ON \"umbracoNode\" (\"level\",\"parentId\",\"sortOrder\",\"nodeObjectType\",\"trashed\")","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:12.7592136Z","@mt":"Create Index:\n {Sql}","Sql":"CREATE  INDEX \"IX_umbracoNode_Path\" ON \"umbracoNode\" (\"path\")","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:12.7595408Z","@mt":"Create Index:\n {Sql}","Sql":"CREATE  INDEX \"IX_umbracoNode_ObjectType_trashed_sorted\" ON \"umbracoNode\" (\"nodeObjectType\",\"trashed\",\"sortOrder\",\"id\")","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:12.7599055Z","@mt":"Create Index:\n {Sql}","Sql":"CREATE  INDEX \"IX_umbracoNode_Trashed\" ON \"umbracoNode\" (\"trashed\")","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:12.7602106Z","@mt":"Create Index:\n {Sql}","Sql":"CREATE  INDEX \"IX_umbracoNode_ObjectType\" ON \"umbracoNode\" (\"nodeObjectType\",\"trashed\")","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:12.7605282Z","@mt":"Creating data in {TableName}","TableName":"umbracoNode","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:12.8063857Z","@mt":"Completed creating data in {TableName}","TableName":"umbracoNode","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:12.8064575Z","@mt":"New table {TableName} was created","TableName":"umbracoNode","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseSchemaCreator","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:12.8075633Z","@mt":"Create table:\n {Sql}","Sql":"CREATE TABLE cmsContentType\r\n(\r\n \"pk\" INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT\r\n, \"nodeId\" INTEGER NOT NULL\r\n, \"alias\" TEXT COLLATE NOCASE NULL\r\n, \"icon\" TEXT COLLATE NOCASE NULL\r\n, \"thumbnail\" TEXT COLLATE NOCASE NOT NULL CONSTRAINT \"DF_cmsContentType_thumbnail\" DEFAULT ('folder.png')\r\n, \"description\" TEXT COLLATE NOCASE NULL\r\n, \"isContainer\" INTEGER NOT NULL CONSTRAINT \"DF_cmsContentType_isContainer\" DEFAULT ('0')\r\n, \"isElement\" INTEGER NOT NULL CONSTRAINT \"DF_cmsContentType_isElement\" DEFAULT ('0')\r\n, \"allowAtRoot\" INTEGER NOT NULL CONSTRAINT \"DF_cmsContentType_allowAtRoot\" DEFAULT ('0')\r\n, \"variations\" INTEGER NOT NULL CONSTRAINT \"DF_cmsContentType_variations\" DEFAULT ('1')\r\n, CONSTRAINT PK_cmsContentType UNIQUE (\"pk\")\r\n, CONSTRAINT FK_cmsContentType_umbracoNode_id FOREIGN KEY (\"nodeId\") REFERENCES \"umbracoNode\" (\"id\")  \r\n)\r\n","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:12.8082910Z","@mt":"Create Index:\n {Sql}","Sql":"CREATE UNIQUE INDEX \"IX_cmsContentType\" ON \"cmsContentType\" (\"nodeId\")","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:12.8091699Z","@mt":"Create Index:\n {Sql}","Sql":"CREATE  INDEX \"IX_cmsContentType_icon\" ON \"cmsContentType\" (\"icon\")","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:12.8094721Z","@mt":"Creating data in {TableName}","TableName":"cmsContentType","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:12.8334832Z","@mt":"Completed creating data in {TableName}","TableName":"cmsContentType","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:12.8335496Z","@mt":"New table {TableName} was created","TableName":"cmsContentType","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseSchemaCreator","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:12.8342866Z","@mt":"Create table:\n {Sql}","Sql":"CREATE TABLE cmsTemplate\r\n(\r\n \"pk\" INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT\r\n, \"nodeId\" INTEGER NOT NULL\r\n, \"alias\" TEXT COLLATE NOCASE NULL\r\n, CONSTRAINT PK_cmsTemplate UNIQUE (\"pk\")\r\n, CONSTRAINT FK_cmsTemplate_umbracoNode FOREIGN KEY (\"nodeId\") REFERENCES \"umbracoNode\" (\"id\")  \r\n)\r\n","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:12.8348267Z","@mt":"Create Index:\n {Sql}","Sql":"CREATE UNIQUE INDEX \"IX_cmsTemplate_nodeId\" ON \"cmsTemplate\" (\"nodeId\")","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:12.8352782Z","@mt":"Creating data in {TableName}","TableName":"cmsTemplate","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:12.8353277Z","@mt":"Completed creating data in {TableName}","TableName":"cmsTemplate","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:12.8353424Z","@mt":"New table {TableName} was created","TableName":"cmsTemplate","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseSchemaCreator","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:12.8358821Z","@mt":"Create table:\n {Sql}","Sql":"CREATE TABLE umbracoContent\r\n(\r\n \"nodeId\" INTEGER NOT NULL\r\n, \"contentTypeId\" INTEGER NOT NULL\r\n, CONSTRAINT PK_umbracoContent PRIMARY KEY (\"nodeId\")\r\n, CONSTRAINT FK_umbracoContent_umbracoNode_id FOREIGN KEY (\"nodeId\") REFERENCES \"umbracoNode\" (\"id\")  \r\n, CONSTRAINT FK_umbracoContent_cmsContentType_NodeId FOREIGN KEY (\"contentTypeId\") REFERENCES \"cmsContentType\" (\"NodeId\")  \r\n)\r\n","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:12.8360567Z","@mt":"Creating data in {TableName}","TableName":"umbracoContent","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:12.8360623Z","@mt":"Completed creating data in {TableName}","TableName":"umbracoContent","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:12.8360763Z","@mt":"New table {TableName} was created","TableName":"umbracoContent","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseSchemaCreator","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:12.8365321Z","@mt":"Create table:\n {Sql}","Sql":"CREATE TABLE umbracoContentVersion\r\n(\r\n \"id\" INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT\r\n, \"nodeId\" INTEGER NOT NULL\r\n, \"versionDate\" TEXT NOT NULL CONSTRAINT \"DF_umbracoContentVersion_versionDate\" DEFAULT (DATE())\r\n, \"userId\" INTEGER NULL\r\n, \"current\" INTEGER NOT NULL\r\n, \"text\" TEXT COLLATE NOCASE NULL\r\n, \"preventCleanup\" INTEGER NOT NULL CONSTRAINT \"DF_umbracoContentVersion_preventCleanup\" DEFAULT ('0')\r\n, CONSTRAINT PK_umbracoContentVersion UNIQUE (\"id\")\r\n, CONSTRAINT FK_umbracoContentVersion_umbracoContent_nodeId FOREIGN KEY (\"nodeId\") REFERENCES \"umbracoContent\" (\"nodeId\")  \r\n, CONSTRAINT FK_umbracoContentVersion_umbracoUser_id FOREIGN KEY (\"userId\") REFERENCES \"umbracoUser\" (\"id\")  \r\n)\r\n","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:12.8370002Z","@mt":"Create Index:\n {Sql}","Sql":"CREATE  INDEX \"IX_umbracoContentVersion_NodeId\" ON \"umbracoContentVersion\" (\"nodeId\",\"current\")","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:12.8373725Z","@mt":"Create Index:\n {Sql}","Sql":"CREATE  INDEX \"IX_umbracoContentVersion_Current\" ON \"umbracoContentVersion\" (\"current\")","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:12.8377288Z","@mt":"Creating data in {TableName}","TableName":"umbracoContentVersion","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:12.8377769Z","@mt":"Completed creating data in {TableName}","TableName":"umbracoContentVersion","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:12.8377923Z","@mt":"New table {TableName} was created","TableName":"umbracoContentVersion","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseSchemaCreator","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:12.8383366Z","@mt":"Create table:\n {Sql}","Sql":"CREATE TABLE umbracoMediaVersion\r\n(\r\n \"id\" INTEGER NOT NULL\r\n, \"path\" TEXT COLLATE NOCASE NULL\r\n, CONSTRAINT PK_umbracoMediaVersion PRIMARY KEY (\"id\")\r\n, CONSTRAINT FK_umbracoMediaVersion_umbracoContentVersion_id FOREIGN KEY (\"id\") REFERENCES \"umbracoContentVersion\" (\"id\")  \r\n)\r\n","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:12.8386073Z","@mt":"Create Index:\n {Sql}","Sql":"CREATE UNIQUE INDEX \"IX_umbracoMediaVersion\" ON \"umbracoMediaVersion\" (\"id\",\"path\")","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:12.8390311Z","@mt":"Creating data in {TableName}","TableName":"umbracoMediaVersion","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:12.8390690Z","@mt":"Completed creating data in {TableName}","TableName":"umbracoMediaVersion","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:12.8390770Z","@mt":"New table {TableName} was created","TableName":"umbracoMediaVersion","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseSchemaCreator","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:12.8393300Z","@mt":"Create table:\n {Sql}","Sql":"CREATE TABLE umbracoDocument\r\n(\r\n \"nodeId\" INTEGER NOT NULL\r\n, \"published\" INTEGER NOT NULL\r\n, \"edited\" INTEGER NOT NULL\r\n, CONSTRAINT PK_umbracoDocument PRIMARY KEY (\"nodeId\")\r\n, CONSTRAINT FK_umbracoDocument_umbracoContent_nodeId FOREIGN KEY (\"nodeId\") REFERENCES \"umbracoContent\" (\"nodeId\")  \r\n)\r\n","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:12.8396217Z","@mt":"Create Index:\n {Sql}","Sql":"CREATE  INDEX \"IX_umbracoDocument_Published\" ON \"umbracoDocument\" (\"published\")","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:12.8399377Z","@mt":"Creating data in {TableName}","TableName":"umbracoDocument","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:12.8399620Z","@mt":"Completed creating data in {TableName}","TableName":"umbracoDocument","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:12.8399675Z","@mt":"New table {TableName} was created","TableName":"umbracoDocument","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseSchemaCreator","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:12.8404105Z","@mt":"Create table:\n {Sql}","Sql":"CREATE TABLE cmsDocumentType\r\n(\r\n \"contentTypeNodeId\" INTEGER NOT NULL\r\n, \"templateNodeId\" INTEGER NOT NULL\r\n, \"IsDefault\" INTEGER NOT NULL CONSTRAINT \"DF_cmsDocumentType_IsDefault\" DEFAULT ('0')\r\n, CONSTRAINT PK_cmsDocumentType PRIMARY KEY (\"contentTypeNodeId\", \"templateNodeId\")\r\n, CONSTRAINT FK_cmsDocumentType_cmsContentType_nodeId FOREIGN KEY (\"contentTypeNodeId\") REFERENCES \"cmsContentType\" (\"nodeId\")  \r\n, CONSTRAINT FK_cmsDocumentType_umbracoNode_id FOREIGN KEY (\"contentTypeNodeId\") REFERENCES \"umbracoNode\" (\"id\")  \r\n, CONSTRAINT FK_cmsDocumentType_cmsTemplate_nodeId FOREIGN KEY (\"templateNodeId\") REFERENCES \"cmsTemplate\" (\"nodeId\")  \r\n)\r\n","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:12.8411753Z","@mt":"Creating data in {TableName}","TableName":"cmsDocumentType","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:12.8412146Z","@mt":"Completed creating data in {TableName}","TableName":"cmsDocumentType","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:12.8412294Z","@mt":"New table {TableName} was created","TableName":"cmsDocumentType","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseSchemaCreator","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:12.8419022Z","@mt":"Create table:\n {Sql}","Sql":"CREATE TABLE umbracoDataType\r\n(\r\n \"nodeId\" INTEGER NOT NULL\r\n, \"propertyEditorAlias\" TEXT COLLATE NOCASE NOT NULL\r\n, \"dbType\" TEXT COLLATE NOCASE NOT NULL\r\n, \"config\" TEXT COLLATE NOCASE NULL\r\n, CONSTRAINT PK_umbracoDataType PRIMARY KEY (\"nodeId\")\r\n, CONSTRAINT FK_umbracoDataType_umbracoNode_id FOREIGN KEY (\"nodeId\") REFERENCES \"umbracoNode\" (\"id\")  \r\n)\r\n","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:12.8424160Z","@mt":"Creating data in {TableName}","TableName":"umbracoDataType","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:12.8724804Z","@mt":"Completed creating data in {TableName}","TableName":"umbracoDataType","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:12.8725163Z","@mt":"New table {TableName} was created","TableName":"umbracoDataType","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseSchemaCreator","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:12.8733124Z","@mt":"Create table:\n {Sql}","Sql":"CREATE TABLE cmsDictionary\r\n(\r\n \"pk\" INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT\r\n, \"id\" TEXT NOT NULL\r\n, \"parent\" TEXT NULL\r\n, \"key\" TEXT COLLATE NOCASE NOT NULL\r\n, CONSTRAINT PK_cmsDictionary UNIQUE (\"pk\")\r\n, CONSTRAINT FK_cmsDictionary_cmsDictionary_id FOREIGN KEY (\"parent\") REFERENCES \"cmsDictionary\" (\"id\")  \r\n)\r\n","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:12.8739728Z","@mt":"Create Index:\n {Sql}","Sql":"CREATE UNIQUE INDEX \"IX_cmsDictionary_id\" ON \"cmsDictionary\" (\"id\")","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:12.8744170Z","@mt":"Create Index:\n {Sql}","Sql":"CREATE  INDEX \"IX_cmsDictionary_Parent\" ON \"cmsDictionary\" (\"parent\")","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:12.8747628Z","@mt":"Create Index:\n {Sql}","Sql":"CREATE UNIQUE INDEX \"IX_cmsDictionary_key\" ON \"cmsDictionary\" (\"key\")","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:12.8751276Z","@mt":"Creating data in {TableName}","TableName":"cmsDictionary","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:12.8751711Z","@mt":"Completed creating data in {TableName}","TableName":"cmsDictionary","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:12.8751857Z","@mt":"New table {TableName} was created","TableName":"cmsDictionary","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseSchemaCreator","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:12.8758413Z","@mt":"Create table:\n {Sql}","Sql":"CREATE TABLE umbracoLanguage\r\n(\r\n \"id\" INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT\r\n, \"languageISOCode\" TEXT COLLATE NOCASE NULL\r\n, \"languageCultureName\" TEXT COLLATE NOCASE NULL\r\n, \"isDefaultVariantLang\" INTEGER NOT NULL CONSTRAINT \"DF_umbracoLanguage_isDefaultVariantLang\" DEFAULT ('0')\r\n, \"mandatory\" INTEGER NOT NULL CONSTRAINT \"DF_umbracoLanguage_mandatory\" DEFAULT ('0')\r\n, \"fallbackLanguageId\" INTEGER NULL\r\n, CONSTRAINT PK_umbracoLanguage UNIQUE (\"id\")\r\n, CONSTRAINT FK_umbracoLanguage_umbracoLanguage_id FOREIGN KEY (\"fallbackLanguageId\") REFERENCES \"umbracoLanguage\" (\"id\")  \r\n)\r\n","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:12.8763662Z","@mt":"Create Index:\n {Sql}","Sql":"CREATE UNIQUE INDEX \"IX_umbracoLanguage_languageISOCode\" ON \"umbracoLanguage\" (\"languageISOCode\")","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:12.8767463Z","@mt":"Create Index:\n {Sql}","Sql":"CREATE  INDEX \"IX_umbracoLanguage_fallbackLanguageId\" ON \"umbracoLanguage\" (\"fallbackLanguageId\")","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:12.8770722Z","@mt":"Creating data in {TableName}","TableName":"umbracoLanguage","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:12.8812969Z","@mt":"Completed creating data in {TableName}","TableName":"umbracoLanguage","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:12.8813530Z","@mt":"New table {TableName} was created","TableName":"umbracoLanguage","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseSchemaCreator","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:12.8819182Z","@mt":"Create table:\n {Sql}","Sql":"CREATE TABLE cmsLanguageText\r\n(\r\n \"pk\" INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT\r\n, \"languageId\" INTEGER NOT NULL\r\n, \"UniqueId\" TEXT NOT NULL\r\n, \"value\" TEXT COLLATE NOCASE NOT NULL\r\n, CONSTRAINT PK_cmsLanguageText UNIQUE (\"pk\")\r\n, CONSTRAINT FK_cmsLanguageText_umbracoLanguage_id FOREIGN KEY (\"languageId\") REFERENCES \"umbracoLanguage\" (\"id\")  \r\n, CONSTRAINT FK_cmsLanguageText_cmsDictionary_id FOREIGN KEY (\"UniqueId\") REFERENCES \"cmsDictionary\" (\"id\")  \r\n)\r\n","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:12.8822601Z","@mt":"Create Index:\n {Sql}","Sql":"CREATE UNIQUE INDEX \"IX_cmsLanguageText_languageId\" ON \"cmsLanguageText\" (\"languageId\",\"UniqueId\")","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:12.8828429Z","@mt":"Creating data in {TableName}","TableName":"cmsLanguageText","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:12.8828951Z","@mt":"Completed creating data in {TableName}","TableName":"cmsLanguageText","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:12.8829345Z","@mt":"New table {TableName} was created","TableName":"cmsLanguageText","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseSchemaCreator","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:12.8836711Z","@mt":"Create table:\n {Sql}","Sql":"CREATE TABLE umbracoDomain\r\n(\r\n \"id\" INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT\r\n, \"domainDefaultLanguage\" INTEGER NULL\r\n, \"domainRootStructureID\" INTEGER NULL\r\n, \"domainName\" TEXT COLLATE NOCASE NOT NULL\r\n, \"sortOrder\" INTEGER NOT NULL\r\n, CONSTRAINT PK_umbracoDomain UNIQUE (\"id\")\r\n, CONSTRAINT FK_umbracoDomain_umbracoNode_id FOREIGN KEY (\"domainRootStructureID\") REFERENCES \"umbracoNode\" (\"id\")  \r\n)\r\n","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:12.8843767Z","@mt":"Creating data in {TableName}","TableName":"umbracoDomain","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:12.8844228Z","@mt":"Completed creating data in {TableName}","TableName":"umbracoDomain","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:12.8844407Z","@mt":"New table {TableName} was created","TableName":"umbracoDomain","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseSchemaCreator","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:12.8853402Z","@mt":"Create table:\n {Sql}","Sql":"CREATE TABLE umbracoLog\r\n(\r\n \"id\" INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT\r\n, \"userId\" INTEGER NULL\r\n, \"NodeId\" INTEGER NOT NULL\r\n, \"entityType\" TEXT COLLATE NOCASE NULL\r\n, \"Datestamp\" TEXT NOT NULL CONSTRAINT \"DF_umbracoLog_Datestamp\" DEFAULT (DATE())\r\n, \"logHeader\" TEXT COLLATE NOCASE NOT NULL\r\n, \"logComment\" TEXT COLLATE NOCASE NULL\r\n, \"parameters\" TEXT COLLATE NOCASE NULL\r\n, CONSTRAINT PK_umbracoLog UNIQUE (\"id\")\r\n, CONSTRAINT FK_umbracoLog_umbracoUser_id FOREIGN KEY (\"userId\") REFERENCES \"umbracoUser\" (\"id\")  \r\n)\r\n","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:12.8860821Z","@mt":"Create Index:\n {Sql}","Sql":"CREATE  INDEX \"IX_umbracoLog\" ON \"umbracoLog\" (\"NodeId\")","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:12.8866153Z","@mt":"Create Index:\n {Sql}","Sql":"CREATE  INDEX \"IX_umbracoLog_datestamp\" ON \"umbracoLog\" (\"Datestamp\",\"userId\",\"NodeId\")","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:12.8870841Z","@mt":"Create Index:\n {Sql}","Sql":"CREATE  INDEX \"IX_umbracoLog_datestamp_logheader\" ON \"umbracoLog\" (\"Datestamp\",\"logHeader\")","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:12.8875516Z","@mt":"Creating data in {TableName}","TableName":"umbracoLog","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:12.8875977Z","@mt":"Completed creating data in {TableName}","TableName":"umbracoLog","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:12.8876273Z","@mt":"New table {TableName} was created","TableName":"umbracoLog","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseSchemaCreator","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:12.8884776Z","@mt":"Create table:\n {Sql}","Sql":"CREATE TABLE cmsMacro\r\n(\r\n \"id\" INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT\r\n, \"uniqueId\" TEXT NOT NULL\r\n, \"macroUseInEditor\" INTEGER NOT NULL CONSTRAINT \"DF_cmsMacro_macroUseInEditor\" DEFAULT ('0')\r\n, \"macroRefreshRate\" INTEGER NOT NULL CONSTRAINT \"DF_cmsMacro_macroRefreshRate\" DEFAULT ('0')\r\n, \"macroAlias\" TEXT COLLATE NOCASE NOT NULL\r\n, \"macroName\" TEXT COLLATE NOCASE NULL\r\n, \"macroCacheByPage\" INTEGER NOT NULL CONSTRAINT \"DF_cmsMacro_macroCacheByPage\" DEFAULT ('1')\r\n, \"macroCachePersonalized\" INTEGER NOT NULL CONSTRAINT \"DF_cmsMacro_macroCachePersonalized\" DEFAULT ('0')\r\n, \"macroDontRender\" INTEGER NOT NULL CONSTRAINT \"DF_cmsMacro_macroDontRender\" DEFAULT ('0')\r\n, \"macroSource\" TEXT COLLATE NOCASE NOT NULL\r\n, \"macroType\" INTEGER NOT NULL\r\n, CONSTRAINT PK_cmsMacro UNIQUE (\"id\")\r\n)\r\n","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:12.8893278Z","@mt":"Create Index:\n {Sql}","Sql":"CREATE UNIQUE INDEX \"IX_cmsMacro_UniqueId\" ON \"cmsMacro\" (\"uniqueId\")","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:12.8897741Z","@mt":"Create Index:\n {Sql}","Sql":"CREATE UNIQUE INDEX \"IX_cmsMacroPropertyAlias\" ON \"cmsMacro\" (\"macroAlias\")","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:12.8901402Z","@mt":"Creating data in {TableName}","TableName":"cmsMacro","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:12.8901752Z","@mt":"Completed creating data in {TableName}","TableName":"cmsMacro","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:12.8902002Z","@mt":"New table {TableName} was created","TableName":"cmsMacro","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseSchemaCreator","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:12.8909589Z","@mt":"Create table:\n {Sql}","Sql":"CREATE TABLE cmsMacroProperty\r\n(\r\n \"id\" INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT\r\n, \"uniquePropertyId\" TEXT NOT NULL\r\n, \"editorAlias\" TEXT COLLATE NOCASE NOT NULL\r\n, \"macro\" INTEGER NOT NULL\r\n, \"macroPropertySortOrder\" INTEGER NOT NULL CONSTRAINT \"DF_cmsMacroProperty_macroPropertySortOrder\" DEFAULT ('0')\r\n, \"macroPropertyAlias\" TEXT COLLATE NOCASE NOT NULL\r\n, \"macroPropertyName\" TEXT COLLATE NOCASE NOT NULL\r\n, CONSTRAINT PK_cmsMacroProperty UNIQUE (\"id\")\r\n, CONSTRAINT FK_cmsMacroProperty_cmsMacro_id FOREIGN KEY (\"macro\") REFERENCES \"cmsMacro\" (\"id\")  \r\n)\r\n","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:12.8916016Z","@mt":"Create Index:\n {Sql}","Sql":"CREATE UNIQUE INDEX \"IX_cmsMacroProperty_UniquePropertyId\" ON \"cmsMacroProperty\" (\"uniquePropertyId\")","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:12.8920385Z","@mt":"Create Index:\n {Sql}","Sql":"CREATE UNIQUE INDEX \"IX_cmsMacroProperty_Alias\" ON \"cmsMacroProperty\" (\"macro\",\"macroPropertyAlias\")","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:12.8924641Z","@mt":"Creating data in {TableName}","TableName":"cmsMacroProperty","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:12.8925089Z","@mt":"Completed creating data in {TableName}","TableName":"cmsMacroProperty","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:12.8925612Z","@mt":"New table {TableName} was created","TableName":"cmsMacroProperty","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseSchemaCreator","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:12.8931604Z","@mt":"Create table:\n {Sql}","Sql":"CREATE TABLE cmsMemberType\r\n(\r\n \"pk\" INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT\r\n, \"NodeId\" INTEGER NOT NULL\r\n, \"propertytypeId\" INTEGER NOT NULL\r\n, \"memberCanEdit\" INTEGER NOT NULL CONSTRAINT \"DF_cmsMemberType_memberCanEdit\" DEFAULT ('0')\r\n, \"viewOnProfile\" INTEGER NOT NULL CONSTRAINT \"DF_cmsMemberType_viewOnProfile\" DEFAULT ('0')\r\n, \"isSensitive\" INTEGER NOT NULL CONSTRAINT \"DF_cmsMemberType_isSensitive\" DEFAULT ('0')\r\n, CONSTRAINT PK_cmsMemberType UNIQUE (\"pk\")\r\n, CONSTRAINT FK_cmsMemberType_umbracoNode_id FOREIGN KEY (\"NodeId\") REFERENCES \"umbracoNode\" (\"id\")  \r\n, CONSTRAINT FK_cmsMemberType_cmsContentType_nodeId FOREIGN KEY (\"NodeId\") REFERENCES \"cmsContentType\" (\"nodeId\")  \r\n)\r\n","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:12.8936410Z","@mt":"Creating data in {TableName}","TableName":"cmsMemberType","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:12.8936763Z","@mt":"Completed creating data in {TableName}","TableName":"cmsMemberType","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:12.8937082Z","@mt":"New table {TableName} was created","TableName":"cmsMemberType","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseSchemaCreator","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:12.8946032Z","@mt":"Create table:\n {Sql}","Sql":"CREATE TABLE cmsMember\r\n(\r\n \"nodeId\" INTEGER NOT NULL\r\n, \"Email\" TEXT COLLATE NOCASE NOT NULL CONSTRAINT \"DF_cmsMember_Email\" DEFAULT ('''')\r\n, \"LoginName\" TEXT COLLATE NOCASE NOT NULL CONSTRAINT \"DF_cmsMember_LoginName\" DEFAULT ('''')\r\n, \"Password\" TEXT COLLATE NOCASE NOT NULL CONSTRAINT \"DF_cmsMember_Password\" DEFAULT ('''')\r\n, \"passwordConfig\" TEXT COLLATE NOCASE NULL\r\n, \"securityStampToken\" TEXT COLLATE NOCASE NULL\r\n, \"emailConfirmedDate\" TEXT NULL\r\n, \"failedPasswordAttempts\" INTEGER NULL\r\n, \"isLockedOut\" INTEGER NULL CONSTRAINT \"DF_cmsMember_isLockedOut\" DEFAULT ('0')\r\n, \"isApproved\" INTEGER NOT NULL CONSTRAINT \"DF_cmsMember_isApproved\" DEFAULT ('1')\r\n, \"lastLoginDate\" TEXT NULL\r\n, \"lastLockoutDate\" TEXT NULL\r\n, \"lastPasswordChangeDate\" TEXT NULL\r\n, CONSTRAINT PK_cmsMember PRIMARY KEY (\"nodeId\")\r\n, CONSTRAINT FK_cmsMember_umbracoContent_nodeId FOREIGN KEY (\"nodeId\") REFERENCES \"umbracoContent\" (\"nodeId\")  \r\n)\r\n","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:12.8951752Z","@mt":"Create Index:\n {Sql}","Sql":"CREATE  INDEX \"IX_cmsMember_LoginName\" ON \"cmsMember\" (\"LoginName\")","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:12.8955936Z","@mt":"Creating data in {TableName}","TableName":"cmsMember","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:12.8956293Z","@mt":"Completed creating data in {TableName}","TableName":"cmsMember","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:12.8956437Z","@mt":"New table {TableName} was created","TableName":"cmsMember","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseSchemaCreator","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:12.8962478Z","@mt":"Create table:\n {Sql}","Sql":"CREATE TABLE cmsMember2MemberGroup\r\n(\r\n \"Member\" INTEGER NOT NULL\r\n, \"MemberGroup\" INTEGER NOT NULL\r\n, CONSTRAINT PK_cmsMember2MemberGroup PRIMARY KEY (\"Member\", \"MemberGroup\")\r\n, CONSTRAINT FK_cmsMember2MemberGroup_cmsMember_nodeId FOREIGN KEY (\"Member\") REFERENCES \"cmsMember\" (\"nodeId\")  \r\n, CONSTRAINT FK_cmsMember2MemberGroup_umbracoNode_id FOREIGN KEY (\"MemberGroup\") REFERENCES \"umbracoNode\" (\"id\")  \r\n)\r\n","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:12.8967131Z","@mt":"Creating data in {TableName}","TableName":"cmsMember2MemberGroup","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:12.8967631Z","@mt":"Completed creating data in {TableName}","TableName":"cmsMember2MemberGroup","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:12.8967774Z","@mt":"New table {TableName} was created","TableName":"cmsMember2MemberGroup","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseSchemaCreator","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:12.8974166Z","@mt":"Create table:\n {Sql}","Sql":"CREATE TABLE cmsPropertyTypeGroup\r\n(\r\n \"id\" INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT\r\n, \"uniqueID\" TEXT NOT NULL CONSTRAINT \"DF_cmsPropertyTypeGroup_uniqueID\" DEFAULT (NEWID())\r\n, \"contenttypeNodeId\" INTEGER NOT NULL\r\n, \"type\" INTEGER NOT NULL CONSTRAINT \"DF_cmsPropertyTypeGroup_type\" DEFAULT ('0')\r\n, \"text\" TEXT COLLATE NOCASE NOT NULL\r\n, \"alias\" TEXT COLLATE NOCASE NOT NULL\r\n, \"sortorder\" INTEGER NOT NULL\r\n, CONSTRAINT PK_cmsPropertyTypeGroup UNIQUE (\"id\")\r\n, CONSTRAINT FK_cmsPropertyTypeGroup_cmsContentType_nodeId FOREIGN KEY (\"contenttypeNodeId\") REFERENCES \"cmsContentType\" (\"nodeId\")  \r\n)\r\n","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:12.8979052Z","@mt":"Create Index:\n {Sql}","Sql":"CREATE UNIQUE INDEX \"IX_cmsPropertyTypeGroupUniqueID\" ON \"cmsPropertyTypeGroup\" (\"uniqueID\")","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:12.8982769Z","@mt":"Creating data in {TableName}","TableName":"cmsPropertyTypeGroup","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:12.9147494Z","@mt":"Completed creating data in {TableName}","TableName":"cmsPropertyTypeGroup","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:12.9148103Z","@mt":"New table {TableName} was created","TableName":"cmsPropertyTypeGroup","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseSchemaCreator","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:12.9158752Z","@mt":"Create table:\n {Sql}","Sql":"CREATE TABLE cmsPropertyType\r\n(\r\n \"id\" INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT\r\n, \"dataTypeId\" INTEGER NOT NULL\r\n, \"contentTypeId\" INTEGER NOT NULL\r\n, \"propertyTypeGroupId\" INTEGER NULL\r\n, \"Alias\" TEXT COLLATE NOCASE NOT NULL\r\n, \"Name\" TEXT COLLATE NOCASE NULL\r\n, \"sortOrder\" INTEGER NOT NULL CONSTRAINT \"DF_cmsPropertyType_sortOrder\" DEFAULT ('0')\r\n, \"mandatory\" INTEGER NOT NULL CONSTRAINT \"DF_cmsPropertyType_mandatory\" DEFAULT ('0')\r\n, \"mandatoryMessage\" TEXT COLLATE NOCASE NULL\r\n, \"validationRegExp\" TEXT COLLATE NOCASE NULL\r\n, \"validationRegExpMessage\" TEXT COLLATE NOCASE NULL\r\n, \"Description\" TEXT COLLATE NOCASE NULL\r\n, \"labelOnTop\" INTEGER NOT NULL CONSTRAINT \"DF_cmsPropertyType_labelOnTop\" DEFAULT ('0')\r\n, \"variations\" INTEGER NOT NULL CONSTRAINT \"DF_cmsPropertyType_variations\" DEFAULT ('1')\r\n, \"UniqueID\" TEXT NOT NULL CONSTRAINT \"DF_cmsPropertyType_UniqueID\" DEFAULT (NEWID())\r\n, CONSTRAINT PK_cmsPropertyType UNIQUE (\"id\")\r\n, CONSTRAINT FK_cmsPropertyType_umbracoDataType_nodeId FOREIGN KEY (\"dataTypeId\") REFERENCES \"umbracoDataType\" (\"nodeId\")  \r\n, CONSTRAINT FK_cmsPropertyType_cmsContentType_nodeId FOREIGN KEY (\"contentTypeId\") REFERENCES \"cmsContentType\" (\"nodeId\")  \r\n, CONSTRAINT FK_cmsPropertyType_cmsPropertyTypeGroup_id FOREIGN KEY (\"propertyTypeGroupId\") REFERENCES \"cmsPropertyTypeGroup\" (\"id\")  \r\n)\r\n","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:12.9162037Z","@mt":"Create Index:\n {Sql}","Sql":"CREATE  INDEX \"IX_cmsPropertyTypeAlias\" ON \"cmsPropertyType\" (\"Alias\")","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:12.9167623Z","@mt":"Create Index:\n {Sql}","Sql":"CREATE UNIQUE INDEX \"IX_cmsPropertyTypeUniqueID\" ON \"cmsPropertyType\" (\"UniqueID\")","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:12.9172791Z","@mt":"Creating data in {TableName}","TableName":"cmsPropertyType","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:12.9425113Z","@mt":"Completed creating data in {TableName}","TableName":"cmsPropertyType","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:12.9425663Z","@mt":"New table {TableName} was created","TableName":"cmsPropertyType","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseSchemaCreator","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:12.9434467Z","@mt":"Create table:\n {Sql}","Sql":"CREATE TABLE umbracoPropertyData\r\n(\r\n \"id\" INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT\r\n, \"versionId\" INTEGER NOT NULL\r\n, \"propertyTypeId\" INTEGER NOT NULL\r\n, \"languageId\" INTEGER NULL\r\n, \"segment\" TEXT COLLATE NOCASE NULL\r\n, \"intValue\" INTEGER NULL\r\n, \"decimalValue\" TEXT NULL\r\n, \"dateValue\" TEXT NULL\r\n, \"varcharValue\" TEXT COLLATE NOCASE NULL\r\n, \"textValue\" TEXT COLLATE NOCASE NULL\r\n, CONSTRAINT PK_umbracoPropertyData UNIQUE (\"id\")\r\n, CONSTRAINT FK_umbracoPropertyData_umbracoContentVersion_id FOREIGN KEY (\"versionId\") REFERENCES \"umbracoContentVersion\" (\"id\")  \r\n, CONSTRAINT FK_umbracoPropertyData_cmsPropertyType_id FOREIGN KEY (\"propertyTypeId\") REFERENCES \"cmsPropertyType\" (\"id\")  \r\n, CONSTRAINT FK_umbracoPropertyData_umbracoLanguage_id FOREIGN KEY (\"languageId\") REFERENCES \"umbracoLanguage\" (\"id\")  \r\n)\r\n","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:12.9440001Z","@mt":"Create Index:\n {Sql}","Sql":"CREATE UNIQUE INDEX \"IX_umbracoPropertyData_VersionId\" ON \"umbracoPropertyData\" (\"versionId\",\"propertyTypeId\",\"languageId\",\"segment\")","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:12.9444124Z","@mt":"Create Index:\n {Sql}","Sql":"CREATE  INDEX \"IX_umbracoPropertyData_PropertyTypeId\" ON \"umbracoPropertyData\" (\"propertyTypeId\")","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:12.9447577Z","@mt":"Create Index:\n {Sql}","Sql":"CREATE  INDEX \"IX_umbracoPropertyData_LanguageId\" ON \"umbracoPropertyData\" (\"languageId\")","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:12.9451031Z","@mt":"Create Index:\n {Sql}","Sql":"CREATE  INDEX \"IX_umbracoPropertyData_Segment\" ON \"umbracoPropertyData\" (\"segment\")","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:12.9456346Z","@mt":"Creating data in {TableName}","TableName":"umbracoPropertyData","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:12.9457035Z","@mt":"Completed creating data in {TableName}","TableName":"umbracoPropertyData","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:12.9457406Z","@mt":"New table {TableName} was created","TableName":"umbracoPropertyData","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseSchemaCreator","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:12.9466236Z","@mt":"Create table:\n {Sql}","Sql":"CREATE TABLE umbracoRelationType\r\n(\r\n \"id\" INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT\r\n, \"typeUniqueId\" TEXT NOT NULL\r\n, \"dual\" INTEGER NOT NULL\r\n, \"parentObjectType\" TEXT NULL\r\n, \"childObjectType\" TEXT NULL\r\n, \"name\" TEXT COLLATE NOCASE NOT NULL\r\n, \"alias\" TEXT COLLATE NOCASE NOT NULL\r\n, \"isDependency\" INTEGER NOT NULL CONSTRAINT \"DF_umbracoRelationType_isDependency\" DEFAULT ('0')\r\n, CONSTRAINT PK_umbracoRelationType UNIQUE (\"id\")\r\n)\r\n","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:12.9474253Z","@mt":"Create Index:\n {Sql}","Sql":"CREATE UNIQUE INDEX \"IX_umbracoRelationType_UniqueId\" ON \"umbracoRelationType\" (\"typeUniqueId\")","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:12.9479238Z","@mt":"Create Index:\n {Sql}","Sql":"CREATE UNIQUE INDEX \"IX_umbracoRelationType_name\" ON \"umbracoRelationType\" (\"name\")","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:12.9484150Z","@mt":"Create Index:\n {Sql}","Sql":"CREATE UNIQUE INDEX \"IX_umbracoRelationType_alias\" ON \"umbracoRelationType\" (\"alias\")","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:12.9489829Z","@mt":"Creating data in {TableName}","TableName":"umbracoRelationType","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:12.9564543Z","@mt":"Completed creating data in {TableName}","TableName":"umbracoRelationType","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:12.9565024Z","@mt":"New table {TableName} was created","TableName":"umbracoRelationType","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseSchemaCreator","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:12.9572661Z","@mt":"Create table:\n {Sql}","Sql":"CREATE TABLE umbracoRelation\r\n(\r\n \"id\" INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT\r\n, \"parentId\" INTEGER NOT NULL\r\n, \"childId\" INTEGER NOT NULL\r\n, \"relType\" INTEGER NOT NULL\r\n, \"datetime\" TEXT NOT NULL CONSTRAINT \"DF_umbracoRelation_datetime\" DEFAULT (DATE())\r\n, \"comment\" TEXT COLLATE NOCASE NOT NULL\r\n, CONSTRAINT PK_umbracoRelation UNIQUE (\"id\")\r\n, CONSTRAINT FK_umbracoRelation_umbracoNode FOREIGN KEY (\"parentId\") REFERENCES \"umbracoNode\" (\"id\")  \r\n, CONSTRAINT FK_umbracoRelation_umbracoNode1 FOREIGN KEY (\"childId\") REFERENCES \"umbracoNode\" (\"id\")  \r\n, CONSTRAINT FK_umbracoRelation_umbracoRelationType_id FOREIGN KEY (\"relType\") REFERENCES \"umbracoRelationType\" (\"id\")  \r\n)\r\n","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:12.9578429Z","@mt":"Create Index:\n {Sql}","Sql":"CREATE UNIQUE INDEX \"IX_umbracoRelation_parentChildType\" ON \"umbracoRelation\" (\"parentId\",\"childId\",\"relType\")","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:12.9583180Z","@mt":"Creating data in {TableName}","TableName":"umbracoRelation","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:12.9583565Z","@mt":"Completed creating data in {TableName}","TableName":"umbracoRelation","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:12.9583705Z","@mt":"New table {TableName} was created","TableName":"umbracoRelation","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseSchemaCreator","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:12.9590169Z","@mt":"Create table:\n {Sql}","Sql":"CREATE TABLE cmsTags\r\n(\r\n \"id\" INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT\r\n, \"group\" TEXT COLLATE NOCASE NOT NULL\r\n, \"languageId\" INTEGER NULL\r\n, \"tag\" TEXT COLLATE NOCASE NOT NULL\r\n, CONSTRAINT PK_cmsTags UNIQUE (\"id\")\r\n, CONSTRAINT FK_cmsTags_umbracoLanguage_id FOREIGN KEY (\"languageId\") REFERENCES \"umbracoLanguage\" (\"id\")  \r\n)\r\n","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:12.9595009Z","@mt":"Create Index:\n {Sql}","Sql":"CREATE  INDEX \"IX_cmsTags_languageId_group\" ON \"cmsTags\" (\"languageId\",\"group\")","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:12.9598692Z","@mt":"Create Index:\n {Sql}","Sql":"CREATE  INDEX \"IX_cmsTags_LanguageId\" ON \"cmsTags\" (\"languageId\")","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:12.9604584Z","@mt":"Create Index:\n {Sql}","Sql":"CREATE UNIQUE INDEX \"IX_cmsTags\" ON \"cmsTags\" (\"group\",\"tag\",\"languageId\")","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:12.9609439Z","@mt":"Creating data in {TableName}","TableName":"cmsTags","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:12.9609984Z","@mt":"Completed creating data in {TableName}","TableName":"cmsTags","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:12.9610176Z","@mt":"New table {TableName} was created","TableName":"cmsTags","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseSchemaCreator","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:12.9618529Z","@mt":"Create table:\n {Sql}","Sql":"CREATE TABLE cmsTagRelationship\r\n(\r\n \"nodeId\" INTEGER NOT NULL\r\n, \"tagId\" INTEGER NOT NULL\r\n, \"propertyTypeId\" INTEGER NOT NULL\r\n, CONSTRAINT PK_cmsTagRelationship PRIMARY KEY (\"nodeId\", \"propertyTypeId\", \"tagId\")\r\n, CONSTRAINT FK_cmsTagRelationship_cmsContent FOREIGN KEY (\"nodeId\") REFERENCES \"umbracoContent\" (\"nodeId\")  \r\n, CONSTRAINT FK_cmsTagRelationship_cmsTags_id FOREIGN KEY (\"tagId\") REFERENCES \"cmsTags\" (\"id\")  \r\n, CONSTRAINT FK_cmsTagRelationship_cmsPropertyType FOREIGN KEY (\"propertyTypeId\") REFERENCES \"cmsPropertyType\" (\"id\")  \r\n)\r\n","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:12.9624512Z","@mt":"Create Index:\n {Sql}","Sql":"CREATE  INDEX \"IX_cmsTagRelationship_tagId_nodeId\" ON \"cmsTagRelationship\" (\"tagId\",\"nodeId\")","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:12.9629200Z","@mt":"Creating data in {TableName}","TableName":"cmsTagRelationship","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:12.9629562Z","@mt":"Completed creating data in {TableName}","TableName":"cmsTagRelationship","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:12.9629744Z","@mt":"New table {TableName} was created","TableName":"cmsTagRelationship","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseSchemaCreator","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:12.9638158Z","@mt":"Create table:\n {Sql}","Sql":"CREATE TABLE cmsContentType2ContentType\r\n(\r\n \"parentContentTypeId\" INTEGER NOT NULL\r\n, \"childContentTypeId\" INTEGER NOT NULL\r\n, CONSTRAINT PK_cmsContentType2ContentType PRIMARY KEY (\"parentContentTypeId\", \"childContentTypeId\")\r\n, CONSTRAINT FK_cmsContentType2ContentType_umbracoNode_parent FOREIGN KEY (\"parentContentTypeId\") REFERENCES \"umbracoNode\" (\"id\")  \r\n, CONSTRAINT FK_cmsContentType2ContentType_umbracoNode_child FOREIGN KEY (\"childContentTypeId\") REFERENCES \"umbracoNode\" (\"id\")  \r\n)\r\n","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:12.9644371Z","@mt":"Creating data in {TableName}","TableName":"cmsContentType2ContentType","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:12.9644869Z","@mt":"Completed creating data in {TableName}","TableName":"cmsContentType2ContentType","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:12.9645065Z","@mt":"New table {TableName} was created","TableName":"cmsContentType2ContentType","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseSchemaCreator","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:12.9654611Z","@mt":"Create table:\n {Sql}","Sql":"CREATE TABLE cmsContentTypeAllowedContentType\r\n(\r\n \"Id\" INTEGER NOT NULL\r\n, \"AllowedId\" INTEGER NOT NULL\r\n, \"SortOrder\" INTEGER NOT NULL CONSTRAINT df_cmsContentTypeAllowedContentType_sortOrder DEFAULT ('0')\r\n, CONSTRAINT PK_cmsContentTypeAllowedContentType PRIMARY KEY (\"Id\", \"AllowedId\")\r\n, CONSTRAINT FK_cmsContentTypeAllowedContentType_cmsContentType FOREIGN KEY (\"Id\") REFERENCES \"cmsContentType\" (\"nodeId\")  \r\n, CONSTRAINT FK_cmsContentTypeAllowedContentType_cmsContentType1 FOREIGN KEY (\"AllowedId\") REFERENCES \"cmsContentType\" (\"nodeId\")  \r\n)\r\n","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:12.9657163Z","@mt":"Creating data in {TableName}","TableName":"cmsContentTypeAllowedContentType","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:12.9799432Z","@mt":"Completed creating data in {TableName}","TableName":"cmsContentTypeAllowedContentType","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:12.9800000Z","@mt":"New table {TableName} was created","TableName":"cmsContentTypeAllowedContentType","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseSchemaCreator","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:12.9809220Z","@mt":"Create table:\n {Sql}","Sql":"CREATE TABLE umbracoUser2NodeNotify\r\n(\r\n \"userId\" INTEGER NOT NULL\r\n, \"nodeId\" INTEGER NOT NULL\r\n, \"action\" TEXT COLLATE NOCASE NOT NULL\r\n, CONSTRAINT PK_umbracoUser2NodeNotify PRIMARY KEY (\"userId\", \"nodeId\", \"action\")\r\n, CONSTRAINT FK_umbracoUser2NodeNotify_umbracoUser_id FOREIGN KEY (\"userId\") REFERENCES \"umbracoUser\" (\"id\")  \r\n, CONSTRAINT FK_umbracoUser2NodeNotify_umbracoNode_id FOREIGN KEY (\"nodeId\") REFERENCES \"umbracoNode\" (\"id\")  \r\n)\r\n","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:12.9815345Z","@mt":"Creating data in {TableName}","TableName":"umbracoUser2NodeNotify","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:12.9815896Z","@mt":"Completed creating data in {TableName}","TableName":"umbracoUser2NodeNotify","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:12.9816119Z","@mt":"New table {TableName} was created","TableName":"umbracoUser2NodeNotify","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseSchemaCreator","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:12.9827225Z","@mt":"Create table:\n {Sql}","Sql":"CREATE TABLE umbracoServer\r\n(\r\n \"id\" INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT\r\n, \"address\" TEXT COLLATE NOCASE NOT NULL\r\n, \"computerName\" TEXT COLLATE NOCASE NOT NULL\r\n, \"registeredDate\" TEXT NOT NULL CONSTRAINT \"DF_umbracoServer_registeredDate\" DEFAULT (DATE())\r\n, \"lastNotifiedDate\" TEXT NOT NULL\r\n, \"isActive\" INTEGER NOT NULL\r\n, \"isSchedulingPublisher\" INTEGER NOT NULL\r\n, CONSTRAINT PK_umbracoServer UNIQUE (\"id\")\r\n)\r\n","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:12.9834236Z","@mt":"Create Index:\n {Sql}","Sql":"CREATE UNIQUE INDEX \"IX_computerName\" ON \"umbracoServer\" (\"computerName\")","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:12.9840037Z","@mt":"Create Index:\n {Sql}","Sql":"CREATE  INDEX \"IX_umbracoServer_isActive\" ON \"umbracoServer\" (\"isActive\")","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:12.9845384Z","@mt":"Creating data in {TableName}","TableName":"umbracoServer","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:12.9845864Z","@mt":"Completed creating data in {TableName}","TableName":"umbracoServer","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:12.9846245Z","@mt":"New table {TableName} was created","TableName":"umbracoServer","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseSchemaCreator","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:12.9856180Z","@mt":"Create table:\n {Sql}","Sql":"CREATE TABLE umbracoAccess\r\n(\r\n \"id\" TEXT NOT NULL\r\n, \"nodeId\" INTEGER NOT NULL\r\n, \"loginNodeId\" INTEGER NOT NULL\r\n, \"noAccessNodeId\" INTEGER NOT NULL\r\n, \"createDate\" TEXT NOT NULL CONSTRAINT \"DF_umbracoAccess_createDate\" DEFAULT (DATE())\r\n, \"updateDate\" TEXT NOT NULL CONSTRAINT \"DF_umbracoAccess_updateDate\" DEFAULT (DATE())\r\n, CONSTRAINT PK_umbracoAccess PRIMARY KEY (\"id\")\r\n, CONSTRAINT FK_umbracoAccess_umbracoNode_id FOREIGN KEY (\"nodeId\") REFERENCES \"umbracoNode\" (\"id\")  \r\n, CONSTRAINT FK_umbracoAccess_umbracoNode_id1 FOREIGN KEY (\"loginNodeId\") REFERENCES \"umbracoNode\" (\"id\")  \r\n, CONSTRAINT FK_umbracoAccess_umbracoNode_id2 FOREIGN KEY (\"noAccessNodeId\") REFERENCES \"umbracoNode\" (\"id\")  \r\n)\r\n","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:12.9862976Z","@mt":"Create Index:\n {Sql}","Sql":"CREATE UNIQUE INDEX \"IX_umbracoAccess_nodeId\" ON \"umbracoAccess\" (\"nodeId\")","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:12.9868063Z","@mt":"Creating data in {TableName}","TableName":"umbracoAccess","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:12.9868771Z","@mt":"Completed creating data in {TableName}","TableName":"umbracoAccess","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:12.9868986Z","@mt":"New table {TableName} was created","TableName":"umbracoAccess","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseSchemaCreator","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:12.9877970Z","@mt":"Create table:\n {Sql}","Sql":"CREATE TABLE umbracoAccessRule\r\n(\r\n \"id\" TEXT NOT NULL\r\n, \"accessId\" TEXT NOT NULL\r\n, \"ruleValue\" TEXT COLLATE NOCASE NOT NULL\r\n, \"ruleType\" TEXT COLLATE NOCASE NOT NULL\r\n, \"createDate\" TEXT NOT NULL CONSTRAINT \"DF_umbracoAccessRule_createDate\" DEFAULT (DATE())\r\n, \"updateDate\" TEXT NOT NULL CONSTRAINT \"DF_umbracoAccessRule_updateDate\" DEFAULT (DATE())\r\n, CONSTRAINT PK_umbracoAccessRule PRIMARY KEY (\"id\")\r\n, CONSTRAINT FK_umbracoAccessRule_umbracoAccess_id FOREIGN KEY (\"accessId\") REFERENCES \"umbracoAccess\" (\"id\")  \r\n)\r\n","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:12.9884857Z","@mt":"Create Index:\n {Sql}","Sql":"CREATE UNIQUE INDEX \"IX_umbracoAccessRule\" ON \"umbracoAccessRule\" (\"ruleValue\",\"ruleType\",\"accessId\")","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:12.9890234Z","@mt":"Creating data in {TableName}","TableName":"umbracoAccessRule","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:12.9890715Z","@mt":"Completed creating data in {TableName}","TableName":"umbracoAccessRule","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:12.9891087Z","@mt":"New table {TableName} was created","TableName":"umbracoAccessRule","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseSchemaCreator","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:12.9900227Z","@mt":"Create table:\n {Sql}","Sql":"CREATE TABLE umbracoCacheInstruction\r\n(\r\n \"id\" INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT\r\n, \"utcStamp\" TEXT NOT NULL\r\n, \"jsonInstruction\" TEXT COLLATE NOCASE NOT NULL\r\n, \"originated\" TEXT COLLATE NOCASE NOT NULL\r\n, \"instructionCount\" INTEGER NOT NULL CONSTRAINT \"DF_umbracoCacheInstruction_instructionCount\" DEFAULT ('1')\r\n, CONSTRAINT PK_umbracoCacheInstruction UNIQUE (\"id\")\r\n)\r\n","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:12.9906145Z","@mt":"Creating data in {TableName}","TableName":"umbracoCacheInstruction","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:12.9906686Z","@mt":"Completed creating data in {TableName}","TableName":"umbracoCacheInstruction","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:12.9906908Z","@mt":"New table {TableName} was created","TableName":"umbracoCacheInstruction","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseSchemaCreator","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:12.9916530Z","@mt":"Create table:\n {Sql}","Sql":"CREATE TABLE umbracoExternalLogin\r\n(\r\n \"id\" INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT\r\n, \"userOrMemberKey\" TEXT NOT NULL\r\n, \"loginProvider\" TEXT COLLATE NOCASE NOT NULL\r\n, \"providerKey\" TEXT COLLATE NOCASE NOT NULL\r\n, \"createDate\" TEXT NOT NULL CONSTRAINT \"DF_umbracoExternalLogin_createDate\" DEFAULT (DATE())\r\n, \"userData\" TEXT COLLATE NOCASE NULL\r\n, CONSTRAINT PK_umbracoExternalLogin UNIQUE (\"id\")\r\n)\r\n","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:12.9924119Z","@mt":"Create Index:\n {Sql}","Sql":"CREATE  INDEX \"IX_umbracoExternalLogin_userOrMemberKey\" ON \"umbracoExternalLogin\" (\"userOrMemberKey\")","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:12.9929428Z","@mt":"Create Index:\n {Sql}","Sql":"CREATE UNIQUE INDEX \"IX_umbracoExternalLogin_LoginProvider\" ON \"umbracoExternalLogin\" (\"loginProvider\",\"userOrMemberKey\")","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:12.9934809Z","@mt":"Create Index:\n {Sql}","Sql":"CREATE  INDEX \"IX_umbracoExternalLogin_ProviderKey\" ON \"umbracoExternalLogin\" (\"loginProvider\",\"providerKey\")","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:12.9940775Z","@mt":"Creating data in {TableName}","TableName":"umbracoExternalLogin","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:12.9941332Z","@mt":"Completed creating data in {TableName}","TableName":"umbracoExternalLogin","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:12.9941526Z","@mt":"New table {TableName} was created","TableName":"umbracoExternalLogin","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseSchemaCreator","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:12.9944498Z","@mt":"Create table:\n {Sql}","Sql":"CREATE TABLE umbracoExternalLoginToken\r\n(\r\n \"id\" INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT\r\n, \"externalLoginId\" INTEGER NOT NULL\r\n, \"name\" TEXT COLLATE NOCASE NOT NULL\r\n, \"value\" TEXT COLLATE NOCASE NOT NULL\r\n, \"createDate\" TEXT NOT NULL CONSTRAINT \"DF_umbracoExternalLoginToken_createDate\" DEFAULT (DATE())\r\n, CONSTRAINT PK_umbracoExternalLoginToken UNIQUE (\"id\")\r\n, CONSTRAINT FK_umbracoExternalLoginToken_umbracoExternalLogin_id FOREIGN KEY (\"externalLoginId\") REFERENCES \"umbracoExternalLogin\" (\"id\")  \r\n)\r\n","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:12.9946724Z","@mt":"Create Index:\n {Sql}","Sql":"CREATE UNIQUE INDEX \"IX_umbracoExternalLoginToken_Name\" ON \"umbracoExternalLoginToken\" (\"externalLoginId\",\"name\")","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:12.9951013Z","@mt":"Creating data in {TableName}","TableName":"umbracoExternalLoginToken","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:12.9951367Z","@mt":"Completed creating data in {TableName}","TableName":"umbracoExternalLoginToken","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:12.9951508Z","@mt":"New table {TableName} was created","TableName":"umbracoExternalLoginToken","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseSchemaCreator","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:12.9957379Z","@mt":"Create table:\n {Sql}","Sql":"CREATE TABLE umbracoTwoFactorLogin\r\n(\r\n \"id\" INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT\r\n, \"userOrMemberKey\" TEXT NOT NULL\r\n, \"providerName\" TEXT COLLATE NOCASE NOT NULL\r\n, \"secret\" TEXT COLLATE NOCASE NOT NULL\r\n, CONSTRAINT PK_umbracoTwoFactorLogin UNIQUE (\"id\")\r\n)\r\n","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:12.9961801Z","@mt":"Create Index:\n {Sql}","Sql":"CREATE  INDEX \"IX_umbracoTwoFactorLogin_userOrMemberKey\" ON \"umbracoTwoFactorLogin\" (\"userOrMemberKey\")","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:12.9965418Z","@mt":"Create Index:\n {Sql}","Sql":"CREATE UNIQUE INDEX \"IX_umbracoTwoFactorLogin_ProviderName\" ON \"umbracoTwoFactorLogin\" (\"providerName\",\"userOrMemberKey\")","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:12.9970600Z","@mt":"Creating data in {TableName}","TableName":"umbracoTwoFactorLogin","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:12.9970973Z","@mt":"Completed creating data in {TableName}","TableName":"umbracoTwoFactorLogin","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:12.9971109Z","@mt":"New table {TableName} was created","TableName":"umbracoTwoFactorLogin","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseSchemaCreator","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:12.9977829Z","@mt":"Create table:\n {Sql}","Sql":"CREATE TABLE umbracoRedirectUrl\r\n(\r\n \"id\" TEXT NOT NULL\r\n, \"contentKey\" TEXT NOT NULL\r\n, \"createDateUtc\" TEXT NOT NULL\r\n, \"url\" TEXT COLLATE NOCASE NOT NULL\r\n, \"culture\" TEXT COLLATE NOCASE NULL\r\n, \"urlHash\" TEXT COLLATE NOCASE NOT NULL\r\n, CONSTRAINT PK_umbracoRedirectUrl PRIMARY KEY (\"id\")\r\n, CONSTRAINT FK_umbracoRedirectUrl_umbracoNode_uniqueID FOREIGN KEY (\"contentKey\") REFERENCES \"umbracoNode\" (\"uniqueID\")  \r\n)\r\n","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:12.9982399Z","@mt":"Create Index:\n {Sql}","Sql":"CREATE  INDEX \"IX_umbracoRedirectUrl_culture_hash\" ON \"umbracoRedirectUrl\" (\"createDateUtc\")","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:12.9987686Z","@mt":"Create Index:\n {Sql}","Sql":"CREATE UNIQUE INDEX \"IX_umbracoRedirectUrl\" ON \"umbracoRedirectUrl\" (\"urlHash\",\"contentKey\",\"culture\",\"createDateUtc\")","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:12.9992755Z","@mt":"Creating data in {TableName}","TableName":"umbracoRedirectUrl","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:12.9993152Z","@mt":"Completed creating data in {TableName}","TableName":"umbracoRedirectUrl","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:12.9993294Z","@mt":"New table {TableName} was created","TableName":"umbracoRedirectUrl","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseSchemaCreator","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:12.9998906Z","@mt":"Create table:\n {Sql}","Sql":"CREATE TABLE umbracoLock\r\n(\r\n \"id\" INTEGER NOT NULL\r\n, \"value\" INTEGER NOT NULL\r\n, \"name\" TEXT COLLATE NOCASE NOT NULL\r\n, CONSTRAINT PK_umbracoLock PRIMARY KEY (\"id\")\r\n)\r\n","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:13.0002863Z","@mt":"Creating data in {TableName}","TableName":"umbracoLock","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:13.0062240Z","@mt":"Completed creating data in {TableName}","TableName":"umbracoLock","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:13.0062769Z","@mt":"New table {TableName} was created","TableName":"umbracoLock","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseSchemaCreator","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:13.0073332Z","@mt":"Create table:\n {Sql}","Sql":"CREATE TABLE umbracoUserGroup\r\n(\r\n \"id\" INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT\r\n, \"userGroupAlias\" TEXT COLLATE NOCASE NOT NULL\r\n, \"userGroupName\" TEXT COLLATE NOCASE NOT NULL\r\n, \"userGroupDefaultPermissions\" TEXT COLLATE NOCASE NULL\r\n, \"createDate\" TEXT NOT NULL CONSTRAINT \"DF_umbracoUserGroup_createDate\" DEFAULT (DATE())\r\n, \"updateDate\" TEXT NOT NULL CONSTRAINT \"DF_umbracoUserGroup_updateDate\" DEFAULT (DATE())\r\n, \"icon\" TEXT COLLATE NOCASE NULL\r\n, \"hasAccessToAllLanguages\" INTEGER NOT NULL\r\n, \"startContentId\" INTEGER NULL\r\n, \"startMediaId\" INTEGER NULL\r\n, CONSTRAINT PK_umbracoUserGroup UNIQUE (\"id\")\r\n, CONSTRAINT FK_startContentId_umbracoNode_id FOREIGN KEY (\"startContentId\") REFERENCES \"umbracoNode\" (\"id\")  \r\n, CONSTRAINT FK_startMediaId_umbracoNode_id FOREIGN KEY (\"startMediaId\") REFERENCES \"umbracoNode\" (\"id\")  \r\n)\r\n","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:13.0079821Z","@mt":"Create Index:\n {Sql}","Sql":"CREATE UNIQUE INDEX \"IX_umbracoUserGroup_userGroupAlias\" ON \"umbracoUserGroup\" (\"userGroupAlias\")","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:13.0084531Z","@mt":"Create Index:\n {Sql}","Sql":"CREATE UNIQUE INDEX \"IX_umbracoUserGroup_userGroupName\" ON \"umbracoUserGroup\" (\"userGroupName\")","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:13.0088236Z","@mt":"Creating data in {TableName}","TableName":"umbracoUserGroup","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:13.0162599Z","@mt":"Completed creating data in {TableName}","TableName":"umbracoUserGroup","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:13.0163101Z","@mt":"New table {TableName} was created","TableName":"umbracoUserGroup","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseSchemaCreator","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:13.0170014Z","@mt":"Create table:\n {Sql}","Sql":"CREATE TABLE umbracoUser2UserGroup\r\n(\r\n \"userId\" INTEGER NOT NULL\r\n, \"userGroupId\" INTEGER NOT NULL\r\n, CONSTRAINT PK_user2userGroup PRIMARY KEY (\"userId\", \"userGroupId\")\r\n, CONSTRAINT FK_umbracoUser2UserGroup_umbracoUser_id FOREIGN KEY (\"userId\") REFERENCES \"umbracoUser\" (\"id\")  \r\n, CONSTRAINT FK_umbracoUser2UserGroup_umbracoUserGroup_id FOREIGN KEY (\"userGroupId\") REFERENCES \"umbracoUserGroup\" (\"id\")  \r\n)\r\n","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:13.0177087Z","@mt":"Creating data in {TableName}","TableName":"umbracoUser2UserGroup","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:13.0223605Z","@mt":"Completed creating data in {TableName}","TableName":"umbracoUser2UserGroup","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:13.0224073Z","@mt":"New table {TableName} was created","TableName":"umbracoUser2UserGroup","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseSchemaCreator","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:13.0231321Z","@mt":"Create table:\n {Sql}","Sql":"CREATE TABLE umbracoUserGroup2NodePermission\r\n(\r\n \"userGroupId\" INTEGER NOT NULL\r\n, \"nodeId\" INTEGER NOT NULL\r\n, \"permission\" TEXT COLLATE NOCASE NOT NULL\r\n, CONSTRAINT PK_umbracoUserGroup2NodePermission PRIMARY KEY (\"userGroupId\", \"nodeId\", \"permission\")\r\n, CONSTRAINT FK_umbracoUserGroup2NodePermission_umbracoUserGroup_id FOREIGN KEY (\"userGroupId\") REFERENCES \"umbracoUserGroup\" (\"id\")  \r\n, CONSTRAINT FK_umbracoUserGroup2NodePermission_umbracoNode_id FOREIGN KEY (\"nodeId\") REFERENCES \"umbracoNode\" (\"id\")  \r\n)\r\n","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:13.0238163Z","@mt":"Create Index:\n {Sql}","Sql":"CREATE  INDEX \"IX_umbracoUser2NodePermission_nodeId\" ON \"umbracoUserGroup2NodePermission\" (\"nodeId\")","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:13.0243747Z","@mt":"Creating data in {TableName}","TableName":"umbracoUserGroup2NodePermission","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:13.0247505Z","@mt":"Completed creating data in {TableName}","TableName":"umbracoUserGroup2NodePermission","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:13.0247628Z","@mt":"New table {TableName} was created","TableName":"umbracoUserGroup2NodePermission","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseSchemaCreator","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:13.0250121Z","@mt":"Create table:\n {Sql}","Sql":"CREATE TABLE umbracoUserGroup2App\r\n(\r\n \"userGroupId\" INTEGER NOT NULL\r\n, \"app\" TEXT COLLATE NOCASE NOT NULL\r\n, CONSTRAINT PK_userGroup2App PRIMARY KEY (\"userGroupId\", \"app\")\r\n, CONSTRAINT FK_umbracoUserGroup2App_umbracoUserGroup_id FOREIGN KEY (\"userGroupId\") REFERENCES \"umbracoUserGroup\" (\"id\")  \r\n)\r\n","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:13.0251853Z","@mt":"Creating data in {TableName}","TableName":"umbracoUserGroup2App","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:13.0307991Z","@mt":"Completed creating data in {TableName}","TableName":"umbracoUserGroup2App","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:13.0308434Z","@mt":"New table {TableName} was created","TableName":"umbracoUserGroup2App","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseSchemaCreator","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:13.0315334Z","@mt":"Create table:\n {Sql}","Sql":"CREATE TABLE umbracoUserStartNode\r\n(\r\n \"id\" INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT\r\n, \"userId\" INTEGER NOT NULL\r\n, \"startNode\" INTEGER NOT NULL\r\n, \"startNodeType\" INTEGER NOT NULL\r\n, CONSTRAINT PK_userStartNode UNIQUE (\"id\")\r\n, CONSTRAINT FK_umbracoUserStartNode_umbracoUser_id FOREIGN KEY (\"userId\") REFERENCES \"umbracoUser\" (\"id\")  \r\n, CONSTRAINT FK_umbracoUserStartNode_umbracoNode_id FOREIGN KEY (\"startNode\") REFERENCES \"umbracoNode\" (\"id\")  \r\n)\r\n","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:13.0321478Z","@mt":"Create Index:\n {Sql}","Sql":"CREATE UNIQUE INDEX \"IX_umbracoUserStartNode_startNodeType\" ON \"umbracoUserStartNode\" (\"startNodeType\",\"startNode\",\"userId\")","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:13.0326593Z","@mt":"Creating data in {TableName}","TableName":"umbracoUserStartNode","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:13.0326973Z","@mt":"Completed creating data in {TableName}","TableName":"umbracoUserStartNode","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:13.0327119Z","@mt":"New table {TableName} was created","TableName":"umbracoUserStartNode","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseSchemaCreator","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:13.0334479Z","@mt":"Create table:\n {Sql}","Sql":"CREATE TABLE cmsContentNu\r\n(\r\n \"nodeId\" INTEGER NOT NULL\r\n, \"published\" INTEGER NOT NULL\r\n, \"data\" TEXT COLLATE NOCASE NULL\r\n, \"rv\" INTEGER NOT NULL\r\n, \"dataRaw\" BLOB NULL\r\n, CONSTRAINT PK_cmsContentNu PRIMARY KEY (\"nodeId\", \"published\")\r\n, CONSTRAINT FK_cmsContentNu_umbracoContent_nodeId FOREIGN KEY (\"nodeId\") REFERENCES \"umbracoContent\" (\"nodeId\")  ON DELETE CASCADE \r\n)\r\n","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:13.0339901Z","@mt":"Create Index:\n {Sql}","Sql":"CREATE  INDEX \"IX_cmsContentNu_published\" ON \"cmsContentNu\" (\"published\",\"nodeId\",\"rv\")","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:13.0344037Z","@mt":"Creating data in {TableName}","TableName":"cmsContentNu","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:13.0344456Z","@mt":"Completed creating data in {TableName}","TableName":"cmsContentNu","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:13.0344599Z","@mt":"New table {TableName} was created","TableName":"cmsContentNu","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseSchemaCreator","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:13.0351083Z","@mt":"Create table:\n {Sql}","Sql":"CREATE TABLE umbracoDocumentVersion\r\n(\r\n \"id\" INTEGER NOT NULL\r\n, \"templateId\" INTEGER NULL\r\n, \"published\" INTEGER NOT NULL\r\n, CONSTRAINT PK_umbracoDocumentVersion PRIMARY KEY (\"id\")\r\n, CONSTRAINT FK_umbracoDocumentVersion_umbracoContentVersion_id FOREIGN KEY (\"id\") REFERENCES \"umbracoContentVersion\" (\"id\")  \r\n, CONSTRAINT FK_umbracoDocumentVersion_cmsTemplate_nodeId FOREIGN KEY (\"templateId\") REFERENCES \"cmsTemplate\" (\"nodeId\")  \r\n)\r\n","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:13.0355776Z","@mt":"Create Index:\n {Sql}","Sql":"CREATE  INDEX \"IX_umbracoDocumentVersion_id_published\" ON \"umbracoDocumentVersion\" (\"id\",\"published\")","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:13.0359769Z","@mt":"Create Index:\n {Sql}","Sql":"CREATE  INDEX \"IX_umbracoDocumentVersion_published\" ON \"umbracoDocumentVersion\" (\"published\")","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:13.0363129Z","@mt":"Creating data in {TableName}","TableName":"umbracoDocumentVersion","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:13.0363543Z","@mt":"Completed creating data in {TableName}","TableName":"umbracoDocumentVersion","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:13.0363679Z","@mt":"New table {TableName} was created","TableName":"umbracoDocumentVersion","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseSchemaCreator","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:13.0369930Z","@mt":"Create table:\n {Sql}","Sql":"CREATE TABLE umbracoKeyValue\r\n(\r\n \"key\" TEXT COLLATE NOCASE NOT NULL\r\n, \"value\" TEXT COLLATE NOCASE NULL\r\n, \"updated\" TEXT NOT NULL CONSTRAINT \"DF_umbracoKeyValue_updated\" DEFAULT (DATE())\r\n, CONSTRAINT PK_umbracoKeyValue PRIMARY KEY (\"key\")\r\n)\r\n","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:13.0374419Z","@mt":"Creating data in {TableName}","TableName":"umbracoKeyValue","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:13.0435620Z","@mt":"Completed creating data in {TableName}","TableName":"umbracoKeyValue","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:13.0436494Z","@mt":"New table {TableName} was created","TableName":"umbracoKeyValue","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseSchemaCreator","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:13.0446491Z","@mt":"Create table:\n {Sql}","Sql":"CREATE TABLE umbracoUserLogin\r\n(\r\n \"sessionId\" TEXT NOT NULL\r\n, \"userId\" INTEGER NOT NULL\r\n, \"loggedInUtc\" TEXT NOT NULL\r\n, \"lastValidatedUtc\" TEXT NOT NULL\r\n, \"loggedOutUtc\" TEXT NULL\r\n, \"ipAddress\" TEXT COLLATE NOCASE NULL\r\n, CONSTRAINT PK_umbracoUserLogin PRIMARY KEY (\"sessionId\")\r\n, CONSTRAINT FK_umbracoUserLogin_umbracoUser_id FOREIGN KEY (\"userId\") REFERENCES \"umbracoUser\" (\"id\")  \r\n)\r\n","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:13.0454902Z","@mt":"Create Index:\n {Sql}","Sql":"CREATE  INDEX \"IX_umbracoUserLogin_lastValidatedUtc\" ON \"umbracoUserLogin\" (\"lastValidatedUtc\")","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:13.0460822Z","@mt":"Creating data in {TableName}","TableName":"umbracoUserLogin","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:13.0461561Z","@mt":"Completed creating data in {TableName}","TableName":"umbracoUserLogin","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:13.0462036Z","@mt":"New table {TableName} was created","TableName":"umbracoUserLogin","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseSchemaCreator","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:13.0470653Z","@mt":"Create table:\n {Sql}","Sql":"CREATE TABLE umbracoConsent\r\n(\r\n \"id\" INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT\r\n, \"current\" INTEGER NOT NULL\r\n, \"source\" TEXT COLLATE NOCASE NOT NULL\r\n, \"context\" TEXT COLLATE NOCASE NOT NULL\r\n, \"action\" TEXT COLLATE NOCASE NOT NULL\r\n, \"createDate\" TEXT NOT NULL CONSTRAINT \"DF_umbracoConsent_createDate\" DEFAULT (DATE())\r\n, \"state\" INTEGER NOT NULL\r\n, \"comment\" TEXT COLLATE NOCASE NULL\r\n, CONSTRAINT PK_umbracoConsent UNIQUE (\"id\")\r\n)\r\n","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:13.0477248Z","@mt":"Creating data in {TableName}","TableName":"umbracoConsent","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:13.0477743Z","@mt":"Completed creating data in {TableName}","TableName":"umbracoConsent","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:13.0477941Z","@mt":"New table {TableName} was created","TableName":"umbracoConsent","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseSchemaCreator","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:13.0487580Z","@mt":"Create table:\n {Sql}","Sql":"CREATE TABLE umbracoAudit\r\n(\r\n \"id\" INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT\r\n, \"performingUserId\" INTEGER NOT NULL\r\n, \"performingDetails\" TEXT COLLATE NOCASE NULL\r\n, \"performingIp\" TEXT COLLATE NOCASE NULL\r\n, \"eventDateUtc\" TEXT NOT NULL CONSTRAINT \"DF_umbracoAudit_eventDateUtc\" DEFAULT (DATE())\r\n, \"affectedUserId\" INTEGER NOT NULL\r\n, \"affectedDetails\" TEXT COLLATE NOCASE NULL\r\n, \"eventType\" TEXT COLLATE NOCASE NOT NULL\r\n, \"eventDetails\" TEXT COLLATE NOCASE NULL\r\n, CONSTRAINT PK_umbracoAudit UNIQUE (\"id\")\r\n)\r\n","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:13.0490149Z","@mt":"Creating data in {TableName}","TableName":"umbracoAudit","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:13.0490348Z","@mt":"Completed creating data in {TableName}","TableName":"umbracoAudit","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:13.0490400Z","@mt":"New table {TableName} was created","TableName":"umbracoAudit","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseSchemaCreator","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:13.0495538Z","@mt":"Create table:\n {Sql}","Sql":"CREATE TABLE umbracoContentVersionCultureVariation\r\n(\r\n \"id\" INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT\r\n, \"versionId\" INTEGER NOT NULL\r\n, \"languageId\" INTEGER NOT NULL\r\n, \"name\" TEXT COLLATE NOCASE NOT NULL\r\n, \"date\" TEXT NOT NULL\r\n, \"availableUserId\" INTEGER NULL\r\n, CONSTRAINT PK_umbracoContentVersionCultureVariation UNIQUE (\"id\")\r\n, CONSTRAINT FK_umbracoContentVersionCultureVariation_umbracoContentVersion_id FOREIGN KEY (\"versionId\") REFERENCES \"umbracoContentVersion\" (\"id\")  \r\n, CONSTRAINT FK_umbracoContentVersionCultureVariation_umbracoLanguage_id FOREIGN KEY (\"languageId\") REFERENCES \"umbracoLanguage\" (\"id\")  \r\n, CONSTRAINT FK_umbracoContentVersionCultureVariation_umbracoUser_id FOREIGN KEY (\"availableUserId\") REFERENCES \"umbracoUser\" (\"id\")  \r\n)\r\n","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:13.0500793Z","@mt":"Create Index:\n {Sql}","Sql":"CREATE UNIQUE INDEX \"IX_umbracoContentVersionCultureVariation_VersionId\" ON \"umbracoContentVersionCultureVariation\" (\"versionId\",\"languageId\")","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:13.0504994Z","@mt":"Create Index:\n {Sql}","Sql":"CREATE  INDEX \"IX_umbracoContentVersionCultureVariation_LanguageId\" ON \"umbracoContentVersionCultureVariation\" (\"languageId\")","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:13.0509056Z","@mt":"Creating data in {TableName}","TableName":"umbracoContentVersionCultureVariation","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:13.0509403Z","@mt":"Completed creating data in {TableName}","TableName":"umbracoContentVersionCultureVariation","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:13.0509535Z","@mt":"New table {TableName} was created","TableName":"umbracoContentVersionCultureVariation","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseSchemaCreator","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:13.0516547Z","@mt":"Create table:\n {Sql}","Sql":"CREATE TABLE umbracoDocumentCultureVariation\r\n(\r\n \"id\" INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT\r\n, \"nodeId\" INTEGER NOT NULL\r\n, \"languageId\" INTEGER NOT NULL\r\n, \"edited\" INTEGER NOT NULL\r\n, \"available\" INTEGER NOT NULL\r\n, \"published\" INTEGER NOT NULL\r\n, \"name\" TEXT COLLATE NOCASE NULL\r\n, CONSTRAINT PK_umbracoDocumentCultureVariation UNIQUE (\"id\")\r\n, CONSTRAINT FK_umbracoDocumentCultureVariation_umbracoNode_id FOREIGN KEY (\"nodeId\") REFERENCES \"umbracoNode\" (\"id\")  \r\n, CONSTRAINT FK_umbracoDocumentCultureVariation_umbracoLanguage_id FOREIGN KEY (\"languageId\") REFERENCES \"umbracoLanguage\" (\"id\")  \r\n)\r\n","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:13.0521485Z","@mt":"Create Index:\n {Sql}","Sql":"CREATE UNIQUE INDEX \"IX_umbracoDocumentCultureVariation_NodeId\" ON \"umbracoDocumentCultureVariation\" (\"nodeId\",\"languageId\")","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:13.0525394Z","@mt":"Create Index:\n {Sql}","Sql":"CREATE  INDEX \"IX_umbracoDocumentCultureVariation_LanguageId\" ON \"umbracoDocumentCultureVariation\" (\"languageId\")","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:13.0529086Z","@mt":"Creating data in {TableName}","TableName":"umbracoDocumentCultureVariation","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:13.0529408Z","@mt":"Completed creating data in {TableName}","TableName":"umbracoDocumentCultureVariation","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:13.0529627Z","@mt":"New table {TableName} was created","TableName":"umbracoDocumentCultureVariation","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseSchemaCreator","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:13.0536056Z","@mt":"Create table:\n {Sql}","Sql":"CREATE TABLE umbracoContentSchedule\r\n(\r\n \"id\" TEXT NOT NULL\r\n, \"nodeId\" INTEGER NOT NULL\r\n, \"languageId\" INTEGER NULL\r\n, \"date\" TEXT NOT NULL\r\n, \"action\" TEXT COLLATE NOCASE NOT NULL\r\n, CONSTRAINT PK_umbracoContentSchedule PRIMARY KEY (\"id\")\r\n, CONSTRAINT FK_umbracoContentSchedule_umbracoContent_nodeId FOREIGN KEY (\"nodeId\") REFERENCES \"umbracoContent\" (\"nodeId\")  \r\n, CONSTRAINT FK_umbracoContentSchedule_umbracoLanguage_id FOREIGN KEY (\"languageId\") REFERENCES \"umbracoLanguage\" (\"id\")  \r\n)\r\n","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:13.0540941Z","@mt":"Creating data in {TableName}","TableName":"umbracoContentSchedule","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:13.0541322Z","@mt":"Completed creating data in {TableName}","TableName":"umbracoContentSchedule","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:13.0541461Z","@mt":"New table {TableName} was created","TableName":"umbracoContentSchedule","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseSchemaCreator","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:13.0547000Z","@mt":"Create table:\n {Sql}","Sql":"CREATE TABLE umbracoLogViewerQuery\r\n(\r\n \"id\" INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT\r\n, \"name\" TEXT COLLATE NOCASE NOT NULL\r\n, \"query\" TEXT COLLATE NOCASE NOT NULL\r\n, CONSTRAINT PK_umbracoLogViewerQuery UNIQUE (\"id\")\r\n)\r\n","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:13.0551953Z","@mt":"Create Index:\n {Sql}","Sql":"CREATE UNIQUE INDEX \"IX_LogViewerQuery_name\" ON \"umbracoLogViewerQuery\" (\"name\")","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:13.0555782Z","@mt":"Creating data in {TableName}","TableName":"umbracoLogViewerQuery","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:13.0608522Z","@mt":"Completed creating data in {TableName}","TableName":"umbracoLogViewerQuery","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:13.0608969Z","@mt":"New table {TableName} was created","TableName":"umbracoLogViewerQuery","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseSchemaCreator","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:13.0615917Z","@mt":"Create table:\n {Sql}","Sql":"CREATE TABLE umbracoContentVersionCleanupPolicy\r\n(\r\n \"contentTypeId\" INTEGER NOT NULL\r\n, \"preventCleanup\" INTEGER NOT NULL\r\n, \"keepAllVersionsNewerThanDays\" INTEGER NULL\r\n, \"keepLatestVersionPerDayForDays\" INTEGER NULL\r\n, \"updated\" TEXT NOT NULL\r\n, CONSTRAINT PK_umbracoContentVersionCleanupPolicy PRIMARY KEY (\"contentTypeId\")\r\n, CONSTRAINT FK_umbracoContentVersionCleanupPolicy_cmsContentType_nodeId FOREIGN KEY (\"contentTypeId\") REFERENCES \"cmsContentType\" (\"nodeId\")  \r\n)\r\n","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:13.0620922Z","@mt":"Creating data in {TableName}","TableName":"umbracoContentVersionCleanupPolicy","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:13.0621297Z","@mt":"Completed creating data in {TableName}","TableName":"umbracoContentVersionCleanupPolicy","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:13.0621511Z","@mt":"New table {TableName} was created","TableName":"umbracoContentVersionCleanupPolicy","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseSchemaCreator","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:13.0628350Z","@mt":"Create table:\n {Sql}","Sql":"CREATE TABLE umbracoUserGroup2Node\r\n(\r\n \"userGroupId\" INTEGER NOT NULL\r\n, \"nodeId\" INTEGER NOT NULL\r\n, CONSTRAINT PK_umbracoUserGroup2Node PRIMARY KEY (\"userGroupId\", \"nodeId\")\r\n, CONSTRAINT FK_umbracoUserGroup2Node_umbracoUserGroup_id FOREIGN KEY (\"userGroupId\") REFERENCES \"umbracoUserGroup\" (\"id\")  \r\n, CONSTRAINT FK_umbracoUserGroup2Node_umbracoNode_id FOREIGN KEY (\"nodeId\") REFERENCES \"umbracoNode\" (\"id\")  \r\n)\r\n","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:13.0633345Z","@mt":"Create Index:\n {Sql}","Sql":"CREATE  INDEX \"IX_umbracoUserGroup2Node_nodeId\" ON \"umbracoUserGroup2Node\" (\"nodeId\")","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:13.0637298Z","@mt":"Creating data in {TableName}","TableName":"umbracoUserGroup2Node","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:13.0637638Z","@mt":"Completed creating data in {TableName}","TableName":"umbracoUserGroup2Node","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:13.0637772Z","@mt":"New table {TableName} was created","TableName":"umbracoUserGroup2Node","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseSchemaCreator","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:13.0644882Z","@mt":"Create table:\n {Sql}","Sql":"CREATE TABLE umbracoCreatedPackageSchema\r\n(\r\n \"id\" INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT\r\n, \"name\" TEXT COLLATE NOCASE NOT NULL\r\n, \"value\" TEXT COLLATE NOCASE NOT NULL\r\n, \"updateDate\" TEXT NOT NULL CONSTRAINT \"DF_umbracoCreatedPackageSchema_updateDate\" DEFAULT (DATE())\r\n, \"packageId\" TEXT NOT NULL\r\n, CONSTRAINT PK_umbracoCreatedPackageSchema UNIQUE (\"id\")\r\n)\r\n","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:13.0649677Z","@mt":"Create Index:\n {Sql}","Sql":"CREATE UNIQUE INDEX \"IX_umbracoCreatedPackageSchema_Name\" ON \"umbracoCreatedPackageSchema\" (\"name\")","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:13.0656445Z","@mt":"Creating data in {TableName}","TableName":"umbracoCreatedPackageSchema","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:13.0656938Z","@mt":"Completed creating data in {TableName}","TableName":"umbracoCreatedPackageSchema","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:13.0657088Z","@mt":"New table {TableName} was created","TableName":"umbracoCreatedPackageSchema","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseSchemaCreator","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:13.0664173Z","@mt":"Create table:\n {Sql}","Sql":"CREATE TABLE umbracoUserGroup2Language\r\n(\r\n \"userGroupId\" INTEGER NOT NULL\r\n, \"languageId\" INTEGER NOT NULL\r\n, CONSTRAINT PK_userGroup2language PRIMARY KEY (\"userGroupId\", \"languageId\")\r\n, CONSTRAINT FK_umbracoUserGroup2Language_umbracoUserGroup_id FOREIGN KEY (\"userGroupId\") REFERENCES \"umbracoUserGroup\" (\"id\")  ON DELETE CASCADE \r\n, CONSTRAINT FK_umbracoUserGroup2Language_umbracoLanguage_id FOREIGN KEY (\"languageId\") REFERENCES \"umbracoLanguage\" (\"id\")  ON DELETE CASCADE \r\n)\r\n","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:13.0670211Z","@mt":"Creating data in {TableName}","TableName":"umbracoUserGroup2Language","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:13.0670682Z","@mt":"Completed creating data in {TableName}","TableName":"umbracoUserGroup2Language","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:13.0670826Z","@mt":"New table {TableName} was created","TableName":"umbracoUserGroup2Language","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseSchemaCreator","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:13.0676992Z","@mt":"Create table:\n {Sql}","Sql":"CREATE TABLE umbracoWebhook\r\n(\r\n \"id\" INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT\r\n, \"key\" TEXT NOT NULL\r\n, \"url\" TEXT COLLATE NOCASE NOT NULL\r\n, \"enabled\" INTEGER NOT NULL\r\n, CONSTRAINT PK_umbracoWebhook UNIQUE (\"id\")\r\n)\r\n","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:13.0681570Z","@mt":"Creating data in {TableName}","TableName":"umbracoWebhook","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:13.0681922Z","@mt":"Completed creating data in {TableName}","TableName":"umbracoWebhook","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:13.0682124Z","@mt":"New table {TableName} was created","TableName":"umbracoWebhook","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseSchemaCreator","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:13.0689174Z","@mt":"Create table:\n {Sql}","Sql":"CREATE TABLE umbracoWebhook2ContentTypeKeys\r\n(\r\n \"webhookId\" INTEGER NOT NULL\r\n, \"entityKey\" TEXT NOT NULL\r\n, CONSTRAINT PK_webhookEntityKey2Webhook PRIMARY KEY (\"webhookId\", \"entityKey\")\r\n, CONSTRAINT FK_umbracoWebhook2ContentTypeKeys_umbracoWebhook_id FOREIGN KEY (\"webhookId\") REFERENCES \"umbracoWebhook\" (\"id\")  ON DELETE CASCADE \r\n)\r\n","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:13.0693490Z","@mt":"Creating data in {TableName}","TableName":"umbracoWebhook2ContentTypeKeys","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:13.0693641Z","@mt":"Completed creating data in {TableName}","TableName":"umbracoWebhook2ContentTypeKeys","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:13.0693691Z","@mt":"New table {TableName} was created","TableName":"umbracoWebhook2ContentTypeKeys","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseSchemaCreator","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:13.0696318Z","@mt":"Create table:\n {Sql}","Sql":"CREATE TABLE umbracoWebhook2Events\r\n(\r\n \"webhookId\" INTEGER NOT NULL\r\n, \"event\" TEXT COLLATE NOCASE NOT NULL\r\n, CONSTRAINT PK_webhookEvent2WebhookDto PRIMARY KEY (\"webhookId\", \"event\")\r\n, CONSTRAINT FK_umbracoWebhook2Events_umbracoWebhook_id FOREIGN KEY (\"webhookId\") REFERENCES \"umbracoWebhook\" (\"id\")  ON DELETE CASCADE \r\n)\r\n","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:13.0700478Z","@mt":"Creating data in {TableName}","TableName":"umbracoWebhook2Events","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:13.0700941Z","@mt":"Completed creating data in {TableName}","TableName":"umbracoWebhook2Events","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:13.0701087Z","@mt":"New table {TableName} was created","TableName":"umbracoWebhook2Events","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseSchemaCreator","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:13.0707712Z","@mt":"Create table:\n {Sql}","Sql":"CREATE TABLE umbracoWebhook2Headers\r\n(\r\n \"webhookId\" INTEGER NOT NULL\r\n, \"Key\" TEXT COLLATE NOCASE NOT NULL\r\n, \"Value\" TEXT COLLATE NOCASE NOT NULL\r\n, CONSTRAINT PK_heaeders2WebhookDto PRIMARY KEY (\"webhookId\", \"key\")\r\n, CONSTRAINT FK_umbracoWebhook2Headers_umbracoWebhook_id FOREIGN KEY (\"webhookId\") REFERENCES \"umbracoWebhook\" (\"id\")  ON DELETE CASCADE \r\n)\r\n","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:13.0713135Z","@mt":"Creating data in {TableName}","TableName":"umbracoWebhook2Headers","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:13.0713636Z","@mt":"Completed creating data in {TableName}","TableName":"umbracoWebhook2Headers","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:13.0713774Z","@mt":"New table {TableName} was created","TableName":"umbracoWebhook2Headers","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseSchemaCreator","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:13.0718255Z","@mt":"Create table:\n {Sql}","Sql":"CREATE TABLE umbracoWebhookLog\r\n(\r\n \"id\" INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT\r\n, \"webhookKey\" TEXT NOT NULL\r\n, \"key\" TEXT NOT NULL\r\n, \"statusCode\" TEXT COLLATE NOCASE NOT NULL\r\n, \"date\" TEXT NOT NULL\r\n, \"url\" TEXT COLLATE NOCASE NOT NULL\r\n, \"eventAlias\" TEXT COLLATE NOCASE NOT NULL\r\n, \"retryCount\" INTEGER NOT NULL\r\n, \"requestHeaders\" TEXT COLLATE NOCASE NOT NULL\r\n, \"requestBody\" TEXT COLLATE NOCASE NOT NULL\r\n, \"responseHeaders\" TEXT COLLATE NOCASE NOT NULL\r\n, \"responseBody\" TEXT COLLATE NOCASE NOT NULL\r\n, \"exceptionOccured\" INTEGER NOT NULL\r\n, CONSTRAINT PK_umbracoWebhookLog UNIQUE (\"id\")\r\n)\r\n","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:13.0720666Z","@mt":"Create Index:\n {Sql}","Sql":"CREATE  INDEX \"IX_umbracoWebhookLog_date\" ON \"umbracoWebhookLog\" (\"date\")","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:13.0724839Z","@mt":"Creating data in {TableName}","TableName":"umbracoWebhookLog","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:13.0725268Z","@mt":"Completed creating data in {TableName}","TableName":"umbracoWebhookLog","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:13.0725411Z","@mt":"New table {TableName} was created","TableName":"umbracoWebhookLog","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseSchemaCreator","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:13.0732182Z","@mt":"Create table:\n {Sql}","Sql":"CREATE TABLE umbracoWebhookRequest\r\n(\r\n \"id\" INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT\r\n, \"webhookKey\" TEXT NOT NULL\r\n, \"eventName\" TEXT COLLATE NOCASE NOT NULL\r\n, \"requestObject\" TEXT COLLATE NOCASE NULL\r\n, \"retryCount\" INTEGER NOT NULL\r\n, CONSTRAINT PK_umbracoWebhookRequest UNIQUE (\"id\")\r\n)\r\n","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:13.0736689Z","@mt":"Creating data in {TableName}","TableName":"umbracoWebhookRequest","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:13.0737039Z","@mt":"Completed creating data in {TableName}","TableName":"umbracoWebhookRequest","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseDataCreator","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:13.0737311Z","@mt":"New table {TableName} was created","TableName":"umbracoWebhookRequest","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.Install.DatabaseSchemaCreator","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:13.0891551Z","@mt":"Unattended install completed.","SourceContext":"Umbraco.Cms.Infrastructure.Install.UnattendedInstaller","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:13.4729976Z","@mt":"No last synced Id found, this generally means this is a new server/install. A cold boot will be triggered.","@l":"Warning","SourceContext":"Umbraco.Cms.Infrastructure.Sync.SyncBootStateAccessor","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"WARN "}
{"@t":"2025-07-17T13:35:15.4801418Z","@mt":"{StartMessage} [Timing {TimingId}]","StartMessage":"Starting unattended package migration for Digital Agency Starter Kit by ByteAnt","TimingId":"e7b3d2a","SourceContext":"Umbraco.Cms.Core.Logging.ProfilingLogger","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:15.4866482Z","@mt":"Starting '{MigrationName}'...","MigrationName":"Digital Agency Starter Kit by ByteAnt","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.MigrationPlanExecutor","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:15.4866728Z","@mt":"At {OrigState}","OrigState":"origin","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.MigrationPlanExecutor","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:15.4867418Z","@mt":"Execute {MigrationType}","MigrationType":"ImportPackageXmlMigration","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.MigrationPlanExecutor","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:20.5244041Z","@mt":"Packager: Error handling DocumentType structure. DocumentType with alias '{DoctypeAlias}' could not be found and was not added to the structure for '{DoctypeStructureAlias}'.","@l":"Warning","DoctypeAlias":"healthcare","DoctypeStructureAlias":"home","SourceContext":"Umbraco.Cms.Infrastructure.Packaging.PackageDataInstallation","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"WARN "}
{"@t":"2025-07-17T13:35:20.5259297Z","@mt":"Packager: Error handling DocumentType structure. DocumentType with alias '{DoctypeAlias}' could not be found and was not added to the structure for '{DoctypeStructureAlias}'.","@l":"Warning","DoctypeAlias":"react","DoctypeStructureAlias":"home","SourceContext":"Umbraco.Cms.Infrastructure.Packaging.PackageDataInstallation","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"WARN "}
{"@t":"2025-07-17T13:35:20.5271488Z","@mt":"Packager: Error handling DocumentType structure. DocumentType with alias '{DoctypeAlias}' could not be found and was not added to the structure for '{DoctypeStructureAlias}'.","@l":"Warning","DoctypeAlias":"azuarePage","DoctypeStructureAlias":"home","SourceContext":"Umbraco.Cms.Infrastructure.Packaging.PackageDataInstallation","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"WARN "}
{"@t":"2025-07-17T13:35:22.6270560Z","@mt":"The reference to {Udi} can not be saved as relation, because doesn't have a node ID.","Udi":"umb://media/ad48dec4706d45b3a427aa1febf2b0ab","SourceContext":"Umbraco.Cms.Infrastructure.Persistence.Repositories.Implement.DocumentRepository","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:22.6272800Z","@mt":"The reference to {Udi} can not be saved as relation, because doesn't have a node ID.","Udi":"umb://media/9fdc066c894f48759ffeae94c4141006","SourceContext":"Umbraco.Cms.Infrastructure.Persistence.Repositories.Implement.DocumentRepository","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:22.6274406Z","@mt":"The reference to {Udi} can not be saved as relation, because doesn't have a node ID.","Udi":"umb://media/4d04ad8009e44cbb97f81d90d122bb2d","SourceContext":"Umbraco.Cms.Infrastructure.Persistence.Repositories.Implement.DocumentRepository","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:22.6274536Z","@mt":"The reference to {Udi} can not be saved as relation, because doesn't have a node ID.","Udi":"umb://document/cb88aaa910a942fbac7be3e993d493f5","SourceContext":"Umbraco.Cms.Infrastructure.Persistence.Repositories.Implement.DocumentRepository","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:22.6274635Z","@mt":"The reference to {Udi} can not be saved as relation, because doesn't have a node ID.","Udi":"umb://document/a37f60827a564f29b27de7584ede41d6","SourceContext":"Umbraco.Cms.Infrastructure.Persistence.Repositories.Implement.DocumentRepository","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:22.6274742Z","@mt":"The reference to {Udi} can not be saved as relation, because doesn't have a node ID.","Udi":"umb://document/2ea83cc7d86543dcae6c78e2eb7acf90","SourceContext":"Umbraco.Cms.Infrastructure.Persistence.Repositories.Implement.DocumentRepository","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:22.6274840Z","@mt":"The reference to {Udi} can not be saved as relation, because doesn't have a node ID.","Udi":"umb://media/088819fe1be9497982581255cc8e3674","SourceContext":"Umbraco.Cms.Infrastructure.Persistence.Repositories.Implement.DocumentRepository","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:22.6275402Z","@mt":"The reference to {Udi} can not be saved as relation, because doesn't have a node ID.","Udi":"umb://media/ab88c5e3bbab4aa8b5a189c08cf78b0d","SourceContext":"Umbraco.Cms.Infrastructure.Persistence.Repositories.Implement.DocumentRepository","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:22.6275526Z","@mt":"The reference to {Udi} can not be saved as relation, because doesn't have a node ID.","Udi":"umb://document/9acdfbe7bfe74acc9d75b2229ece935b","SourceContext":"Umbraco.Cms.Infrastructure.Persistence.Repositories.Implement.DocumentRepository","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:22.6275576Z","@mt":"The reference to {Udi} can not be saved as relation, because doesn't have a node ID.","Udi":"umb://document/3ed97b5f70644ed9b38c116cffe188ad","SourceContext":"Umbraco.Cms.Infrastructure.Persistence.Repositories.Implement.DocumentRepository","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:22.6275685Z","@mt":"The reference to {Udi} can not be saved as relation, because doesn't have a node ID.","Udi":"umb://media/4e5764de9b3c47ac9e38d271d4168497","SourceContext":"Umbraco.Cms.Infrastructure.Persistence.Repositories.Implement.DocumentRepository","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:22.6275731Z","@mt":"The reference to {Udi} can not be saved as relation, because doesn't have a node ID.","Udi":"umb://media/808035c526524b409165734f5a39346b","SourceContext":"Umbraco.Cms.Infrastructure.Persistence.Repositories.Implement.DocumentRepository","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:22.6275816Z","@mt":"The reference to {Udi} can not be saved as relation, because doesn't have a node ID.","Udi":"umb://media/dcb4a53111954d399f1c34560dca39ef","SourceContext":"Umbraco.Cms.Infrastructure.Persistence.Repositories.Implement.DocumentRepository","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:22.6275854Z","@mt":"The reference to {Udi} can not be saved as relation, because doesn't have a node ID.","Udi":"umb://media/c2ad03a6674b4211a5beb54f72fb74b4","SourceContext":"Umbraco.Cms.Infrastructure.Persistence.Repositories.Implement.DocumentRepository","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:22.6275891Z","@mt":"The reference to {Udi} can not be saved as relation, because doesn't have a node ID.","Udi":"umb://media/bcc26d14bae0400cae892af74a042fc9","SourceContext":"Umbraco.Cms.Infrastructure.Persistence.Repositories.Implement.DocumentRepository","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:22.6275936Z","@mt":"The reference to {Udi} can not be saved as relation, because doesn't have a node ID.","Udi":"umb://media/ed4dda27289745c69d594a9f5499088c","SourceContext":"Umbraco.Cms.Infrastructure.Persistence.Repositories.Implement.DocumentRepository","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:22.6275969Z","@mt":"The reference to {Udi} can not be saved as relation, because doesn't have a node ID.","Udi":"umb://media/b8a7a98394604d97bd3d365c75bb0ab3","SourceContext":"Umbraco.Cms.Infrastructure.Persistence.Repositories.Implement.DocumentRepository","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:22.6276419Z","@mt":"The reference to {Udi} can not be saved as relation, because doesn't have a node ID.","Udi":"umb://media/619347b1d07b421c8f0feb0de4414255","SourceContext":"Umbraco.Cms.Infrastructure.Persistence.Repositories.Implement.DocumentRepository","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:22.6276978Z","@mt":"The reference to {Udi} can not be saved as relation, because doesn't have a node ID.","Udi":"umb://document/485343b1d99c4789a676e9b4c98a38d4","SourceContext":"Umbraco.Cms.Infrastructure.Persistence.Repositories.Implement.DocumentRepository","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:22.6277108Z","@mt":"The reference to {Udi} can not be saved as relation, because doesn't have a node ID.","Udi":"umb://document/5582ae2fefa8422fa9c97ff1c9e8dd85","SourceContext":"Umbraco.Cms.Infrastructure.Persistence.Repositories.Implement.DocumentRepository","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:22.6277216Z","@mt":"The reference to {Udi} can not be saved as relation, because doesn't have a node ID.","Udi":"umb://document/29b0213bbf6b4181a1b9a1aa41dc36e9","SourceContext":"Umbraco.Cms.Infrastructure.Persistence.Repositories.Implement.DocumentRepository","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:22.6277288Z","@mt":"The reference to {Udi} can not be saved as relation, because doesn't have a node ID.","Udi":"umb://document/11f34a4454ae4404a16a86b6aecedee4","SourceContext":"Umbraco.Cms.Infrastructure.Persistence.Repositories.Implement.DocumentRepository","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:22.6277630Z","@mt":"The reference to {Udi} can not be saved as relation, because doesn't have a node ID.","Udi":"umb://document/eafde402622248f989fe973dff9ab880","SourceContext":"Umbraco.Cms.Infrastructure.Persistence.Repositories.Implement.DocumentRepository","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:22.6278006Z","@mt":"The reference to {Udi} can not be saved as relation, because doesn't have a node ID.","Udi":"umb://document/bff7b1f1cc494bda8699ecb85f18bc83","SourceContext":"Umbraco.Cms.Infrastructure.Persistence.Repositories.Implement.DocumentRepository","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:22.6278151Z","@mt":"The reference to {Udi} can not be saved as relation, because doesn't have a node ID.","Udi":"umb://document/e29fd99c5d6048ab888f26a551d1b056","SourceContext":"Umbraco.Cms.Infrastructure.Persistence.Repositories.Implement.DocumentRepository","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:22.6278237Z","@mt":"The reference to {Udi} can not be saved as relation, because doesn't have a node ID.","Udi":"umb://document/79b4ffc708ee4fdda5133e9261859ce5","SourceContext":"Umbraco.Cms.Infrastructure.Persistence.Repositories.Implement.DocumentRepository","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:22.6278535Z","@mt":"The reference to {Udi} can not be saved as relation, because doesn't have a node ID.","Udi":"umb://document/71a7e71e7db24b9da71c559b11c683c0","SourceContext":"Umbraco.Cms.Infrastructure.Persistence.Repositories.Implement.DocumentRepository","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:22.6278667Z","@mt":"The reference to {Udi} can not be saved as relation, because doesn't have a node ID.","Udi":"umb://media/9d5e89e93e4f40c5a4cecf5d0faa840d","SourceContext":"Umbraco.Cms.Infrastructure.Persistence.Repositories.Implement.DocumentRepository","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:22.6280144Z","@mt":"The reference to {Udi} can not be saved as relation, because doesn't have a node ID.","Udi":"umb://media/c99cf6b963ea4bc7b587aed230d54349","SourceContext":"Umbraco.Cms.Infrastructure.Persistence.Repositories.Implement.DocumentRepository","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:22.6280260Z","@mt":"The reference to {Udi} can not be saved as relation, because doesn't have a node ID.","Udi":"umb://media/4caeb00085d340edbc3897f2a0ed141a","SourceContext":"Umbraco.Cms.Infrastructure.Persistence.Repositories.Implement.DocumentRepository","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:22.6829883Z","@mt":"The reference to {Udi} can not be saved as relation, because doesn't have a node ID.","Udi":"umb://document/2ea83cc7d86543dcae6c78e2eb7acf90","SourceContext":"Umbraco.Cms.Infrastructure.Persistence.Repositories.Implement.DocumentRepository","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:22.6830557Z","@mt":"The reference to {Udi} can not be saved as relation, because doesn't have a node ID.","Udi":"umb://document/a37f60827a564f29b27de7584ede41d6","SourceContext":"Umbraco.Cms.Infrastructure.Persistence.Repositories.Implement.DocumentRepository","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:22.6830747Z","@mt":"The reference to {Udi} can not be saved as relation, because doesn't have a node ID.","Udi":"umb://document/cb88aaa910a942fbac7be3e993d493f5","SourceContext":"Umbraco.Cms.Infrastructure.Persistence.Repositories.Implement.DocumentRepository","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:22.7623863Z","@mt":"The reference to {Udi} can not be saved as relation, because doesn't have a node ID.","Udi":"umb://media/c787c02309ac4fe98041f1e4ba253929","SourceContext":"Umbraco.Cms.Infrastructure.Persistence.Repositories.Implement.DocumentRepository","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:22.7624935Z","@mt":"The reference to {Udi} can not be saved as relation, because doesn't have a node ID.","Udi":"umb://media/ff9472052bce41d285fb3c8fe46039c2","SourceContext":"Umbraco.Cms.Infrastructure.Persistence.Repositories.Implement.DocumentRepository","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:22.7625171Z","@mt":"The reference to {Udi} can not be saved as relation, because doesn't have a node ID.","Udi":"umb://media/e8b769066a7a431895ed138368d7cc73","SourceContext":"Umbraco.Cms.Infrastructure.Persistence.Repositories.Implement.DocumentRepository","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:22.7625283Z","@mt":"The reference to {Udi} can not be saved as relation, because doesn't have a node ID.","Udi":"umb://media/a6fc50f8070c4ae996a44df3e6357979","SourceContext":"Umbraco.Cms.Infrastructure.Persistence.Repositories.Implement.DocumentRepository","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:22.7625355Z","@mt":"The reference to {Udi} can not be saved as relation, because doesn't have a node ID.","Udi":"umb://media/36a2398ece454bb491bce13160d09dab","SourceContext":"Umbraco.Cms.Infrastructure.Persistence.Repositories.Implement.DocumentRepository","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:22.7625417Z","@mt":"The reference to {Udi} can not be saved as relation, because doesn't have a node ID.","Udi":"umb://media/b4770d8785f341afb9fd5bdb1cb63fbb","SourceContext":"Umbraco.Cms.Infrastructure.Persistence.Repositories.Implement.DocumentRepository","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:22.7625477Z","@mt":"The reference to {Udi} can not be saved as relation, because doesn't have a node ID.","Udi":"umb://media/d95b06bbc0a04049bb1ae219df6a8a91","SourceContext":"Umbraco.Cms.Infrastructure.Persistence.Repositories.Implement.DocumentRepository","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:22.7625540Z","@mt":"The reference to {Udi} can not be saved as relation, because doesn't have a node ID.","Udi":"umb://media/e499f99be12e45b380420cb9d8983f41","SourceContext":"Umbraco.Cms.Infrastructure.Persistence.Repositories.Implement.DocumentRepository","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:22.7625645Z","@mt":"The reference to {Udi} can not be saved as relation, because doesn't have a node ID.","Udi":"umb://media/b92ab5420ef644babf7bed8c6657c1f2","SourceContext":"Umbraco.Cms.Infrastructure.Persistence.Repositories.Implement.DocumentRepository","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:22.7625709Z","@mt":"The reference to {Udi} can not be saved as relation, because doesn't have a node ID.","Udi":"umb://media/50d4b72cfd564397a1373c6189cd80a3","SourceContext":"Umbraco.Cms.Infrastructure.Persistence.Repositories.Implement.DocumentRepository","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:22.7625767Z","@mt":"The reference to {Udi} can not be saved as relation, because doesn't have a node ID.","Udi":"umb://media/7542297a93b44b03aae163c80cc9bce2","SourceContext":"Umbraco.Cms.Infrastructure.Persistence.Repositories.Implement.DocumentRepository","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:22.7625826Z","@mt":"The reference to {Udi} can not be saved as relation, because doesn't have a node ID.","Udi":"umb://media/e69634b1b8a343fd9bc6795256b56a70","SourceContext":"Umbraco.Cms.Infrastructure.Persistence.Repositories.Implement.DocumentRepository","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:22.8098279Z","@mt":"The reference to {Udi} can not be saved as relation, because doesn't have a node ID.","Udi":"umb://media/8aaef92870ba40948076fd2268aebfe5","SourceContext":"Umbraco.Cms.Infrastructure.Persistence.Repositories.Implement.DocumentRepository","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:22.8098924Z","@mt":"The reference to {Udi} can not be saved as relation, because doesn't have a node ID.","Udi":"umb://media/a3171294a87c4d58bd6656f96036fa15","SourceContext":"Umbraco.Cms.Infrastructure.Persistence.Repositories.Implement.DocumentRepository","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:22.8099071Z","@mt":"The reference to {Udi} can not be saved as relation, because doesn't have a node ID.","Udi":"umb://media/8868a614fa9d4d8e8f77d9a97998e049","SourceContext":"Umbraco.Cms.Infrastructure.Persistence.Repositories.Implement.DocumentRepository","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:22.8099150Z","@mt":"The reference to {Udi} can not be saved as relation, because doesn't have a node ID.","Udi":"umb://media/de521c78730f4aaf9be6c927b09ecce5","SourceContext":"Umbraco.Cms.Infrastructure.Persistence.Repositories.Implement.DocumentRepository","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:22.8099273Z","@mt":"The reference to {Udi} can not be saved as relation, because doesn't have a node ID.","Udi":"umb://media/50962c395192454f847e411aa2a95d5d","SourceContext":"Umbraco.Cms.Infrastructure.Persistence.Repositories.Implement.DocumentRepository","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:22.8099353Z","@mt":"The reference to {Udi} can not be saved as relation, because doesn't have a node ID.","Udi":"umb://media/614ac8d0981a4178acb5423a45315f91","SourceContext":"Umbraco.Cms.Infrastructure.Persistence.Repositories.Implement.DocumentRepository","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:22.8099424Z","@mt":"The reference to {Udi} can not be saved as relation, because doesn't have a node ID.","Udi":"umb://media/f1058f2c0ab141019ad1d24037a86790","SourceContext":"Umbraco.Cms.Infrastructure.Persistence.Repositories.Implement.DocumentRepository","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:22.8099491Z","@mt":"The reference to {Udi} can not be saved as relation, because doesn't have a node ID.","Udi":"umb://media/757e794cca8b49fe8c41880d09a71423","SourceContext":"Umbraco.Cms.Infrastructure.Persistence.Repositories.Implement.DocumentRepository","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:22.8099550Z","@mt":"The reference to {Udi} can not be saved as relation, because doesn't have a node ID.","Udi":"umb://media/ef63bd3374c04979beb2ae86f546d9d4","SourceContext":"Umbraco.Cms.Infrastructure.Persistence.Repositories.Implement.DocumentRepository","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:22.8099611Z","@mt":"The reference to {Udi} can not be saved as relation, because doesn't have a node ID.","Udi":"umb://media/1ba2ea746028406b9dd7eb842007dc59","SourceContext":"Umbraco.Cms.Infrastructure.Persistence.Repositories.Implement.DocumentRepository","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:22.8099682Z","@mt":"The reference to {Udi} can not be saved as relation, because doesn't have a node ID.","Udi":"umb://media/a1d65a543e38455ebf7f900a880b382f","SourceContext":"Umbraco.Cms.Infrastructure.Persistence.Repositories.Implement.DocumentRepository","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:22.8452574Z","@mt":"The reference to {Udi} can not be saved as relation, because doesn't have a node ID.","Udi":"umb://media/33f469ca22204bee852fd8611a027e59","SourceContext":"Umbraco.Cms.Infrastructure.Persistence.Repositories.Implement.DocumentRepository","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:22.8453274Z","@mt":"The reference to {Udi} can not be saved as relation, because doesn't have a node ID.","Udi":"umb://media/55e897a0f4cf4a45976d6e30c7157a38","SourceContext":"Umbraco.Cms.Infrastructure.Persistence.Repositories.Implement.DocumentRepository","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:22.8453415Z","@mt":"The reference to {Udi} can not be saved as relation, because doesn't have a node ID.","Udi":"umb://media/2d286cd1ade74d36b94d60cf068fd073","SourceContext":"Umbraco.Cms.Infrastructure.Persistence.Repositories.Implement.DocumentRepository","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:22.8453485Z","@mt":"The reference to {Udi} can not be saved as relation, because doesn't have a node ID.","Udi":"umb://media/6e25dce32bb644daa66166b9c030cb5c","SourceContext":"Umbraco.Cms.Infrastructure.Persistence.Repositories.Implement.DocumentRepository","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:22.8453556Z","@mt":"The reference to {Udi} can not be saved as relation, because doesn't have a node ID.","Udi":"umb://media/377997f4a7bf4c2082029f9f8252fa59","SourceContext":"Umbraco.Cms.Infrastructure.Persistence.Repositories.Implement.DocumentRepository","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:22.8453654Z","@mt":"The reference to {Udi} can not be saved as relation, because doesn't have a node ID.","Udi":"umb://media/21a986cb390d47d8ace2245d6be24853","SourceContext":"Umbraco.Cms.Infrastructure.Persistence.Repositories.Implement.DocumentRepository","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:22.9004546Z","@mt":"The reference to {Udi} can not be saved as relation, because doesn't have a node ID.","Udi":"umb://media/208b049c30b64fb3bbb49258dd989da4","SourceContext":"Umbraco.Cms.Infrastructure.Persistence.Repositories.Implement.DocumentRepository","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:22.9004773Z","@mt":"The reference to {Udi} can not be saved as relation, because doesn't have a node ID.","Udi":"umb://media/8a09b42d13334a80b00c70b071f5a447","SourceContext":"Umbraco.Cms.Infrastructure.Persistence.Repositories.Implement.DocumentRepository","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:22.9004823Z","@mt":"The reference to {Udi} can not be saved as relation, because doesn't have a node ID.","Udi":"umb://media/3740eb9021d1498290bc025b4c420bc5","SourceContext":"Umbraco.Cms.Infrastructure.Persistence.Repositories.Implement.DocumentRepository","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:22.9004847Z","@mt":"The reference to {Udi} can not be saved as relation, because doesn't have a node ID.","Udi":"umb://media/bb15ceb9a1bb436fb258aebe955ab1be","SourceContext":"Umbraco.Cms.Infrastructure.Persistence.Repositories.Implement.DocumentRepository","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:22.9004868Z","@mt":"The reference to {Udi} can not be saved as relation, because doesn't have a node ID.","Udi":"umb://media/3b9d116bcf3c440abd72eed26aa5f1d2","SourceContext":"Umbraco.Cms.Infrastructure.Persistence.Repositories.Implement.DocumentRepository","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:22.9004890Z","@mt":"The reference to {Udi} can not be saved as relation, because doesn't have a node ID.","Udi":"umb://media/d8a3db8f7698459581c0ef7ab556ceb4","SourceContext":"Umbraco.Cms.Infrastructure.Persistence.Repositories.Implement.DocumentRepository","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:22.9004910Z","@mt":"The reference to {Udi} can not be saved as relation, because doesn't have a node ID.","Udi":"umb://media/43ab02b52f3f4f5197238f938734fd33","SourceContext":"Umbraco.Cms.Infrastructure.Persistence.Repositories.Implement.DocumentRepository","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:22.9004939Z","@mt":"The reference to {Udi} can not be saved as relation, because doesn't have a node ID.","Udi":"umb://media/43f35b3db136464db8d404f0ac87fd0d","SourceContext":"Umbraco.Cms.Infrastructure.Persistence.Repositories.Implement.DocumentRepository","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:22.9004959Z","@mt":"The reference to {Udi} can not be saved as relation, because doesn't have a node ID.","Udi":"umb://media/534e4a38224045979288416d053d0362","SourceContext":"Umbraco.Cms.Infrastructure.Persistence.Repositories.Implement.DocumentRepository","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:22.9228062Z","@mt":"The reference to {Udi} can not be saved as relation, because doesn't have a node ID.","Udi":"umb://media/66201e8892444ae0a7af6534bda0e455","SourceContext":"Umbraco.Cms.Infrastructure.Persistence.Repositories.Implement.DocumentRepository","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:22.9228928Z","@mt":"The reference to {Udi} can not be saved as relation, because doesn't have a node ID.","Udi":"umb://media/65e6a42c2a2e4b3ebbda86a6ec0972ce","SourceContext":"Umbraco.Cms.Infrastructure.Persistence.Repositories.Implement.DocumentRepository","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:22.9229106Z","@mt":"The reference to {Udi} can not be saved as relation, because doesn't have a node ID.","Udi":"umb://media/a814b2cad53a444d8c45dcf2f0ae607f","SourceContext":"Umbraco.Cms.Infrastructure.Persistence.Repositories.Implement.DocumentRepository","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:22.9229197Z","@mt":"The reference to {Udi} can not be saved as relation, because doesn't have a node ID.","Udi":"umb://media/ba52c5d5541148dbacc80d396b5ec157","SourceContext":"Umbraco.Cms.Infrastructure.Persistence.Repositories.Implement.DocumentRepository","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:22.9494681Z","@mt":"The reference to {Udi} can not be saved as relation, because doesn't have a node ID.","Udi":"umb://media/208b049c30b64fb3bbb49258dd989da4","SourceContext":"Umbraco.Cms.Infrastructure.Persistence.Repositories.Implement.DocumentRepository","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:22.9495258Z","@mt":"The reference to {Udi} can not be saved as relation, because doesn't have a node ID.","Udi":"umb://media/f9f4baa50cde43af9e2a878bc94bf618","SourceContext":"Umbraco.Cms.Infrastructure.Persistence.Repositories.Implement.DocumentRepository","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:22.9495437Z","@mt":"The reference to {Udi} can not be saved as relation, because doesn't have a node ID.","Udi":"umb://media/368c11c2af2243bc9c81b6fc5aa6a763","SourceContext":"Umbraco.Cms.Infrastructure.Persistence.Repositories.Implement.DocumentRepository","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:22.9495509Z","@mt":"The reference to {Udi} can not be saved as relation, because doesn't have a node ID.","Udi":"umb://media/656d735749a64872a9e9b0ec4d2e8da5","SourceContext":"Umbraco.Cms.Infrastructure.Persistence.Repositories.Implement.DocumentRepository","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:22.9495575Z","@mt":"The reference to {Udi} can not be saved as relation, because doesn't have a node ID.","Udi":"umb://media/83901f511f2e4622bceba91a27e50177","SourceContext":"Umbraco.Cms.Infrastructure.Persistence.Repositories.Implement.DocumentRepository","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:22.9495647Z","@mt":"The reference to {Udi} can not be saved as relation, because doesn't have a node ID.","Udi":"umb://media/c1ce6136f5a647458a2af50437b58746","SourceContext":"Umbraco.Cms.Infrastructure.Persistence.Repositories.Implement.DocumentRepository","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:22.9764625Z","@mt":"The reference to {Udi} can not be saved as relation, because doesn't have a node ID.","Udi":"umb://media/45265098a37b44a488938930870f2f40","SourceContext":"Umbraco.Cms.Infrastructure.Persistence.Repositories.Implement.DocumentRepository","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:22.9765234Z","@mt":"The reference to {Udi} can not be saved as relation, because doesn't have a node ID.","Udi":"umb://media/c685194ae61545bf87b4e685301324a1","SourceContext":"Umbraco.Cms.Infrastructure.Persistence.Repositories.Implement.DocumentRepository","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:22.9765440Z","@mt":"The reference to {Udi} can not be saved as relation, because doesn't have a node ID.","Udi":"umb://media/6ff24563180c4bd5956d86ba4c90a5f1","SourceContext":"Umbraco.Cms.Infrastructure.Persistence.Repositories.Implement.DocumentRepository","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:22.9765605Z","@mt":"The reference to {Udi} can not be saved as relation, because doesn't have a node ID.","Udi":"umb://media/2892a95869ee4ee98081e72f2ba011bf","SourceContext":"Umbraco.Cms.Infrastructure.Persistence.Repositories.Implement.DocumentRepository","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:22.9765698Z","@mt":"The reference to {Udi} can not be saved as relation, because doesn't have a node ID.","Udi":"umb://media/d0865937fdc5470999303dd53efdb6d3","SourceContext":"Umbraco.Cms.Infrastructure.Persistence.Repositories.Implement.DocumentRepository","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:23.0104112Z","@mt":"The reference to {Udi} can not be saved as relation, because doesn't have a node ID.","Udi":"umb://media/a2e7d7856f254edcb24e87b9de4f3351","SourceContext":"Umbraco.Cms.Infrastructure.Persistence.Repositories.Implement.DocumentRepository","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:23.0104887Z","@mt":"The reference to {Udi} can not be saved as relation, because doesn't have a node ID.","Udi":"umb://media/e86f1687e1f44952bf2ca99ed7aebe50","SourceContext":"Umbraco.Cms.Infrastructure.Persistence.Repositories.Implement.DocumentRepository","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:23.0105070Z","@mt":"The reference to {Udi} can not be saved as relation, because doesn't have a node ID.","Udi":"umb://media/a02c266e10504ea1918f56a084474a25","SourceContext":"Umbraco.Cms.Infrastructure.Persistence.Repositories.Implement.DocumentRepository","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:23.0105297Z","@mt":"The reference to {Udi} can not be saved as relation, because doesn't have a node ID.","Udi":"umb://media/09c7cbfd788f4b40afecb584bcf234c7","SourceContext":"Umbraco.Cms.Infrastructure.Persistence.Repositories.Implement.DocumentRepository","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:23.0105478Z","@mt":"The reference to {Udi} can not be saved as relation, because doesn't have a node ID.","Udi":"umb://media/0e890ab4e911432aaf331d31c2b40bd1","SourceContext":"Umbraco.Cms.Infrastructure.Persistence.Repositories.Implement.DocumentRepository","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:23.0501428Z","@mt":"The reference to {Udi} can not be saved as relation, because doesn't have a node ID.","Udi":"umb://media/90c9c84f819a440ab0aa676a9dace63d","SourceContext":"Umbraco.Cms.Infrastructure.Persistence.Repositories.Implement.DocumentRepository","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:23.0501979Z","@mt":"The reference to {Udi} can not be saved as relation, because doesn't have a node ID.","Udi":"umb://media/db4de26762814b98b4c620aa789498db","SourceContext":"Umbraco.Cms.Infrastructure.Persistence.Repositories.Implement.DocumentRepository","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:23.0502116Z","@mt":"The reference to {Udi} can not be saved as relation, because doesn't have a node ID.","Udi":"umb://media/3f7c616c6d8f44f9b0098ee76da53fcf","SourceContext":"Umbraco.Cms.Infrastructure.Persistence.Repositories.Implement.DocumentRepository","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:23.0502200Z","@mt":"The reference to {Udi} can not be saved as relation, because doesn't have a node ID.","Udi":"umb://media/853e4ff6af9f429da818e89818014eaf","SourceContext":"Umbraco.Cms.Infrastructure.Persistence.Repositories.Implement.DocumentRepository","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:23.0502268Z","@mt":"The reference to {Udi} can not be saved as relation, because doesn't have a node ID.","Udi":"umb://media/577cd6431dd046eda9daf09e548b1d81","SourceContext":"Umbraco.Cms.Infrastructure.Persistence.Repositories.Implement.DocumentRepository","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:23.0502331Z","@mt":"The reference to {Udi} can not be saved as relation, because doesn't have a node ID.","Udi":"umb://media/f14152ec8f474dd08cef3977d1484139","SourceContext":"Umbraco.Cms.Infrastructure.Persistence.Repositories.Implement.DocumentRepository","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:23.0670007Z","@mt":"The reference to {Udi} can not be saved as relation, because doesn't have a node ID.","Udi":"umb://media/8aaef92870ba40948076fd2268aebfe5","SourceContext":"Umbraco.Cms.Infrastructure.Persistence.Repositories.Implement.DocumentRepository","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:23.1016808Z","@mt":"The reference to {Udi} can not be saved as relation, because doesn't have a node ID.","Udi":"umb://document/71a7e71e7db24b9da71c559b11c683c0","SourceContext":"Umbraco.Cms.Infrastructure.Persistence.Repositories.Implement.DocumentRepository","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:23.1278848Z","@mt":"The reference to {Udi} can not be saved as relation, because doesn't have a node ID.","Udi":"umb://document/71a7e71e7db24b9da71c559b11c683c0","SourceContext":"Umbraco.Cms.Infrastructure.Persistence.Repositories.Implement.DocumentRepository","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:23.1419795Z","@mt":"The reference to {Udi} can not be saved as relation, because doesn't have a node ID.","Udi":"umb://document/71a7e71e7db24b9da71c559b11c683c0","SourceContext":"Umbraco.Cms.Infrastructure.Persistence.Repositories.Implement.DocumentRepository","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:23.1595839Z","@mt":"The reference to {Udi} can not be saved as relation, because doesn't have a node ID.","Udi":"umb://document/9c4dffe2201541998576fdf7120c861d","SourceContext":"Umbraco.Cms.Infrastructure.Persistence.Repositories.Implement.DocumentRepository","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:23.2120980Z","@mt":"The reference to {Udi} can not be saved as relation, because doesn't have a node ID.","Udi":"umb://media/6e6358f2632d48918d644040b0b9e9e1","SourceContext":"Umbraco.Cms.Infrastructure.Persistence.Repositories.Implement.DocumentRepository","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:23.2366385Z","@mt":"The reference to {Udi} can not be saved as relation, because doesn't have a node ID.","Udi":"umb://media/ca7083c3e08a44a6bd9d6ae37c2c23af","SourceContext":"Umbraco.Cms.Infrastructure.Persistence.Repositories.Implement.DocumentRepository","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:23.2646797Z","@mt":"The reference to {Udi} can not be saved as relation, because doesn't have a node ID.","Udi":"umb://media/b78914f63588416d9a1bc9c975a5273c","SourceContext":"Umbraco.Cms.Infrastructure.Persistence.Repositories.Implement.DocumentRepository","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:23.2647555Z","@mt":"The reference to {Udi} can not be saved as relation, because doesn't have a node ID.","Udi":"umb://media/7062c50dfb9d40faae12ebaed03f439e","SourceContext":"Umbraco.Cms.Infrastructure.Persistence.Repositories.Implement.DocumentRepository","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:23.2647632Z","@mt":"The reference to {Udi} can not be saved as relation, because doesn't have a node ID.","Udi":"umb://media/0633b8ded16342b9b6876e104bce780f","SourceContext":"Umbraco.Cms.Infrastructure.Persistence.Repositories.Implement.DocumentRepository","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:23.2647658Z","@mt":"The reference to {Udi} can not be saved as relation, because doesn't have a node ID.","Udi":"umb://media/648651e27055412885eedac2453577b5","SourceContext":"Umbraco.Cms.Infrastructure.Persistence.Repositories.Implement.DocumentRepository","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:23.2647679Z","@mt":"The reference to {Udi} can not be saved as relation, because doesn't have a node ID.","Udi":"umb://media/f158621aa7594660af812bd599858c6f","SourceContext":"Umbraco.Cms.Infrastructure.Persistence.Repositories.Implement.DocumentRepository","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:23.2647976Z","@mt":"The reference to {Udi} can not be saved as relation, because doesn't have a node ID.","Udi":"umb://media/6c4bce902b5043c29771b149812d598c","SourceContext":"Umbraco.Cms.Infrastructure.Persistence.Repositories.Implement.DocumentRepository","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:23.2648038Z","@mt":"The reference to {Udi} can not be saved as relation, because doesn't have a node ID.","Udi":"umb://media/dfdb62c081754987b733fa18edaced41","SourceContext":"Umbraco.Cms.Infrastructure.Persistence.Repositories.Implement.DocumentRepository","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:23.2648096Z","@mt":"The reference to {Udi} can not be saved as relation, because doesn't have a node ID.","Udi":"umb://media/f3af5c19bc254c23b5772c37d430149e","SourceContext":"Umbraco.Cms.Infrastructure.Persistence.Repositories.Implement.DocumentRepository","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:23.2648151Z","@mt":"The reference to {Udi} can not be saved as relation, because doesn't have a node ID.","Udi":"umb://media/df10b084502842d9a16d552a742391ca","SourceContext":"Umbraco.Cms.Infrastructure.Persistence.Repositories.Implement.DocumentRepository","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:23.2648214Z","@mt":"The reference to {Udi} can not be saved as relation, because doesn't have a node ID.","Udi":"umb://media/e536984d5fba4862a8a1d0cc355042c3","SourceContext":"Umbraco.Cms.Infrastructure.Persistence.Repositories.Implement.DocumentRepository","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:23.2879908Z","@mt":"The reference to {Udi} can not be saved as relation, because doesn't have a node ID.","Udi":"umb://document/ab815464c2f24b34a627f73d94b6fbd4","SourceContext":"Umbraco.Cms.Infrastructure.Persistence.Repositories.Implement.DocumentRepository","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:23.2880892Z","@mt":"The reference to {Udi} can not be saved as relation, because doesn't have a node ID.","Udi":"umb://media/bfe89a2246564b7680e2d08427b2895e","SourceContext":"Umbraco.Cms.Infrastructure.Persistence.Repositories.Implement.DocumentRepository","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:23.2881027Z","@mt":"The reference to {Udi} can not be saved as relation, because doesn't have a node ID.","Udi":"umb://media/24612ca48fde42ad81a2787b8c4e13c7","SourceContext":"Umbraco.Cms.Infrastructure.Persistence.Repositories.Implement.DocumentRepository","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:23.2881103Z","@mt":"The reference to {Udi} can not be saved as relation, because doesn't have a node ID.","Udi":"umb://media/48bb958368dd40dc9a3ccf373d44e2f1","SourceContext":"Umbraco.Cms.Infrastructure.Persistence.Repositories.Implement.DocumentRepository","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:23.3427562Z","@mt":"The reference to {Udi} can not be saved as relation, because doesn't have a node ID.","Udi":"umb://media/6ef4afe005534b82b80b854dc3be8ab0","SourceContext":"Umbraco.Cms.Infrastructure.Persistence.Repositories.Implement.DocumentRepository","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:23.3428074Z","@mt":"The reference to {Udi} can not be saved as relation, because doesn't have a node ID.","Udi":"umb://media/7d6b3860030048b69b9bd0ca639a6ee2","SourceContext":"Umbraco.Cms.Infrastructure.Persistence.Repositories.Implement.DocumentRepository","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:23.3428288Z","@mt":"The reference to {Udi} can not be saved as relation, because doesn't have a node ID.","Udi":"umb://media/a105e855ae3c48d5a032980cd15ab96f","SourceContext":"Umbraco.Cms.Infrastructure.Persistence.Repositories.Implement.DocumentRepository","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:23.3428366Z","@mt":"The reference to {Udi} can not be saved as relation, because doesn't have a node ID.","Udi":"umb://media/f4fef72480be49df897192c02a8c75ca","SourceContext":"Umbraco.Cms.Infrastructure.Persistence.Repositories.Implement.DocumentRepository","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:23.3428445Z","@mt":"The reference to {Udi} can not be saved as relation, because doesn't have a node ID.","Udi":"umb://media/f936044efffc4923be67b0ce27be4bf9","SourceContext":"Umbraco.Cms.Infrastructure.Persistence.Repositories.Implement.DocumentRepository","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:23.3428516Z","@mt":"The reference to {Udi} can not be saved as relation, because doesn't have a node ID.","Udi":"umb://media/a041c85240264a0090504ddf9818bcc4","SourceContext":"Umbraco.Cms.Infrastructure.Persistence.Repositories.Implement.DocumentRepository","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:23.3428599Z","@mt":"The reference to {Udi} can not be saved as relation, because doesn't have a node ID.","Udi":"umb://media/97bb23ee60164e6f81d801539eda4a01","SourceContext":"Umbraco.Cms.Infrastructure.Persistence.Repositories.Implement.DocumentRepository","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:23.3428657Z","@mt":"The reference to {Udi} can not be saved as relation, because doesn't have a node ID.","Udi":"umb://media/70ba30b874ca4cd79cce10d37f271fe4","SourceContext":"Umbraco.Cms.Infrastructure.Persistence.Repositories.Implement.DocumentRepository","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:23.3428719Z","@mt":"The reference to {Udi} can not be saved as relation, because doesn't have a node ID.","Udi":"umb://media/bf105fbf9cd54014be24d4ba455bb278","SourceContext":"Umbraco.Cms.Infrastructure.Persistence.Repositories.Implement.DocumentRepository","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:23.3428786Z","@mt":"The reference to {Udi} can not be saved as relation, because doesn't have a node ID.","Udi":"umb://media/65029f2a27f2498f9c0b1a13b5ee7401","SourceContext":"Umbraco.Cms.Infrastructure.Persistence.Repositories.Implement.DocumentRepository","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:27.9810861Z","@mt":"Package migration executed. Summary: Conflicting macros found, they will be overwritten: FAQ, latestBlogposts, featuredProducts, featuredProduct\r\nConflicting templates found, they will be overwritten: About, Author, Blog, Blogpost, Contact, ContentPage, Home, LandingPage, Master, NotFoundError, Portfolio, Project, robotsTxt, Service, Services, Vacancy, XMLSitemap\r\nConflicting stylesheets found, they will be overwritten: rte, site, styles, umbraco-starterkit-style\r\nData types installed: 297\r\nLanguages installed: 1\r\nDictionary items installed: 54\r\nMacros installed: 4\r\nMacro partial views installed: 3\r\nTemplates installed: 17\r\nDocument types installed: 55\r\nMedia types installed: 3\r\nStylesheets installed: 23\r\nScripts installed: 0\r\nPartial views installed: 15\r\nEntity containers installed: 1\r\nContent items installed: 33\r\nMedia items installed: 159","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.MigrationContext","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:28.0894036Z","@mt":"At {OrigState}","OrigState":"5647FFC7-4773-40C4-9E39-5E466C4BAAD2","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.MigrationPlanExecutor","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:28.0896073Z","@mt":"Done","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.MigrationPlanExecutor","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:28.0966135Z","@mt":"{EndMessage} ({Duration}ms) [Timing {TimingId}]","EndMessage":"Unattended upgrade completed for Digital Agency Starter Kit by ByteAnt","Duration":12616,"TimingId":"e7b3d2a","SourceContext":"Umbraco.Cms.Core.Logging.ProfilingLogger","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:28.2797765Z","@mt":"Registered with MainDom, localContentDbExists? {LocalContentDbExists}, localMediaDbExists? {LocalMediaDbExists}","LocalContentDbExists":false,"LocalMediaDbExists":false,"SourceContext":"Umbraco.Cms.Infrastructure.PublishedCache.PublishedSnapshotService","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:28.2798418Z","@mt":"Creating the content store, localContentDbExists? {LocalContentDbExists}","LocalContentDbExists":false,"SourceContext":"Umbraco.Cms.Infrastructure.PublishedCache.PublishedSnapshotService","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:28.2825066Z","@mt":"Creating the media store, localMediaDbExists? {LocalMediaDbExists}","LocalMediaDbExists":false,"SourceContext":"Umbraco.Cms.Infrastructure.PublishedCache.PublishedSnapshotService","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:28.3549265Z","@mt":"{StartMessage} [Timing {TimingId}]","StartMessage":"Loading content from database","TimingId":"33fb636","SourceContext":"Umbraco.Cms.Core.Logging.ProfilingLogger","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:28.4733059Z","@mt":"{EndMessage} ({Duration}ms) [Timing {TimingId}]","EndMessage":"Completed.","Duration":118,"TimingId":"33fb636","SourceContext":"Umbraco.Cms.Core.Logging.ProfilingLogger","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:28.4760373Z","@mt":"{StartMessage} [Timing {TimingId}]","StartMessage":"Loading media from database","TimingId":"f9e27da","SourceContext":"Umbraco.Cms.Core.Logging.ProfilingLogger","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:28.5077187Z","@mt":"{EndMessage} ({Duration}ms) [Timing {TimingId}]","EndMessage":"Completed.","Duration":31,"TimingId":"f9e27da","SourceContext":"Umbraco.Cms.Core.Logging.ProfilingLogger","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:28.7893700Z","@mt":"Document {ContentName} (id={ContentId}) cultures: {Cultures} have been published.","ContentName":"Home","ContentId":1425,"Cultures":"en-us,de","SourceContext":"Umbraco.Cms.Core.Services.ContentService","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:29.0607693Z","@mt":"Document {ContentName} (id={ContentId}) cultures: {Cultures} have been published.","ContentName":"What we do","ContentId":1426,"Cultures":"en-us,de","SourceContext":"Umbraco.Cms.Core.Services.ContentService","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:29.2279380Z","@mt":"Document {ContentName} (id={ContentId}) cultures: {Cultures} have been published.","ContentName":"Web Development","ContentId":1427,"Cultures":"en-us,de","SourceContext":"Umbraco.Cms.Core.Services.ContentService","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:29.4993552Z","@mt":"Document {ContentName} (id={ContentId}) cultures: {Cultures} have been published.","ContentName":"SEO Services","ContentId":1428,"Cultures":"en-us,de","SourceContext":"Umbraco.Cms.Core.Services.ContentService","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:29.7424730Z","@mt":"Document {ContentName} (id={ContentId}) cultures: {Cultures} have been published.","ContentName":"Product Design & Build","ContentId":1429,"Cultures":"en-us,de","SourceContext":"Umbraco.Cms.Core.Services.ContentService","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:29.8626291Z","@mt":"Document {ContentName} (id={ContentId}) cultures: {Cultures} have been published.","ContentName":"Success stories","ContentId":1430,"Cultures":"en-us,de","SourceContext":"Umbraco.Cms.Core.Services.ContentService","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:29.9098275Z","@mt":"Document {ContentName} (id={ContentId}) cultures: {Cultures} have been published.","ContentName":"SmartLock","ContentId":1431,"Cultures":"en-us,de","SourceContext":"Umbraco.Cms.Core.Services.ContentService","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:29.9922048Z","@mt":"Document {ContentName} (id={ContentId}) cultures: {Cultures} have been published.","ContentName":"Healthcare Mobile App","ContentId":1432,"Cultures":"en-us","SourceContext":"Umbraco.Cms.Core.Services.ContentService","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:30.0683831Z","@mt":"Document {ContentName} (id={ContentId}) cultures: {Cultures} have been published.","ContentName":"GymShop Website","ContentId":1433,"Cultures":"en-us,de","SourceContext":"Umbraco.Cms.Core.Services.ContentService","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:30.1935893Z","@mt":"Document {ContentName} (id={ContentId}) cultures: {Cultures} have been published.","ContentName":"Real Estate Marketplace","ContentId":1434,"Cultures":"en-us,de","SourceContext":"Umbraco.Cms.Core.Services.ContentService","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:30.2802719Z","@mt":"Document {ContentName} (id={ContentId}) cultures: {Cultures} have been published.","ContentName":"Website enhancement for an event agency","ContentId":1435,"Cultures":"en-us,de","SourceContext":"Umbraco.Cms.Core.Services.ContentService","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:30.4005096Z","@mt":"Document {ContentName} (id={ContentId}) cultures: {Cultures} have been published.","ContentName":"Who we are","ContentId":1436,"Cultures":"en-us,de","SourceContext":"Umbraco.Cms.Core.Services.ContentService","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:30.4781719Z","@mt":"The reference to {Udi} can not be saved as relation, because doesn't have a node ID.","Udi":"umb://media/577cd6431dd046eda9daf09e548b1d81","SourceContext":"Umbraco.Cms.Infrastructure.Persistence.Repositories.Implement.DocumentRepository","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:30.4782366Z","@mt":"The reference to {Udi} can not be saved as relation, because doesn't have a node ID.","Udi":"umb://media/f14152ec8f474dd08cef3977d1484139","SourceContext":"Umbraco.Cms.Infrastructure.Persistence.Repositories.Implement.DocumentRepository","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:30.4872326Z","@mt":"Document {ContentName} (id={ContentId}) cultures: {Cultures} have been published.","ContentName":"Blog","ContentId":1437,"Cultures":"en-us,de","SourceContext":"Umbraco.Cms.Core.Services.ContentService","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:30.5178482Z","@mt":"Document {ContentName} (id={ContentId}) has been published.","ContentName":"Authors","ContentId":1438,"SourceContext":"Umbraco.Cms.Core.Services.ContentService","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:30.5392379Z","@mt":"Document {ContentName} (id={ContentId}) cultures: {Cultures} have been published.","ContentName":"John Snow","ContentId":1439,"Cultures":"en-us,de","SourceContext":"Umbraco.Cms.Core.Services.ContentService","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:30.5756121Z","@mt":"Document {ContentName} (id={ContentId}) has been published.","ContentName":"Calls to Action","ContentId":1440,"SourceContext":"Umbraco.Cms.Core.Services.ContentService","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:30.5923044Z","@mt":"Document {ContentName} (id={ContentId}) cultures: {Cultures} have been published.","ContentName":"ALL","ContentId":1441,"Cultures":"en-us","SourceContext":"Umbraco.Cms.Core.Services.ContentService","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:30.6200860Z","@mt":"Document {ContentName} (id={ContentId}) cultures: {Cultures} have been published.","ContentName":"TechTrends","ContentId":1442,"Cultures":"en-us","SourceContext":"Umbraco.Cms.Core.Services.ContentService","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:30.6461019Z","@mt":"Document {ContentName} (id={ContentId}) cultures: {Cultures} have been published.","ContentName":"IoT","ContentId":1443,"Cultures":"en-us","SourceContext":"Umbraco.Cms.Core.Services.ContentService","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:30.6645606Z","@mt":"The reference to {Udi} can not be saved as relation, because doesn't have a node ID.","Udi":"umb://document/9c4dffe2201541998576fdf7120c861d","SourceContext":"Umbraco.Cms.Infrastructure.Persistence.Repositories.Implement.DocumentRepository","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:30.6742502Z","@mt":"Document {ContentName} (id={ContentId}) cultures: {Cultures} have been published.","ContentName":"5 UX UI Design Trends that Dominate 2023 and Beyond","ContentId":1444,"Cultures":"en-us","SourceContext":"Umbraco.Cms.Core.Services.ContentService","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:30.7223196Z","@mt":"Document {ContentName} (id={ContentId}) cultures: {Cultures} have been published.","ContentName":"10 SEO tips to boost your website","ContentId":1445,"Cultures":"en-us","SourceContext":"Umbraco.Cms.Core.Services.ContentService","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:30.7692641Z","@mt":"Document {ContentName} (id={ContentId}) cultures: {Cultures} have been published.","ContentName":"What is web design? A comprehensive guide","ContentId":1446,"Cultures":"en-us","SourceContext":"Umbraco.Cms.Core.Services.ContentService","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:30.8227104Z","@mt":"Document {ContentName} (id={ContentId}) cultures: {Cultures} have been published.","ContentName":"An extremely comprehensive guide to branding and its importance","ContentId":1447,"Cultures":"en-us","SourceContext":"Umbraco.Cms.Core.Services.ContentService","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:30.8538144Z","@mt":"The reference to {Udi} can not be saved as relation, because doesn't have a node ID.","Udi":"umb://document/ab815464c2f24b34a627f73d94b6fbd4","SourceContext":"Umbraco.Cms.Infrastructure.Persistence.Repositories.Implement.DocumentRepository","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:30.8913252Z","@mt":"Document {ContentName} (id={ContentId}) cultures: {Cultures} have been published.","ContentName":"Contact us","ContentId":1448,"Cultures":"en-us,de","SourceContext":"Umbraco.Cms.Core.Services.ContentService","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:31.0092886Z","@mt":"Document {ContentName} (id={ContentId}) cultures: {Cultures} have been published.","ContentName":"Careers","ContentId":1449,"Cultures":"en-us,de","SourceContext":"Umbraco.Cms.Core.Services.ContentService","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:31.0891981Z","@mt":"The reference to {Udi} can not be saved as relation, because doesn't have a node ID.","Udi":"umb://media/f936044efffc4923be67b0ce27be4bf9","SourceContext":"Umbraco.Cms.Infrastructure.Persistence.Repositories.Implement.DocumentRepository","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:31.0893569Z","@mt":"The reference to {Udi} can not be saved as relation, because doesn't have a node ID.","Udi":"umb://media/a041c85240264a0090504ddf9818bcc4","SourceContext":"Umbraco.Cms.Infrastructure.Persistence.Repositories.Implement.DocumentRepository","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:31.0970560Z","@mt":"Document {ContentName} (id={ContentId}) cultures: {Cultures} have been published.","ContentName":"QA Engineer","ContentId":1450,"Cultures":"en-us,de","SourceContext":"Umbraco.Cms.Core.Services.ContentService","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:31.1211827Z","@mt":"Document {ContentName} (id={ContentId}) cultures: {Cultures} have been published.","ContentName":"Project Manager","ContentId":1451,"Cultures":"en-us","SourceContext":"Umbraco.Cms.Core.Services.ContentService","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:31.1449820Z","@mt":"Document {ContentName} (id={ContentId}) cultures: {Cultures} have been published.","ContentName":"DevOps Engineer","ContentId":1452,"Cultures":"en-us","SourceContext":"Umbraco.Cms.Core.Services.ContentService","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:31.1771815Z","@mt":"Document {ContentName} (id={ContentId}) has been published.","ContentName":"Sitemap","ContentId":1453,"SourceContext":"Umbraco.Cms.Core.Services.ContentService","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:31.1922485Z","@mt":"Document {ContentName} (id={ContentId}) has been published.","ContentName":"Not found","ContentId":1454,"SourceContext":"Umbraco.Cms.Core.Services.ContentService","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:31.2067762Z","@mt":"Document {ContentName} (id={ContentId}) cultures: {Cultures} have been published.","ContentName":"robots.txt","ContentId":1455,"Cultures":"en-us","SourceContext":"Umbraco.Cms.Core.Services.ContentService","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:31.2230680Z","@mt":"Document {ContentName} (id={ContentId}) has been published.","ContentName":"FAQ","ContentId":1456,"SourceContext":"Umbraco.Cms.Core.Services.ContentService","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:31.2338063Z","@mt":"Document {ContentName} (id={ContentId}) has been published.","ContentName":"How to use the Starter Kit!","ContentId":1457,"SourceContext":"Umbraco.Cms.Core.Services.ContentService","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:31.3152453Z","@mt":"Notifications can not be sent, no site URL is set (might be during boot process?)","@l":"Warning","SourceContext":"Umbraco.Cms.Core.Events.UserNotificationsHandler.Notifier","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"WARN "}
{"@t":"2025-07-17T13:35:31.3157612Z","@mt":"Notifications can not be sent, no site URL is set (might be during boot process?)","@l":"Warning","SourceContext":"Umbraco.Cms.Core.Events.UserNotificationsHandler.Notifier","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"WARN "}
{"@t":"2025-07-17T13:35:31.3158773Z","@mt":"Notifications can not be sent, no site URL is set (might be during boot process?)","@l":"Warning","SourceContext":"Umbraco.Cms.Core.Events.UserNotificationsHandler.Notifier","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"WARN "}
{"@t":"2025-07-17T13:35:31.3159009Z","@mt":"Notifications can not be sent, no site URL is set (might be during boot process?)","@l":"Warning","SourceContext":"Umbraco.Cms.Core.Events.UserNotificationsHandler.Notifier","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"WARN "}
{"@t":"2025-07-17T13:35:31.3159144Z","@mt":"Notifications can not be sent, no site URL is set (might be during boot process?)","@l":"Warning","SourceContext":"Umbraco.Cms.Core.Events.UserNotificationsHandler.Notifier","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"WARN "}
{"@t":"2025-07-17T13:35:31.3159254Z","@mt":"Notifications can not be sent, no site URL is set (might be during boot process?)","@l":"Warning","SourceContext":"Umbraco.Cms.Core.Events.UserNotificationsHandler.Notifier","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"WARN "}
{"@t":"2025-07-17T13:35:31.3159370Z","@mt":"Notifications can not be sent, no site URL is set (might be during boot process?)","@l":"Warning","SourceContext":"Umbraco.Cms.Core.Events.UserNotificationsHandler.Notifier","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"WARN "}
{"@t":"2025-07-17T13:35:31.3159461Z","@mt":"Notifications can not be sent, no site URL is set (might be during boot process?)","@l":"Warning","SourceContext":"Umbraco.Cms.Core.Events.UserNotificationsHandler.Notifier","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"WARN "}
{"@t":"2025-07-17T13:35:31.3159581Z","@mt":"Notifications can not be sent, no site URL is set (might be during boot process?)","@l":"Warning","SourceContext":"Umbraco.Cms.Core.Events.UserNotificationsHandler.Notifier","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"WARN "}
{"@t":"2025-07-17T13:35:31.3159671Z","@mt":"Notifications can not be sent, no site URL is set (might be during boot process?)","@l":"Warning","SourceContext":"Umbraco.Cms.Core.Events.UserNotificationsHandler.Notifier","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"WARN "}
{"@t":"2025-07-17T13:35:31.3159775Z","@mt":"Notifications can not be sent, no site URL is set (might be during boot process?)","@l":"Warning","SourceContext":"Umbraco.Cms.Core.Events.UserNotificationsHandler.Notifier","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"WARN "}
{"@t":"2025-07-17T13:35:31.3159886Z","@mt":"Notifications can not be sent, no site URL is set (might be during boot process?)","@l":"Warning","SourceContext":"Umbraco.Cms.Core.Events.UserNotificationsHandler.Notifier","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"WARN "}
{"@t":"2025-07-17T13:35:31.3159992Z","@mt":"Notifications can not be sent, no site URL is set (might be during boot process?)","@l":"Warning","SourceContext":"Umbraco.Cms.Core.Events.UserNotificationsHandler.Notifier","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"WARN "}
{"@t":"2025-07-17T13:35:31.3160129Z","@mt":"Notifications can not be sent, no site URL is set (might be during boot process?)","@l":"Warning","SourceContext":"Umbraco.Cms.Core.Events.UserNotificationsHandler.Notifier","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"WARN "}
{"@t":"2025-07-17T13:35:31.3160265Z","@mt":"Notifications can not be sent, no site URL is set (might be during boot process?)","@l":"Warning","SourceContext":"Umbraco.Cms.Core.Events.UserNotificationsHandler.Notifier","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"WARN "}
{"@t":"2025-07-17T13:35:31.3160362Z","@mt":"Notifications can not be sent, no site URL is set (might be during boot process?)","@l":"Warning","SourceContext":"Umbraco.Cms.Core.Events.UserNotificationsHandler.Notifier","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"WARN "}
{"@t":"2025-07-17T13:35:31.3160527Z","@mt":"Notifications can not be sent, no site URL is set (might be during boot process?)","@l":"Warning","SourceContext":"Umbraco.Cms.Core.Events.UserNotificationsHandler.Notifier","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"WARN "}
{"@t":"2025-07-17T13:35:31.3160661Z","@mt":"Notifications can not be sent, no site URL is set (might be during boot process?)","@l":"Warning","SourceContext":"Umbraco.Cms.Core.Events.UserNotificationsHandler.Notifier","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"WARN "}
{"@t":"2025-07-17T13:35:31.3160772Z","@mt":"Notifications can not be sent, no site URL is set (might be during boot process?)","@l":"Warning","SourceContext":"Umbraco.Cms.Core.Events.UserNotificationsHandler.Notifier","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"WARN "}
{"@t":"2025-07-17T13:35:31.3160879Z","@mt":"Notifications can not be sent, no site URL is set (might be during boot process?)","@l":"Warning","SourceContext":"Umbraco.Cms.Core.Events.UserNotificationsHandler.Notifier","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"WARN "}
{"@t":"2025-07-17T13:35:31.3160991Z","@mt":"Notifications can not be sent, no site URL is set (might be during boot process?)","@l":"Warning","SourceContext":"Umbraco.Cms.Core.Events.UserNotificationsHandler.Notifier","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"WARN "}
{"@t":"2025-07-17T13:35:31.3161086Z","@mt":"Notifications can not be sent, no site URL is set (might be during boot process?)","@l":"Warning","SourceContext":"Umbraco.Cms.Core.Events.UserNotificationsHandler.Notifier","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"WARN "}
{"@t":"2025-07-17T13:35:31.3161196Z","@mt":"Notifications can not be sent, no site URL is set (might be during boot process?)","@l":"Warning","SourceContext":"Umbraco.Cms.Core.Events.UserNotificationsHandler.Notifier","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"WARN "}
{"@t":"2025-07-17T13:35:31.3161287Z","@mt":"Notifications can not be sent, no site URL is set (might be during boot process?)","@l":"Warning","SourceContext":"Umbraco.Cms.Core.Events.UserNotificationsHandler.Notifier","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"WARN "}
{"@t":"2025-07-17T13:35:31.3161399Z","@mt":"Notifications can not be sent, no site URL is set (might be during boot process?)","@l":"Warning","SourceContext":"Umbraco.Cms.Core.Events.UserNotificationsHandler.Notifier","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"WARN "}
{"@t":"2025-07-17T13:35:31.3161487Z","@mt":"Notifications can not be sent, no site URL is set (might be during boot process?)","@l":"Warning","SourceContext":"Umbraco.Cms.Core.Events.UserNotificationsHandler.Notifier","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"WARN "}
{"@t":"2025-07-17T13:35:31.3161608Z","@mt":"Notifications can not be sent, no site URL is set (might be during boot process?)","@l":"Warning","SourceContext":"Umbraco.Cms.Core.Events.UserNotificationsHandler.Notifier","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"WARN "}
{"@t":"2025-07-17T13:35:31.3161715Z","@mt":"Notifications can not be sent, no site URL is set (might be during boot process?)","@l":"Warning","SourceContext":"Umbraco.Cms.Core.Events.UserNotificationsHandler.Notifier","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"WARN "}
{"@t":"2025-07-17T13:35:31.3161826Z","@mt":"Notifications can not be sent, no site URL is set (might be during boot process?)","@l":"Warning","SourceContext":"Umbraco.Cms.Core.Events.UserNotificationsHandler.Notifier","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"WARN "}
{"@t":"2025-07-17T13:35:31.3161947Z","@mt":"Notifications can not be sent, no site URL is set (might be during boot process?)","@l":"Warning","SourceContext":"Umbraco.Cms.Core.Events.UserNotificationsHandler.Notifier","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"WARN "}
{"@t":"2025-07-17T13:35:31.3162042Z","@mt":"Notifications can not be sent, no site URL is set (might be during boot process?)","@l":"Warning","SourceContext":"Umbraco.Cms.Core.Events.UserNotificationsHandler.Notifier","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"WARN "}
{"@t":"2025-07-17T13:35:31.3162126Z","@mt":"Notifications can not be sent, no site URL is set (might be during boot process?)","@l":"Warning","SourceContext":"Umbraco.Cms.Core.Events.UserNotificationsHandler.Notifier","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"WARN "}
{"@t":"2025-07-17T13:35:31.3162246Z","@mt":"Notifications can not be sent, no site URL is set (might be during boot process?)","@l":"Warning","SourceContext":"Umbraco.Cms.Core.Events.UserNotificationsHandler.Notifier","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"WARN "}
{"@t":"2025-07-17T13:35:31.3162329Z","@mt":"Notifications can not be sent, no site URL is set (might be during boot process?)","@l":"Warning","SourceContext":"Umbraco.Cms.Core.Events.UserNotificationsHandler.Notifier","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"WARN "}
{"@t":"2025-07-17T13:35:31.3162428Z","@mt":"Notifications can not be sent, no site URL is set (might be during boot process?)","@l":"Warning","SourceContext":"Umbraco.Cms.Core.Events.UserNotificationsHandler.Notifier","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"WARN "}
{"@t":"2025-07-17T13:35:31.3162538Z","@mt":"Notifications can not be sent, no site URL is set (might be during boot process?)","@l":"Warning","SourceContext":"Umbraco.Cms.Core.Events.UserNotificationsHandler.Notifier","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"WARN "}
{"@t":"2025-07-17T13:35:31.3162641Z","@mt":"Notifications can not be sent, no site URL is set (might be during boot process?)","@l":"Warning","SourceContext":"Umbraco.Cms.Core.Events.UserNotificationsHandler.Notifier","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"WARN "}
{"@t":"2025-07-17T13:35:31.3162741Z","@mt":"Notifications can not be sent, no site URL is set (might be during boot process?)","@l":"Warning","SourceContext":"Umbraco.Cms.Core.Events.UserNotificationsHandler.Notifier","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"WARN "}
{"@t":"2025-07-17T13:35:31.3162857Z","@mt":"Notifications can not be sent, no site URL is set (might be during boot process?)","@l":"Warning","SourceContext":"Umbraco.Cms.Core.Events.UserNotificationsHandler.Notifier","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"WARN "}
{"@t":"2025-07-17T13:35:31.3162951Z","@mt":"Notifications can not be sent, no site URL is set (might be during boot process?)","@l":"Warning","SourceContext":"Umbraco.Cms.Core.Events.UserNotificationsHandler.Notifier","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"WARN "}
{"@t":"2025-07-17T13:35:31.3163063Z","@mt":"Notifications can not be sent, no site URL is set (might be during boot process?)","@l":"Warning","SourceContext":"Umbraco.Cms.Core.Events.UserNotificationsHandler.Notifier","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"WARN "}
{"@t":"2025-07-17T13:35:31.3163152Z","@mt":"Notifications can not be sent, no site URL is set (might be during boot process?)","@l":"Warning","SourceContext":"Umbraco.Cms.Core.Events.UserNotificationsHandler.Notifier","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"WARN "}
{"@t":"2025-07-17T13:35:31.3163258Z","@mt":"Notifications can not be sent, no site URL is set (might be during boot process?)","@l":"Warning","SourceContext":"Umbraco.Cms.Core.Events.UserNotificationsHandler.Notifier","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"WARN "}
{"@t":"2025-07-17T13:35:31.3163363Z","@mt":"Notifications can not be sent, no site URL is set (might be during boot process?)","@l":"Warning","SourceContext":"Umbraco.Cms.Core.Events.UserNotificationsHandler.Notifier","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"WARN "}
{"@t":"2025-07-17T13:35:31.3163465Z","@mt":"Notifications can not be sent, no site URL is set (might be during boot process?)","@l":"Warning","SourceContext":"Umbraco.Cms.Core.Events.UserNotificationsHandler.Notifier","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"WARN "}
{"@t":"2025-07-17T13:35:31.3163556Z","@mt":"Notifications can not be sent, no site URL is set (might be during boot process?)","@l":"Warning","SourceContext":"Umbraco.Cms.Core.Events.UserNotificationsHandler.Notifier","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"WARN "}
{"@t":"2025-07-17T13:35:31.3163663Z","@mt":"Notifications can not be sent, no site URL is set (might be during boot process?)","@l":"Warning","SourceContext":"Umbraco.Cms.Core.Events.UserNotificationsHandler.Notifier","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"WARN "}
{"@t":"2025-07-17T13:35:31.3163751Z","@mt":"Notifications can not be sent, no site URL is set (might be during boot process?)","@l":"Warning","SourceContext":"Umbraco.Cms.Core.Events.UserNotificationsHandler.Notifier","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"WARN "}
{"@t":"2025-07-17T13:35:31.3163861Z","@mt":"Notifications can not be sent, no site URL is set (might be during boot process?)","@l":"Warning","SourceContext":"Umbraco.Cms.Core.Events.UserNotificationsHandler.Notifier","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"WARN "}
{"@t":"2025-07-17T13:35:31.3163946Z","@mt":"Notifications can not be sent, no site URL is set (might be during boot process?)","@l":"Warning","SourceContext":"Umbraco.Cms.Core.Events.UserNotificationsHandler.Notifier","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"WARN "}
{"@t":"2025-07-17T13:35:31.3164047Z","@mt":"Notifications can not be sent, no site URL is set (might be during boot process?)","@l":"Warning","SourceContext":"Umbraco.Cms.Core.Events.UserNotificationsHandler.Notifier","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"WARN "}
{"@t":"2025-07-17T13:35:31.3164148Z","@mt":"Notifications can not be sent, no site URL is set (might be during boot process?)","@l":"Warning","SourceContext":"Umbraco.Cms.Core.Events.UserNotificationsHandler.Notifier","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"WARN "}
{"@t":"2025-07-17T13:35:31.3164250Z","@mt":"Notifications can not be sent, no site URL is set (might be during boot process?)","@l":"Warning","SourceContext":"Umbraco.Cms.Core.Events.UserNotificationsHandler.Notifier","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"WARN "}
{"@t":"2025-07-17T13:35:31.3164347Z","@mt":"Notifications can not be sent, no site URL is set (might be during boot process?)","@l":"Warning","SourceContext":"Umbraco.Cms.Core.Events.UserNotificationsHandler.Notifier","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"WARN "}
{"@t":"2025-07-17T13:35:31.3164454Z","@mt":"Notifications can not be sent, no site URL is set (might be during boot process?)","@l":"Warning","SourceContext":"Umbraco.Cms.Core.Events.UserNotificationsHandler.Notifier","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"WARN "}
{"@t":"2025-07-17T13:35:31.3164539Z","@mt":"Notifications can not be sent, no site URL is set (might be during boot process?)","@l":"Warning","SourceContext":"Umbraco.Cms.Core.Events.UserNotificationsHandler.Notifier","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"WARN "}
{"@t":"2025-07-17T13:35:31.3164651Z","@mt":"Notifications can not be sent, no site URL is set (might be during boot process?)","@l":"Warning","SourceContext":"Umbraco.Cms.Core.Events.UserNotificationsHandler.Notifier","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"WARN "}
{"@t":"2025-07-17T13:35:31.3164738Z","@mt":"Notifications can not be sent, no site URL is set (might be during boot process?)","@l":"Warning","SourceContext":"Umbraco.Cms.Core.Events.UserNotificationsHandler.Notifier","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"WARN "}
{"@t":"2025-07-17T13:35:31.3165171Z","@mt":"Notifications can not be sent, no site URL is set (might be during boot process?)","@l":"Warning","SourceContext":"Umbraco.Cms.Core.Events.UserNotificationsHandler.Notifier","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"WARN "}
{"@t":"2025-07-17T13:35:31.3165348Z","@mt":"Notifications can not be sent, no site URL is set (might be during boot process?)","@l":"Warning","SourceContext":"Umbraco.Cms.Core.Events.UserNotificationsHandler.Notifier","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"WARN "}
{"@t":"2025-07-17T13:35:31.3165479Z","@mt":"Notifications can not be sent, no site URL is set (might be during boot process?)","@l":"Warning","SourceContext":"Umbraco.Cms.Core.Events.UserNotificationsHandler.Notifier","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"WARN "}
{"@t":"2025-07-17T13:35:31.3165568Z","@mt":"Notifications can not be sent, no site URL is set (might be during boot process?)","@l":"Warning","SourceContext":"Umbraco.Cms.Core.Events.UserNotificationsHandler.Notifier","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"WARN "}
{"@t":"2025-07-17T13:35:31.3165688Z","@mt":"Notifications can not be sent, no site URL is set (might be during boot process?)","@l":"Warning","SourceContext":"Umbraco.Cms.Core.Events.UserNotificationsHandler.Notifier","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"WARN "}
{"@t":"2025-07-17T13:35:31.3165776Z","@mt":"Notifications can not be sent, no site URL is set (might be during boot process?)","@l":"Warning","SourceContext":"Umbraco.Cms.Core.Events.UserNotificationsHandler.Notifier","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"WARN "}
{"@t":"2025-07-17T13:35:31.3165892Z","@mt":"Notifications can not be sent, no site URL is set (might be during boot process?)","@l":"Warning","SourceContext":"Umbraco.Cms.Core.Events.UserNotificationsHandler.Notifier","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"WARN "}
{"@t":"2025-07-17T13:35:31.3165984Z","@mt":"Notifications can not be sent, no site URL is set (might be during boot process?)","@l":"Warning","SourceContext":"Umbraco.Cms.Core.Events.UserNotificationsHandler.Notifier","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"WARN "}
{"@t":"2025-07-17T13:35:31.4388613Z","@mt":"Adding examine event handlers for {RegisteredIndexers} index providers.","RegisteredIndexers":3,"SourceContext":"Umbraco.Cms.Infrastructure.Examine.ExamineUmbracoIndexingHandler","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:31.5241847Z","@mt":"Notifications can not be sent, no site URL is set (might be during boot process?)","@l":"Warning","SourceContext":"Umbraco.Cms.Core.Events.UserNotificationsHandler.Notifier","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"WARN "}
{"@t":"2025-07-17T13:35:31.7597943Z","@mt":"Starting '{MigrationName}'...","MigrationName":"StarterKitFormSubmits","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.MigrationPlanExecutor","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:31.7598239Z","@mt":"At {OrigState}","OrigState":"origin","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.MigrationPlanExecutor","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:31.7598369Z","@mt":"Execute {MigrationType}","MigrationType":"AddStarterKitFormSubmitsTable","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.MigrationPlanExecutor","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:31.7622284Z","@mt":"Create table:\n {Sql}","Sql":"CREATE TABLE StarterKitFormSubmits\r\n(\r\n \"Id\" INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT\r\n, \"UserId\" INTEGER NOT NULL\r\n, CONSTRAINT PK_StarterKitFormSubmits UNIQUE (\"Id\")\r\n)\r\n","SourceContext":"Umbraco.Cms.Persistence.Sqlite.Services.SqliteSyntaxProvider","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:31.7641430Z","@mt":"At {OrigState}","OrigState":"Add-Starter-Kit-Form-Submits-Table","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.MigrationPlanExecutor","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:31.7641782Z","@mt":"Done","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.MigrationPlanExecutor","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:33.2705262Z","@mt":"Starting recurring background jobs hosted services","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:33.2723401Z","@mt":"Starting background hosted service for {job}","job":"HealthCheckNotifierJob","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:33.2806669Z","@mt":"Starting background hosted service for {job}","job":"KeepAliveJob","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:33.2812186Z","@mt":"Starting background hosted service for {job}","job":"LogScrubberJob","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:33.2818950Z","@mt":"Starting background hosted service for {job}","job":"ContentVersionCleanupJob","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:33.2824123Z","@mt":"Starting background hosted service for {job}","job":"ScheduledPublishingJob","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:33.2828556Z","@mt":"Starting background hosted service for {job}","job":"TempFileCleanupJob","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:33.2834107Z","@mt":"Starting background hosted service for {job}","job":"InstructionProcessJob","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:33.2842460Z","@mt":"Starting background hosted service for {job}","job":"TouchServerJob","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:33.2848836Z","@mt":"Starting background hosted service for {job}","job":"WebhookFiring","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:33.2854549Z","@mt":"Starting background hosted service for {job}","job":"WebhookLoggingCleanup","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:33.2858970Z","@mt":"Starting background hosted service for {job}","job":"ReportSiteJob","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:33.2860049Z","@mt":"Completed starting recurring background jobs hosted services","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:33.5964483Z","@mt":"Now listening on: {address}","address":"https://localhost:44309","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:33.5965956Z","@mt":"Now listening on: {address}","address":"http://localhost:37806","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:33.5967680Z","@mt":"Application started. Press Ctrl+C to shut down.","SourceContext":"Microsoft.Hosting.Lifetime","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:33.5968062Z","@mt":"Hosting environment: {EnvName}","EnvName":"Development","SourceContext":"Microsoft.Hosting.Lifetime","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:33.5968249Z","@mt":"Content root path: {ContentRoot}","ContentRoot":"D:\\tests\\MDDPlusPortal\\MDDPlusPortal","SourceContext":"Microsoft.Hosting.Lifetime","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:35:48.3070469Z","@mt":"No umbracoApplicationUrl for service (yet), skip.","@l":"Warning","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.Jobs.ServerRegistration.TouchServerJob","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":39,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"WARN "}
{"@t":"2025-07-17T13:36:21.2851725Z","@mt":"Sending image. Request uri: '{Uri}'. Cached Key: '{Key}'","@tr":"29177f149db788ead8b7fbfdc313d8f6","@sp":"cf638931d7c496a1","Uri":"https://localhost:44309/media/34ef3hkp/logo.png?rmode=pad&ranchor=left&width=149&height=59&bgcolor=transparent&v=1dbf738cdb82acc","Key":"36147f86784b","EventId":{"Id":3},"SourceContext":"SixLabors.ImageSharp.Web.Middleware.ImageSharpMiddleware","RequestId":"0HNE56RD4E2GA:00000007","RequestPath":"/media/34ef3hkp/logo.png","ConnectionId":"0HNE56RD4E2GA","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":55,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:36:22.9268244Z","@mt":"Sending image. Request uri: '{Uri}'. Cached Key: '{Key}'","@tr":"33e82d58c8842a923b21a3c056ded713","@sp":"02c2c493c3adb1d7","Uri":"https://localhost:44309/media/a0yk2tgt/fawicon.png?width=32&height=32","Key":"efb5bb92f3e3","EventId":{"Id":3},"SourceContext":"SixLabors.ImageSharp.Web.Middleware.ImageSharpMiddleware","RequestId":"0HNE56RD4E2GA:0000004B","RequestPath":"/media/a0yk2tgt/fawicon.png","ConnectionId":"0HNE56RD4E2GA","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":69,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:36:37.9892488Z","@mt":"Could not deserialize string '{JsonString}' into an image cropper value.","@l":"Error","@x":"Newtonsoft.Json.JsonReaderException: Error parsing comment. Expected: *, got m. Path '', line 1, position 1.\r\n   at Newtonsoft.Json.JsonTextReader.ParseComment(Boolean setToken)\r\n   at Newtonsoft.Json.JsonTextReader.ParseValue()\r\n   at Newtonsoft.Json.JsonReader.ReadForType(JsonContract contract, Boolean hasConverter)\r\n   at Newtonsoft.Json.Serialization.JsonSerializerInternalReader.Deserialize(JsonReader reader, Type objectType, Boolean checkAdditionalContent)\r\n   at Newtonsoft.Json.JsonSerializer.DeserializeInternal(JsonReader reader, Type objectType)\r\n   at Newtonsoft.Json.JsonConvert.DeserializeObject(String value, Type type, JsonSerializerSettings settings)\r\n   at Newtonsoft.Json.JsonConvert.DeserializeObject[T](String value, JsonSerializerSettings settings)\r\n   at Umbraco.Cms.Core.PropertyEditors.ValueConverters.ImageCropperValueConverter.ConvertSourceToIntermediate(IPublishedElement owner, IPublishedPropertyType propertyType, Object source, Boolean preview)","@tr":"8d90a0774680785c540b844e3a39f0fd","@sp":"298038a1ccc31859","JsonString":"/media/f12l3cv0/abstract-blur-empty-green-gradient-studio-well-use-as-background-website-template-frame-business-report_1258-54629.jpg","SourceContext":"Umbraco.Cms.Core.PropertyEditors.ValueConverters.ImageCropperValueConverter","ActionId":"1a07bf3e-821d-45fe-a6cf-a402c4a82f02","ActionName":"Umbraco.Cms.Web.Common.Controllers.RenderController.Index (Umbraco.Web.Common)","RequestId":"0HNE56RD4E2GA:00000059","RequestPath":"/services/seo-services/","ConnectionId":"0HNE56RD4E2GA","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":61,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"ERROR","HttpRequestId":"57bc0b5e-a1ca-40f0-a0c7-a2ea4d8d07ff","HttpRequestNumber":1,"HttpSessionId":"87cfd800-3d8a-9bf7-ff82-ef10c4a6c0d9"}
{"@t":"2025-07-17T13:36:38.0003751Z","@mt":"Could not deserialize string '{JsonString}' into an image cropper value.","@l":"Error","@x":"Newtonsoft.Json.JsonReaderException: Error parsing comment. Expected: *, got m. Path '', line 1, position 1.\r\n   at Newtonsoft.Json.JsonTextReader.ParseComment(Boolean setToken)\r\n   at Newtonsoft.Json.JsonTextReader.ParseValue()\r\n   at Newtonsoft.Json.JsonReader.ReadForType(JsonContract contract, Boolean hasConverter)\r\n   at Newtonsoft.Json.Serialization.JsonSerializerInternalReader.Deserialize(JsonReader reader, Type objectType, Boolean checkAdditionalContent)\r\n   at Newtonsoft.Json.JsonSerializer.DeserializeInternal(JsonReader reader, Type objectType)\r\n   at Newtonsoft.Json.JsonConvert.DeserializeObject(String value, Type type, JsonSerializerSettings settings)\r\n   at Newtonsoft.Json.JsonConvert.DeserializeObject[T](String value, JsonSerializerSettings settings)\r\n   at Umbraco.Cms.Core.PropertyEditors.ValueConverters.ImageCropperValueConverter.ConvertSourceToIntermediate(IPublishedElement owner, IPublishedPropertyType propertyType, Object source, Boolean preview)","@tr":"8d90a0774680785c540b844e3a39f0fd","@sp":"298038a1ccc31859","JsonString":"/media/sijbpi12/a1d39b9944184055866844a5d88fdec3.png","SourceContext":"Umbraco.Cms.Core.PropertyEditors.ValueConverters.ImageCropperValueConverter","ActionId":"1a07bf3e-821d-45fe-a6cf-a402c4a82f02","ActionName":"Umbraco.Cms.Web.Common.Controllers.RenderController.Index (Umbraco.Web.Common)","RequestId":"0HNE56RD4E2GA:00000059","RequestPath":"/services/seo-services/","ConnectionId":"0HNE56RD4E2GA","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":61,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"ERROR","HttpRequestId":"57bc0b5e-a1ca-40f0-a0c7-a2ea4d8d07ff","HttpRequestNumber":1,"HttpSessionId":"87cfd800-3d8a-9bf7-ff82-ef10c4a6c0d9"}
{"@t":"2025-07-17T13:36:38.2917252Z","@mt":"Sending image. Request uri: '{Uri}'. Cached Key: '{Key}'","@tr":"cced594883c6c2c393979b976baff68c","@sp":"dcbb10561103fed5","Uri":"https://localhost:44309/media/sijbpi12/a1d39b9944184055866844a5d88fdec3.png?width=400&height=300&v=1dbf738cd29cb1a","Key":"206be36a0852","EventId":{"Id":3},"SourceContext":"SixLabors.ImageSharp.Web.Middleware.ImageSharpMiddleware","RequestId":"0HNE56RD4E2GA:0000005B","RequestPath":"/media/sijbpi12/a1d39b9944184055866844a5d88fdec3.png","ConnectionId":"0HNE56RD4E2GA","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":68,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:36:38.3757094Z","@mt":"Sending image. Request uri: '{Uri}'. Cached Key: '{Key}'","@tr":"500347af6db970a79c7a6a37e3505ad8","@sp":"f88063cfda4c2ca4","Uri":"https://localhost:44309/media/01eeuyi0/eyebuydirect_logo.png?rmode=boxpad&width=200&height=47&bgcolor=white&v=1dbf738cd324104","Key":"d064f67884f8","EventId":{"Id":3},"SourceContext":"SixLabors.ImageSharp.Web.Middleware.ImageSharpMiddleware","RequestId":"0HNE56RD4E2GA:00000079","RequestPath":"/media/01eeuyi0/eyebuydirect_logo.png","ConnectionId":"0HNE56RD4E2GA","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":24,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:36:38.3921422Z","@mt":"Sending image. Request uri: '{Uri}'. Cached Key: '{Key}'","@tr":"dfc0ef4f2688f843158b4116c923365f","@sp":"b182552891b70337","Uri":"https://localhost:44309/media/h2wjmues/завантаження.png?rmode=boxpad&width=200&height=47&bgcolor=white&v=1dbf738cd306f66","Key":"7ace5d0f9938","EventId":{"Id":3},"SourceContext":"SixLabors.ImageSharp.Web.Middleware.ImageSharpMiddleware","RequestId":"0HNE56RD4E2GA:00000077","RequestPath":"/media/h2wjmues/%D0%B7%D0%B0%D0%B2%D0%B0%D0%BD%D1%82%D0%B0%D0%B6%D0%B5%D0%BD%D0%BD%D1%8F.png","ConnectionId":"0HNE56RD4E2GA","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":68,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:36:38.5368423Z","@mt":"Sending image. Request uri: '{Uri}'. Cached Key: '{Key}'","@tr":"060bf9935a881269f30896d32f156350","@sp":"4f8d8ad2cf818898","Uri":"https://localhost:44309/media/fkhbl2nr/1_brandpluslogos_e86a51d3-11b7-42e1-b2bd-cdad912e132d-prv.jpg?rmode=boxpad&width=200&height=47&bgcolor=white&v=1dbf738cd344b57","Key":"aebd4a569f2d","EventId":{"Id":3},"SourceContext":"SixLabors.ImageSharp.Web.Middleware.ImageSharpMiddleware","RequestId":"0HNE56RD4E2GA:00000075","RequestPath":"/media/fkhbl2nr/1_brandpluslogos_e86a51d3-11b7-42e1-b2bd-cdad912e132d-prv.jpg","ConnectionId":"0HNE56RD4E2GA","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":54,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:36:40.1813154Z","@mt":"Sending image. Request uri: '{Uri}'. Cached Key: '{Key}'","@tr":"5066c6a6325e3cd69c7d173a97e32676","@sp":"f4ffd1dd0ad1488a","Uri":"https://localhost:44309/media/jj2h23ml/back-in-time-1.png?rmode=max&width=400&height=300&v=1dbf738cd3a91a0","Key":"ee4689c9ec4f","EventId":{"Id":3},"SourceContext":"SixLabors.ImageSharp.Web.Middleware.ImageSharpMiddleware","RequestId":"0HNE56RD4E2GA:0000007B","RequestPath":"/media/jj2h23ml/back-in-time-1.png","ConnectionId":"0HNE56RD4E2GA","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":54,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:36:40.3198102Z","@mt":"Sending image. Request uri: '{Uri}'. Cached Key: '{Key}'","@tr":"dc1715f6732069b5bba8116238f5ad51","@sp":"3fc8ee5861e5f820","Uri":"https://localhost:44309/media/gw4hh14a/growth-2.png?rmode=max&width=400&height=300&v=1dbf738cd3c7cbc","Key":"a6aa34d7d96a","EventId":{"Id":3},"SourceContext":"SixLabors.ImageSharp.Web.Middleware.ImageSharpMiddleware","RequestId":"0HNE56RD4E2GA:0000007D","RequestPath":"/media/gw4hh14a/growth-2.png","ConnectionId":"0HNE56RD4E2GA","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":54,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:36:40.9396218Z","@mt":"Sending image. Request uri: '{Uri}'. Cached Key: '{Key}'","@tr":"f04c81c11ff06abcd22d26a2efd438c0","@sp":"266d29ae0ea835ef","Uri":"https://localhost:44309/media/olvkegzi/results.png?rmode=max&width=400&height=300&v=1dbf738cd3e7a69","Key":"571cab6f41a5","EventId":{"Id":3},"SourceContext":"SixLabors.ImageSharp.Web.Middleware.ImageSharpMiddleware","RequestId":"0HNE56RD4E2GA:0000007F","RequestPath":"/media/olvkegzi/results.png","ConnectionId":"0HNE56RD4E2GA","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":69,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:36:42.2329585Z","@mt":"Sending image. Request uri: '{Uri}'. Cached Key: '{Key}'","@tr":"cb9a5acbc2e0aa14f6cf328570e22ac4","@sp":"03de389880c6753a","Uri":"https://localhost:44309/media/kjnnnijy/target.png?rmode=max&width=400&height=300&v=1dbf738cd408864","Key":"c9c240a2bba6","EventId":{"Id":3},"SourceContext":"SixLabors.ImageSharp.Web.Middleware.ImageSharpMiddleware","RequestId":"0HNE56RD4E2GA:00000081","RequestPath":"/media/kjnnnijy/target.png","ConnectionId":"0HNE56RD4E2GA","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":61,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-17T13:36:54.1296908Z","@mt":"The Delivery API is not enabled, no indexing will performed for the Delivery API content index.","SourceContext":"Umbraco.Cms.Infrastructure.Examine.DeliveryApiContentIndexPopulator","ProcessId":8232,"ProcessName":"MDDPlusPortal","ThreadId":40,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
