{"@t":"2025-07-21T08:07:49.6009938Z","@mt":"Acquiring MainDom.","SourceContext":"Umbraco.Cms.Core.Runtime.MainDom","ProcessId":23216,"ProcessName":"iisexpress","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-21T08:07:49.6139681Z","@mt":"Acquired MainDom.","SourceContext":"Umbraco.Cms.Core.Runtime.MainDom","ProcessId":23216,"ProcessName":"iisexpress","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-21T08:07:50.0961222Z","@mt":"Starting '{MigrationName}'...","MigrationName":"StarterKitFormSubmits","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.MigrationPlanExecutor","ProcessId":23216,"ProcessName":"iisexpress","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-21T08:07:50.0961927Z","@mt":"At {OrigState}","OrigState":"Add-Starter-Kit-Form-Submits-Table","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.MigrationPlanExecutor","ProcessId":23216,"ProcessName":"iisexpress","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-21T08:07:50.0962438Z","@mt":"Done","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.MigrationPlanExecutor","ProcessId":23216,"ProcessName":"iisexpress","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-21T08:07:51.1213109Z","@mt":"Starting recurring background jobs hosted services","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":23216,"ProcessName":"iisexpress","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-21T08:07:51.1220202Z","@mt":"Starting background hosted service for {job}","job":"HealthCheckNotifierJob","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":23216,"ProcessName":"iisexpress","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-21T08:07:51.1252847Z","@mt":"Starting background hosted service for {job}","job":"KeepAliveJob","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":23216,"ProcessName":"iisexpress","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-21T08:07:51.1254997Z","@mt":"Starting background hosted service for {job}","job":"LogScrubberJob","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":23216,"ProcessName":"iisexpress","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-21T08:07:51.1256789Z","@mt":"Starting background hosted service for {job}","job":"ContentVersionCleanupJob","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":23216,"ProcessName":"iisexpress","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-21T08:07:51.1258652Z","@mt":"Starting background hosted service for {job}","job":"ScheduledPublishingJob","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":23216,"ProcessName":"iisexpress","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-21T08:07:51.1260290Z","@mt":"Starting background hosted service for {job}","job":"TempFileCleanupJob","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":23216,"ProcessName":"iisexpress","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-21T08:07:51.1262080Z","@mt":"Starting background hosted service for {job}","job":"InstructionProcessJob","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":23216,"ProcessName":"iisexpress","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-21T08:07:51.1264327Z","@mt":"Starting background hosted service for {job}","job":"TouchServerJob","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":23216,"ProcessName":"iisexpress","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-21T08:07:51.1266654Z","@mt":"Starting background hosted service for {job}","job":"WebhookFiring","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":23216,"ProcessName":"iisexpress","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-21T08:07:51.1268639Z","@mt":"Starting background hosted service for {job}","job":"WebhookLoggingCleanup","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":23216,"ProcessName":"iisexpress","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-21T08:07:51.1270185Z","@mt":"Starting background hosted service for {job}","job":"ReportSiteJob","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":23216,"ProcessName":"iisexpress","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-21T08:07:51.1270809Z","@mt":"Completed starting recurring background jobs hosted services","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":23216,"ProcessName":"iisexpress","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-21T08:07:51.2542482Z","@mt":"Application started. Press Ctrl+C to shut down.","SourceContext":"Microsoft.Hosting.Lifetime","ProcessId":23216,"ProcessName":"iisexpress","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-21T08:07:51.2599084Z","@mt":"Hosting environment: {EnvName}","EnvName":"Development","SourceContext":"Microsoft.Hosting.Lifetime","ProcessId":23216,"ProcessName":"iisexpress","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-21T08:07:51.2599411Z","@mt":"Content root path: {ContentRoot}","ContentRoot":"D:\\tests\\MDDPlusPortal\\MDDPlusPortal","SourceContext":"Microsoft.Hosting.Lifetime","ProcessId":23216,"ProcessName":"iisexpress","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-21T08:07:51.6395569Z","@mt":"Registered with MainDom, localContentDbExists? {LocalContentDbExists}, localMediaDbExists? {LocalMediaDbExists}","@tr":"5d7816a5dcdba5b911123a9e7b50b83e","@sp":"91412f6da9fb449b","LocalContentDbExists":true,"LocalMediaDbExists":true,"SourceContext":"Umbraco.Cms.Infrastructure.PublishedCache.PublishedSnapshotService","RequestId":"40000019-000c-ff00-b63f-84710c7967bb","RequestPath":"/","ProcessId":23216,"ProcessName":"iisexpress","ThreadId":17,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO ","HttpRequestId":"5d48edc7-c948-4a80-b2f0-f1f21634c13c","HttpRequestNumber":1,"HttpSessionId":"0"}
{"@t":"2025-07-21T08:07:51.6415219Z","@mt":"Creating the content store, localContentDbExists? {LocalContentDbExists}","@tr":"5d7816a5dcdba5b911123a9e7b50b83e","@sp":"91412f6da9fb449b","LocalContentDbExists":true,"SourceContext":"Umbraco.Cms.Infrastructure.PublishedCache.PublishedSnapshotService","RequestId":"40000019-000c-ff00-b63f-84710c7967bb","RequestPath":"/","ProcessId":23216,"ProcessName":"iisexpress","ThreadId":17,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO ","HttpRequestId":"5d48edc7-c948-4a80-b2f0-f1f21634c13c","HttpRequestNumber":1,"HttpSessionId":"0"}
{"@t":"2025-07-21T08:07:51.6428965Z","@mt":"Creating the media store, localMediaDbExists? {LocalMediaDbExists}","@tr":"5d7816a5dcdba5b911123a9e7b50b83e","@sp":"91412f6da9fb449b","LocalMediaDbExists":true,"SourceContext":"Umbraco.Cms.Infrastructure.PublishedCache.PublishedSnapshotService","RequestId":"40000019-000c-ff00-b63f-84710c7967bb","RequestPath":"/","ProcessId":23216,"ProcessName":"iisexpress","ThreadId":17,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO ","HttpRequestId":"5d48edc7-c948-4a80-b2f0-f1f21634c13c","HttpRequestNumber":1,"HttpSessionId":"0"}
{"@t":"2025-07-21T08:07:51.8562194Z","@mt":"{StartMessage} [Timing {TimingId}]","@tr":"5d7816a5dcdba5b911123a9e7b50b83e","@sp":"91412f6da9fb449b","StartMessage":"Loading content from local cache file","TimingId":"c399ba5","SourceContext":"Umbraco.Cms.Core.Logging.ProfilingLogger","RequestId":"40000019-000c-ff00-b63f-84710c7967bb","RequestPath":"/","ProcessId":23216,"ProcessName":"iisexpress","ThreadId":17,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO ","HttpRequestId":"5d48edc7-c948-4a80-b2f0-f1f21634c13c","HttpRequestNumber":1,"HttpSessionId":"0"}
{"@t":"2025-07-21T08:07:51.8773073Z","@mt":"{EndMessage} ({Duration}ms) [Timing {TimingId}]","@tr":"5d7816a5dcdba5b911123a9e7b50b83e","@sp":"91412f6da9fb449b","EndMessage":"Completed.","Duration":21,"TimingId":"c399ba5","SourceContext":"Umbraco.Cms.Core.Logging.ProfilingLogger","RequestId":"40000019-000c-ff00-b63f-84710c7967bb","RequestPath":"/","ProcessId":23216,"ProcessName":"iisexpress","ThreadId":17,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO ","HttpRequestId":"5d48edc7-c948-4a80-b2f0-f1f21634c13c","HttpRequestNumber":1,"HttpSessionId":"0"}
{"@t":"2025-07-21T08:07:51.8808530Z","@mt":"{StartMessage} [Timing {TimingId}]","@tr":"5d7816a5dcdba5b911123a9e7b50b83e","@sp":"91412f6da9fb449b","StartMessage":"Loading media from local cache file","TimingId":"1183ad8","SourceContext":"Umbraco.Cms.Core.Logging.ProfilingLogger","RequestId":"40000019-000c-ff00-b63f-84710c7967bb","RequestPath":"/","ProcessId":23216,"ProcessName":"iisexpress","ThreadId":17,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO ","HttpRequestId":"5d48edc7-c948-4a80-b2f0-f1f21634c13c","HttpRequestNumber":1,"HttpSessionId":"0"}
{"@t":"2025-07-21T08:07:51.8850153Z","@mt":"{EndMessage} ({Duration}ms) [Timing {TimingId}]","@tr":"5d7816a5dcdba5b911123a9e7b50b83e","@sp":"91412f6da9fb449b","EndMessage":"Completed.","Duration":4,"TimingId":"1183ad8","SourceContext":"Umbraco.Cms.Core.Logging.ProfilingLogger","RequestId":"40000019-000c-ff00-b63f-84710c7967bb","RequestPath":"/","ProcessId":23216,"ProcessName":"iisexpress","ThreadId":17,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO ","HttpRequestId":"5d48edc7-c948-4a80-b2f0-f1f21634c13c","HttpRequestNumber":1,"HttpSessionId":"0"}
{"@t":"2025-07-21T08:08:52.3211460Z","@mt":"The Delivery API is not enabled, no indexing will performed for the Delivery API content index.","SourceContext":"Umbraco.Cms.Infrastructure.Examine.DeliveryApiContentIndexPopulator","ProcessId":23216,"ProcessName":"iisexpress","ThreadId":21,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-21T08:11:31.6385021Z","@mt":"Sending image. Request uri: '{Uri}'. Cached Key: '{Key}'","@tr":"a17a8ed28747a5b0e70df771bd3ca4e6","@sp":"3126a4e850251f17","Uri":"https://localhost:44309/media/jhemtee0/icon.png?rmode=max&width=500&v=1dbf71fa85bf4eb","Key":"98f035db5467","EventId":{"Id":3},"SourceContext":"SixLabors.ImageSharp.Web.Middleware.ImageSharpMiddleware","RequestId":"4000001e-0000-fe00-b63f-84710c7967bb","RequestPath":"/media/jhemtee0/icon.png","ProcessId":23216,"ProcessName":"iisexpress","ThreadId":3,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-21T08:11:31.6385030Z","@mt":"Sending image. Request uri: '{Uri}'. Cached Key: '{Key}'","@tr":"9eca6b6160d12c023d13982de63142bf","@sp":"6efb92784468a703","Uri":"https://localhost:44309/media/mhre1w2b/pexels-photo-2661922.jpeg?rmode=max&width=500&v=1dbf71fa85eb0ff","Key":"40ed1d26408d","EventId":{"Id":3},"SourceContext":"SixLabors.ImageSharp.Web.Middleware.ImageSharpMiddleware","RequestId":"40000016-0002-fc00-b63f-84710c7967bb","RequestPath":"/media/mhre1w2b/pexels-photo-2661922.jpeg","ProcessId":23216,"ProcessName":"iisexpress","ThreadId":75,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-21T08:11:57.9150959Z","@mt":"Sending image. Request uri: '{Uri}'. Cached Key: '{Key}'","@tr":"c6468607044e4565c3a65de3b7778e09","@sp":"427b86cc14bc909a","Uri":"https://localhost:44309/media/hzfe2u0a/image.png?rmode=max&width=500&height=135","Key":"b1e5792a4df9","EventId":{"Id":3},"SourceContext":"SixLabors.ImageSharp.Web.Middleware.ImageSharpMiddleware","RequestId":"40000012-0009-ff00-b63f-84710c7967bb","RequestPath":"/media/hzfe2u0a/image.png","ProcessId":23216,"ProcessName":"iisexpress","ThreadId":75,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-21T08:11:57.9815417Z","@mt":"Sending image. Request uri: '{Uri}'. Cached Key: '{Key}'","@tr":"a1a3cf6402370f3b1ef5d4cc3808348f","@sp":"f2e20a11f2845ff4","Uri":"https://localhost:44309/media/hnnpgqzw/image.png?rmode=max&width=500&height=483","Key":"fe472390231b","EventId":{"Id":3},"SourceContext":"SixLabors.ImageSharp.Web.Middleware.ImageSharpMiddleware","RequestId":"40000003-000f-fc00-b63f-84710c7967bb","RequestPath":"/media/hnnpgqzw/image.png","ProcessId":23216,"ProcessName":"iisexpress","ThreadId":81,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-21T08:11:57.9909506Z","@mt":"Sending image. Request uri: '{Uri}'. Cached Key: '{Key}'","@tr":"fb6a04fd5d8d356d655abe6ce3b35dc1","@sp":"cb59ea3af041837a","Uri":"https://localhost:44309/media/rhnlhwey/image.png?rmode=max&width=500&height=394","Key":"0a887a6c13df","EventId":{"Id":3},"SourceContext":"SixLabors.ImageSharp.Web.Middleware.ImageSharpMiddleware","RequestId":"40000006-0011-fa00-b63f-84710c7967bb","RequestPath":"/media/rhnlhwey/image.png","ProcessId":23216,"ProcessName":"iisexpress","ThreadId":84,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-21T08:11:58.0038659Z","@mt":"Sending image. Request uri: '{Uri}'. Cached Key: '{Key}'","@tr":"6eb30c41afc7f8b9de57622a57ac5aa6","@sp":"4f1eef04fc9413ed","Uri":"https://localhost:44309/media/wmbkwqz5/image.png?rmode=max&width=500&height=377","Key":"b3abb1894de0","EventId":{"Id":3},"SourceContext":"SixLabors.ImageSharp.Web.Middleware.ImageSharpMiddleware","RequestId":"4000000d-000f-fd00-b63f-84710c7967bb","RequestPath":"/media/wmbkwqz5/image.png","ProcessId":23216,"ProcessName":"iisexpress","ThreadId":87,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-21T08:12:28.0590640Z","@mt":"Sending image. Request uri: '{Uri}'. Cached Key: '{Key}'","@tr":"5c51d075a0d4459fadfb6b5a27867753","@sp":"17d2bc432a108188","Uri":"https://localhost:44309/media/x51jzspk/logo-light.png?rmode=max&width=500&v=1dbf71fa86cbcd8","Key":"ac4fc60d8ff2","EventId":{"Id":3},"SourceContext":"SixLabors.ImageSharp.Web.Middleware.ImageSharpMiddleware","RequestId":"40000006-0004-fd00-b63f-84710c7967bb","RequestPath":"/media/x51jzspk/logo-light.png","ProcessId":23216,"ProcessName":"iisexpress","ThreadId":56,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-21T08:12:28.6494791Z","@mt":"Sending image. Request uri: '{Uri}'. Cached Key: '{Key}'","@tr":"8db0908a200125b03eb35d5f0bf0e7b9","@sp":"c253a1fabfd64979","Uri":"https://localhost:44309/media/34ef3hkp/logo.png?rmode=max&width=500&v=1dbf71fa86ad507","Key":"6af4b4f2a8f8","EventId":{"Id":3},"SourceContext":"SixLabors.ImageSharp.Web.Middleware.ImageSharpMiddleware","RequestId":"40000004-000b-f700-b63f-84710c7967bb","RequestPath":"/media/34ef3hkp/logo.png","ProcessId":23216,"ProcessName":"iisexpress","ThreadId":54,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-21T08:12:30.7140568Z","@mt":"Sending image. Request uri: '{Uri}'. Cached Key: '{Key}'","@tr":"5736755f828a0727e615e0e8176654be","@sp":"c4d95d0229eacb2b","Uri":"https://localhost:44309/media/a0yk2tgt/fawicon.png?rmode=max&width=500&v=1dbf71fa86eba0c","Key":"8e1e9857cc83","EventId":{"Id":3},"SourceContext":"SixLabors.ImageSharp.Web.Middleware.ImageSharpMiddleware","RequestId":"4000004e-0002-fe00-b63f-84710c7967bb","RequestPath":"/media/a0yk2tgt/fawicon.png","ProcessId":23216,"ProcessName":"iisexpress","ThreadId":56,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-21T08:12:35.4259970Z","@mt":"Sending image. Request uri: '{Uri}'. Cached Key: '{Key}'","@tr":"1d2d3f4cdc0f02c9d3e1331a3b134543","@sp":"b5bbc641252ecb30","Uri":"https://localhost:44309/media/ehne11mx/623c1e9eae7e8c3ebda393d20276cd34.png?rmode=max&width=500&v=1dbf71fa774d92f","Key":"93212868ff54","EventId":{"Id":3},"SourceContext":"SixLabors.ImageSharp.Web.Middleware.ImageSharpMiddleware","RequestId":"4000001a-0009-ff00-b63f-84710c7967bb","RequestPath":"/media/ehne11mx/623c1e9eae7e8c3ebda393d20276cd34.png","ProcessId":23216,"ProcessName":"iisexpress","ThreadId":27,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-21T08:12:35.4457384Z","@mt":"Sending image. Request uri: '{Uri}'. Cached Key: '{Key}'","@tr":"a06eb2466333faca3a1bb6f64d65b112","@sp":"0dfa5aa7c78ee326","Uri":"https://localhost:44309/media/g4sljxev/vital-energy-logo.png?rmode=max&width=500&v=1dbf71fa7cea87e","Key":"49e4d83afc2f","EventId":{"Id":3},"SourceContext":"SixLabors.ImageSharp.Web.Middleware.ImageSharpMiddleware","RequestId":"40000018-0002-fd00-b63f-84710c7967bb","RequestPath":"/media/g4sljxev/vital-energy-logo.png","ProcessId":23216,"ProcessName":"iisexpress","ThreadId":27,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-21T08:12:35.4457899Z","@mt":"Sending image. Request uri: '{Uri}'. Cached Key: '{Key}'","@tr":"0925062feee72b21ce67824ec1aeed98","@sp":"b5a95d9af5596ea8","Uri":"https://localhost:44309/media/w3djgyl1/6949a5af98af0a1c12e32935d58412db.jpg?rmode=max&width=500&v=1dbf71fa772d887","Key":"fc6da7f18d91","EventId":{"Id":3},"SourceContext":"SixLabors.ImageSharp.Web.Middleware.ImageSharpMiddleware","RequestId":"4000000a-0004-fd00-b63f-84710c7967bb","RequestPath":"/media/w3djgyl1/6949a5af98af0a1c12e32935d58412db.jpg","ProcessId":23216,"ProcessName":"iisexpress","ThreadId":73,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-21T08:12:35.4541929Z","@mt":"Sending image. Request uri: '{Uri}'. Cached Key: '{Key}'","@tr":"982bef584eea5a9fcf2352b938205529","@sp":"967719e3ad808eaa","Uri":"https://localhost:44309/media/uabl1qsl/veriforce-logo-mobile.png?rmode=max&width=500&v=1dbf71fa7d0af70","Key":"96b726dc19ee","EventId":{"Id":3},"SourceContext":"SixLabors.ImageSharp.Web.Middleware.ImageSharpMiddleware","RequestId":"40000004-000b-fb00-b63f-84710c7967bb","RequestPath":"/media/uabl1qsl/veriforce-logo-mobile.png","ProcessId":23216,"ProcessName":"iisexpress","ThreadId":53,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-21T08:12:35.4566573Z","@mt":"Sending image. Request uri: '{Uri}'. Cached Key: '{Key}'","@tr":"42e0161dda32fb846d2ec42762c20493","@sp":"821071bd697a79fb","Uri":"https://localhost:44309/media/cjwnfpha/top-backend-programming-languages-infographic-image.png?rmode=max&width=500&v=1dbf71fa7d34290","Key":"b2e59b438333","EventId":{"Id":3},"SourceContext":"SixLabors.ImageSharp.Web.Middleware.ImageSharpMiddleware","RequestId":"4000000e-0013-fe00-b63f-84710c7967bb","RequestPath":"/media/cjwnfpha/top-backend-programming-languages-infographic-image.png","ProcessId":23216,"ProcessName":"iisexpress","ThreadId":54,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-21T08:12:35.4679408Z","@mt":"Sending image. Request uri: '{Uri}'. Cached Key: '{Key}'","@tr":"41aab5d2b0778afeed92e4126ce87c32","@sp":"4eb229d43e221300","Uri":"https://localhost:44309/media/1bsfotr2/images.png?rmode=max&width=500&v=1dbf71fa7c446c7","Key":"45e55a26c9f1","EventId":{"Id":3},"SourceContext":"SixLabors.ImageSharp.Web.Middleware.ImageSharpMiddleware","RequestId":"40000004-0004-fa00-b63f-84710c7967bb","RequestPath":"/media/1bsfotr2/images.png","ProcessId":23216,"ProcessName":"iisexpress","ThreadId":57,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-21T08:12:35.4756893Z","@mt":"Sending image. Request uri: '{Uri}'. Cached Key: '{Key}'","@tr":"51697cea62209735222eb9f32820d5b0","@sp":"cd6395cdb3a12bc7","Uri":"https://localhost:44309/media/qwulpoda/1615993866.png?rmode=max&width=500&v=1dbf71fa770df36","Key":"cd2e4c1200e3","EventId":{"Id":3},"SourceContext":"SixLabors.ImageSharp.Web.Middleware.ImageSharpMiddleware","RequestId":"4000000c-0004-fc00-b63f-84710c7967bb","RequestPath":"/media/qwulpoda/1615993866.png","ProcessId":23216,"ProcessName":"iisexpress","ThreadId":53,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-21T08:12:35.5373441Z","@mt":"Sending image. Request uri: '{Uri}'. Cached Key: '{Key}'","@tr":"6866a7a19e89ac32467b73c35b33fec2","@sp":"5a1e31012e644b57","Uri":"https://localhost:44309/media/ym0jbacs/softserve_logo_2017svg.png?rmode=max&width=500&v=1dbf71fa7ccc68d","Key":"caaecbfd2d84","EventId":{"Id":3},"SourceContext":"SixLabors.ImageSharp.Web.Middleware.ImageSharpMiddleware","RequestId":"40000004-000b-fa00-b63f-84710c7967bb","RequestPath":"/media/ym0jbacs/softserve_logo_2017svg.png","ProcessId":23216,"ProcessName":"iisexpress","ThreadId":54,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-21T08:12:55.8084682Z","@mt":"Sending image. Request uri: '{Uri}'. Cached Key: '{Key}'","@tr":"bd2ee9751206d994fb98f493e773d7c6","@sp":"1719f796360ad165","Uri":"https://localhost:44309/media/ylperlln/development.png?rmode=max&width=500&v=1dbf71fa7da3f47","Key":"f687bf9ed8c7","EventId":{"Id":3},"SourceContext":"SixLabors.ImageSharp.Web.Middleware.ImageSharpMiddleware","RequestId":"40000010-0004-fc00-b63f-84710c7967bb","RequestPath":"/media/ylperlln/development.png","ProcessId":23216,"ProcessName":"iisexpress","ThreadId":27,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-21T08:13:13.7085394Z","@mt":"Sending image. Request uri: '{Uri}'. Cached Key: '{Key}'","@tr":"8bfcbbd62607896f9866414fb2ac67a4","@sp":"72aab0233addbf87","Uri":"https://localhost:44309/media/nidnzh0p/design.png?rmode=max&width=500&v=1dbf71fa7d7f559","Key":"d4e49b86dcba","EventId":{"Id":3},"SourceContext":"SixLabors.ImageSharp.Web.Middleware.ImageSharpMiddleware","RequestId":"40000013-0008-fc00-b63f-84710c7967bb","RequestPath":"/media/nidnzh0p/design.png","ProcessId":23216,"ProcessName":"iisexpress","ThreadId":54,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-21T08:13:23.6765175Z","@mt":"Sending image. Request uri: '{Uri}'. Cached Key: '{Key}'","@tr":"03a499d5ed59ab13c331469082b8daaf","@sp":"9e7e413d6d97e2d9","Uri":"https://localhost:44309/media/q0wbitpe/goal.png?rmode=max&width=500&v=1dbf71fa7d5d279","Key":"585107c313cf","EventId":{"Id":3},"SourceContext":"SixLabors.ImageSharp.Web.Middleware.ImageSharpMiddleware","RequestId":"40000008-0001-f800-b63f-84710c7967bb","RequestPath":"/media/q0wbitpe/goal.png","ProcessId":23216,"ProcessName":"iisexpress","ThreadId":55,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-21T08:13:41.4271438Z","@mt":"Document {ContentName} (id={ContentId}) cultures: {Cultures} have been published.","@tr":"62dce7821121621253755c7ddd47b50c","@sp":"e2990d2335e6b8f2","ContentName":"Home","ContentId":1425,"Cultures":"en-us","SourceContext":"Umbraco.Cms.Core.Services.ContentService","ActionId":"39bbf62c-4a1d-4fdf-9279-8b9b82bf34f5","ActionName":"Umbraco.Cms.Web.BackOffice.Controllers.ContentController.PostSave (Umbraco.Web.BackOffice)","RequestId":"40000018-0006-f800-b63f-84710c7967bb","RequestPath":"/umbraco/backoffice/umbracoapi/content/PostSave","ProcessId":23216,"ProcessName":"iisexpress","ThreadId":57,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO ","HttpRequestId":"f7d11e10-b006-4bc4-9067-1cfb12f771bf","HttpRequestNumber":2,"HttpSessionId":"5086600f-efc6-16c4-f33b-9013f5066efd"}
{"@t":"2025-07-21T08:13:41.8357501Z","@mt":"Adding examine event handlers for {RegisteredIndexers} index providers.","@tr":"62dce7821121621253755c7ddd47b50c","@sp":"e2990d2335e6b8f2","RegisteredIndexers":3,"SourceContext":"Umbraco.Cms.Infrastructure.Examine.ExamineUmbracoIndexingHandler","ActionId":"39bbf62c-4a1d-4fdf-9279-8b9b82bf34f5","ActionName":"Umbraco.Cms.Web.BackOffice.Controllers.ContentController.PostSave (Umbraco.Web.BackOffice)","RequestId":"40000018-0006-f800-b63f-84710c7967bb","RequestPath":"/umbraco/backoffice/umbracoapi/content/PostSave","ProcessId":23216,"ProcessName":"iisexpress","ThreadId":57,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO ","HttpRequestId":"f7d11e10-b006-4bc4-9067-1cfb12f771bf","HttpRequestNumber":2,"HttpSessionId":"5086600f-efc6-16c4-f33b-9013f5066efd"}
{"@t":"2025-07-21T08:13:54.6531927Z","@mt":"Could not deserialize string '{JsonString}' into an image cropper value.","@l":"Error","@x":"Newtonsoft.Json.JsonReaderException: Error parsing comment. Expected: *, got m. Path '', line 1, position 1.\r\n   at Newtonsoft.Json.JsonTextReader.ParseComment(Boolean setToken)\r\n   at Newtonsoft.Json.JsonTextReader.ParseValue()\r\n   at Newtonsoft.Json.JsonReader.ReadForType(JsonContract contract, Boolean hasConverter)\r\n   at Newtonsoft.Json.Serialization.JsonSerializerInternalReader.Deserialize(JsonReader reader, Type objectType, Boolean checkAdditionalContent)\r\n   at Newtonsoft.Json.JsonSerializer.DeserializeInternal(JsonReader reader, Type objectType)\r\n   at Newtonsoft.Json.JsonConvert.DeserializeObject(String value, Type type, JsonSerializerSettings settings)\r\n   at Newtonsoft.Json.JsonConvert.DeserializeObject[T](String value, JsonSerializerSettings settings)\r\n   at Umbraco.Cms.Core.PropertyEditors.ValueConverters.ImageCropperValueConverter.ConvertSourceToIntermediate(IPublishedElement owner, IPublishedPropertyType propertyType, Object source, Boolean preview)","@tr":"98263e331a9a3d651013a2b8ad36f0be","@sp":"df4e26bcc6dfcdd5","JsonString":"/media/zrupimza/close-up-hand-typing-keyboard.jpg","SourceContext":"Umbraco.Cms.Core.PropertyEditors.ValueConverters.ImageCropperValueConverter","ActionId":"82c7070b-0a0b-4dae-a0ba-29573652b486","ActionName":"Umbraco.Cms.Web.Common.Controllers.RenderController.Index (Umbraco.Web.Common)","RequestId":"40000006-0004-fa00-b63f-84710c7967bb","RequestPath":"/services/product-design-build/","ProcessId":23216,"ProcessName":"iisexpress","ThreadId":63,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"ERROR","HttpRequestId":"ff23674c-6136-414e-9490-e6dddf0cb4ba","HttpRequestNumber":3,"HttpSessionId":"f8e6ee37-599d-7681-762f-bb3bd2472ae3"}
{"@t":"2025-07-21T08:13:54.7977872Z","@mt":"Could not deserialize string '{JsonString}' into an image cropper value.","@l":"Error","@x":"Newtonsoft.Json.JsonReaderException: Error parsing comment. Expected: *, got m. Path '', line 1, position 1.\r\n   at Newtonsoft.Json.JsonTextReader.ParseComment(Boolean setToken)\r\n   at Newtonsoft.Json.JsonTextReader.ParseValue()\r\n   at Newtonsoft.Json.JsonReader.ReadForType(JsonContract contract, Boolean hasConverter)\r\n   at Newtonsoft.Json.Serialization.JsonSerializerInternalReader.Deserialize(JsonReader reader, Type objectType, Boolean checkAdditionalContent)\r\n   at Newtonsoft.Json.JsonSerializer.DeserializeInternal(JsonReader reader, Type objectType)\r\n   at Newtonsoft.Json.JsonConvert.DeserializeObject(String value, Type type, JsonSerializerSettings settings)\r\n   at Newtonsoft.Json.JsonConvert.DeserializeObject[T](String value, JsonSerializerSettings settings)\r\n   at Umbraco.Cms.Core.PropertyEditors.ValueConverters.ImageCropperValueConverter.ConvertSourceToIntermediate(IPublishedElement owner, IPublishedPropertyType propertyType, Object source, Boolean preview)","@tr":"98263e331a9a3d651013a2b8ad36f0be","@sp":"df4e26bcc6dfcdd5","JsonString":"/media/u0lpqvop/adaptive-apprroach.png","SourceContext":"Umbraco.Cms.Core.PropertyEditors.ValueConverters.ImageCropperValueConverter","ActionId":"82c7070b-0a0b-4dae-a0ba-29573652b486","ActionName":"Umbraco.Cms.Web.Common.Controllers.RenderController.Index (Umbraco.Web.Common)","RequestId":"40000006-0004-fa00-b63f-84710c7967bb","RequestPath":"/services/product-design-build/","ProcessId":23216,"ProcessName":"iisexpress","ThreadId":63,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"ERROR","HttpRequestId":"ff23674c-6136-414e-9490-e6dddf0cb4ba","HttpRequestNumber":3,"HttpSessionId":"f8e6ee37-599d-7681-762f-bb3bd2472ae3"}
{"@t":"2025-07-21T08:13:54.9381809Z","@mt":"Could not deserialize string '{JsonString}' into an image cropper value.","@l":"Error","@x":"Newtonsoft.Json.JsonReaderException: Error parsing comment. Expected: *, got m. Path '', line 1, position 1.\r\n   at Newtonsoft.Json.JsonTextReader.ParseComment(Boolean setToken)\r\n   at Newtonsoft.Json.JsonTextReader.ParseValue()\r\n   at Newtonsoft.Json.JsonReader.ReadForType(JsonContract contract, Boolean hasConverter)\r\n   at Newtonsoft.Json.Serialization.JsonSerializerInternalReader.Deserialize(JsonReader reader, Type objectType, Boolean checkAdditionalContent)\r\n   at Newtonsoft.Json.JsonSerializer.DeserializeInternal(JsonReader reader, Type objectType)\r\n   at Newtonsoft.Json.JsonConvert.DeserializeObject(String value, Type type, JsonSerializerSettings settings)\r\n   at Newtonsoft.Json.JsonConvert.DeserializeObject[T](String value, JsonSerializerSettings settings)\r\n   at Umbraco.Cms.Core.PropertyEditors.ValueConverters.ImageCropperValueConverter.ConvertSourceToIntermediate(IPublishedElement owner, IPublishedPropertyType propertyType, Object source, Boolean preview)","@tr":"98263e331a9a3d651013a2b8ad36f0be","@sp":"df4e26bcc6dfcdd5","JsonString":"/media/03wcqlph/emergency-tech.png","SourceContext":"Umbraco.Cms.Core.PropertyEditors.ValueConverters.ImageCropperValueConverter","ActionId":"82c7070b-0a0b-4dae-a0ba-29573652b486","ActionName":"Umbraco.Cms.Web.Common.Controllers.RenderController.Index (Umbraco.Web.Common)","RequestId":"40000006-0004-fa00-b63f-84710c7967bb","RequestPath":"/services/product-design-build/","ProcessId":23216,"ProcessName":"iisexpress","ThreadId":63,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"ERROR","HttpRequestId":"ff23674c-6136-414e-9490-e6dddf0cb4ba","HttpRequestNumber":3,"HttpSessionId":"f8e6ee37-599d-7681-762f-bb3bd2472ae3"}
{"@t":"2025-07-21T08:13:55.0627467Z","@mt":"Could not deserialize string '{JsonString}' into an image cropper value.","@l":"Error","@x":"Newtonsoft.Json.JsonReaderException: Error parsing comment. Expected: *, got m. Path '', line 1, position 1.\r\n   at Newtonsoft.Json.JsonTextReader.ParseComment(Boolean setToken)\r\n   at Newtonsoft.Json.JsonTextReader.ParseValue()\r\n   at Newtonsoft.Json.JsonReader.ReadForType(JsonContract contract, Boolean hasConverter)\r\n   at Newtonsoft.Json.Serialization.JsonSerializerInternalReader.Deserialize(JsonReader reader, Type objectType, Boolean checkAdditionalContent)\r\n   at Newtonsoft.Json.JsonSerializer.DeserializeInternal(JsonReader reader, Type objectType)\r\n   at Newtonsoft.Json.JsonConvert.DeserializeObject(String value, Type type, JsonSerializerSettings settings)\r\n   at Newtonsoft.Json.JsonConvert.DeserializeObject[T](String value, JsonSerializerSettings settings)\r\n   at Umbraco.Cms.Core.PropertyEditors.ValueConverters.ImageCropperValueConverter.ConvertSourceToIntermediate(IPublishedElement owner, IPublishedPropertyType propertyType, Object source, Boolean preview)","@tr":"98263e331a9a3d651013a2b8ad36f0be","@sp":"df4e26bcc6dfcdd5","JsonString":"/media/2xock1vw/human-centered.png","SourceContext":"Umbraco.Cms.Core.PropertyEditors.ValueConverters.ImageCropperValueConverter","ActionId":"82c7070b-0a0b-4dae-a0ba-29573652b486","ActionName":"Umbraco.Cms.Web.Common.Controllers.RenderController.Index (Umbraco.Web.Common)","RequestId":"40000006-0004-fa00-b63f-84710c7967bb","RequestPath":"/services/product-design-build/","ProcessId":23216,"ProcessName":"iisexpress","ThreadId":63,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"ERROR","HttpRequestId":"ff23674c-6136-414e-9490-e6dddf0cb4ba","HttpRequestNumber":3,"HttpSessionId":"f8e6ee37-599d-7681-762f-bb3bd2472ae3"}
{"@t":"2025-07-21T08:13:55.2348928Z","@mt":"Sending image. Request uri: '{Uri}'. Cached Key: '{Key}'","@tr":"3038d0f7e6dcd5a506d5c8b208b2f4de","@sp":"920be805441cf276","Uri":"https://localhost:44309/media/u0lpqvop/adaptive-apprroach.png?width=400&height=300&v=1dbf738cd5e3f2b","Key":"40f4ff51ed7d","EventId":{"Id":3},"SourceContext":"SixLabors.ImageSharp.Web.Middleware.ImageSharpMiddleware","RequestId":"40000005-0006-fc00-b63f-84710c7967bb","RequestPath":"/media/u0lpqvop/adaptive-apprroach.png","ProcessId":23216,"ProcessName":"iisexpress","ThreadId":57,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-21T08:13:55.2348928Z","@mt":"Sending image. Request uri: '{Uri}'. Cached Key: '{Key}'","@tr":"8ddb34f765fca3ea0988aa0e937aee09","@sp":"bdf0a5554879c91e","Uri":"https://localhost:44309/media/03wcqlph/emergency-tech.png?width=400&height=300&v=1dbf738cd601771","Key":"24f4024b5f01","EventId":{"Id":3},"SourceContext":"SixLabors.ImageSharp.Web.Middleware.ImageSharpMiddleware","RequestId":"40000054-0008-ff00-b63f-84710c7967bb","RequestPath":"/media/03wcqlph/emergency-tech.png","ProcessId":23216,"ProcessName":"iisexpress","ThreadId":54,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-21T08:13:55.2379408Z","@mt":"Sending image. Request uri: '{Uri}'. Cached Key: '{Key}'","@tr":"9e212a506bd949dcbfe3d8748ee7c43a","@sp":"78347f3a52c93991","Uri":"https://localhost:44309/media/2xock1vw/human-centered.png?width=400&height=300&v=1dbf738cd61aaec","Key":"d59bec3de1e4","EventId":{"Id":3},"SourceContext":"SixLabors.ImageSharp.Web.Middleware.ImageSharpMiddleware","RequestId":"4000001c-0006-f800-b63f-84710c7967bb","RequestPath":"/media/2xock1vw/human-centered.png","ProcessId":23216,"ProcessName":"iisexpress","ThreadId":77,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-21T08:13:56.1636913Z","@mt":"Sending image. Request uri: '{Uri}'. Cached Key: '{Key}'","@tr":"fa79fca010bb514c55925115448f7bea","@sp":"e0119fbd526cb7fc","Uri":"https://localhost:44309/media/4klhsn35/high-angle-hard-drive-still-life.jpg?rmode=max&width=400&height=300&v=1dbf738cd749a32","Key":"b4b0f662e087","EventId":{"Id":3},"SourceContext":"SixLabors.ImageSharp.Web.Middleware.ImageSharpMiddleware","RequestId":"4000000c-0004-fd00-b63f-84710c7967bb","RequestPath":"/media/4klhsn35/high-angle-hard-drive-still-life.jpg","ProcessId":23216,"ProcessName":"iisexpress","ThreadId":54,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-21T08:13:58.1130864Z","@mt":"Sending image. Request uri: '{Uri}'. Cached Key: '{Key}'","@tr":"131e3b612cebc3cd20902bd8a7c445ea","@sp":"e626338399475c41","Uri":"https://localhost:44309/media/zwdksiin/top-view-storage-devices-glasses_23-2149319333.jpg?rmode=max&width=400&height=300&v=1dbf738cd8906fd","Key":"df6af796bde4","EventId":{"Id":3},"SourceContext":"SixLabors.ImageSharp.Web.Middleware.ImageSharpMiddleware","RequestId":"40000019-0008-fc00-b63f-84710c7967bb","RequestPath":"/media/zwdksiin/top-view-storage-devices-glasses_23-2149319333.jpg","ProcessId":23216,"ProcessName":"iisexpress","ThreadId":53,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-21T08:14:06.9733375Z","@mt":"Could not deserialize string '{JsonString}' into an image cropper value.","@l":"Error","@x":"Newtonsoft.Json.JsonReaderException: Error parsing comment. Expected: *, got m. Path '', line 1, position 1.\r\n   at Newtonsoft.Json.JsonTextReader.ParseComment(Boolean setToken)\r\n   at Newtonsoft.Json.JsonTextReader.ParseValue()\r\n   at Newtonsoft.Json.JsonReader.ReadForType(JsonContract contract, Boolean hasConverter)\r\n   at Newtonsoft.Json.Serialization.JsonSerializerInternalReader.Deserialize(JsonReader reader, Type objectType, Boolean checkAdditionalContent)\r\n   at Newtonsoft.Json.JsonSerializer.DeserializeInternal(JsonReader reader, Type objectType)\r\n   at Newtonsoft.Json.JsonConvert.DeserializeObject(String value, Type type, JsonSerializerSettings settings)\r\n   at Newtonsoft.Json.JsonConvert.DeserializeObject[T](String value, JsonSerializerSettings settings)\r\n   at Umbraco.Cms.Core.PropertyEditors.ValueConverters.ImageCropperValueConverter.ConvertSourceToIntermediate(IPublishedElement owner, IPublishedPropertyType propertyType, Object source, Boolean preview)","@tr":"c0e1aeae0c7ef243c6721d14e0f75506","@sp":"6c794098d4a32421","JsonString":"/media/jhemtee0/icon.png","SourceContext":"Umbraco.Cms.Core.PropertyEditors.ValueConverters.ImageCropperValueConverter","ActionId":"82c7070b-0a0b-4dae-a0ba-29573652b486","ActionName":"Umbraco.Cms.Web.Common.Controllers.RenderController.Index (Umbraco.Web.Common)","RequestId":"40000012-000a-f600-b63f-84710c7967bb","RequestPath":"/about-us/","ProcessId":23216,"ProcessName":"iisexpress","ThreadId":78,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"ERROR","HttpRequestId":"8fe728f7-0c5c-4d9f-8b21-4e6d932d34be","HttpRequestNumber":4,"HttpSessionId":"96b00d25-cdf1-6896-fd84-63330277bc7e"}
{"@t":"2025-07-21T08:14:07.0913082Z","@mt":"Could not deserialize string '{JsonString}' into an image cropper value.","@l":"Error","@x":"Newtonsoft.Json.JsonReaderException: Error parsing comment. Expected: *, got m. Path '', line 1, position 1.\r\n   at Newtonsoft.Json.JsonTextReader.ParseComment(Boolean setToken)\r\n   at Newtonsoft.Json.JsonTextReader.ParseValue()\r\n   at Newtonsoft.Json.JsonReader.ReadForType(JsonContract contract, Boolean hasConverter)\r\n   at Newtonsoft.Json.Serialization.JsonSerializerInternalReader.Deserialize(JsonReader reader, Type objectType, Boolean checkAdditionalContent)\r\n   at Newtonsoft.Json.JsonSerializer.DeserializeInternal(JsonReader reader, Type objectType)\r\n   at Newtonsoft.Json.JsonConvert.DeserializeObject(String value, Type type, JsonSerializerSettings settings)\r\n   at Newtonsoft.Json.JsonConvert.DeserializeObject[T](String value, JsonSerializerSettings settings)\r\n   at Umbraco.Cms.Core.PropertyEditors.ValueConverters.ImageCropperValueConverter.ConvertSourceToIntermediate(IPublishedElement owner, IPublishedPropertyType propertyType, Object source, Boolean preview)","@tr":"c0e1aeae0c7ef243c6721d14e0f75506","@sp":"6c794098d4a32421","JsonString":"/media/mhre1w2b/pexels-photo-2661922.jpeg","SourceContext":"Umbraco.Cms.Core.PropertyEditors.ValueConverters.ImageCropperValueConverter","ActionId":"82c7070b-0a0b-4dae-a0ba-29573652b486","ActionName":"Umbraco.Cms.Web.Common.Controllers.RenderController.Index (Umbraco.Web.Common)","RequestId":"40000012-000a-f600-b63f-84710c7967bb","RequestPath":"/about-us/","ProcessId":23216,"ProcessName":"iisexpress","ThreadId":78,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"ERROR","HttpRequestId":"8fe728f7-0c5c-4d9f-8b21-4e6d932d34be","HttpRequestNumber":4,"HttpSessionId":"96b00d25-cdf1-6896-fd84-63330277bc7e"}
{"@t":"2025-07-21T08:14:07.2510273Z","@mt":"Sending image. Request uri: '{Uri}'. Cached Key: '{Key}'","@tr":"0b3a8799ae3a7a10d7a97ab3250b5b8d","@sp":"7667439893579b7f","Uri":"https://localhost:44309/media/x51jzspk/logo-light.png?rmode=pad&ranchor=left&width=149&height=59&bgcolor=transparent&v=1dbf738cdba0de6","Key":"5efec6e740f4","EventId":{"Id":3},"SourceContext":"SixLabors.ImageSharp.Web.Middleware.ImageSharpMiddleware","RequestId":"4000001b-0008-fc00-b63f-84710c7967bb","RequestPath":"/media/x51jzspk/logo-light.png","ProcessId":23216,"ProcessName":"iisexpress","ThreadId":77,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-21T08:14:07.3187744Z","@mt":"Sending image. Request uri: '{Uri}'. Cached Key: '{Key}'","@tr":"bf9b8d8f3652308b415ec42610878ccc","@sp":"ec1fcbbc1e3e5a3e","Uri":"https://localhost:44309/media/jhemtee0/icon.png?width=185&height=185&v=1dbf738cda93fc4","Key":"b2928b826644","EventId":{"Id":3},"SourceContext":"SixLabors.ImageSharp.Web.Middleware.ImageSharpMiddleware","RequestId":"40000004-0011-f900-b63f-84710c7967bb","RequestPath":"/media/jhemtee0/icon.png","ProcessId":23216,"ProcessName":"iisexpress","ThreadId":86,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-21T08:14:25.4429284Z","@mt":"Sending image. Request uri: '{Uri}'. Cached Key: '{Key}'","@tr":"34873d59b823c64a4e545888db0b6fd8","@sp":"be75b6a9d0734c1a","Uri":"https://localhost:44309/media/g5hlnct5/4a5235_43a477c4822947a5bdebaf6247304813-mv2.png?width=730&height=300","Key":"165cc00db18d","EventId":{"Id":3},"SourceContext":"SixLabors.ImageSharp.Web.Middleware.ImageSharpMiddleware","RequestId":"4000001c-000a-f100-b63f-84710c7967bb","RequestPath":"/media/g5hlnct5/4a5235_43a477c4822947a5bdebaf6247304813-mv2.png","ProcessId":23216,"ProcessName":"iisexpress","ThreadId":78,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-21T08:14:27.1590236Z","@mt":"Sending image. Request uri: '{Uri}'. Cached Key: '{Key}'","@tr":"7a5e98da8a064dfc9de5cb35d83dd3e2","@sp":"642a1102fa173011","Uri":"https://localhost:44309/media/u2veoi3d/4a5235_7c3c58c03bb742d1a159cc2d96da8cb4-mv2.png?width=730&height=300","Key":"ccebdaa219f9","EventId":{"Id":3},"SourceContext":"SixLabors.ImageSharp.Web.Middleware.ImageSharpMiddleware","RequestId":"********-0000-fe00-b63f-84710c7967bb","RequestPath":"/media/u2veoi3d/4a5235_7c3c58c03bb742d1a159cc2d96da8cb4-mv2.png","ProcessId":23216,"ProcessName":"iisexpress","ThreadId":27,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-21T08:14:30.1715180Z","@mt":"Sending image. Request uri: '{Uri}'. Cached Key: '{Key}'","@tr":"fbb16148c9a8b7e90e9bd22cf32b3ee0","@sp":"effaeb92b7e54ff5","Uri":"https://localhost:44309/media/zfkl3hws/a1d39b9944184055866844a5d88fdec3.png?width=730&height=300","Key":"6092841c9845","EventId":{"Id":3},"SourceContext":"SixLabors.ImageSharp.Web.Middleware.ImageSharpMiddleware","RequestId":"40000016-0006-fd00-b63f-84710c7967bb","RequestPath":"/media/zfkl3hws/a1d39b9944184055866844a5d88fdec3.png","ProcessId":23216,"ProcessName":"iisexpress","ThreadId":63,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-21T08:15:44.3793859Z","@mt":"Document {ContentName} (id={ContentId}) cultures: {Cultures} have been published.","@tr":"504be2f6b680f8f35084602033c5dee1","@sp":"30825ff8607c3f93","ContentName":"Home","ContentId":1425,"Cultures":"en-us","SourceContext":"Umbraco.Cms.Core.Services.ContentService","ActionId":"39bbf62c-4a1d-4fdf-9279-8b9b82bf34f5","ActionName":"Umbraco.Cms.Web.BackOffice.Controllers.ContentController.PostSave (Umbraco.Web.BackOffice)","RequestId":"4000001f-0008-fc00-b63f-84710c7967bb","RequestPath":"/umbraco/backoffice/umbracoapi/content/PostSave","ProcessId":23216,"ProcessName":"iisexpress","ThreadId":80,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO ","HttpRequestId":"7c62dc4c-9e73-470a-a6ba-88790b750881","HttpRequestNumber":5,"HttpSessionId":"b8a77fd6-9aa0-3ade-8d0c-7470e9773b99"}
{"@t":"2025-07-21T08:17:28.4457094Z","@mt":"Document {ContentName} (id={ContentId}) cultures: {Cultures} have been published.","@tr":"751d8709e7ab49381e7c60aa7be44d58","@sp":"d9285f3ab14992b9","ContentName":"الرئيسية","ContentId":1425,"Cultures":"ar-sa","SourceContext":"Umbraco.Cms.Core.Services.ContentService","ActionId":"39bbf62c-4a1d-4fdf-9279-8b9b82bf34f5","ActionName":"Umbraco.Cms.Web.BackOffice.Controllers.ContentController.PostSave (Umbraco.Web.BackOffice)","RequestId":"4000001a-000a-f600-b63f-84710c7967bb","RequestPath":"/umbraco/backoffice/umbracoapi/content/PostSave","ProcessId":23216,"ProcessName":"iisexpress","ThreadId":95,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO ","HttpRequestId":"980c6ba9-d97a-438e-b4da-353c6a477feb","HttpRequestNumber":6,"HttpSessionId":"20d8e3e3-8039-24da-1d2a-65898550ffcb"}
{"@t":"2025-07-21T08:17:28.5135913Z","@mt":"The root node {RootNodeName} was published in culture {Culture}, but there's no domain configured for it, this will cause routing and caching issues, please register a domain for it","@l":"Warning","@tr":"751d8709e7ab49381e7c60aa7be44d58","@sp":"d9285f3ab14992b9","RootNodeName":"الرئيسية","Culture":"ar-SA","SourceContext":"Umbraco.Cms.Web.BackOffice.Controllers.ContentController","ActionId":"39bbf62c-4a1d-4fdf-9279-8b9b82bf34f5","ActionName":"Umbraco.Cms.Web.BackOffice.Controllers.ContentController.PostSave (Umbraco.Web.BackOffice)","RequestId":"4000001a-000a-f600-b63f-84710c7967bb","RequestPath":"/umbraco/backoffice/umbracoapi/content/PostSave","ProcessId":23216,"ProcessName":"iisexpress","ThreadId":95,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"WARN ","HttpRequestId":"980c6ba9-d97a-438e-b4da-353c6a477feb","HttpRequestNumber":6,"HttpSessionId":"20d8e3e3-8039-24da-1d2a-65898550ffcb"}
{"@t":"2025-07-21T08:17:44.4925187Z","@mt":"Document {ContentName} (id={ContentId}) cultures: {Cultures} have been published.","@tr":"2f2c49219977f36f0334d3b74a8b82b7","@sp":"82f8e5478c328cd9","ContentName":"الرئيسية","ContentId":1425,"Cultures":"de,ar-sa","SourceContext":"Umbraco.Cms.Core.Services.ContentService","ActionId":"39bbf62c-4a1d-4fdf-9279-8b9b82bf34f5","ActionName":"Umbraco.Cms.Web.BackOffice.Controllers.ContentController.PostSave (Umbraco.Web.BackOffice)","RequestId":"4000001c-0004-fe00-b63f-84710c7967bb","RequestPath":"/umbraco/backoffice/umbracoapi/content/PostSave","ProcessId":23216,"ProcessName":"iisexpress","ThreadId":15,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO ","HttpRequestId":"d2258d18-3757-45ca-8dbf-e4131ea614b3","HttpRequestNumber":7,"HttpSessionId":"182fc832-e5ca-fce8-4a1b-21a81faa4e5c"}
{"@t":"2025-07-21T08:17:44.5452662Z","@mt":"The root node {RootNodeName} was published in culture {Culture}, but there's no domain configured for it, this will cause routing and caching issues, please register a domain for it","@l":"Warning","@tr":"2f2c49219977f36f0334d3b74a8b82b7","@sp":"82f8e5478c328cd9","RootNodeName":"الرئيسية","Culture":"ar-SA","SourceContext":"Umbraco.Cms.Web.BackOffice.Controllers.ContentController","ActionId":"39bbf62c-4a1d-4fdf-9279-8b9b82bf34f5","ActionName":"Umbraco.Cms.Web.BackOffice.Controllers.ContentController.PostSave (Umbraco.Web.BackOffice)","RequestId":"4000001c-0004-fe00-b63f-84710c7967bb","RequestPath":"/umbraco/backoffice/umbracoapi/content/PostSave","ProcessId":23216,"ProcessName":"iisexpress","ThreadId":15,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"WARN ","HttpRequestId":"d2258d18-3757-45ca-8dbf-e4131ea614b3","HttpRequestNumber":7,"HttpSessionId":"182fc832-e5ca-fce8-4a1b-21a81faa4e5c"}
{"@t":"2025-07-21T08:17:55.3709464Z","@mt":"An unhandled exception has occurred while executing the request.","@l":"Error","@x":"System.Collections.Generic.KeyNotFoundException: The given key 'ar-sa' was not present in the dictionary.\r\n   at System.Collections.Generic.Dictionary`2.get_Item(TKey key)\r\n   at AspNetCore.Views_Master.ExecuteAsync() in D:\\tests\\MDDPlusPortal\\MDDPlusPortal\\Views\\Master.cshtml:line 37\r\n   at Microsoft.AspNetCore.Mvc.Razor.RazorView.RenderPageCoreAsync(IRazorPage page, ViewContext context)\r\n   at Microsoft.AspNetCore.Mvc.Razor.RazorView.RenderPageAsync(IRazorPage page, ViewContext context, Boolean invokeViewStarts)\r\n   at Microsoft.AspNetCore.Mvc.Razor.RazorView.RenderLayoutAsync(ViewContext context, ViewBufferTextWriter bodyWriter)\r\n   at Microsoft.AspNetCore.Mvc.Razor.RazorView.RenderAsync(ViewContext context)\r\n   at Microsoft.AspNetCore.Mvc.ViewFeatures.ViewExecutor.ExecuteAsync(ViewContext viewContext, String contentType, Nullable`1 statusCode)\r\n   at Microsoft.AspNetCore.Mvc.ViewFeatures.ViewExecutor.ExecuteAsync(ViewContext viewContext, String contentType, Nullable`1 statusCode)\r\n   at Microsoft.AspNetCore.Mvc.ViewFeatures.ViewExecutor.ExecuteAsync(ActionContext actionContext, IView view, ViewDataDictionary viewData, ITempDataDictionary tempData, String contentType, Nullable`1 statusCode)\r\n   at Microsoft.AspNetCore.Mvc.ViewFeatures.ViewResultExecutor.ExecuteAsync(ActionContext context, ViewResult result)\r\n   at Microsoft.AspNetCore.Mvc.ViewResult.ExecuteResultAsync(ActionContext context)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeResultAsync>g__Logged|22_0(ResourceInvoker invoker, IActionResult result)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextResultFilterAsync>g__Awaited|30_0[TFilter,TFilterAsync](ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Rethrow(ResultExecutedContextSealed context)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.ResultNext[TFilter,TFilterAsync](State& next, Scope& scope, Object& state, Boolean& isCompleted)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.InvokeResultFilters()\r\n--- End of stack trace from previous location ---\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextResourceFilter>g__Awaited|25_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Rethrow(ResourceExecutedContextSealed context)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.InvokeFilterPipelineAsync()\r\n--- End of stack trace from previous location ---\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)\r\n   at Umbraco.Cms.Web.Common.Middleware.BasicAuthenticationMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.InterfaceMiddlewareBinder.<>c__DisplayClass2_0.<<CreateMiddleware>b__0>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Umbraco.Cms.Web.BackOffice.Middleware.BackOfficeExternalLoginProviderErrorMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.InterfaceMiddlewareBinder.<>c__DisplayClass2_0.<<CreateMiddleware>b__0>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Swashbuckle.AspNetCore.SwaggerUI.SwaggerUIMiddleware.Invoke(HttpContext httpContext)\r\n   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)\r\n   at Microsoft.AspNetCore.Session.SessionMiddleware.Invoke(HttpContext context)\r\n   at Microsoft.AspNetCore.Session.SessionMiddleware.Invoke(HttpContext context)\r\n   at Microsoft.AspNetCore.Localization.RequestLocalizationMiddleware.Invoke(HttpContext context)\r\n   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)\r\n   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)\r\n   at StackExchange.Profiling.MiniProfilerMiddleware.Invoke(HttpContext context) in C:\\projects\\dotnet\\src\\MiniProfiler.AspNetCore\\MiniProfilerMiddleware.cs:line 112\r\n   at Umbraco.Cms.Web.Common.Middleware.UmbracoRequestMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Umbraco.Cms.Web.Common.Middleware.UmbracoRequestMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.InterfaceMiddlewareBinder.<>c__DisplayClass2_0.<<CreateMiddleware>b__0>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Umbraco.Cms.Web.Common.Middleware.PreviewAuthenticationMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.InterfaceMiddlewareBinder.<>c__DisplayClass2_0.<<CreateMiddleware>b__0>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Umbraco.Cms.Web.Common.Middleware.UmbracoRequestLoggingMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.InterfaceMiddlewareBinder.<>c__DisplayClass2_0.<<CreateMiddleware>b__0>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at SixLabors.ImageSharp.Web.Middleware.ImageSharpMiddleware.Invoke(HttpContext httpContext, Boolean retry)\r\n   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)","@tr":"c3820a99eb8496e309685a8eb59f80da","@sp":"acaf59293bb2c2fe","EventId":{"Id":1,"Name":"UnhandledException"},"SourceContext":"Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddleware","RequestId":"40000006-0008-f900-b63f-84710c7967bb","RequestPath":"/","ProcessId":23216,"ProcessName":"iisexpress","ThreadId":86,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"ERROR"}
{"@t":"2025-07-21T08:19:14.4136459Z","@mt":"Document {ContentName} (id={ContentId}) cultures: {Cultures} have been published.","@tr":"81a880a0a79f4b1d1265d4a7f77834a0","@sp":"b0f1e0daf6d81326","ContentName":"الرئيسية","ContentId":1425,"Cultures":"en-us","SourceContext":"Umbraco.Cms.Core.Services.ContentService","ActionId":"39bbf62c-4a1d-4fdf-9279-8b9b82bf34f5","ActionName":"Umbraco.Cms.Web.BackOffice.Controllers.ContentController.PostSave (Umbraco.Web.BackOffice)","RequestId":"40000010-0007-fc00-b63f-84710c7967bb","RequestPath":"/umbraco/backoffice/umbracoapi/content/PostSave","ProcessId":23216,"ProcessName":"iisexpress","ThreadId":88,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO ","HttpRequestId":"06192dd2-e26f-4db7-b49e-966553d2c5dc","HttpRequestNumber":8,"HttpSessionId":"32b6e70c-914f-0691-a83f-483e9840536f"}
{"@t":"2025-07-21T08:19:43.0106839Z","@mt":"An unhandled exception has occurred while executing the request.","@l":"Error","@x":"System.Collections.Generic.KeyNotFoundException: The given key 'ar-sa' was not present in the dictionary.\r\n   at System.Collections.Generic.Dictionary`2.get_Item(TKey key)\r\n   at AspNetCore.Views_Master.ExecuteAsync() in D:\\tests\\MDDPlusPortal\\MDDPlusPortal\\Views\\Master.cshtml:line 37\r\n   at Microsoft.AspNetCore.Mvc.Razor.RazorView.RenderPageCoreAsync(IRazorPage page, ViewContext context)\r\n   at Microsoft.AspNetCore.Mvc.Razor.RazorView.RenderPageAsync(IRazorPage page, ViewContext context, Boolean invokeViewStarts)\r\n   at Microsoft.AspNetCore.Mvc.Razor.RazorView.RenderLayoutAsync(ViewContext context, ViewBufferTextWriter bodyWriter)\r\n   at Microsoft.AspNetCore.Mvc.Razor.RazorView.RenderAsync(ViewContext context)\r\n   at Microsoft.AspNetCore.Mvc.ViewFeatures.ViewExecutor.ExecuteAsync(ViewContext viewContext, String contentType, Nullable`1 statusCode)\r\n   at Microsoft.AspNetCore.Mvc.ViewFeatures.ViewExecutor.ExecuteAsync(ViewContext viewContext, String contentType, Nullable`1 statusCode)\r\n   at Microsoft.AspNetCore.Mvc.ViewFeatures.ViewExecutor.ExecuteAsync(ActionContext actionContext, IView view, ViewDataDictionary viewData, ITempDataDictionary tempData, String contentType, Nullable`1 statusCode)\r\n   at Microsoft.AspNetCore.Mvc.ViewFeatures.ViewResultExecutor.ExecuteAsync(ActionContext context, ViewResult result)\r\n   at Microsoft.AspNetCore.Mvc.ViewResult.ExecuteResultAsync(ActionContext context)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeResultAsync>g__Logged|22_0(ResourceInvoker invoker, IActionResult result)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextResultFilterAsync>g__Awaited|30_0[TFilter,TFilterAsync](ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Rethrow(ResultExecutedContextSealed context)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.ResultNext[TFilter,TFilterAsync](State& next, Scope& scope, Object& state, Boolean& isCompleted)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.InvokeResultFilters()\r\n--- End of stack trace from previous location ---\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextResourceFilter>g__Awaited|25_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Rethrow(ResourceExecutedContextSealed context)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.InvokeFilterPipelineAsync()\r\n--- End of stack trace from previous location ---\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)\r\n   at Umbraco.Cms.Web.Common.Middleware.BasicAuthenticationMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.InterfaceMiddlewareBinder.<>c__DisplayClass2_0.<<CreateMiddleware>b__0>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Umbraco.Cms.Web.BackOffice.Middleware.BackOfficeExternalLoginProviderErrorMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.InterfaceMiddlewareBinder.<>c__DisplayClass2_0.<<CreateMiddleware>b__0>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Swashbuckle.AspNetCore.SwaggerUI.SwaggerUIMiddleware.Invoke(HttpContext httpContext)\r\n   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)\r\n   at Microsoft.AspNetCore.Session.SessionMiddleware.Invoke(HttpContext context)\r\n   at Microsoft.AspNetCore.Session.SessionMiddleware.Invoke(HttpContext context)\r\n   at Microsoft.AspNetCore.Localization.RequestLocalizationMiddleware.Invoke(HttpContext context)\r\n   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)\r\n   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)\r\n   at StackExchange.Profiling.MiniProfilerMiddleware.Invoke(HttpContext context) in C:\\projects\\dotnet\\src\\MiniProfiler.AspNetCore\\MiniProfilerMiddleware.cs:line 112\r\n   at Umbraco.Cms.Web.Common.Middleware.UmbracoRequestMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Umbraco.Cms.Web.Common.Middleware.UmbracoRequestMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.InterfaceMiddlewareBinder.<>c__DisplayClass2_0.<<CreateMiddleware>b__0>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Umbraco.Cms.Web.Common.Middleware.PreviewAuthenticationMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.InterfaceMiddlewareBinder.<>c__DisplayClass2_0.<<CreateMiddleware>b__0>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Umbraco.Cms.Web.Common.Middleware.UmbracoRequestLoggingMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.InterfaceMiddlewareBinder.<>c__DisplayClass2_0.<<CreateMiddleware>b__0>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at SixLabors.ImageSharp.Web.Middleware.ImageSharpMiddleware.Invoke(HttpContext httpContext, Boolean retry)\r\n   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)","@tr":"bb4fcd24a393a66b3e8075c1e835e09d","@sp":"18e3eda4eff0ac35","EventId":{"Id":1,"Name":"UnhandledException"},"SourceContext":"Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddleware","RequestId":"40000018-0013-ff00-b63f-84710c7967bb","RequestPath":"/","ProcessId":23216,"ProcessName":"iisexpress","ThreadId":15,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"ERROR"}
{"@t":"2025-07-21T08:22:19.8328204Z","@mt":"An unhandled exception has occurred while executing the request.","@l":"Error","@x":"System.Collections.Generic.KeyNotFoundException: The given key 'ar-sa' was not present in the dictionary.\r\n   at System.Collections.Generic.Dictionary`2.get_Item(TKey key)\r\n   at AspNetCore.Views_Master.ExecuteAsync() in D:\\tests\\MDDPlusPortal\\MDDPlusPortal\\Views\\Master.cshtml:line 37\r\n   at Microsoft.AspNetCore.Mvc.Razor.RazorView.RenderPageCoreAsync(IRazorPage page, ViewContext context)\r\n   at Microsoft.AspNetCore.Mvc.Razor.RazorView.RenderPageAsync(IRazorPage page, ViewContext context, Boolean invokeViewStarts)\r\n   at Microsoft.AspNetCore.Mvc.Razor.RazorView.RenderLayoutAsync(ViewContext context, ViewBufferTextWriter bodyWriter)\r\n   at Microsoft.AspNetCore.Mvc.Razor.RazorView.RenderAsync(ViewContext context)\r\n   at Microsoft.AspNetCore.Mvc.ViewFeatures.ViewExecutor.ExecuteAsync(ViewContext viewContext, String contentType, Nullable`1 statusCode)\r\n   at Microsoft.AspNetCore.Mvc.ViewFeatures.ViewExecutor.ExecuteAsync(ViewContext viewContext, String contentType, Nullable`1 statusCode)\r\n   at Microsoft.AspNetCore.Mvc.ViewFeatures.ViewExecutor.ExecuteAsync(ActionContext actionContext, IView view, ViewDataDictionary viewData, ITempDataDictionary tempData, String contentType, Nullable`1 statusCode)\r\n   at Microsoft.AspNetCore.Mvc.ViewFeatures.ViewResultExecutor.ExecuteAsync(ActionContext context, ViewResult result)\r\n   at Microsoft.AspNetCore.Mvc.ViewResult.ExecuteResultAsync(ActionContext context)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeResultAsync>g__Logged|22_0(ResourceInvoker invoker, IActionResult result)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextResultFilterAsync>g__Awaited|30_0[TFilter,TFilterAsync](ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Rethrow(ResultExecutedContextSealed context)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.ResultNext[TFilter,TFilterAsync](State& next, Scope& scope, Object& state, Boolean& isCompleted)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.InvokeResultFilters()\r\n--- End of stack trace from previous location ---\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextResourceFilter>g__Awaited|25_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Rethrow(ResourceExecutedContextSealed context)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.InvokeFilterPipelineAsync()\r\n--- End of stack trace from previous location ---\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)\r\n   at Umbraco.Cms.Web.Common.Middleware.BasicAuthenticationMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.InterfaceMiddlewareBinder.<>c__DisplayClass2_0.<<CreateMiddleware>b__0>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Umbraco.Cms.Web.BackOffice.Middleware.BackOfficeExternalLoginProviderErrorMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.InterfaceMiddlewareBinder.<>c__DisplayClass2_0.<<CreateMiddleware>b__0>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Swashbuckle.AspNetCore.SwaggerUI.SwaggerUIMiddleware.Invoke(HttpContext httpContext)\r\n   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)\r\n   at Microsoft.AspNetCore.Session.SessionMiddleware.Invoke(HttpContext context)\r\n   at Microsoft.AspNetCore.Session.SessionMiddleware.Invoke(HttpContext context)\r\n   at Microsoft.AspNetCore.Localization.RequestLocalizationMiddleware.Invoke(HttpContext context)\r\n   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)\r\n   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)\r\n   at StackExchange.Profiling.MiniProfilerMiddleware.Invoke(HttpContext context) in C:\\projects\\dotnet\\src\\MiniProfiler.AspNetCore\\MiniProfilerMiddleware.cs:line 112\r\n   at Umbraco.Cms.Web.Common.Middleware.UmbracoRequestMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Umbraco.Cms.Web.Common.Middleware.UmbracoRequestMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.InterfaceMiddlewareBinder.<>c__DisplayClass2_0.<<CreateMiddleware>b__0>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Umbraco.Cms.Web.Common.Middleware.PreviewAuthenticationMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.InterfaceMiddlewareBinder.<>c__DisplayClass2_0.<<CreateMiddleware>b__0>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Umbraco.Cms.Web.Common.Middleware.UmbracoRequestLoggingMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.InterfaceMiddlewareBinder.<>c__DisplayClass2_0.<<CreateMiddleware>b__0>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at SixLabors.ImageSharp.Web.Middleware.ImageSharpMiddleware.Invoke(HttpContext httpContext, Boolean retry)\r\n   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)","@tr":"3391f077150029140891e63501f806af","@sp":"333732b6c2eae0a3","EventId":{"Id":1,"Name":"UnhandledException"},"SourceContext":"Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddleware","RequestId":"40000012-0000-f800-b63f-84710c7967bb","RequestPath":"/","ProcessId":23216,"ProcessName":"iisexpress","ThreadId":98,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"ERROR"}
{"@t":"2025-07-21T08:22:27.3755761Z","@mt":"Document {ContentName} (id={ContentId}) cultures: {Cultures} have been published.","@tr":"d2dbb2873b4f092a0d3408a065df41d7","@sp":"f10953d61f5b79a9","ContentName":"Home","ContentId":1425,"Cultures":"en-us","SourceContext":"Umbraco.Cms.Core.Services.ContentService","ActionId":"39bbf62c-4a1d-4fdf-9279-8b9b82bf34f5","ActionName":"Umbraco.Cms.Web.BackOffice.Controllers.ContentController.PostSave (Umbraco.Web.BackOffice)","RequestId":"40000018-0000-f800-b63f-84710c7967bb","RequestPath":"/umbraco/backoffice/umbracoapi/content/PostSave","ProcessId":23216,"ProcessName":"iisexpress","ThreadId":124,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO ","HttpRequestId":"c3acd3d0-3c39-4bb8-ae50-af48724c430c","HttpRequestNumber":9,"HttpSessionId":"969203f2-4730-766e-39c1-8dc0619f4ee1"}
{"@t":"2025-07-21T08:24:43.1236105Z","@mt":"{StartMessage} [Timing {TimingId}]","@tr":"27bf2ced3624be4e41894e01b9769b30","@sp":"2ce8cf21f756e564","StartMessage":"Loading content from database","TimingId":"b6deeae","SourceContext":"Umbraco.Cms.Core.Logging.ProfilingLogger","ActionId":"ac05cd89-d758-469b-a69c-64eb26bef949","ActionName":"Umbraco.Cms.Web.BackOffice.Controllers.LanguageController.SaveLanguage (Umbraco.Web.BackOffice)","RequestId":"4000000c-0008-f800-b63f-84710c7967bb","RequestPath":"/umbraco/backoffice/umbracoapi/language/SaveLanguage","ProcessId":23216,"ProcessName":"iisexpress","ThreadId":87,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO ","HttpRequestId":"16fd1490-d770-451c-8685-b1d96be7b83f","HttpRequestNumber":10,"HttpSessionId":"28ed612d-e14f-524f-6ef8-d0e121a9bcca"}
{"@t":"2025-07-21T08:24:43.1564266Z","@mt":"{EndMessage} ({Duration}ms) [Timing {TimingId}]","@tr":"27bf2ced3624be4e41894e01b9769b30","@sp":"2ce8cf21f756e564","EndMessage":"Completed.","Duration":32,"TimingId":"b6deeae","SourceContext":"Umbraco.Cms.Core.Logging.ProfilingLogger","ActionId":"ac05cd89-d758-469b-a69c-64eb26bef949","ActionName":"Umbraco.Cms.Web.BackOffice.Controllers.LanguageController.SaveLanguage (Umbraco.Web.BackOffice)","RequestId":"4000000c-0008-f800-b63f-84710c7967bb","RequestPath":"/umbraco/backoffice/umbracoapi/language/SaveLanguage","ProcessId":23216,"ProcessName":"iisexpress","ThreadId":87,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO ","HttpRequestId":"16fd1490-d770-451c-8685-b1d96be7b83f","HttpRequestNumber":10,"HttpSessionId":"28ed612d-e14f-524f-6ef8-d0e121a9bcca"}
{"@t":"2025-07-21T08:24:43.6740796Z","@mt":"The Delivery API is not enabled, no indexing will performed for the Delivery API content index.","SourceContext":"Umbraco.Cms.Infrastructure.Examine.DeliveryApiContentIndexPopulator","ProcessId":23216,"ProcessName":"iisexpress","ThreadId":100,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-21T08:26:15.4456176Z","@mt":"GetUrl exception.","@l":"Error","@x":"System.Exception: Failed to get node at /. This might be because you're trying to publish a variant, with no domains setup\r\n   at Umbraco.Cms.Infrastructure.PublishedCache.ContentCache.ApplyHideTopLevelNodeFromPath(IPublishedContent content, IList`1 segments, Boolean preview)\r\n   at Umbraco.Cms.Infrastructure.PublishedCache.ContentCache.GetRouteByIdInternal(Boolean preview, Int32 contentId, Nullable`1 hideTopLevelNode, String culture)\r\n   at Umbraco.Extensions.AppCacheExtensions.<>c__DisplayClass5_0`1.<GetCacheItem>b__0()\r\n   at Umbraco.Cms.Core.Cache.SafeLazy.<>c__DisplayClass1_0.<GetSafeLazy>b__0()\r\n--- End of stack trace from previous location ---\r\n   at Umbraco.Cms.Core.Routing.DefaultUrlProvider.GetUrl(IPublishedContent content, UrlMode mode, String culture, Uri current)\r\n   at Umbraco.Cms.Core.Routing.UrlProvider.<>c__DisplayClass14_0.<GetUrl>b__1(IUrlProvider provider)\r\n   at System.Linq.Enumerable.SelectEnumerableIterator`2.MoveNext()\r\n   at System.Linq.Enumerable.TryGetFirst[TSource](IEnumerable`1 source, Func`2 predicate, Boolean& found)\r\n   at Umbraco.Cms.Core.Routing.UrlProvider.GetUrl(IPublishedContent content, UrlMode mode, String culture, Uri current)\r\n   at Umbraco.Cms.Core.Routing.UrlProvider.GetUrl(Int32 id, UrlMode mode, String culture, Uri current)\r\n   at Umbraco.Extensions.UrlProviderExtensions.GetContentUrlsByCultureAsync(IContent content, IEnumerable`1 cultures, IPublishedRouter publishedRouter, IUmbracoContext umbracoContext, IContentService contentService, ILocalizedTextService textService, IVariationContextAccessor variationContextAccessor, ILogger logger, UriUtility uriUtility, IPublishedUrlProvider publishedUrlProvider)","@tr":"32009f8841b69989d609bfdb4be4ab05","@sp":"337a4d2c08f08150","SourceContext":"Umbraco.Cms.Core.Models.IContent","ActionId":"dd285985-731e-47e8-a18a-0e654da6cfe0","ActionName":"Umbraco.Cms.Web.BackOffice.Controllers.ContentController.GetById (Umbraco.Web.BackOffice)","RequestId":"********-0006-fd00-b63f-84710c7967bb","RequestPath":"/umbraco/backoffice/umbracoapi/content/GetById","ProcessId":23216,"ProcessName":"iisexpress","ThreadId":57,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"ERROR","HttpRequestId":"3701bd72-2c50-4ff1-a76f-9956f5fe6f1e","HttpRequestNumber":11,"HttpSessionId":"59a8d250-d24c-7046-4ae7-841a277987fb"}
{"@t":"2025-07-21T08:26:48.3191173Z","@mt":"An unhandled exception has occurred while executing the request.","@l":"Error","@x":"System.Collections.Generic.KeyNotFoundException: The given key 'ar' was not present in the dictionary.\r\n   at System.Collections.Generic.Dictionary`2.get_Item(TKey key)\r\n   at AspNetCore.Views_Master.ExecuteAsync() in D:\\tests\\MDDPlusPortal\\MDDPlusPortal\\Views\\Master.cshtml:line 37\r\n   at Microsoft.AspNetCore.Mvc.Razor.RazorView.RenderPageCoreAsync(IRazorPage page, ViewContext context)\r\n   at Microsoft.AspNetCore.Mvc.Razor.RazorView.RenderPageAsync(IRazorPage page, ViewContext context, Boolean invokeViewStarts)\r\n   at Microsoft.AspNetCore.Mvc.Razor.RazorView.RenderLayoutAsync(ViewContext context, ViewBufferTextWriter bodyWriter)\r\n   at Microsoft.AspNetCore.Mvc.Razor.RazorView.RenderAsync(ViewContext context)\r\n   at Microsoft.AspNetCore.Mvc.ViewFeatures.ViewExecutor.ExecuteAsync(ViewContext viewContext, String contentType, Nullable`1 statusCode)\r\n   at Microsoft.AspNetCore.Mvc.ViewFeatures.ViewExecutor.ExecuteAsync(ViewContext viewContext, String contentType, Nullable`1 statusCode)\r\n   at Microsoft.AspNetCore.Mvc.ViewFeatures.ViewExecutor.ExecuteAsync(ActionContext actionContext, IView view, ViewDataDictionary viewData, ITempDataDictionary tempData, String contentType, Nullable`1 statusCode)\r\n   at Microsoft.AspNetCore.Mvc.ViewFeatures.ViewResultExecutor.ExecuteAsync(ActionContext context, ViewResult result)\r\n   at Microsoft.AspNetCore.Mvc.ViewResult.ExecuteResultAsync(ActionContext context)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeResultAsync>g__Logged|22_0(ResourceInvoker invoker, IActionResult result)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextResultFilterAsync>g__Awaited|30_0[TFilter,TFilterAsync](ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Rethrow(ResultExecutedContextSealed context)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.ResultNext[TFilter,TFilterAsync](State& next, Scope& scope, Object& state, Boolean& isCompleted)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.InvokeResultFilters()\r\n--- End of stack trace from previous location ---\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextResourceFilter>g__Awaited|25_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Rethrow(ResourceExecutedContextSealed context)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.InvokeFilterPipelineAsync()\r\n--- End of stack trace from previous location ---\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)\r\n   at Umbraco.Cms.Web.Common.Middleware.BasicAuthenticationMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.InterfaceMiddlewareBinder.<>c__DisplayClass2_0.<<CreateMiddleware>b__0>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Umbraco.Cms.Web.BackOffice.Middleware.BackOfficeExternalLoginProviderErrorMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.InterfaceMiddlewareBinder.<>c__DisplayClass2_0.<<CreateMiddleware>b__0>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Swashbuckle.AspNetCore.SwaggerUI.SwaggerUIMiddleware.Invoke(HttpContext httpContext)\r\n   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)\r\n   at Microsoft.AspNetCore.Session.SessionMiddleware.Invoke(HttpContext context)\r\n   at Microsoft.AspNetCore.Session.SessionMiddleware.Invoke(HttpContext context)\r\n   at Microsoft.AspNetCore.Localization.RequestLocalizationMiddleware.Invoke(HttpContext context)\r\n   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)\r\n   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)\r\n   at StackExchange.Profiling.MiniProfilerMiddleware.Invoke(HttpContext context) in C:\\projects\\dotnet\\src\\MiniProfiler.AspNetCore\\MiniProfilerMiddleware.cs:line 112\r\n   at Umbraco.Cms.Web.Common.Middleware.UmbracoRequestMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Umbraco.Cms.Web.Common.Middleware.UmbracoRequestMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.InterfaceMiddlewareBinder.<>c__DisplayClass2_0.<<CreateMiddleware>b__0>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Umbraco.Cms.Web.Common.Middleware.PreviewAuthenticationMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.InterfaceMiddlewareBinder.<>c__DisplayClass2_0.<<CreateMiddleware>b__0>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Umbraco.Cms.Web.Common.Middleware.UmbracoRequestLoggingMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.InterfaceMiddlewareBinder.<>c__DisplayClass2_0.<<CreateMiddleware>b__0>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at SixLabors.ImageSharp.Web.Middleware.ImageSharpMiddleware.Invoke(HttpContext httpContext, Boolean retry)\r\n   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)","@tr":"3926fd3557785cadd1c7cd38e87b97fc","@sp":"147c5551ff9698f0","EventId":{"Id":1,"Name":"UnhandledException"},"SourceContext":"Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddleware","RequestId":"4000000b-0008-fe00-b63f-84710c7967bb","RequestPath":"/","ProcessId":23216,"ProcessName":"iisexpress","ThreadId":107,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"ERROR"}
{"@t":"2025-07-21T08:27:00.7719088Z","@mt":"An unhandled exception has occurred while executing the request.","@l":"Error","@x":"System.Collections.Generic.KeyNotFoundException: The given key 'ar' was not present in the dictionary.\r\n   at System.Collections.Generic.Dictionary`2.get_Item(TKey key)\r\n   at AspNetCore.Views_Master.ExecuteAsync() in D:\\tests\\MDDPlusPortal\\MDDPlusPortal\\Views\\Master.cshtml:line 37\r\n   at Microsoft.AspNetCore.Mvc.Razor.RazorView.RenderPageCoreAsync(IRazorPage page, ViewContext context)\r\n   at Microsoft.AspNetCore.Mvc.Razor.RazorView.RenderPageAsync(IRazorPage page, ViewContext context, Boolean invokeViewStarts)\r\n   at Microsoft.AspNetCore.Mvc.Razor.RazorView.RenderLayoutAsync(ViewContext context, ViewBufferTextWriter bodyWriter)\r\n   at Microsoft.AspNetCore.Mvc.Razor.RazorView.RenderAsync(ViewContext context)\r\n   at Microsoft.AspNetCore.Mvc.ViewFeatures.ViewExecutor.ExecuteAsync(ViewContext viewContext, String contentType, Nullable`1 statusCode)\r\n   at Microsoft.AspNetCore.Mvc.ViewFeatures.ViewExecutor.ExecuteAsync(ViewContext viewContext, String contentType, Nullable`1 statusCode)\r\n   at Microsoft.AspNetCore.Mvc.ViewFeatures.ViewExecutor.ExecuteAsync(ActionContext actionContext, IView view, ViewDataDictionary viewData, ITempDataDictionary tempData, String contentType, Nullable`1 statusCode)\r\n   at Microsoft.AspNetCore.Mvc.ViewFeatures.ViewResultExecutor.ExecuteAsync(ActionContext context, ViewResult result)\r\n   at Microsoft.AspNetCore.Mvc.ViewResult.ExecuteResultAsync(ActionContext context)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeResultAsync>g__Logged|22_0(ResourceInvoker invoker, IActionResult result)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextResultFilterAsync>g__Awaited|30_0[TFilter,TFilterAsync](ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Rethrow(ResultExecutedContextSealed context)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.ResultNext[TFilter,TFilterAsync](State& next, Scope& scope, Object& state, Boolean& isCompleted)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.InvokeResultFilters()\r\n--- End of stack trace from previous location ---\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextResourceFilter>g__Awaited|25_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Rethrow(ResourceExecutedContextSealed context)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.InvokeFilterPipelineAsync()\r\n--- End of stack trace from previous location ---\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)\r\n   at Umbraco.Cms.Web.Common.Middleware.BasicAuthenticationMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.InterfaceMiddlewareBinder.<>c__DisplayClass2_0.<<CreateMiddleware>b__0>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Umbraco.Cms.Web.BackOffice.Middleware.BackOfficeExternalLoginProviderErrorMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.InterfaceMiddlewareBinder.<>c__DisplayClass2_0.<<CreateMiddleware>b__0>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Swashbuckle.AspNetCore.SwaggerUI.SwaggerUIMiddleware.Invoke(HttpContext httpContext)\r\n   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)\r\n   at Microsoft.AspNetCore.Session.SessionMiddleware.Invoke(HttpContext context)\r\n   at Microsoft.AspNetCore.Session.SessionMiddleware.Invoke(HttpContext context)\r\n   at Microsoft.AspNetCore.Localization.RequestLocalizationMiddleware.Invoke(HttpContext context)\r\n   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)\r\n   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)\r\n   at StackExchange.Profiling.MiniProfilerMiddleware.Invoke(HttpContext context) in C:\\projects\\dotnet\\src\\MiniProfiler.AspNetCore\\MiniProfilerMiddleware.cs:line 112\r\n   at Umbraco.Cms.Web.Common.Middleware.UmbracoRequestMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Umbraco.Cms.Web.Common.Middleware.UmbracoRequestMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.InterfaceMiddlewareBinder.<>c__DisplayClass2_0.<<CreateMiddleware>b__0>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Umbraco.Cms.Web.Common.Middleware.PreviewAuthenticationMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.InterfaceMiddlewareBinder.<>c__DisplayClass2_0.<<CreateMiddleware>b__0>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Umbraco.Cms.Web.Common.Middleware.UmbracoRequestLoggingMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.InterfaceMiddlewareBinder.<>c__DisplayClass2_0.<<CreateMiddleware>b__0>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at SixLabors.ImageSharp.Web.Middleware.ImageSharpMiddleware.Invoke(HttpContext httpContext, Boolean retry)\r\n   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)","@tr":"875ec4ecbd448f4ec28e10147d82f8c1","@sp":"4b6ba25ba8101855","EventId":{"Id":1,"Name":"UnhandledException"},"SourceContext":"Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddleware","RequestId":"4000002f-000c-ff00-b63f-84710c7967bb","RequestPath":"/","ProcessId":23216,"ProcessName":"iisexpress","ThreadId":107,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"ERROR"}
{"@t":"2025-07-21T08:27:02.4053098Z","@mt":"Document {ContentName} (id={ContentId}) cultures: {Cultures} have been published.","@tr":"3117e1a73d43274d5277df76830e2bda","@sp":"ca2087bfb2dac524","ContentName":"Home","ContentId":1425,"Cultures":"en-us","SourceContext":"Umbraco.Cms.Core.Services.ContentService","ActionId":"39bbf62c-4a1d-4fdf-9279-8b9b82bf34f5","ActionName":"Umbraco.Cms.Web.BackOffice.Controllers.ContentController.PostSave (Umbraco.Web.BackOffice)","RequestId":"********-0006-ff00-b63f-84710c7967bb","RequestPath":"/umbraco/backoffice/umbracoapi/content/PostSave","ProcessId":23216,"ProcessName":"iisexpress","ThreadId":104,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO ","HttpRequestId":"25671711-8a0f-48ad-bb68-4b5f751d2d33","HttpRequestNumber":12,"HttpSessionId":"5c2e65d3-c9b2-9ca7-0f20-2d2349ad05c4"}
{"@t":"2025-07-21T08:27:27.3581956Z","@mt":"Document {ContentName} (id={ContentId}) cultures: {Cultures} have been published.","@tr":"e3c391f35d33fe44cc6a4dd3ce5a2134","@sp":"e20f94d667e1bdf5","ContentName":"Home","ContentId":1425,"Cultures":"en-us","SourceContext":"Umbraco.Cms.Core.Services.ContentService","ActionId":"39bbf62c-4a1d-4fdf-9279-8b9b82bf34f5","ActionName":"Umbraco.Cms.Web.BackOffice.Controllers.ContentController.PostSave (Umbraco.Web.BackOffice)","RequestId":"4000000a-0007-fd00-b63f-84710c7967bb","RequestPath":"/umbraco/backoffice/umbracoapi/content/PostSave","ProcessId":23216,"ProcessName":"iisexpress","ThreadId":118,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO ","HttpRequestId":"65c5412c-de9c-42de-8637-0bc233a19708","HttpRequestNumber":13,"HttpSessionId":"0b05debc-2067-23bb-a745-7dd0f5c7d6cc"}
{"@t":"2025-07-21T08:27:34.6989559Z","@mt":"Document {ContentName} (id={ContentId}) cultures: {Cultures} have been published.","@tr":"48e8e91b72368686e7a53781bbcaae1e","@sp":"700683cdb4e70f29","ContentName":"Home","ContentId":1425,"Cultures":"ar","SourceContext":"Umbraco.Cms.Core.Services.ContentService","ActionId":"39bbf62c-4a1d-4fdf-9279-8b9b82bf34f5","ActionName":"Umbraco.Cms.Web.BackOffice.Controllers.ContentController.PostSave (Umbraco.Web.BackOffice)","RequestId":"4000002c-0000-f800-b63f-84710c7967bb","RequestPath":"/umbraco/backoffice/umbracoapi/content/PostSave","ProcessId":23216,"ProcessName":"iisexpress","ThreadId":87,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO ","HttpRequestId":"29ba0ac0-b00a-43e7-8992-6b6fc2b5e21f","HttpRequestNumber":14,"HttpSessionId":"5e77fad5-085a-d171-3aed-bc5d2b1003e6"}
{"@t":"2025-07-21T08:27:43.7518602Z","@mt":"An unhandled exception has occurred while executing the request.","@l":"Error","@x":"System.Collections.Generic.KeyNotFoundException: The given key 'ar' was not present in the dictionary.\r\n   at System.Collections.Generic.Dictionary`2.get_Item(TKey key)\r\n   at AspNetCore.Views_Master.ExecuteAsync() in D:\\tests\\MDDPlusPortal\\MDDPlusPortal\\Views\\Master.cshtml:line 37\r\n   at Microsoft.AspNetCore.Mvc.Razor.RazorView.RenderPageCoreAsync(IRazorPage page, ViewContext context)\r\n   at Microsoft.AspNetCore.Mvc.Razor.RazorView.RenderPageAsync(IRazorPage page, ViewContext context, Boolean invokeViewStarts)\r\n   at Microsoft.AspNetCore.Mvc.Razor.RazorView.RenderLayoutAsync(ViewContext context, ViewBufferTextWriter bodyWriter)\r\n   at Microsoft.AspNetCore.Mvc.Razor.RazorView.RenderAsync(ViewContext context)\r\n   at Microsoft.AspNetCore.Mvc.ViewFeatures.ViewExecutor.ExecuteAsync(ViewContext viewContext, String contentType, Nullable`1 statusCode)\r\n   at Microsoft.AspNetCore.Mvc.ViewFeatures.ViewExecutor.ExecuteAsync(ViewContext viewContext, String contentType, Nullable`1 statusCode)\r\n   at Microsoft.AspNetCore.Mvc.ViewFeatures.ViewExecutor.ExecuteAsync(ActionContext actionContext, IView view, ViewDataDictionary viewData, ITempDataDictionary tempData, String contentType, Nullable`1 statusCode)\r\n   at Microsoft.AspNetCore.Mvc.ViewFeatures.ViewResultExecutor.ExecuteAsync(ActionContext context, ViewResult result)\r\n   at Microsoft.AspNetCore.Mvc.ViewResult.ExecuteResultAsync(ActionContext context)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeResultAsync>g__Logged|22_0(ResourceInvoker invoker, IActionResult result)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextResultFilterAsync>g__Awaited|30_0[TFilter,TFilterAsync](ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Rethrow(ResultExecutedContextSealed context)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.ResultNext[TFilter,TFilterAsync](State& next, Scope& scope, Object& state, Boolean& isCompleted)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.InvokeResultFilters()\r\n--- End of stack trace from previous location ---\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextResourceFilter>g__Awaited|25_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Rethrow(ResourceExecutedContextSealed context)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.InvokeFilterPipelineAsync()\r\n--- End of stack trace from previous location ---\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)\r\n   at Umbraco.Cms.Web.Common.Middleware.BasicAuthenticationMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.InterfaceMiddlewareBinder.<>c__DisplayClass2_0.<<CreateMiddleware>b__0>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Umbraco.Cms.Web.BackOffice.Middleware.BackOfficeExternalLoginProviderErrorMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.InterfaceMiddlewareBinder.<>c__DisplayClass2_0.<<CreateMiddleware>b__0>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Swashbuckle.AspNetCore.SwaggerUI.SwaggerUIMiddleware.Invoke(HttpContext httpContext)\r\n   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)\r\n   at Microsoft.AspNetCore.Session.SessionMiddleware.Invoke(HttpContext context)\r\n   at Microsoft.AspNetCore.Session.SessionMiddleware.Invoke(HttpContext context)\r\n   at Microsoft.AspNetCore.Localization.RequestLocalizationMiddleware.Invoke(HttpContext context)\r\n   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)\r\n   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)\r\n   at StackExchange.Profiling.MiniProfilerMiddleware.Invoke(HttpContext context) in C:\\projects\\dotnet\\src\\MiniProfiler.AspNetCore\\MiniProfilerMiddleware.cs:line 112\r\n   at Umbraco.Cms.Web.Common.Middleware.UmbracoRequestMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Umbraco.Cms.Web.Common.Middleware.UmbracoRequestMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.InterfaceMiddlewareBinder.<>c__DisplayClass2_0.<<CreateMiddleware>b__0>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Umbraco.Cms.Web.Common.Middleware.PreviewAuthenticationMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.InterfaceMiddlewareBinder.<>c__DisplayClass2_0.<<CreateMiddleware>b__0>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Umbraco.Cms.Web.Common.Middleware.UmbracoRequestLoggingMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.InterfaceMiddlewareBinder.<>c__DisplayClass2_0.<<CreateMiddleware>b__0>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at SixLabors.ImageSharp.Web.Middleware.ImageSharpMiddleware.Invoke(HttpContext httpContext, Boolean retry)\r\n   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)","@tr":"e5301d2ad005551d1607a0dad96bbefb","@sp":"74a6db6923b6a9a1","EventId":{"Id":1,"Name":"UnhandledException"},"SourceContext":"Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddleware","RequestId":"40000008-0008-f700-b63f-84710c7967bb","RequestPath":"/ar","ProcessId":23216,"ProcessName":"iisexpress","ThreadId":114,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"ERROR"}
{"@t":"2025-07-21T08:28:16.0276687Z","@mt":"{StartMessage} [Timing {TimingId}]","@tr":"9c1cfd4bc62a3905e3d7ef0eb6d351e0","@sp":"c49a9d1e96829d6e","StartMessage":"Loading content from database","TimingId":"ba6a285","SourceContext":"Umbraco.Cms.Core.Logging.ProfilingLogger","ActionId":"ac05cd89-d758-469b-a69c-64eb26bef949","ActionName":"Umbraco.Cms.Web.BackOffice.Controllers.LanguageController.SaveLanguage (Umbraco.Web.BackOffice)","RequestId":"4000000c-0004-f500-b63f-84710c7967bb","RequestPath":"/umbraco/backoffice/umbracoapi/language/SaveLanguage","ProcessId":23216,"ProcessName":"iisexpress","ThreadId":27,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO ","HttpRequestId":"9447b00b-e2cd-464d-8d6b-10733efd2242","HttpRequestNumber":15,"HttpSessionId":"e38431d8-1fc4-a792-4cbe-8799e80c151a"}
{"@t":"2025-07-21T08:28:16.0427846Z","@mt":"{EndMessage} ({Duration}ms) [Timing {TimingId}]","@tr":"9c1cfd4bc62a3905e3d7ef0eb6d351e0","@sp":"c49a9d1e96829d6e","EndMessage":"Completed.","Duration":15,"TimingId":"ba6a285","SourceContext":"Umbraco.Cms.Core.Logging.ProfilingLogger","ActionId":"ac05cd89-d758-469b-a69c-64eb26bef949","ActionName":"Umbraco.Cms.Web.BackOffice.Controllers.LanguageController.SaveLanguage (Umbraco.Web.BackOffice)","RequestId":"4000000c-0004-f500-b63f-84710c7967bb","RequestPath":"/umbraco/backoffice/umbracoapi/language/SaveLanguage","ProcessId":23216,"ProcessName":"iisexpress","ThreadId":27,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO ","HttpRequestId":"9447b00b-e2cd-464d-8d6b-10733efd2242","HttpRequestNumber":15,"HttpSessionId":"e38431d8-1fc4-a792-4cbe-8799e80c151a"}
{"@t":"2025-07-21T08:28:16.5522735Z","@mt":"The Delivery API is not enabled, no indexing will performed for the Delivery API content index.","SourceContext":"Umbraco.Cms.Infrastructure.Examine.DeliveryApiContentIndexPopulator","ProcessId":23216,"ProcessName":"iisexpress","ThreadId":67,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-21T08:28:48.1558121Z","@mt":"Document {ContentName} (id={ContentId}) cultures: {Cultures} have been published.","@tr":"f965c3ccebe0ef2717ffe55bf11ab702","@sp":"bdcf02c80e99e8ed","ContentName":"Home","ContentId":1425,"Cultures":"ar-sa","SourceContext":"Umbraco.Cms.Core.Services.ContentService","ActionId":"39bbf62c-4a1d-4fdf-9279-8b9b82bf34f5","ActionName":"Umbraco.Cms.Web.BackOffice.Controllers.ContentController.PostSave (Umbraco.Web.BackOffice)","RequestId":"4000001d-0006-fe00-b63f-84710c7967bb","RequestPath":"/umbraco/backoffice/umbracoapi/content/PostSave","ProcessId":23216,"ProcessName":"iisexpress","ThreadId":120,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO ","HttpRequestId":"a406d425-5ae3-4e97-a12a-36f93e7f4ffc","HttpRequestNumber":16,"HttpSessionId":"5ca42b27-67a6-ec43-0bd3-15f14e458e1e"}
{"@t":"2025-07-21T08:29:15.0513576Z","@mt":"An unhandled exception has occurred while executing the request.","@l":"Error","@x":"System.Collections.Generic.KeyNotFoundException: The given key 'ar-sa' was not present in the dictionary.\r\n   at System.Collections.Generic.Dictionary`2.get_Item(TKey key)\r\n   at AspNetCore.Views_Master.ExecuteAsync() in D:\\tests\\MDDPlusPortal\\MDDPlusPortal\\Views\\Master.cshtml:line 37\r\n   at Microsoft.AspNetCore.Mvc.Razor.RazorView.RenderPageCoreAsync(IRazorPage page, ViewContext context)\r\n   at Microsoft.AspNetCore.Mvc.Razor.RazorView.RenderPageAsync(IRazorPage page, ViewContext context, Boolean invokeViewStarts)\r\n   at Microsoft.AspNetCore.Mvc.Razor.RazorView.RenderLayoutAsync(ViewContext context, ViewBufferTextWriter bodyWriter)\r\n   at Microsoft.AspNetCore.Mvc.Razor.RazorView.RenderAsync(ViewContext context)\r\n   at Microsoft.AspNetCore.Mvc.ViewFeatures.ViewExecutor.ExecuteAsync(ViewContext viewContext, String contentType, Nullable`1 statusCode)\r\n   at Microsoft.AspNetCore.Mvc.ViewFeatures.ViewExecutor.ExecuteAsync(ViewContext viewContext, String contentType, Nullable`1 statusCode)\r\n   at Microsoft.AspNetCore.Mvc.ViewFeatures.ViewExecutor.ExecuteAsync(ActionContext actionContext, IView view, ViewDataDictionary viewData, ITempDataDictionary tempData, String contentType, Nullable`1 statusCode)\r\n   at Microsoft.AspNetCore.Mvc.ViewFeatures.ViewResultExecutor.ExecuteAsync(ActionContext context, ViewResult result)\r\n   at Microsoft.AspNetCore.Mvc.ViewResult.ExecuteResultAsync(ActionContext context)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeResultAsync>g__Logged|22_0(ResourceInvoker invoker, IActionResult result)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextResultFilterAsync>g__Awaited|30_0[TFilter,TFilterAsync](ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Rethrow(ResultExecutedContextSealed context)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.ResultNext[TFilter,TFilterAsync](State& next, Scope& scope, Object& state, Boolean& isCompleted)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.InvokeResultFilters()\r\n--- End of stack trace from previous location ---\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextResourceFilter>g__Awaited|25_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Rethrow(ResourceExecutedContextSealed context)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.InvokeFilterPipelineAsync()\r\n--- End of stack trace from previous location ---\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)\r\n   at Umbraco.Cms.Web.Common.Middleware.BasicAuthenticationMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.InterfaceMiddlewareBinder.<>c__DisplayClass2_0.<<CreateMiddleware>b__0>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Umbraco.Cms.Web.BackOffice.Middleware.BackOfficeExternalLoginProviderErrorMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.InterfaceMiddlewareBinder.<>c__DisplayClass2_0.<<CreateMiddleware>b__0>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Swashbuckle.AspNetCore.SwaggerUI.SwaggerUIMiddleware.Invoke(HttpContext httpContext)\r\n   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)\r\n   at Microsoft.AspNetCore.Session.SessionMiddleware.Invoke(HttpContext context)\r\n   at Microsoft.AspNetCore.Session.SessionMiddleware.Invoke(HttpContext context)\r\n   at Microsoft.AspNetCore.Localization.RequestLocalizationMiddleware.Invoke(HttpContext context)\r\n   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)\r\n   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)\r\n   at StackExchange.Profiling.MiniProfilerMiddleware.Invoke(HttpContext context) in C:\\projects\\dotnet\\src\\MiniProfiler.AspNetCore\\MiniProfilerMiddleware.cs:line 112\r\n   at Umbraco.Cms.Web.Common.Middleware.UmbracoRequestMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Umbraco.Cms.Web.Common.Middleware.UmbracoRequestMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.InterfaceMiddlewareBinder.<>c__DisplayClass2_0.<<CreateMiddleware>b__0>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Umbraco.Cms.Web.Common.Middleware.PreviewAuthenticationMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.InterfaceMiddlewareBinder.<>c__DisplayClass2_0.<<CreateMiddleware>b__0>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Umbraco.Cms.Web.Common.Middleware.UmbracoRequestLoggingMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.InterfaceMiddlewareBinder.<>c__DisplayClass2_0.<<CreateMiddleware>b__0>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at SixLabors.ImageSharp.Web.Middleware.ImageSharpMiddleware.Invoke(HttpContext httpContext, Boolean retry)\r\n   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)","@tr":"151cde1119489bf9f9a1a3c321820feb","@sp":"a97e3d5dbe802ffb","EventId":{"Id":1,"Name":"UnhandledException"},"SourceContext":"Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddleware","RequestId":"40000008-0012-fe00-b63f-84710c7967bb","RequestPath":"/ar","ProcessId":23216,"ProcessName":"iisexpress","ThreadId":111,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"ERROR"}
{"@t":"2025-07-21T08:35:31.9568033Z","@mt":"The Delivery API is not enabled, no indexing will performed for the Delivery API content index.","@tr":"7ed8276e24e4c43fbbed43cd726d7b41","@sp":"9021326a57eec159","SourceContext":"Umbraco.Cms.Infrastructure.Examine.DeliveryApiContentIndexPopulator","ActionId":"783b2e23-bac5-4d89-bb04-f2fc3407b426","ActionName":"Umbraco.Cms.Web.BackOffice.Controllers.ExamineManagementController.GetIndexerDetails (Umbraco.Web.BackOffice)","RequestId":"40000041-000a-fb00-b63f-84710c7967bb","RequestPath":"/umbraco/backoffice/umbracoapi/examinemanagement/GetIndexerDetails","ProcessId":23216,"ProcessName":"iisexpress","ThreadId":25,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO ","HttpRequestId":"ab12998a-cac3-49f5-a8c1-208b0ccd3461","HttpRequestNumber":17,"HttpSessionId":"aca9f8b4-4006-4c98-42b0-e4b1db138bb7"}
{"@t":"2025-07-21T08:35:58.3792801Z","@mt":"An unhandled exception has occurred while executing the request.","@l":"Error","@x":"System.Collections.Generic.KeyNotFoundException: The given key 'ar-sa' was not present in the dictionary.\r\n   at System.Collections.Generic.Dictionary`2.get_Item(TKey key)\r\n   at AspNetCore.Views_Master.ExecuteAsync() in D:\\tests\\MDDPlusPortal\\MDDPlusPortal\\Views\\Master.cshtml:line 37\r\n   at Microsoft.AspNetCore.Mvc.Razor.RazorView.RenderPageCoreAsync(IRazorPage page, ViewContext context)\r\n   at Microsoft.AspNetCore.Mvc.Razor.RazorView.RenderPageAsync(IRazorPage page, ViewContext context, Boolean invokeViewStarts)\r\n   at Microsoft.AspNetCore.Mvc.Razor.RazorView.RenderLayoutAsync(ViewContext context, ViewBufferTextWriter bodyWriter)\r\n   at Microsoft.AspNetCore.Mvc.Razor.RazorView.RenderAsync(ViewContext context)\r\n   at Microsoft.AspNetCore.Mvc.ViewFeatures.ViewExecutor.ExecuteAsync(ViewContext viewContext, String contentType, Nullable`1 statusCode)\r\n   at Microsoft.AspNetCore.Mvc.ViewFeatures.ViewExecutor.ExecuteAsync(ViewContext viewContext, String contentType, Nullable`1 statusCode)\r\n   at Microsoft.AspNetCore.Mvc.ViewFeatures.ViewExecutor.ExecuteAsync(ActionContext actionContext, IView view, ViewDataDictionary viewData, ITempDataDictionary tempData, String contentType, Nullable`1 statusCode)\r\n   at Microsoft.AspNetCore.Mvc.ViewFeatures.ViewResultExecutor.ExecuteAsync(ActionContext context, ViewResult result)\r\n   at Microsoft.AspNetCore.Mvc.ViewResult.ExecuteResultAsync(ActionContext context)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeResultAsync>g__Logged|22_0(ResourceInvoker invoker, IActionResult result)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextResultFilterAsync>g__Awaited|30_0[TFilter,TFilterAsync](ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Rethrow(ResultExecutedContextSealed context)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.ResultNext[TFilter,TFilterAsync](State& next, Scope& scope, Object& state, Boolean& isCompleted)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.InvokeResultFilters()\r\n--- End of stack trace from previous location ---\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextResourceFilter>g__Awaited|25_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Rethrow(ResourceExecutedContextSealed context)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.InvokeFilterPipelineAsync()\r\n--- End of stack trace from previous location ---\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)\r\n   at Umbraco.Cms.Web.Common.Middleware.BasicAuthenticationMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.InterfaceMiddlewareBinder.<>c__DisplayClass2_0.<<CreateMiddleware>b__0>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Umbraco.Cms.Web.BackOffice.Middleware.BackOfficeExternalLoginProviderErrorMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.InterfaceMiddlewareBinder.<>c__DisplayClass2_0.<<CreateMiddleware>b__0>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Swashbuckle.AspNetCore.SwaggerUI.SwaggerUIMiddleware.Invoke(HttpContext httpContext)\r\n   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)\r\n   at Microsoft.AspNetCore.Session.SessionMiddleware.Invoke(HttpContext context)\r\n   at Microsoft.AspNetCore.Session.SessionMiddleware.Invoke(HttpContext context)\r\n   at Microsoft.AspNetCore.Localization.RequestLocalizationMiddleware.Invoke(HttpContext context)\r\n   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)\r\n   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)\r\n   at StackExchange.Profiling.MiniProfilerMiddleware.Invoke(HttpContext context) in C:\\projects\\dotnet\\src\\MiniProfiler.AspNetCore\\MiniProfilerMiddleware.cs:line 112\r\n   at Umbraco.Cms.Web.Common.Middleware.UmbracoRequestMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Umbraco.Cms.Web.Common.Middleware.UmbracoRequestMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.InterfaceMiddlewareBinder.<>c__DisplayClass2_0.<<CreateMiddleware>b__0>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Umbraco.Cms.Web.Common.Middleware.PreviewAuthenticationMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.InterfaceMiddlewareBinder.<>c__DisplayClass2_0.<<CreateMiddleware>b__0>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Umbraco.Cms.Web.Common.Middleware.UmbracoRequestLoggingMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.InterfaceMiddlewareBinder.<>c__DisplayClass2_0.<<CreateMiddleware>b__0>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at SixLabors.ImageSharp.Web.Middleware.ImageSharpMiddleware.Invoke(HttpContext httpContext, Boolean retry)\r\n   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)","@tr":"4a28e8e7eca6cd598708bc4a1430fda8","@sp":"175cb07525c31740","EventId":{"Id":1,"Name":"UnhandledException"},"SourceContext":"Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddleware","RequestId":"40000043-000a-fb00-b63f-84710c7967bb","RequestPath":"/ar","ProcessId":23216,"ProcessName":"iisexpress","ThreadId":97,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"ERROR"}
{"@t":"2025-07-21T08:36:55.5545579Z","@mt":"{StartMessage} [Timing {TimingId}]","@tr":"35b2acf1a140f33b81e64880ec091a08","@sp":"8e86e2546245e59e","StartMessage":"Loading content from database","TimingId":"24c2ac3","SourceContext":"Umbraco.Cms.Core.Logging.ProfilingLogger","ActionId":"65d6a2ea-b24a-4db9-b095-4754416755e4","ActionName":"Umbraco.Cms.Web.BackOffice.Controllers.LanguageController.DeleteLanguage (Umbraco.Web.BackOffice)","RequestId":"40000010-0000-fb00-b63f-84710c7967bb","RequestPath":"/umbraco/backoffice/umbracoapi/language/DeleteLanguage","ProcessId":23216,"ProcessName":"iisexpress","ThreadId":16,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO ","HttpRequestId":"d347910f-967a-49ad-8836-dd53371ca803","HttpRequestNumber":18,"HttpSessionId":"4ebca301-3beb-da36-f670-d0a805047c63"}
{"@t":"2025-07-21T08:36:55.5702511Z","@mt":"{EndMessage} ({Duration}ms) [Timing {TimingId}]","@tr":"35b2acf1a140f33b81e64880ec091a08","@sp":"8e86e2546245e59e","EndMessage":"Completed.","Duration":15,"TimingId":"24c2ac3","SourceContext":"Umbraco.Cms.Core.Logging.ProfilingLogger","ActionId":"65d6a2ea-b24a-4db9-b095-4754416755e4","ActionName":"Umbraco.Cms.Web.BackOffice.Controllers.LanguageController.DeleteLanguage (Umbraco.Web.BackOffice)","RequestId":"40000010-0000-fb00-b63f-84710c7967bb","RequestPath":"/umbraco/backoffice/umbracoapi/language/DeleteLanguage","ProcessId":23216,"ProcessName":"iisexpress","ThreadId":16,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO ","HttpRequestId":"d347910f-967a-49ad-8836-dd53371ca803","HttpRequestNumber":18,"HttpSessionId":"4ebca301-3beb-da36-f670-d0a805047c63"}
{"@t":"2025-07-21T08:36:56.1018371Z","@mt":"The Delivery API is not enabled, no indexing will performed for the Delivery API content index.","SourceContext":"Umbraco.Cms.Infrastructure.Examine.DeliveryApiContentIndexPopulator","ProcessId":23216,"ProcessName":"iisexpress","ThreadId":90,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-21T08:37:02.8093828Z","@mt":"An unhandled exception has occurred while executing the request.","@l":"Error","@x":"System.Collections.Generic.KeyNotFoundException: The given key 'ar-sa' was not present in the dictionary.\r\n   at System.Collections.Generic.Dictionary`2.get_Item(TKey key)\r\n   at AspNetCore.Views_Master.ExecuteAsync() in D:\\tests\\MDDPlusPortal\\MDDPlusPortal\\Views\\Master.cshtml:line 37\r\n   at Microsoft.AspNetCore.Mvc.Razor.RazorView.RenderPageCoreAsync(IRazorPage page, ViewContext context)\r\n   at Microsoft.AspNetCore.Mvc.Razor.RazorView.RenderPageAsync(IRazorPage page, ViewContext context, Boolean invokeViewStarts)\r\n   at Microsoft.AspNetCore.Mvc.Razor.RazorView.RenderLayoutAsync(ViewContext context, ViewBufferTextWriter bodyWriter)\r\n   at Microsoft.AspNetCore.Mvc.Razor.RazorView.RenderAsync(ViewContext context)\r\n   at Microsoft.AspNetCore.Mvc.ViewFeatures.ViewExecutor.ExecuteAsync(ViewContext viewContext, String contentType, Nullable`1 statusCode)\r\n   at Microsoft.AspNetCore.Mvc.ViewFeatures.ViewExecutor.ExecuteAsync(ViewContext viewContext, String contentType, Nullable`1 statusCode)\r\n   at Microsoft.AspNetCore.Mvc.ViewFeatures.ViewExecutor.ExecuteAsync(ActionContext actionContext, IView view, ViewDataDictionary viewData, ITempDataDictionary tempData, String contentType, Nullable`1 statusCode)\r\n   at Microsoft.AspNetCore.Mvc.ViewFeatures.ViewResultExecutor.ExecuteAsync(ActionContext context, ViewResult result)\r\n   at Microsoft.AspNetCore.Mvc.ViewResult.ExecuteResultAsync(ActionContext context)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeResultAsync>g__Logged|22_0(ResourceInvoker invoker, IActionResult result)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextResultFilterAsync>g__Awaited|30_0[TFilter,TFilterAsync](ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Rethrow(ResultExecutedContextSealed context)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.ResultNext[TFilter,TFilterAsync](State& next, Scope& scope, Object& state, Boolean& isCompleted)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.InvokeResultFilters()\r\n--- End of stack trace from previous location ---\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextResourceFilter>g__Awaited|25_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Rethrow(ResourceExecutedContextSealed context)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.InvokeFilterPipelineAsync()\r\n--- End of stack trace from previous location ---\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)\r\n   at Umbraco.Cms.Web.Common.Middleware.BasicAuthenticationMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.InterfaceMiddlewareBinder.<>c__DisplayClass2_0.<<CreateMiddleware>b__0>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Umbraco.Cms.Web.BackOffice.Middleware.BackOfficeExternalLoginProviderErrorMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.InterfaceMiddlewareBinder.<>c__DisplayClass2_0.<<CreateMiddleware>b__0>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Swashbuckle.AspNetCore.SwaggerUI.SwaggerUIMiddleware.Invoke(HttpContext httpContext)\r\n   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)\r\n   at Microsoft.AspNetCore.Session.SessionMiddleware.Invoke(HttpContext context)\r\n   at Microsoft.AspNetCore.Session.SessionMiddleware.Invoke(HttpContext context)\r\n   at Microsoft.AspNetCore.Localization.RequestLocalizationMiddleware.Invoke(HttpContext context)\r\n   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)\r\n   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)\r\n   at StackExchange.Profiling.MiniProfilerMiddleware.Invoke(HttpContext context) in C:\\projects\\dotnet\\src\\MiniProfiler.AspNetCore\\MiniProfilerMiddleware.cs:line 112\r\n   at Umbraco.Cms.Web.Common.Middleware.UmbracoRequestMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Umbraco.Cms.Web.Common.Middleware.UmbracoRequestMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.InterfaceMiddlewareBinder.<>c__DisplayClass2_0.<<CreateMiddleware>b__0>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Umbraco.Cms.Web.Common.Middleware.PreviewAuthenticationMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.InterfaceMiddlewareBinder.<>c__DisplayClass2_0.<<CreateMiddleware>b__0>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Umbraco.Cms.Web.Common.Middleware.UmbracoRequestLoggingMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.InterfaceMiddlewareBinder.<>c__DisplayClass2_0.<<CreateMiddleware>b__0>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at SixLabors.ImageSharp.Web.Middleware.ImageSharpMiddleware.Invoke(HttpContext httpContext, Boolean retry)\r\n   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)","@tr":"074358e20b9579b7a2566e871715af9b","@sp":"b9b4c98288aad299","EventId":{"Id":1,"Name":"UnhandledException"},"SourceContext":"Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddleware","RequestId":"4000003b-0008-fc00-b63f-84710c7967bb","RequestPath":"/","ProcessId":23216,"ProcessName":"iisexpress","ThreadId":16,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"ERROR"}
{"@t":"2025-07-21T08:37:37.2396486Z","@mt":"Document {ContentName} (id={ContentId}) cultures: {Cultures} have been published.","@tr":"3682c6e129e22374f5cbfc196e97fd23","@sp":"692704d115739c92","ContentName":"Home","ContentId":1425,"Cultures":"en-us","SourceContext":"Umbraco.Cms.Core.Services.ContentService","ActionId":"39bbf62c-4a1d-4fdf-9279-8b9b82bf34f5","ActionName":"Umbraco.Cms.Web.BackOffice.Controllers.ContentController.PostSave (Umbraco.Web.BackOffice)","RequestId":"40000022-0008-fb00-b63f-84710c7967bb","RequestPath":"/umbraco/backoffice/umbracoapi/content/PostSave","ProcessId":23216,"ProcessName":"iisexpress","ThreadId":73,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO ","HttpRequestId":"99444814-7119-41ab-902a-b4ba39a645c5","HttpRequestNumber":19,"HttpSessionId":"497ced2a-88e9-54b6-9c03-84dc7c38e401"}
{"@t":"2025-07-21T08:38:26.9881260Z","@mt":"{StartMessage} [Timing {TimingId}]","@tr":"7f5418be1e93f04fefdf69cc6f9145b3","@sp":"585b58d4182777c5","StartMessage":"Loading content from database","TimingId":"6581458","SourceContext":"Umbraco.Cms.Core.Logging.ProfilingLogger","ActionId":"ac05cd89-d758-469b-a69c-64eb26bef949","ActionName":"Umbraco.Cms.Web.BackOffice.Controllers.LanguageController.SaveLanguage (Umbraco.Web.BackOffice)","RequestId":"40000006-0007-fb00-b63f-84710c7967bb","RequestPath":"/umbraco/backoffice/umbracoapi/language/SaveLanguage","ProcessId":23216,"ProcessName":"iisexpress","ThreadId":88,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO ","HttpRequestId":"c755bc34-3bb9-422f-a68a-b9b92e5a9911","HttpRequestNumber":20,"HttpSessionId":"d5314415-4b84-6b5a-9482-4cb262ddc3d3"}
{"@t":"2025-07-21T08:38:27.0040661Z","@mt":"{EndMessage} ({Duration}ms) [Timing {TimingId}]","@tr":"7f5418be1e93f04fefdf69cc6f9145b3","@sp":"585b58d4182777c5","EndMessage":"Completed.","Duration":15,"TimingId":"6581458","SourceContext":"Umbraco.Cms.Core.Logging.ProfilingLogger","ActionId":"ac05cd89-d758-469b-a69c-64eb26bef949","ActionName":"Umbraco.Cms.Web.BackOffice.Controllers.LanguageController.SaveLanguage (Umbraco.Web.BackOffice)","RequestId":"40000006-0007-fb00-b63f-84710c7967bb","RequestPath":"/umbraco/backoffice/umbracoapi/language/SaveLanguage","ProcessId":23216,"ProcessName":"iisexpress","ThreadId":88,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO ","HttpRequestId":"c755bc34-3bb9-422f-a68a-b9b92e5a9911","HttpRequestNumber":20,"HttpSessionId":"d5314415-4b84-6b5a-9482-4cb262ddc3d3"}
{"@t":"2025-07-21T08:38:27.4914774Z","@mt":"The Delivery API is not enabled, no indexing will performed for the Delivery API content index.","SourceContext":"Umbraco.Cms.Infrastructure.Examine.DeliveryApiContentIndexPopulator","ProcessId":23216,"ProcessName":"iisexpress","ThreadId":98,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-21T08:39:43.4541931Z","@mt":"Sending image. Request uri: '{Uri}'. Cached Key: '{Key}'","@tr":"45bdae452812f71488af9de1b96ad777","@sp":"633e9b5f897ec848","Uri":"https://localhost:44309/media/x51jzspk/logo-light.png?rmode=max&width=500&v=1dbf71fa86cbcd8","Key":"ac4fc60d8ff2","EventId":{"Id":3},"SourceContext":"SixLabors.ImageSharp.Web.Middleware.ImageSharpMiddleware","RequestId":"4000001c-0000-f900-b63f-84710c7967bb","RequestPath":"/media/x51jzspk/logo-light.png","ProcessId":23216,"ProcessName":"iisexpress","ThreadId":15,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-21T08:39:43.4555020Z","@mt":"Sending image. Request uri: '{Uri}'. Cached Key: '{Key}'","@tr":"ce640987c8f8f7e57bb3c77b929ac871","@sp":"8e387d0aaaf7da1c","Uri":"https://localhost:44309/media/34ef3hkp/logo.png?rmode=max&width=500&v=1dbf71fa86ad507","Key":"6af4b4f2a8f8","EventId":{"Id":3},"SourceContext":"SixLabors.ImageSharp.Web.Middleware.ImageSharpMiddleware","RequestId":"40000022-0008-f700-b63f-84710c7967bb","RequestPath":"/media/34ef3hkp/logo.png","ProcessId":23216,"ProcessName":"iisexpress","ThreadId":73,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-21T08:39:43.4590063Z","@mt":"Sending image. Request uri: '{Uri}'. Cached Key: '{Key}'","@tr":"58597fd2a7ef2685d2ef80afae37283b","@sp":"c158259ddcfeea57","Uri":"https://localhost:44309/media/a0yk2tgt/fawicon.png?rmode=max&width=500&v=1dbf71fa86eba0c","Key":"8e1e9857cc83","EventId":{"Id":3},"SourceContext":"SixLabors.ImageSharp.Web.Middleware.ImageSharpMiddleware","RequestId":"4000000e-0002-f900-b63f-84710c7967bb","RequestPath":"/media/a0yk2tgt/fawicon.png","ProcessId":23216,"ProcessName":"iisexpress","ThreadId":103,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-21T08:39:48.9138603Z","@mt":"Sending image. Request uri: '{Uri}'. Cached Key: '{Key}'","@tr":"fd163fff5b6af415b110565114a55827","@sp":"1d79eb1802c5ea86","Uri":"https://localhost:44309/media/cjwnfpha/top-backend-programming-languages-infographic-image.png?rmode=max&width=500&v=1dbf71fa7d34290","Key":"b2e59b438333","EventId":{"Id":3},"SourceContext":"SixLabors.ImageSharp.Web.Middleware.ImageSharpMiddleware","RequestId":"4000001e-0000-f900-b63f-84710c7967bb","RequestPath":"/media/cjwnfpha/top-backend-programming-languages-infographic-image.png","ProcessId":23216,"ProcessName":"iisexpress","ThreadId":21,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-21T08:39:49.4632105Z","@mt":"Sending image. Request uri: '{Uri}'. Cached Key: '{Key}'","@tr":"4a6dfe4b1677865221d7ba7ef0614e12","@sp":"d51396976937eddc","Uri":"https://localhost:44309/media/w3djgyl1/6949a5af98af0a1c12e32935d58412db.jpg?rmode=max&width=500&v=1dbf71fa772d887","Key":"fc6da7f18d91","EventId":{"Id":3},"SourceContext":"SixLabors.ImageSharp.Web.Middleware.ImageSharpMiddleware","RequestId":"40000004-0004-f100-b63f-84710c7967bb","RequestPath":"/media/w3djgyl1/6949a5af98af0a1c12e32935d58412db.jpg","ProcessId":23216,"ProcessName":"iisexpress","ThreadId":10,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-21T08:39:49.4663127Z","@mt":"Sending image. Request uri: '{Uri}'. Cached Key: '{Key}'","@tr":"e1d628d7623ac1d6802508728cdda169","@sp":"dfec4469babb8277","Uri":"https://localhost:44309/media/ehne11mx/623c1e9eae7e8c3ebda393d20276cd34.png?rmode=max&width=500&v=1dbf71fa774d92f","Key":"93212868ff54","EventId":{"Id":3},"SourceContext":"SixLabors.ImageSharp.Web.Middleware.ImageSharpMiddleware","RequestId":"40000004-0004-f200-b63f-84710c7967bb","RequestPath":"/media/ehne11mx/623c1e9eae7e8c3ebda393d20276cd34.png","ProcessId":23216,"ProcessName":"iisexpress","ThreadId":21,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-21T08:39:49.5591054Z","@mt":"Sending image. Request uri: '{Uri}'. Cached Key: '{Key}'","@tr":"d3cee07e96ee9c2a9f4ff59cbba2f5fe","@sp":"888ffaea6fc8e109","Uri":"https://localhost:44309/media/1bsfotr2/images.png?rmode=max&width=500&v=1dbf71fa7c446c7","Key":"45e55a26c9f1","EventId":{"Id":3},"SourceContext":"SixLabors.ImageSharp.Web.Middleware.ImageSharpMiddleware","RequestId":"40000018-000a-f900-b63f-84710c7967bb","RequestPath":"/media/1bsfotr2/images.png","ProcessId":23216,"ProcessName":"iisexpress","ThreadId":98,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-21T08:39:49.5594565Z","@mt":"Sending image. Request uri: '{Uri}'. Cached Key: '{Key}'","@tr":"28dd16bc8754038734ea134e105b6931","@sp":"9a7f90118b34a28b","Uri":"https://localhost:44309/media/qwulpoda/1615993866.png?rmode=max&width=500&v=1dbf71fa770df36","Key":"cd2e4c1200e3","EventId":{"Id":3},"SourceContext":"SixLabors.ImageSharp.Web.Middleware.ImageSharpMiddleware","RequestId":"4000003c-0004-fe00-b63f-84710c7967bb","RequestPath":"/media/qwulpoda/1615993866.png","ProcessId":23216,"ProcessName":"iisexpress","ThreadId":25,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-21T08:39:49.5601349Z","@mt":"Sending image. Request uri: '{Uri}'. Cached Key: '{Key}'","@tr":"9bf46b150354c05545067a7427424dd2","@sp":"1a9bff45b84ef706","Uri":"https://localhost:44309/media/g4sljxev/vital-energy-logo.png?rmode=max&width=500&v=1dbf71fa7cea87e","Key":"49e4d83afc2f","EventId":{"Id":3},"SourceContext":"SixLabors.ImageSharp.Web.Middleware.ImageSharpMiddleware","RequestId":"40000024-0008-f700-b63f-84710c7967bb","RequestPath":"/media/g4sljxev/vital-energy-logo.png","ProcessId":23216,"ProcessName":"iisexpress","ThreadId":91,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-21T08:39:49.5621479Z","@mt":"Sending image. Request uri: '{Uri}'. Cached Key: '{Key}'","@tr":"01f6146d37c15b5d813f1530a1467dac","@sp":"9f9c72a355890c3e","Uri":"https://localhost:44309/media/uabl1qsl/veriforce-logo-mobile.png?rmode=max&width=500&v=1dbf71fa7d0af70","Key":"96b726dc19ee","EventId":{"Id":3},"SourceContext":"SixLabors.ImageSharp.Web.Middleware.ImageSharpMiddleware","RequestId":"40000008-000a-f500-b63f-84710c7967bb","RequestPath":"/media/uabl1qsl/veriforce-logo-mobile.png","ProcessId":23216,"ProcessName":"iisexpress","ThreadId":85,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-21T08:39:59.9689274Z","@mt":"Document {ContentName} (id={ContentId}) cultures: {Cultures} have been published.","@tr":"520592ab37f322a540a94356fc3de6c6","@sp":"d8881ac965e77ad3","ContentName":"Home","ContentId":1425,"Cultures":"ar-sa","SourceContext":"Umbraco.Cms.Core.Services.ContentService","ActionId":"39bbf62c-4a1d-4fdf-9279-8b9b82bf34f5","ActionName":"Umbraco.Cms.Web.BackOffice.Controllers.ContentController.PostSave (Umbraco.Web.BackOffice)","RequestId":"4000004e-0000-f800-b63f-84710c7967bb","RequestPath":"/umbraco/backoffice/umbracoapi/content/PostSave","ProcessId":23216,"ProcessName":"iisexpress","ThreadId":85,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO ","HttpRequestId":"44455b22-30a3-428d-9898-f0281bca51ae","HttpRequestNumber":21,"HttpSessionId":"7ff24732-e57c-c1dd-b223-7e08dae9f689"}
{"@t":"2025-07-21T08:40:06.5122982Z","@mt":"An unhandled exception has occurred while executing the request.","@l":"Error","@x":"System.Collections.Generic.KeyNotFoundException: The given key 'ar-sa' was not present in the dictionary.\r\n   at System.Collections.Generic.Dictionary`2.get_Item(TKey key)\r\n   at AspNetCore.Views_Master.ExecuteAsync() in D:\\tests\\MDDPlusPortal\\MDDPlusPortal\\Views\\Master.cshtml:line 37\r\n   at Microsoft.AspNetCore.Mvc.Razor.RazorView.RenderPageCoreAsync(IRazorPage page, ViewContext context)\r\n   at Microsoft.AspNetCore.Mvc.Razor.RazorView.RenderPageAsync(IRazorPage page, ViewContext context, Boolean invokeViewStarts)\r\n   at Microsoft.AspNetCore.Mvc.Razor.RazorView.RenderLayoutAsync(ViewContext context, ViewBufferTextWriter bodyWriter)\r\n   at Microsoft.AspNetCore.Mvc.Razor.RazorView.RenderAsync(ViewContext context)\r\n   at Microsoft.AspNetCore.Mvc.ViewFeatures.ViewExecutor.ExecuteAsync(ViewContext viewContext, String contentType, Nullable`1 statusCode)\r\n   at Microsoft.AspNetCore.Mvc.ViewFeatures.ViewExecutor.ExecuteAsync(ViewContext viewContext, String contentType, Nullable`1 statusCode)\r\n   at Microsoft.AspNetCore.Mvc.ViewFeatures.ViewExecutor.ExecuteAsync(ActionContext actionContext, IView view, ViewDataDictionary viewData, ITempDataDictionary tempData, String contentType, Nullable`1 statusCode)\r\n   at Microsoft.AspNetCore.Mvc.ViewFeatures.ViewResultExecutor.ExecuteAsync(ActionContext context, ViewResult result)\r\n   at Microsoft.AspNetCore.Mvc.ViewResult.ExecuteResultAsync(ActionContext context)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeResultAsync>g__Logged|22_0(ResourceInvoker invoker, IActionResult result)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextResultFilterAsync>g__Awaited|30_0[TFilter,TFilterAsync](ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Rethrow(ResultExecutedContextSealed context)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.ResultNext[TFilter,TFilterAsync](State& next, Scope& scope, Object& state, Boolean& isCompleted)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.InvokeResultFilters()\r\n--- End of stack trace from previous location ---\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextResourceFilter>g__Awaited|25_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Rethrow(ResourceExecutedContextSealed context)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.InvokeFilterPipelineAsync()\r\n--- End of stack trace from previous location ---\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)\r\n   at Umbraco.Cms.Web.Common.Middleware.BasicAuthenticationMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.InterfaceMiddlewareBinder.<>c__DisplayClass2_0.<<CreateMiddleware>b__0>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Umbraco.Cms.Web.BackOffice.Middleware.BackOfficeExternalLoginProviderErrorMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.InterfaceMiddlewareBinder.<>c__DisplayClass2_0.<<CreateMiddleware>b__0>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Swashbuckle.AspNetCore.SwaggerUI.SwaggerUIMiddleware.Invoke(HttpContext httpContext)\r\n   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)\r\n   at Microsoft.AspNetCore.Session.SessionMiddleware.Invoke(HttpContext context)\r\n   at Microsoft.AspNetCore.Session.SessionMiddleware.Invoke(HttpContext context)\r\n   at Microsoft.AspNetCore.Localization.RequestLocalizationMiddleware.Invoke(HttpContext context)\r\n   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)\r\n   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)\r\n   at StackExchange.Profiling.MiniProfilerMiddleware.Invoke(HttpContext context) in C:\\projects\\dotnet\\src\\MiniProfiler.AspNetCore\\MiniProfilerMiddleware.cs:line 112\r\n   at Umbraco.Cms.Web.Common.Middleware.UmbracoRequestMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Umbraco.Cms.Web.Common.Middleware.UmbracoRequestMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.InterfaceMiddlewareBinder.<>c__DisplayClass2_0.<<CreateMiddleware>b__0>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Umbraco.Cms.Web.Common.Middleware.PreviewAuthenticationMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.InterfaceMiddlewareBinder.<>c__DisplayClass2_0.<<CreateMiddleware>b__0>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Umbraco.Cms.Web.Common.Middleware.UmbracoRequestLoggingMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.InterfaceMiddlewareBinder.<>c__DisplayClass2_0.<<CreateMiddleware>b__0>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at SixLabors.ImageSharp.Web.Middleware.ImageSharpMiddleware.Invoke(HttpContext httpContext, Boolean retry)\r\n   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)","@tr":"b82a825581768cfdeff36c492bb06e2e","@sp":"56e00ee76cda9ff1","EventId":{"Id":1,"Name":"UnhandledException"},"SourceContext":"Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddleware","RequestId":"40000050-0000-f800-b63f-84710c7967bb","RequestPath":"/","ProcessId":23216,"ProcessName":"iisexpress","ThreadId":91,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"ERROR"}
{"@t":"2025-07-21T08:40:41.6168938Z","@mt":"An unhandled exception has occurred while executing the request.","@l":"Error","@x":"System.Collections.Generic.KeyNotFoundException: The given key 'ar-sa' was not present in the dictionary.\r\n   at System.Collections.Generic.Dictionary`2.get_Item(TKey key)\r\n   at AspNetCore.Views_Master.ExecuteAsync() in D:\\tests\\MDDPlusPortal\\MDDPlusPortal\\Views\\Master.cshtml:line 37\r\n   at Microsoft.AspNetCore.Mvc.Razor.RazorView.RenderPageCoreAsync(IRazorPage page, ViewContext context)\r\n   at Microsoft.AspNetCore.Mvc.Razor.RazorView.RenderPageAsync(IRazorPage page, ViewContext context, Boolean invokeViewStarts)\r\n   at Microsoft.AspNetCore.Mvc.Razor.RazorView.RenderLayoutAsync(ViewContext context, ViewBufferTextWriter bodyWriter)\r\n   at Microsoft.AspNetCore.Mvc.Razor.RazorView.RenderAsync(ViewContext context)\r\n   at Microsoft.AspNetCore.Mvc.ViewFeatures.ViewExecutor.ExecuteAsync(ViewContext viewContext, String contentType, Nullable`1 statusCode)\r\n   at Microsoft.AspNetCore.Mvc.ViewFeatures.ViewExecutor.ExecuteAsync(ViewContext viewContext, String contentType, Nullable`1 statusCode)\r\n   at Microsoft.AspNetCore.Mvc.ViewFeatures.ViewExecutor.ExecuteAsync(ActionContext actionContext, IView view, ViewDataDictionary viewData, ITempDataDictionary tempData, String contentType, Nullable`1 statusCode)\r\n   at Microsoft.AspNetCore.Mvc.ViewFeatures.ViewResultExecutor.ExecuteAsync(ActionContext context, ViewResult result)\r\n   at Microsoft.AspNetCore.Mvc.ViewResult.ExecuteResultAsync(ActionContext context)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeResultAsync>g__Logged|22_0(ResourceInvoker invoker, IActionResult result)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextResultFilterAsync>g__Awaited|30_0[TFilter,TFilterAsync](ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Rethrow(ResultExecutedContextSealed context)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.ResultNext[TFilter,TFilterAsync](State& next, Scope& scope, Object& state, Boolean& isCompleted)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.InvokeResultFilters()\r\n--- End of stack trace from previous location ---\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextResourceFilter>g__Awaited|25_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Rethrow(ResourceExecutedContextSealed context)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.InvokeFilterPipelineAsync()\r\n--- End of stack trace from previous location ---\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)\r\n   at Umbraco.Cms.Web.Common.Middleware.BasicAuthenticationMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.InterfaceMiddlewareBinder.<>c__DisplayClass2_0.<<CreateMiddleware>b__0>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Umbraco.Cms.Web.BackOffice.Middleware.BackOfficeExternalLoginProviderErrorMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.InterfaceMiddlewareBinder.<>c__DisplayClass2_0.<<CreateMiddleware>b__0>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Swashbuckle.AspNetCore.SwaggerUI.SwaggerUIMiddleware.Invoke(HttpContext httpContext)\r\n   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)\r\n   at Microsoft.AspNetCore.Session.SessionMiddleware.Invoke(HttpContext context)\r\n   at Microsoft.AspNetCore.Session.SessionMiddleware.Invoke(HttpContext context)\r\n   at Microsoft.AspNetCore.Localization.RequestLocalizationMiddleware.Invoke(HttpContext context)\r\n   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)\r\n   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)\r\n   at StackExchange.Profiling.MiniProfilerMiddleware.Invoke(HttpContext context) in C:\\projects\\dotnet\\src\\MiniProfiler.AspNetCore\\MiniProfilerMiddleware.cs:line 112\r\n   at Umbraco.Cms.Web.Common.Middleware.UmbracoRequestMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Umbraco.Cms.Web.Common.Middleware.UmbracoRequestMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.InterfaceMiddlewareBinder.<>c__DisplayClass2_0.<<CreateMiddleware>b__0>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Umbraco.Cms.Web.Common.Middleware.PreviewAuthenticationMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.InterfaceMiddlewareBinder.<>c__DisplayClass2_0.<<CreateMiddleware>b__0>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Umbraco.Cms.Web.Common.Middleware.UmbracoRequestLoggingMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.InterfaceMiddlewareBinder.<>c__DisplayClass2_0.<<CreateMiddleware>b__0>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at SixLabors.ImageSharp.Web.Middleware.ImageSharpMiddleware.Invoke(HttpContext httpContext, Boolean retry)\r\n   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)","@tr":"8eb50d5a57f30e93065021352fe400e2","@sp":"f6c9934b0f0f9230","EventId":{"Id":1,"Name":"UnhandledException"},"SourceContext":"Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddleware","RequestId":"40000016-0000-f600-b63f-84710c7967bb","RequestPath":"/de","ProcessId":23216,"ProcessName":"iisexpress","ThreadId":53,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"ERROR"}
{"@t":"2025-07-21T08:40:46.7955054Z","@mt":"An unhandled exception has occurred while executing the request.","@l":"Error","@x":"System.Collections.Generic.KeyNotFoundException: The given key 'ar-sa' was not present in the dictionary.\r\n   at System.Collections.Generic.Dictionary`2.get_Item(TKey key)\r\n   at AspNetCore.Views_Master.ExecuteAsync() in D:\\tests\\MDDPlusPortal\\MDDPlusPortal\\Views\\Master.cshtml:line 37\r\n   at Microsoft.AspNetCore.Mvc.Razor.RazorView.RenderPageCoreAsync(IRazorPage page, ViewContext context)\r\n   at Microsoft.AspNetCore.Mvc.Razor.RazorView.RenderPageAsync(IRazorPage page, ViewContext context, Boolean invokeViewStarts)\r\n   at Microsoft.AspNetCore.Mvc.Razor.RazorView.RenderLayoutAsync(ViewContext context, ViewBufferTextWriter bodyWriter)\r\n   at Microsoft.AspNetCore.Mvc.Razor.RazorView.RenderAsync(ViewContext context)\r\n   at Microsoft.AspNetCore.Mvc.ViewFeatures.ViewExecutor.ExecuteAsync(ViewContext viewContext, String contentType, Nullable`1 statusCode)\r\n   at Microsoft.AspNetCore.Mvc.ViewFeatures.ViewExecutor.ExecuteAsync(ViewContext viewContext, String contentType, Nullable`1 statusCode)\r\n   at Microsoft.AspNetCore.Mvc.ViewFeatures.ViewExecutor.ExecuteAsync(ActionContext actionContext, IView view, ViewDataDictionary viewData, ITempDataDictionary tempData, String contentType, Nullable`1 statusCode)\r\n   at Microsoft.AspNetCore.Mvc.ViewFeatures.ViewResultExecutor.ExecuteAsync(ActionContext context, ViewResult result)\r\n   at Microsoft.AspNetCore.Mvc.ViewResult.ExecuteResultAsync(ActionContext context)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeResultAsync>g__Logged|22_0(ResourceInvoker invoker, IActionResult result)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextResultFilterAsync>g__Awaited|30_0[TFilter,TFilterAsync](ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Rethrow(ResultExecutedContextSealed context)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.ResultNext[TFilter,TFilterAsync](State& next, Scope& scope, Object& state, Boolean& isCompleted)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.InvokeResultFilters()\r\n--- End of stack trace from previous location ---\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextResourceFilter>g__Awaited|25_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Rethrow(ResourceExecutedContextSealed context)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.InvokeFilterPipelineAsync()\r\n--- End of stack trace from previous location ---\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)\r\n   at Umbraco.Cms.Web.Common.Middleware.BasicAuthenticationMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.InterfaceMiddlewareBinder.<>c__DisplayClass2_0.<<CreateMiddleware>b__0>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Umbraco.Cms.Web.BackOffice.Middleware.BackOfficeExternalLoginProviderErrorMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.InterfaceMiddlewareBinder.<>c__DisplayClass2_0.<<CreateMiddleware>b__0>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Swashbuckle.AspNetCore.SwaggerUI.SwaggerUIMiddleware.Invoke(HttpContext httpContext)\r\n   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)\r\n   at Microsoft.AspNetCore.Session.SessionMiddleware.Invoke(HttpContext context)\r\n   at Microsoft.AspNetCore.Session.SessionMiddleware.Invoke(HttpContext context)\r\n   at Microsoft.AspNetCore.Localization.RequestLocalizationMiddleware.Invoke(HttpContext context)\r\n   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)\r\n   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)\r\n   at StackExchange.Profiling.MiniProfilerMiddleware.Invoke(HttpContext context) in C:\\projects\\dotnet\\src\\MiniProfiler.AspNetCore\\MiniProfilerMiddleware.cs:line 112\r\n   at Umbraco.Cms.Web.Common.Middleware.UmbracoRequestMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Umbraco.Cms.Web.Common.Middleware.UmbracoRequestMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.InterfaceMiddlewareBinder.<>c__DisplayClass2_0.<<CreateMiddleware>b__0>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Umbraco.Cms.Web.Common.Middleware.PreviewAuthenticationMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.InterfaceMiddlewareBinder.<>c__DisplayClass2_0.<<CreateMiddleware>b__0>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Umbraco.Cms.Web.Common.Middleware.UmbracoRequestLoggingMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.InterfaceMiddlewareBinder.<>c__DisplayClass2_0.<<CreateMiddleware>b__0>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at SixLabors.ImageSharp.Web.Middleware.ImageSharpMiddleware.Invoke(HttpContext httpContext, Boolean retry)\r\n   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)","@tr":"657c2259b6be7c47bcbff59acc576876","@sp":"29d3a2cc0daf3bc2","EventId":{"Id":1,"Name":"UnhandledException"},"SourceContext":"Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddleware","RequestId":"40000052-0008-fd00-b63f-84710c7967bb","RequestPath":"/en","ProcessId":23216,"ProcessName":"iisexpress","ThreadId":53,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"ERROR"}
{"@t":"2025-07-21T08:47:57.4833550Z","@mt":"Sending image. Request uri: '{Uri}'. Cached Key: '{Key}'","@tr":"289f98f421704a1364a35f5b11b9edbe","@sp":"f1033f4daa8abc89","Uri":"https://localhost:44309/media/f12l3cv0/abstract-blur-empty-green-gradient-studio-well-use-as-background-website-template-frame-business-report_1258-54629.jpg?rmode=max&width=500&v=1dbf71fa7de84b9","Key":"b00fe90c9f6c","EventId":{"Id":3},"SourceContext":"SixLabors.ImageSharp.Web.Middleware.ImageSharpMiddleware","RequestId":"4000004e-0004-fe00-b63f-84710c7967bb","RequestPath":"/media/f12l3cv0/abstract-blur-empty-green-gradient-studio-well-use-as-background-website-template-frame-business-report_1258-54629.jpg","ProcessId":23216,"ProcessName":"iisexpress","ThreadId":96,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-21T08:52:07.7238669Z","@mt":"Document {ContentName} (id={ContentId}) cultures: {Cultures} have been published.","@tr":"4fc2a3de32d6c4a0e424224dcf58a9c3","@sp":"5c5b3673aea5719c","ContentName":"Home","ContentId":1425,"Cultures":"ar-sa","SourceContext":"Umbraco.Cms.Core.Services.ContentService","ActionId":"39bbf62c-4a1d-4fdf-9279-8b9b82bf34f5","ActionName":"Umbraco.Cms.Web.BackOffice.Controllers.ContentController.PostSave (Umbraco.Web.BackOffice)","RequestId":"40000036-0008-f900-b63f-84710c7967bb","RequestPath":"/umbraco/backoffice/umbracoapi/content/PostSave","ProcessId":23216,"ProcessName":"iisexpress","ThreadId":115,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO ","HttpRequestId":"505be11c-e5ec-4ed4-92ea-5fb9bfaaf724","HttpRequestNumber":22,"HttpSessionId":"91f64d87-aa2c-b6f1-ab7d-f4744f46d4de"}
{"@t":"2025-07-21T08:52:22.0503362Z","@mt":"Document {ContentName} (id={ContentId}) cultures: {Cultures} have been published.","@tr":"e4dcc366849aeed6f6e4e68c4869bdeb","@sp":"89ea2edfd13f0b06","ContentName":"Home","ContentId":1425,"Cultures":"ar-sa","SourceContext":"Umbraco.Cms.Core.Services.ContentService","ActionId":"39bbf62c-4a1d-4fdf-9279-8b9b82bf34f5","ActionName":"Umbraco.Cms.Web.BackOffice.Controllers.ContentController.PostSave (Umbraco.Web.BackOffice)","RequestId":"4000001b-0002-f800-b63f-84710c7967bb","RequestPath":"/umbraco/backoffice/umbracoapi/content/PostSave","ProcessId":23216,"ProcessName":"iisexpress","ThreadId":95,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO ","HttpRequestId":"c973296c-fe38-4379-82e1-a14b86bf18fd","HttpRequestNumber":23,"HttpSessionId":"aeb3926c-ddef-f728-baf4-3eff47ba5664"}
{"@t":"2025-07-21T08:52:36.1891723Z","@mt":"Document {ContentName} (id={ContentId}) cultures: {Cultures} have been published.","@tr":"e41ded676a01eeea639cb54d6c06ca27","@sp":"b1fa334084119a56","ContentName":"Home","ContentId":1425,"Cultures":"ar-sa","SourceContext":"Umbraco.Cms.Core.Services.ContentService","ActionId":"39bbf62c-4a1d-4fdf-9279-8b9b82bf34f5","ActionName":"Umbraco.Cms.Web.BackOffice.Controllers.ContentController.PostSave (Umbraco.Web.BackOffice)","RequestId":"40000011-0009-f200-b63f-84710c7967bb","RequestPath":"/umbraco/backoffice/umbracoapi/content/PostSave","ProcessId":23216,"ProcessName":"iisexpress","ThreadId":95,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO ","HttpRequestId":"674ab50a-6351-47c3-ba99-237feadff54a","HttpRequestNumber":24,"HttpSessionId":"fb1d331f-78f3-1cf7-a034-f1dee103e11d"}
{"@t":"2025-07-21T08:54:23.5118961Z","@mt":"{StartMessage} [Timing {TimingId}]","@tr":"bf83a277bc032a98a5a747d74def2780","@sp":"ad5f0eab7a084711","StartMessage":"Loading content from database","TimingId":"64cce6f","SourceContext":"Umbraco.Cms.Core.Logging.ProfilingLogger","ActionId":"65d6a2ea-b24a-4db9-b095-4754416755e4","ActionName":"Umbraco.Cms.Web.BackOffice.Controllers.LanguageController.DeleteLanguage (Umbraco.Web.BackOffice)","RequestId":"40000014-0009-ee00-b63f-84710c7967bb","RequestPath":"/umbraco/backoffice/umbracoapi/language/DeleteLanguage","ProcessId":23216,"ProcessName":"iisexpress","ThreadId":115,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO ","HttpRequestId":"bff3df62-9677-47ef-850e-c499842e5ac6","HttpRequestNumber":25,"HttpSessionId":"ee94255e-7410-8a7e-99af-c3e2e6766ae7"}
{"@t":"2025-07-21T08:54:23.5318625Z","@mt":"{EndMessage} ({Duration}ms) [Timing {TimingId}]","@tr":"bf83a277bc032a98a5a747d74def2780","@sp":"ad5f0eab7a084711","EndMessage":"Completed.","Duration":20,"TimingId":"64cce6f","SourceContext":"Umbraco.Cms.Core.Logging.ProfilingLogger","ActionId":"65d6a2ea-b24a-4db9-b095-4754416755e4","ActionName":"Umbraco.Cms.Web.BackOffice.Controllers.LanguageController.DeleteLanguage (Umbraco.Web.BackOffice)","RequestId":"40000014-0009-ee00-b63f-84710c7967bb","RequestPath":"/umbraco/backoffice/umbracoapi/language/DeleteLanguage","ProcessId":23216,"ProcessName":"iisexpress","ThreadId":115,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO ","HttpRequestId":"bff3df62-9677-47ef-850e-c499842e5ac6","HttpRequestNumber":25,"HttpSessionId":"ee94255e-7410-8a7e-99af-c3e2e6766ae7"}
{"@t":"2025-07-21T08:54:23.9876773Z","@mt":"The Delivery API is not enabled, no indexing will performed for the Delivery API content index.","SourceContext":"Umbraco.Cms.Infrastructure.Examine.DeliveryApiContentIndexPopulator","ProcessId":23216,"ProcessName":"iisexpress","ThreadId":102,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-21T08:56:23.3138285Z","@mt":"Document {ContentName} (id={ContentId}) cultures: {Cultures} have been published.","@tr":"f109640605f59ebca461ce6a50e5effe","@sp":"ed91f2321446dffb","ContentName":"Home","ContentId":1425,"Cultures":"ar-sa","SourceContext":"Umbraco.Cms.Core.Services.ContentService","ActionId":"39bbf62c-4a1d-4fdf-9279-8b9b82bf34f5","ActionName":"Umbraco.Cms.Web.BackOffice.Controllers.ContentController.PostSave (Umbraco.Web.BackOffice)","RequestId":"********-0002-f800-b63f-84710c7967bb","RequestPath":"/umbraco/backoffice/umbracoapi/content/PostSave","ProcessId":23216,"ProcessName":"iisexpress","ThreadId":107,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO ","HttpRequestId":"********-4ef3-4d3e-b605-184ef31c99bc","HttpRequestNumber":26,"HttpSessionId":"8fdb1ea7-d606-e2b5-c8e8-cf0c7289c6da"}
{"@t":"2025-07-21T08:56:23.3882530Z","@mt":"The root node {RootNodeName} was published in culture {Culture}, but there's no domain configured for it, this will cause routing and caching issues, please register a domain for it","@l":"Warning","@tr":"f109640605f59ebca461ce6a50e5effe","@sp":"ed91f2321446dffb","RootNodeName":"Home","Culture":"ar-SA","SourceContext":"Umbraco.Cms.Web.BackOffice.Controllers.ContentController","ActionId":"39bbf62c-4a1d-4fdf-9279-8b9b82bf34f5","ActionName":"Umbraco.Cms.Web.BackOffice.Controllers.ContentController.PostSave (Umbraco.Web.BackOffice)","RequestId":"********-0002-f800-b63f-84710c7967bb","RequestPath":"/umbraco/backoffice/umbracoapi/content/PostSave","ProcessId":23216,"ProcessName":"iisexpress","ThreadId":107,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"WARN ","HttpRequestId":"********-4ef3-4d3e-b605-184ef31c99bc","HttpRequestNumber":26,"HttpSessionId":"8fdb1ea7-d606-e2b5-c8e8-cf0c7289c6da"}
{"@t":"2025-07-21T08:58:35.0150042Z","@mt":"Document {ContentName} (id={ContentId}) cultures: {Cultures} have been published.","@tr":"d70253cf3a39dd43f800945ad3690a14","@sp":"df66ea62146757c5","ContentName":"Home","ContentId":1425,"Cultures":"ar-sa","SourceContext":"Umbraco.Cms.Core.Services.ContentService","ActionId":"39bbf62c-4a1d-4fdf-9279-8b9b82bf34f5","ActionName":"Umbraco.Cms.Web.BackOffice.Controllers.ContentController.PostSave (Umbraco.Web.BackOffice)","RequestId":"********-0009-fe00-b63f-84710c7967bb","RequestPath":"/umbraco/backoffice/umbracoapi/content/PostSave","ProcessId":23216,"ProcessName":"iisexpress","ThreadId":75,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO ","HttpRequestId":"2087f17d-fbf8-4695-a030-2c9471bbb727","HttpRequestNumber":27,"HttpSessionId":"1eb6d0d9-1075-6922-9724-019c1d135e9a"}
{"@t":"2025-07-21T08:58:35.1165190Z","@mt":"The root node {RootNodeName} was published in culture {Culture}, but there's no domain configured for it, this will cause routing and caching issues, please register a domain for it","@l":"Warning","@tr":"d70253cf3a39dd43f800945ad3690a14","@sp":"df66ea62146757c5","RootNodeName":"Home","Culture":"ar-SA","SourceContext":"Umbraco.Cms.Web.BackOffice.Controllers.ContentController","ActionId":"39bbf62c-4a1d-4fdf-9279-8b9b82bf34f5","ActionName":"Umbraco.Cms.Web.BackOffice.Controllers.ContentController.PostSave (Umbraco.Web.BackOffice)","RequestId":"********-0009-fe00-b63f-84710c7967bb","RequestPath":"/umbraco/backoffice/umbracoapi/content/PostSave","ProcessId":23216,"ProcessName":"iisexpress","ThreadId":75,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"WARN ","HttpRequestId":"2087f17d-fbf8-4695-a030-2c9471bbb727","HttpRequestNumber":27,"HttpSessionId":"1eb6d0d9-1075-6922-9724-019c1d135e9a"}
{"@t":"2025-07-21T09:12:10.1446917Z","@mt":"Acquiring MainDom.","SourceContext":"Umbraco.Cms.Core.Runtime.MainDom","ProcessId":34324,"ProcessName":"iisexpress","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-21T09:12:10.1628281Z","@mt":"Acquired MainDom.","SourceContext":"Umbraco.Cms.Core.Runtime.MainDom","ProcessId":34324,"ProcessName":"iisexpress","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-21T09:12:10.9117298Z","@mt":"Starting '{MigrationName}'...","MigrationName":"StarterKitFormSubmits","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.MigrationPlanExecutor","ProcessId":34324,"ProcessName":"iisexpress","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-21T09:12:10.9118466Z","@mt":"At {OrigState}","OrigState":"Add-Starter-Kit-Form-Submits-Table","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.MigrationPlanExecutor","ProcessId":34324,"ProcessName":"iisexpress","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-21T09:12:10.9119682Z","@mt":"Done","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.MigrationPlanExecutor","ProcessId":34324,"ProcessName":"iisexpress","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-21T09:12:12.6326195Z","@mt":"Starting recurring background jobs hosted services","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":34324,"ProcessName":"iisexpress","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-21T09:12:12.6343774Z","@mt":"Starting background hosted service for {job}","job":"HealthCheckNotifierJob","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":34324,"ProcessName":"iisexpress","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-21T09:12:12.6419251Z","@mt":"Starting background hosted service for {job}","job":"KeepAliveJob","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":34324,"ProcessName":"iisexpress","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-21T09:12:12.6424543Z","@mt":"Starting background hosted service for {job}","job":"LogScrubberJob","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":34324,"ProcessName":"iisexpress","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-21T09:12:12.6428760Z","@mt":"Starting background hosted service for {job}","job":"ContentVersionCleanupJob","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":34324,"ProcessName":"iisexpress","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-21T09:12:12.6432830Z","@mt":"Starting background hosted service for {job}","job":"ScheduledPublishingJob","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":34324,"ProcessName":"iisexpress","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-21T09:12:12.6437459Z","@mt":"Starting background hosted service for {job}","job":"TempFileCleanupJob","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":34324,"ProcessName":"iisexpress","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-21T09:12:12.6441836Z","@mt":"Starting background hosted service for {job}","job":"InstructionProcessJob","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":34324,"ProcessName":"iisexpress","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-21T09:12:12.6447792Z","@mt":"Starting background hosted service for {job}","job":"TouchServerJob","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":34324,"ProcessName":"iisexpress","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-21T09:12:12.6452469Z","@mt":"Starting background hosted service for {job}","job":"WebhookFiring","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":34324,"ProcessName":"iisexpress","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-21T09:12:12.6457068Z","@mt":"Starting background hosted service for {job}","job":"WebhookLoggingCleanup","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":34324,"ProcessName":"iisexpress","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-21T09:12:12.6461360Z","@mt":"Starting background hosted service for {job}","job":"ReportSiteJob","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":34324,"ProcessName":"iisexpress","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-21T09:12:12.6462831Z","@mt":"Completed starting recurring background jobs hosted services","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":34324,"ProcessName":"iisexpress","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-21T09:12:12.9342696Z","@mt":"Application started. Press Ctrl+C to shut down.","SourceContext":"Microsoft.Hosting.Lifetime","ProcessId":34324,"ProcessName":"iisexpress","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-21T09:12:12.9343418Z","@mt":"Hosting environment: {EnvName}","EnvName":"Development","SourceContext":"Microsoft.Hosting.Lifetime","ProcessId":34324,"ProcessName":"iisexpress","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-21T09:12:12.9343668Z","@mt":"Content root path: {ContentRoot}","ContentRoot":"D:\\tests\\MDDPlusPortal\\MDDPlusPortal","SourceContext":"Microsoft.Hosting.Lifetime","ProcessId":34324,"ProcessName":"iisexpress","ThreadId":1,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-21T09:12:13.3086665Z","@mt":"Registered with MainDom, localContentDbExists? {LocalContentDbExists}, localMediaDbExists? {LocalMediaDbExists}","@tr":"2fb1f31107c6820ae87372a06b0340ec","@sp":"f473cc867710212b","LocalContentDbExists":true,"LocalMediaDbExists":true,"SourceContext":"Umbraco.Cms.Infrastructure.PublishedCache.PublishedSnapshotService","RequestId":"40000017-0006-f400-b63f-84710c7967bb","RequestPath":"/","ProcessId":34324,"ProcessName":"iisexpress","ThreadId":10,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO ","HttpRequestId":"77e47219-5460-4380-b0f6-acdfadab44ed","HttpRequestNumber":1,"HttpSessionId":"0"}
{"@t":"2025-07-21T09:12:13.3105781Z","@mt":"Creating the content store, localContentDbExists? {LocalContentDbExists}","@tr":"2fb1f31107c6820ae87372a06b0340ec","@sp":"f473cc867710212b","LocalContentDbExists":true,"SourceContext":"Umbraco.Cms.Infrastructure.PublishedCache.PublishedSnapshotService","RequestId":"40000017-0006-f400-b63f-84710c7967bb","RequestPath":"/","ProcessId":34324,"ProcessName":"iisexpress","ThreadId":10,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO ","HttpRequestId":"77e47219-5460-4380-b0f6-acdfadab44ed","HttpRequestNumber":1,"HttpSessionId":"0"}
{"@t":"2025-07-21T09:12:13.3124797Z","@mt":"Creating the media store, localMediaDbExists? {LocalMediaDbExists}","@tr":"2fb1f31107c6820ae87372a06b0340ec","@sp":"f473cc867710212b","LocalMediaDbExists":true,"SourceContext":"Umbraco.Cms.Infrastructure.PublishedCache.PublishedSnapshotService","RequestId":"40000017-0006-f400-b63f-84710c7967bb","RequestPath":"/","ProcessId":34324,"ProcessName":"iisexpress","ThreadId":10,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO ","HttpRequestId":"77e47219-5460-4380-b0f6-acdfadab44ed","HttpRequestNumber":1,"HttpSessionId":"0"}
{"@t":"2025-07-21T09:12:13.6460686Z","@mt":"{StartMessage} [Timing {TimingId}]","@tr":"2fb1f31107c6820ae87372a06b0340ec","@sp":"f473cc867710212b","StartMessage":"Loading content from local cache file","TimingId":"0e2a60a","SourceContext":"Umbraco.Cms.Core.Logging.ProfilingLogger","RequestId":"40000017-0006-f400-b63f-84710c7967bb","RequestPath":"/","ProcessId":34324,"ProcessName":"iisexpress","ThreadId":10,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO ","HttpRequestId":"77e47219-5460-4380-b0f6-acdfadab44ed","HttpRequestNumber":1,"HttpSessionId":"0"}
{"@t":"2025-07-21T09:12:13.6666972Z","@mt":"{EndMessage} ({Duration}ms) [Timing {TimingId}]","@tr":"2fb1f31107c6820ae87372a06b0340ec","@sp":"f473cc867710212b","EndMessage":"Completed.","Duration":20,"TimingId":"0e2a60a","SourceContext":"Umbraco.Cms.Core.Logging.ProfilingLogger","RequestId":"40000017-0006-f400-b63f-84710c7967bb","RequestPath":"/","ProcessId":34324,"ProcessName":"iisexpress","ThreadId":10,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO ","HttpRequestId":"77e47219-5460-4380-b0f6-acdfadab44ed","HttpRequestNumber":1,"HttpSessionId":"0"}
{"@t":"2025-07-21T09:12:13.6703514Z","@mt":"{StartMessage} [Timing {TimingId}]","@tr":"2fb1f31107c6820ae87372a06b0340ec","@sp":"f473cc867710212b","StartMessage":"Loading media from local cache file","TimingId":"cdd92e3","SourceContext":"Umbraco.Cms.Core.Logging.ProfilingLogger","RequestId":"40000017-0006-f400-b63f-84710c7967bb","RequestPath":"/","ProcessId":34324,"ProcessName":"iisexpress","ThreadId":10,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO ","HttpRequestId":"77e47219-5460-4380-b0f6-acdfadab44ed","HttpRequestNumber":1,"HttpSessionId":"0"}
{"@t":"2025-07-21T09:12:13.6725170Z","@mt":"{EndMessage} ({Duration}ms) [Timing {TimingId}]","@tr":"2fb1f31107c6820ae87372a06b0340ec","@sp":"f473cc867710212b","EndMessage":"Completed.","Duration":2,"TimingId":"cdd92e3","SourceContext":"Umbraco.Cms.Core.Logging.ProfilingLogger","RequestId":"40000017-0006-f400-b63f-84710c7967bb","RequestPath":"/","ProcessId":34324,"ProcessName":"iisexpress","ThreadId":10,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO ","HttpRequestId":"77e47219-5460-4380-b0f6-acdfadab44ed","HttpRequestNumber":1,"HttpSessionId":"0"}
{"@t":"2025-07-21T09:12:21.4017273Z","@mt":"Sending image. Request uri: '{Uri}'. Cached Key: '{Key}'","@tr":"3b69e41ddf554649c1440aa6dafdb99f","@sp":"2bff5060dce11ead","Uri":"https://localhost:44309/media/34ef3hkp/logo.png?rmode=pad&ranchor=left&width=149&height=59&bgcolor=transparent&v=1dbf738cdb82acc","Key":"36147f86784b","EventId":{"Id":3},"SourceContext":"SixLabors.ImageSharp.Web.Middleware.ImageSharpMiddleware","RequestId":"40000020-0013-fe00-b63f-84710c7967bb","RequestPath":"/media/34ef3hkp/logo.png","ProcessId":34324,"ProcessName":"iisexpress","ThreadId":45,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-21T09:13:13.9680372Z","@mt":"The Delivery API is not enabled, no indexing will performed for the Delivery API content index.","SourceContext":"Umbraco.Cms.Infrastructure.Examine.DeliveryApiContentIndexPopulator","ProcessId":34324,"ProcessName":"iisexpress","ThreadId":7,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-21T09:13:31.9242682Z","@mt":"Sending image. Request uri: '{Uri}'. Cached Key: '{Key}'","@tr":"2918ab1dfbcc70a2b367edae3136b211","@sp":"7023c501bfb96e8b","Uri":"https://localhost:44309/media/x51jzspk/logo-light.png?rmode=max&width=500&v=1dbf71fa86cbcd8","Key":"ac4fc60d8ff2","EventId":{"Id":3},"SourceContext":"SixLabors.ImageSharp.Web.Middleware.ImageSharpMiddleware","RequestId":"4000001a-0001-fd00-b63f-84710c7967bb","RequestPath":"/media/x51jzspk/logo-light.png","ProcessId":34324,"ProcessName":"iisexpress","ThreadId":63,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-21T09:13:32.0361728Z","@mt":"Sending image. Request uri: '{Uri}'. Cached Key: '{Key}'","@tr":"c9f62b6358bbc44f4ac756632ddf9ac9","@sp":"2cdb58e4fd991f7d","Uri":"https://localhost:44309/media/34ef3hkp/logo.png?rmode=max&width=500&v=1dbf71fa86ad507","Key":"6af4b4f2a8f8","EventId":{"Id":3},"SourceContext":"SixLabors.ImageSharp.Web.Middleware.ImageSharpMiddleware","RequestId":"40000018-0008-ef00-b63f-84710c7967bb","RequestPath":"/media/34ef3hkp/logo.png","ProcessId":34324,"ProcessName":"iisexpress","ThreadId":63,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-21T09:13:33.5311580Z","@mt":"Sending image. Request uri: '{Uri}'. Cached Key: '{Key}'","@tr":"57ed2a106da24fb8182e3dc6012f69e1","@sp":"cbe1e029b731fc62","Uri":"https://localhost:44309/media/a0yk2tgt/fawicon.png?rmode=max&width=500&v=1dbf71fa86eba0c","Key":"8e1e9857cc83","EventId":{"Id":3},"SourceContext":"SixLabors.ImageSharp.Web.Middleware.ImageSharpMiddleware","RequestId":"4000001c-0001-fd00-b63f-84710c7967bb","RequestPath":"/media/a0yk2tgt/fawicon.png","ProcessId":34324,"ProcessName":"iisexpress","ThreadId":57,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-21T09:13:34.2461403Z","@mt":"Sending image. Request uri: '{Uri}'. Cached Key: '{Key}'","@tr":"9312cb55e5e0934135c75820c4498f4e","@sp":"3494d5e447cbfc5d","Uri":"https://localhost:44309/media/w3djgyl1/6949a5af98af0a1c12e32935d58412db.jpg?rmode=max&width=500&v=1dbf71fa772d887","Key":"fc6da7f18d91","EventId":{"Id":3},"SourceContext":"SixLabors.ImageSharp.Web.Middleware.ImageSharpMiddleware","RequestId":"4000001a-0008-ef00-b63f-84710c7967bb","RequestPath":"/media/w3djgyl1/6949a5af98af0a1c12e32935d58412db.jpg","ProcessId":34324,"ProcessName":"iisexpress","ThreadId":7,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-21T09:13:34.2745655Z","@mt":"Sending image. Request uri: '{Uri}'. Cached Key: '{Key}'","@tr":"82bac4f80afe5de0df01530c29f1768b","@sp":"fa938972a73acaf8","Uri":"https://localhost:44309/media/ehne11mx/623c1e9eae7e8c3ebda393d20276cd34.png?rmode=max&width=500&v=1dbf71fa774d92f","Key":"93212868ff54","EventId":{"Id":3},"SourceContext":"SixLabors.ImageSharp.Web.Middleware.ImageSharpMiddleware","RequestId":"40000070-0008-fd00-b63f-84710c7967bb","RequestPath":"/media/ehne11mx/623c1e9eae7e8c3ebda393d20276cd34.png","ProcessId":34324,"ProcessName":"iisexpress","ThreadId":64,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-21T09:13:34.2779458Z","@mt":"Sending image. Request uri: '{Uri}'. Cached Key: '{Key}'","@tr":"0d0c47b9594faa8b35e6f6f6cd9f4813","@sp":"935e982de524027b","Uri":"https://localhost:44309/media/qwulpoda/1615993866.png?rmode=max&width=500&v=1dbf71fa770df36","Key":"cd2e4c1200e3","EventId":{"Id":3},"SourceContext":"SixLabors.ImageSharp.Web.Middleware.ImageSharpMiddleware","RequestId":"40000032-000a-fa00-b63f-84710c7967bb","RequestPath":"/media/qwulpoda/1615993866.png","ProcessId":34324,"ProcessName":"iisexpress","ThreadId":62,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO "}
{"@t":"2025-07-21T09:13:34.5737270Z","@mt":"Document {ContentName} (id={ContentId}) cultures: {Cultures} have been published.","@tr":"b061ec4bbfbcf1e5e629dc032ac73e95","@sp":"3284d80bc21e7fa9","ContentName":"Home","ContentId":1425,"Cultures":"ar-sa","SourceContext":"Umbraco.Cms.Core.Services.ContentService","ActionId":"3e63588b-880e-4882-aedf-74b96338855b","ActionName":"Umbraco.Cms.Web.BackOffice.Controllers.ContentController.PostSave (Umbraco.Web.BackOffice)","RequestId":"********-000a-f300-b63f-84710c7967bb","RequestPath":"/umbraco/backoffice/umbracoapi/content/PostSave","ProcessId":34324,"ProcessName":"iisexpress","ThreadId":65,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO ","HttpRequestId":"e69ec063-24ad-45b4-83ef-1a21b60fe550","HttpRequestNumber":2,"HttpSessionId":"b5b657a5-c15d-0750-9f63-27dd79bd7b32"}
{"@t":"2025-07-21T09:13:35.2292845Z","@mt":"Adding examine event handlers for {RegisteredIndexers} index providers.","@tr":"b061ec4bbfbcf1e5e629dc032ac73e95","@sp":"3284d80bc21e7fa9","RegisteredIndexers":3,"SourceContext":"Umbraco.Cms.Infrastructure.Examine.ExamineUmbracoIndexingHandler","ActionId":"3e63588b-880e-4882-aedf-74b96338855b","ActionName":"Umbraco.Cms.Web.BackOffice.Controllers.ContentController.PostSave (Umbraco.Web.BackOffice)","RequestId":"********-000a-f300-b63f-84710c7967bb","RequestPath":"/umbraco/backoffice/umbracoapi/content/PostSave","ProcessId":34324,"ProcessName":"iisexpress","ThreadId":65,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO ","HttpRequestId":"e69ec063-24ad-45b4-83ef-1a21b60fe550","HttpRequestNumber":2,"HttpSessionId":"b5b657a5-c15d-0750-9f63-27dd79bd7b32"}
{"@t":"2025-07-21T09:13:35.3736900Z","@mt":"The root node {RootNodeName} was published in culture {Culture}, but there's no domain configured for it, this will cause routing and caching issues, please register a domain for it","@l":"Warning","@tr":"b061ec4bbfbcf1e5e629dc032ac73e95","@sp":"3284d80bc21e7fa9","RootNodeName":"Home","Culture":"ar-SA","SourceContext":"Umbraco.Cms.Web.BackOffice.Controllers.ContentController","ActionId":"3e63588b-880e-4882-aedf-74b96338855b","ActionName":"Umbraco.Cms.Web.BackOffice.Controllers.ContentController.PostSave (Umbraco.Web.BackOffice)","RequestId":"********-000a-f300-b63f-84710c7967bb","RequestPath":"/umbraco/backoffice/umbracoapi/content/PostSave","ProcessId":34324,"ProcessName":"iisexpress","ThreadId":65,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"WARN ","HttpRequestId":"e69ec063-24ad-45b4-83ef-1a21b60fe550","HttpRequestNumber":2,"HttpSessionId":"b5b657a5-c15d-0750-9f63-27dd79bd7b32"}
{"@t":"2025-07-21T09:33:36.3491121Z","@mt":"User logged will be logged out due to timeout: {Username}, IP Address: {IPAddress}","@tr":"3baa31645f5886a654680eff92f72c37","@sp":"68654b1b0c9ea38b","Username":"<EMAIL>","IPAddress":"::1","SourceContext":"Umbraco.Cms.Web.BackOffice.Controllers.AuthenticationController","ActionId":"c1c23419-9bde-4def-8cf3-44a4e90f6e19","ActionName":"Umbraco.Cms.Web.BackOffice.Controllers.AuthenticationController.GetRemainingTimeoutSeconds (Umbraco.Web.BackOffice)","RequestId":"********-000d-fe00-b63f-84710c7967bb","RequestPath":"/umbraco/backoffice/umbracoapi/authentication/GetRemainingTimeoutSeconds","ProcessId":34324,"ProcessName":"iisexpress","ThreadId":45,"ApplicationId":"c376686b128b741232929a92438a9a794ff6e845","MachineName":"MDD-0001","Log4NetLevel":"INFO ","HttpRequestId":"0534d3fb-d6d6-4c84-9f3a-c54f635c8187","HttpRequestNumber":3,"HttpSessionId":"f64a0139-a307-8350-fe2d-1f6a264291aa"}
