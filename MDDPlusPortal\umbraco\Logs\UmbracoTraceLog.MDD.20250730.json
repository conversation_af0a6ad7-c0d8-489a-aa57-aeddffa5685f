{"@t":"2025-07-30T11:31:51.8651359Z","@mt":"Acquiring MainDom.","SourceContext":"Umbraco.Cms.Core.Runtime.MainDom","ProcessId":19844,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"713aaee0eaa6e0965789a291adf650aecafbc4bd","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-30T11:31:51.8712274Z","@mt":"Acquired MainDom.","SourceContext":"Umbraco.Cms.Core.Runtime.MainDom","ProcessId":19844,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"713aaee0eaa6e0965789a291adf650aecafbc4bd","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-30T11:31:52.1566799Z","@mt":"No last synced Id found, this generally means this is a new server/install. A cold boot will be triggered.","@l":"Warning","SourceContext":"Umbraco.Cms.Infrastructure.Sync.SyncBootStateAccessor","ProcessId":19844,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"713aaee0eaa6e0965789a291adf650aecafbc4bd","MachineName":"MDD","Log4NetLevel":"WARN "}
{"@t":"2025-07-30T11:31:52.1599029Z","@mt":"Starting '{MigrationName}'...","MigrationName":"StarterKitFormSubmits","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.MigrationPlanExecutor","ProcessId":19844,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"713aaee0eaa6e0965789a291adf650aecafbc4bd","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-30T11:31:52.1599490Z","@mt":"At {OrigState}","OrigState":"Add-Starter-Kit-Form-Submits-Table","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.MigrationPlanExecutor","ProcessId":19844,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"713aaee0eaa6e0965789a291adf650aecafbc4bd","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-30T11:31:52.1599841Z","@mt":"Done","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.MigrationPlanExecutor","ProcessId":19844,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"713aaee0eaa6e0965789a291adf650aecafbc4bd","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-30T11:31:52.8554159Z","@mt":"Starting recurring background jobs hosted services","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":19844,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"713aaee0eaa6e0965789a291adf650aecafbc4bd","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-30T11:31:52.8559636Z","@mt":"Starting background hosted service for {job}","job":"HealthCheckNotifierJob","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":19844,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"713aaee0eaa6e0965789a291adf650aecafbc4bd","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-30T11:31:52.8591942Z","@mt":"Starting background hosted service for {job}","job":"KeepAliveJob","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":19844,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"713aaee0eaa6e0965789a291adf650aecafbc4bd","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-30T11:31:52.8593914Z","@mt":"Starting background hosted service for {job}","job":"LogScrubberJob","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":19844,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"713aaee0eaa6e0965789a291adf650aecafbc4bd","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-30T11:31:52.8595216Z","@mt":"Starting background hosted service for {job}","job":"ContentVersionCleanupJob","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":19844,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"713aaee0eaa6e0965789a291adf650aecafbc4bd","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-30T11:31:52.8596277Z","@mt":"Starting background hosted service for {job}","job":"ScheduledPublishingJob","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":19844,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"713aaee0eaa6e0965789a291adf650aecafbc4bd","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-30T11:31:52.8597427Z","@mt":"Starting background hosted service for {job}","job":"TempFileCleanupJob","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":19844,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"713aaee0eaa6e0965789a291adf650aecafbc4bd","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-30T11:31:52.8598527Z","@mt":"Starting background hosted service for {job}","job":"InstructionProcessJob","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":19844,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"713aaee0eaa6e0965789a291adf650aecafbc4bd","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-30T11:31:52.8600463Z","@mt":"Starting background hosted service for {job}","job":"TouchServerJob","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":19844,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"713aaee0eaa6e0965789a291adf650aecafbc4bd","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-30T11:31:52.8601919Z","@mt":"Starting background hosted service for {job}","job":"WebhookFiring","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":19844,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"713aaee0eaa6e0965789a291adf650aecafbc4bd","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-30T11:31:52.8603041Z","@mt":"Starting background hosted service for {job}","job":"WebhookLoggingCleanup","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":19844,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"713aaee0eaa6e0965789a291adf650aecafbc4bd","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-30T11:31:52.8604060Z","@mt":"Starting background hosted service for {job}","job":"ReportSiteJob","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":19844,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"713aaee0eaa6e0965789a291adf650aecafbc4bd","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-30T11:31:52.8604548Z","@mt":"Completed starting recurring background jobs hosted services","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":19844,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"713aaee0eaa6e0965789a291adf650aecafbc4bd","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-30T11:31:52.9618153Z","@mt":"Now listening on: {address}","address":"https://localhost:44309","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","ProcessId":19844,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"713aaee0eaa6e0965789a291adf650aecafbc4bd","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-30T11:31:52.9618703Z","@mt":"Now listening on: {address}","address":"http://localhost:37806","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","ProcessId":19844,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"713aaee0eaa6e0965789a291adf650aecafbc4bd","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-30T11:31:52.9619460Z","@mt":"Application started. Press Ctrl+C to shut down.","SourceContext":"Microsoft.Hosting.Lifetime","ProcessId":19844,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"713aaee0eaa6e0965789a291adf650aecafbc4bd","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-30T11:31:52.9619636Z","@mt":"Hosting environment: {EnvName}","EnvName":"Development","SourceContext":"Microsoft.Hosting.Lifetime","ProcessId":19844,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"713aaee0eaa6e0965789a291adf650aecafbc4bd","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-30T11:31:52.9619728Z","@mt":"Content root path: {ContentRoot}","ContentRoot":"D:\\MDDPlusPortal\\MDDPlusPortal","SourceContext":"Microsoft.Hosting.Lifetime","ProcessId":19844,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"713aaee0eaa6e0965789a291adf650aecafbc4bd","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-30T11:32:07.8685997Z","@mt":"No umbracoApplicationUrl for service (yet), skip.","@l":"Warning","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.Jobs.ServerRegistration.TouchServerJob","ProcessId":19844,"ProcessName":"MDDPlusPortal","ThreadId":13,"ApplicationId":"713aaee0eaa6e0965789a291adf650aecafbc4bd","MachineName":"MDD","Log4NetLevel":"WARN "}
{"@t":"2025-07-30T11:32:09.7875907Z","@mt":"Registered with MainDom, localContentDbExists? {LocalContentDbExists}, localMediaDbExists? {LocalMediaDbExists}","@tr":"92f73bcb81c5b91690dc04c8d6ce6453","@sp":"ccdc7cd4b4eec3f3","LocalContentDbExists":true,"LocalMediaDbExists":true,"SourceContext":"Umbraco.Cms.Infrastructure.PublishedCache.PublishedSnapshotService","RequestId":"0HNEFBIS8HTSL:00000001","RequestPath":"/","ConnectionId":"0HNEFBIS8HTSL","ProcessId":19844,"ProcessName":"MDDPlusPortal","ThreadId":22,"ApplicationId":"713aaee0eaa6e0965789a291adf650aecafbc4bd","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"4d3ab3b1-17f7-4cc2-b39a-036e5959e3fb","HttpRequestNumber":1,"HttpSessionId":"0"}
{"@t":"2025-07-30T11:32:09.7891331Z","@mt":"Creating the content store, localContentDbExists? {LocalContentDbExists}","@tr":"92f73bcb81c5b91690dc04c8d6ce6453","@sp":"ccdc7cd4b4eec3f3","LocalContentDbExists":true,"SourceContext":"Umbraco.Cms.Infrastructure.PublishedCache.PublishedSnapshotService","RequestId":"0HNEFBIS8HTSL:00000001","RequestPath":"/","ConnectionId":"0HNEFBIS8HTSL","ProcessId":19844,"ProcessName":"MDDPlusPortal","ThreadId":22,"ApplicationId":"713aaee0eaa6e0965789a291adf650aecafbc4bd","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"4d3ab3b1-17f7-4cc2-b39a-036e5959e3fb","HttpRequestNumber":1,"HttpSessionId":"0"}
{"@t":"2025-07-30T11:32:09.7900499Z","@mt":"Creating the media store, localMediaDbExists? {LocalMediaDbExists}","@tr":"92f73bcb81c5b91690dc04c8d6ce6453","@sp":"ccdc7cd4b4eec3f3","LocalMediaDbExists":true,"SourceContext":"Umbraco.Cms.Infrastructure.PublishedCache.PublishedSnapshotService","RequestId":"0HNEFBIS8HTSL:00000001","RequestPath":"/","ConnectionId":"0HNEFBIS8HTSL","ProcessId":19844,"ProcessName":"MDDPlusPortal","ThreadId":22,"ApplicationId":"713aaee0eaa6e0965789a291adf650aecafbc4bd","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"4d3ab3b1-17f7-4cc2-b39a-036e5959e3fb","HttpRequestNumber":1,"HttpSessionId":"0"}
{"@t":"2025-07-30T11:32:09.9508622Z","@mt":"{StartMessage} [Timing {TimingId}]","@tr":"92f73bcb81c5b91690dc04c8d6ce6453","@sp":"ccdc7cd4b4eec3f3","StartMessage":"Loading content from database","TimingId":"3327359","SourceContext":"Umbraco.Cms.Core.Logging.ProfilingLogger","RequestId":"0HNEFBIS8HTSL:00000001","RequestPath":"/","ConnectionId":"0HNEFBIS8HTSL","ProcessId":19844,"ProcessName":"MDDPlusPortal","ThreadId":22,"ApplicationId":"713aaee0eaa6e0965789a291adf650aecafbc4bd","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"4d3ab3b1-17f7-4cc2-b39a-036e5959e3fb","HttpRequestNumber":1,"HttpSessionId":"0"}
{"@t":"2025-07-30T11:32:10.0531362Z","@mt":"{EndMessage} ({Duration}ms) [Timing {TimingId}]","@tr":"92f73bcb81c5b91690dc04c8d6ce6453","@sp":"ccdc7cd4b4eec3f3","EndMessage":"Completed.","Duration":102,"TimingId":"3327359","SourceContext":"Umbraco.Cms.Core.Logging.ProfilingLogger","RequestId":"0HNEFBIS8HTSL:00000001","RequestPath":"/","ConnectionId":"0HNEFBIS8HTSL","ProcessId":19844,"ProcessName":"MDDPlusPortal","ThreadId":22,"ApplicationId":"713aaee0eaa6e0965789a291adf650aecafbc4bd","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"4d3ab3b1-17f7-4cc2-b39a-036e5959e3fb","HttpRequestNumber":1,"HttpSessionId":"0"}
{"@t":"2025-07-30T11:32:10.0712615Z","@mt":"{StartMessage} [Timing {TimingId}]","@tr":"92f73bcb81c5b91690dc04c8d6ce6453","@sp":"ccdc7cd4b4eec3f3","StartMessage":"Loading media from database","TimingId":"6495f69","SourceContext":"Umbraco.Cms.Core.Logging.ProfilingLogger","RequestId":"0HNEFBIS8HTSL:00000001","RequestPath":"/","ConnectionId":"0HNEFBIS8HTSL","ProcessId":19844,"ProcessName":"MDDPlusPortal","ThreadId":22,"ApplicationId":"713aaee0eaa6e0965789a291adf650aecafbc4bd","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"4d3ab3b1-17f7-4cc2-b39a-036e5959e3fb","HttpRequestNumber":1,"HttpSessionId":"0"}
{"@t":"2025-07-30T11:32:10.0844085Z","@mt":"{EndMessage} ({Duration}ms) [Timing {TimingId}]","@tr":"92f73bcb81c5b91690dc04c8d6ce6453","@sp":"ccdc7cd4b4eec3f3","EndMessage":"Completed.","Duration":13,"TimingId":"6495f69","SourceContext":"Umbraco.Cms.Core.Logging.ProfilingLogger","RequestId":"0HNEFBIS8HTSL:00000001","RequestPath":"/","ConnectionId":"0HNEFBIS8HTSL","ProcessId":19844,"ProcessName":"MDDPlusPortal","ThreadId":22,"ApplicationId":"713aaee0eaa6e0965789a291adf650aecafbc4bd","MachineName":"MDD","Log4NetLevel":"INFO ","HttpRequestId":"4d3ab3b1-17f7-4cc2-b39a-036e5959e3fb","HttpRequestNumber":1,"HttpSessionId":"0"}
{"@t":"2025-07-30T11:32:11.5143479Z","@mt":"Failed to build models.","@l":"Error","@x":"System.IO.IOException: The process cannot access the file 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\0970b890-eb4d-4ce1-9431-df3879744cac.tmp' because it is being used by another process.\r\n   at System.IO.FileSystem.DeleteFile(String fullPath)\r\n   at Umbraco.Cms.Web.Common.ModelsBuilder.InMemoryAuto.InMemoryModelFactory.TryDeleteUnusedAssemblies(String dllPathFile)\r\n   at Umbraco.Cms.Web.Common.ModelsBuilder.InMemoryAuto.InMemoryModelFactory.GetModelsAssembly(Boolean forceRebuild)\r\n   at Umbraco.Cms.Web.Common.ModelsBuilder.InMemoryAuto.InMemoryModelFactory.EnsureModels()","@tr":"92f73bcb81c5b91690dc04c8d6ce6453","@sp":"ccdc7cd4b4eec3f3","SourceContext":"Umbraco.Cms.Web.Common.ModelsBuilder.InMemoryAuto.InMemoryModelFactory","RequestId":"0HNEFBIS8HTSL:00000001","RequestPath":"/","ConnectionId":"0HNEFBIS8HTSL","ProcessId":19844,"ProcessName":"MDDPlusPortal","ThreadId":22,"ApplicationId":"713aaee0eaa6e0965789a291adf650aecafbc4bd","MachineName":"MDD","Log4NetLevel":"ERROR","HttpRequestId":"4d3ab3b1-17f7-4cc2-b39a-036e5959e3fb","HttpRequestNumber":1,"HttpSessionId":"0"}
{"@t":"2025-07-30T11:32:11.5144412Z","@mt":"Running without models.","@l":"Warning","@tr":"92f73bcb81c5b91690dc04c8d6ce6453","@sp":"ccdc7cd4b4eec3f3","SourceContext":"Umbraco.Cms.Web.Common.ModelsBuilder.InMemoryAuto.InMemoryModelFactory","RequestId":"0HNEFBIS8HTSL:00000001","RequestPath":"/","ConnectionId":"0HNEFBIS8HTSL","ProcessId":19844,"ProcessName":"MDDPlusPortal","ThreadId":22,"ApplicationId":"713aaee0eaa6e0965789a291adf650aecafbc4bd","MachineName":"MDD","Log4NetLevel":"WARN ","HttpRequestId":"4d3ab3b1-17f7-4cc2-b39a-036e5959e3fb","HttpRequestNumber":1,"HttpSessionId":"0"}
{"@t":"2025-07-30T11:32:12.6725974Z","@mt":"An unhandled exception has occurred while executing the request.","@l":"Error","@x":"Umbraco.Cms.Web.Common.ModelBinders.ModelBindingException: Cannot bind source content type Umbraco.Cms.Infrastructure.PublishedCache.PublishedContent to model type Umbraco.Cms.Web.Common.PublishedModels.Home. The view model is a ModelsBuilder type, but the source is not. The application is in an unstable state and should be restarted.\r\n   at Umbraco.Cms.Web.Common.ModelBinders.ContentModelBinder.ThrowModelBindingException(Boolean sourceContent, Boolean modelContent, Type sourceType, Type modelType)\r\n   at Umbraco.Cms.Web.Common.ModelBinders.ContentModelBinder.BindModel(ModelBindingContext bindingContext, Object source, Type modelType)\r\n   at Umbraco.Cms.Web.Common.Views.UmbracoViewPage`1.BindViewData(ContentModelBinder contentModelBinder, ViewDataDictionary viewData)\r\n   at Umbraco.Cms.Web.Common.Views.UmbracoViewPage`1.set_ViewContext(ViewContext value)\r\n   at Microsoft.AspNetCore.Mvc.Razor.RazorView.RenderPageCoreAsync(IRazorPage page, ViewContext context)\r\n   at Microsoft.AspNetCore.Mvc.Razor.RazorView.RenderPageAsync(IRazorPage page, ViewContext context, Boolean invokeViewStarts)\r\n   at Microsoft.AspNetCore.Mvc.Razor.RazorView.RenderAsync(ViewContext context)\r\n   at Microsoft.AspNetCore.Mvc.ViewFeatures.ViewExecutor.ExecuteAsync(ViewContext viewContext, String contentType, Nullable`1 statusCode)\r\n   at Microsoft.AspNetCore.Mvc.ViewFeatures.ViewExecutor.ExecuteAsync(ViewContext viewContext, String contentType, Nullable`1 statusCode)\r\n   at Microsoft.AspNetCore.Mvc.ViewFeatures.ViewExecutor.ExecuteAsync(ActionContext actionContext, IView view, ViewDataDictionary viewData, ITempDataDictionary tempData, String contentType, Nullable`1 statusCode)\r\n   at Microsoft.AspNetCore.Mvc.ViewFeatures.ViewResultExecutor.ExecuteAsync(ActionContext context, ViewResult result)\r\n   at Microsoft.AspNetCore.Mvc.ViewResult.ExecuteResultAsync(ActionContext context)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextResultFilterAsync>g__Awaited|30_0[TFilter,TFilterAsync](ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Rethrow(ResultExecutedContextSealed context)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.ResultNext[TFilter,TFilterAsync](State& next, Scope& scope, Object& state, Boolean& isCompleted)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.InvokeResultFilters()\r\n--- End of stack trace from previous location ---\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextResourceFilter>g__Awaited|25_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Rethrow(ResourceExecutedContextSealed context)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.InvokeFilterPipelineAsync()\r\n--- End of stack trace from previous location ---\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)\r\n   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)\r\n   at Umbraco.Cms.Web.Common.Middleware.BasicAuthenticationMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.InterfaceMiddlewareBinder.<>c__DisplayClass2_0.<<CreateMiddleware>b__0>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Umbraco.Cms.Web.BackOffice.Middleware.BackOfficeExternalLoginProviderErrorMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.InterfaceMiddlewareBinder.<>c__DisplayClass2_0.<<CreateMiddleware>b__0>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Swashbuckle.AspNetCore.SwaggerUI.SwaggerUIMiddleware.Invoke(HttpContext httpContext)\r\n   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)\r\n   at Microsoft.AspNetCore.Session.SessionMiddleware.Invoke(HttpContext context)\r\n   at Microsoft.AspNetCore.Session.SessionMiddleware.Invoke(HttpContext context)\r\n   at Microsoft.AspNetCore.Localization.RequestLocalizationMiddleware.Invoke(HttpContext context)\r\n   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)\r\n   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)\r\n   at StackExchange.Profiling.MiniProfilerMiddleware.Invoke(HttpContext context) in C:\\projects\\dotnet\\src\\MiniProfiler.AspNetCore\\MiniProfilerMiddleware.cs:line 112\r\n   at Umbraco.Cms.Web.Common.Middleware.UmbracoRequestMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Umbraco.Cms.Web.Common.Middleware.UmbracoRequestMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.InterfaceMiddlewareBinder.<>c__DisplayClass2_0.<<CreateMiddleware>b__0>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Umbraco.Cms.Web.Common.Middleware.PreviewAuthenticationMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.InterfaceMiddlewareBinder.<>c__DisplayClass2_0.<<CreateMiddleware>b__0>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at Umbraco.Cms.Web.Common.Middleware.UmbracoRequestLoggingMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)\r\n   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.InterfaceMiddlewareBinder.<>c__DisplayClass2_0.<<CreateMiddleware>b__0>d.MoveNext()\r\n--- End of stack trace from previous location ---\r\n   at SixLabors.ImageSharp.Web.Middleware.ImageSharpMiddleware.Invoke(HttpContext httpContext, Boolean retry)\r\n   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)","@tr":"92f73bcb81c5b91690dc04c8d6ce6453","@sp":"ccdc7cd4b4eec3f3","EventId":{"Id":1,"Name":"UnhandledException"},"SourceContext":"Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddleware","RequestId":"0HNEFBIS8HTSL:00000001","RequestPath":"/","ConnectionId":"0HNEFBIS8HTSL","ProcessId":19844,"ProcessName":"MDDPlusPortal","ThreadId":22,"ApplicationId":"713aaee0eaa6e0965789a291adf650aecafbc4bd","MachineName":"MDD","Log4NetLevel":"ERROR"}
{"@t":"2025-07-30T11:32:13.2707361Z","@mt":"Application is shutting down...","SourceContext":"Microsoft.Hosting.Lifetime","ProcessId":19844,"ProcessName":"MDDPlusPortal","ThreadId":18,"ApplicationId":"713aaee0eaa6e0965789a291adf650aecafbc4bd","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-30T11:32:13.2718437Z","@mt":"Stopping ({SignalSource})","SignalSource":"environment","SourceContext":"Umbraco.Cms.Core.Runtime.MainDom","ProcessId":19844,"ProcessName":"MDDPlusPortal","ThreadId":18,"ApplicationId":"713aaee0eaa6e0965789a291adf650aecafbc4bd","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-30T11:32:13.2789015Z","@mt":"Released from MainDom","SourceContext":"Umbraco.Cms.Infrastructure.PublishedCache.PublishedSnapshotService","ProcessId":19844,"ProcessName":"MDDPlusPortal","ThreadId":18,"ApplicationId":"713aaee0eaa6e0965789a291adf650aecafbc4bd","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-30T11:32:13.2791733Z","@mt":"FileSystemMainDomLock Disposing...","SourceContext":"Umbraco.Cms.Infrastructure.Runtime.FileSystemMainDomLock","ProcessId":19844,"ProcessName":"MDDPlusPortal","ThreadId":18,"ApplicationId":"713aaee0eaa6e0965789a291adf650aecafbc4bd","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-30T11:32:13.2792903Z","@mt":"Released ({SignalSource})","SignalSource":"environment","SourceContext":"Umbraco.Cms.Core.Runtime.MainDom","ProcessId":19844,"ProcessName":"MDDPlusPortal","ThreadId":18,"ApplicationId":"713aaee0eaa6e0965789a291adf650aecafbc4bd","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-30T11:32:13.2875702Z","@mt":"Stopping recurring background jobs hosted services","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":19844,"ProcessName":"MDDPlusPortal","ThreadId":31,"ApplicationId":"713aaee0eaa6e0965789a291adf650aecafbc4bd","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-30T11:32:13.2876859Z","@mt":"Stopping background hosted service for {job}","job":"HealthCheckNotifierJob","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":19844,"ProcessName":"MDDPlusPortal","ThreadId":31,"ApplicationId":"713aaee0eaa6e0965789a291adf650aecafbc4bd","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-30T11:32:13.2912935Z","@mt":"Stopping background hosted service for {job}","job":"KeepAliveJob","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":19844,"ProcessName":"MDDPlusPortal","ThreadId":31,"ApplicationId":"713aaee0eaa6e0965789a291adf650aecafbc4bd","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-30T11:32:13.2913816Z","@mt":"Stopping background hosted service for {job}","job":"LogScrubberJob","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":19844,"ProcessName":"MDDPlusPortal","ThreadId":31,"ApplicationId":"713aaee0eaa6e0965789a291adf650aecafbc4bd","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-30T11:32:13.2914065Z","@mt":"Stopping background hosted service for {job}","job":"ContentVersionCleanupJob","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":19844,"ProcessName":"MDDPlusPortal","ThreadId":31,"ApplicationId":"713aaee0eaa6e0965789a291adf650aecafbc4bd","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-30T11:32:13.2914299Z","@mt":"Stopping background hosted service for {job}","job":"ScheduledPublishingJob","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":19844,"ProcessName":"MDDPlusPortal","ThreadId":31,"ApplicationId":"713aaee0eaa6e0965789a291adf650aecafbc4bd","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-30T11:32:13.2914482Z","@mt":"Stopping background hosted service for {job}","job":"TempFileCleanupJob","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":19844,"ProcessName":"MDDPlusPortal","ThreadId":31,"ApplicationId":"713aaee0eaa6e0965789a291adf650aecafbc4bd","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-30T11:32:13.2914672Z","@mt":"Stopping background hosted service for {job}","job":"InstructionProcessJob","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":19844,"ProcessName":"MDDPlusPortal","ThreadId":31,"ApplicationId":"713aaee0eaa6e0965789a291adf650aecafbc4bd","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-30T11:32:13.2914810Z","@mt":"Stopping background hosted service for {job}","job":"TouchServerJob","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":19844,"ProcessName":"MDDPlusPortal","ThreadId":31,"ApplicationId":"713aaee0eaa6e0965789a291adf650aecafbc4bd","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-30T11:32:13.2915081Z","@mt":"Stopping background hosted service for {job}","job":"WebhookFiring","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":19844,"ProcessName":"MDDPlusPortal","ThreadId":31,"ApplicationId":"713aaee0eaa6e0965789a291adf650aecafbc4bd","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-30T11:32:13.2915273Z","@mt":"Stopping background hosted service for {job}","job":"WebhookLoggingCleanup","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":19844,"ProcessName":"MDDPlusPortal","ThreadId":31,"ApplicationId":"713aaee0eaa6e0965789a291adf650aecafbc4bd","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-30T11:32:13.2915534Z","@mt":"Stopping background hosted service for {job}","job":"ReportSiteJob","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":19844,"ProcessName":"MDDPlusPortal","ThreadId":31,"ApplicationId":"713aaee0eaa6e0965789a291adf650aecafbc4bd","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-30T11:32:13.2915767Z","@mt":"Completed stopping recurring background jobs hosted services","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":19844,"ProcessName":"MDDPlusPortal","ThreadId":31,"ApplicationId":"713aaee0eaa6e0965789a291adf650aecafbc4bd","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-30T11:32:13.2918882Z","@mt":"Queued Hosted Service is stopping.","SourceContext":"Umbraco.Cms.Infrastructure.HostedServices.QueuedHostedService","ProcessId":19844,"ProcessName":"MDDPlusPortal","ThreadId":31,"ApplicationId":"713aaee0eaa6e0965789a291adf650aecafbc4bd","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-30T11:33:46.2620197Z","@mt":"Acquiring MainDom.","SourceContext":"Umbraco.Cms.Core.Runtime.MainDom","ProcessId":8940,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"713aaee0eaa6e0965789a291adf650aecafbc4bd","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-30T11:33:46.2669835Z","@mt":"Acquired MainDom.","SourceContext":"Umbraco.Cms.Core.Runtime.MainDom","ProcessId":8940,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"713aaee0eaa6e0965789a291adf650aecafbc4bd","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-30T11:33:46.4847674Z","@mt":"Starting '{MigrationName}'...","MigrationName":"StarterKitFormSubmits","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.MigrationPlanExecutor","ProcessId":8940,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"713aaee0eaa6e0965789a291adf650aecafbc4bd","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-30T11:33:46.4848450Z","@mt":"At {OrigState}","OrigState":"Add-Starter-Kit-Form-Submits-Table","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.MigrationPlanExecutor","ProcessId":8940,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"713aaee0eaa6e0965789a291adf650aecafbc4bd","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-30T11:33:46.4848841Z","@mt":"Done","SourceContext":"Umbraco.Cms.Infrastructure.Migrations.MigrationPlanExecutor","ProcessId":8940,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"713aaee0eaa6e0965789a291adf650aecafbc4bd","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-30T11:33:47.1314158Z","@mt":"Starting recurring background jobs hosted services","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":8940,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"713aaee0eaa6e0965789a291adf650aecafbc4bd","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-30T11:33:47.1319767Z","@mt":"Starting background hosted service for {job}","job":"HealthCheckNotifierJob","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":8940,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"713aaee0eaa6e0965789a291adf650aecafbc4bd","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-30T11:33:47.1351192Z","@mt":"Starting background hosted service for {job}","job":"KeepAliveJob","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":8940,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"713aaee0eaa6e0965789a291adf650aecafbc4bd","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-30T11:33:47.1353164Z","@mt":"Starting background hosted service for {job}","job":"LogScrubberJob","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":8940,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"713aaee0eaa6e0965789a291adf650aecafbc4bd","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-30T11:33:47.1354391Z","@mt":"Starting background hosted service for {job}","job":"ContentVersionCleanupJob","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":8940,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"713aaee0eaa6e0965789a291adf650aecafbc4bd","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-30T11:33:47.1355251Z","@mt":"Starting background hosted service for {job}","job":"ScheduledPublishingJob","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":8940,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"713aaee0eaa6e0965789a291adf650aecafbc4bd","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-30T11:33:47.1356446Z","@mt":"Starting background hosted service for {job}","job":"TempFileCleanupJob","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":8940,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"713aaee0eaa6e0965789a291adf650aecafbc4bd","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-30T11:33:47.1357600Z","@mt":"Starting background hosted service for {job}","job":"InstructionProcessJob","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":8940,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"713aaee0eaa6e0965789a291adf650aecafbc4bd","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-30T11:33:47.1359604Z","@mt":"Starting background hosted service for {job}","job":"TouchServerJob","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":8940,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"713aaee0eaa6e0965789a291adf650aecafbc4bd","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-30T11:33:47.1361062Z","@mt":"Starting background hosted service for {job}","job":"WebhookFiring","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":8940,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"713aaee0eaa6e0965789a291adf650aecafbc4bd","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-30T11:33:47.1362170Z","@mt":"Starting background hosted service for {job}","job":"WebhookLoggingCleanup","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":8940,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"713aaee0eaa6e0965789a291adf650aecafbc4bd","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-30T11:33:47.1363464Z","@mt":"Starting background hosted service for {job}","job":"ReportSiteJob","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":8940,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"713aaee0eaa6e0965789a291adf650aecafbc4bd","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-30T11:33:47.1363843Z","@mt":"Completed starting recurring background jobs hosted services","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":8940,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"713aaee0eaa6e0965789a291adf650aecafbc4bd","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-30T11:33:47.2255278Z","@mt":"Now listening on: {address}","address":"https://localhost:44309","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","ProcessId":8940,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"713aaee0eaa6e0965789a291adf650aecafbc4bd","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-30T11:33:47.2255838Z","@mt":"Now listening on: {address}","address":"http://localhost:37806","EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime","ProcessId":8940,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"713aaee0eaa6e0965789a291adf650aecafbc4bd","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-30T11:33:47.2256449Z","@mt":"Application started. Press Ctrl+C to shut down.","SourceContext":"Microsoft.Hosting.Lifetime","ProcessId":8940,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"713aaee0eaa6e0965789a291adf650aecafbc4bd","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-30T11:33:47.2256687Z","@mt":"Hosting environment: {EnvName}","EnvName":"Development","SourceContext":"Microsoft.Hosting.Lifetime","ProcessId":8940,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"713aaee0eaa6e0965789a291adf650aecafbc4bd","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-30T11:33:47.2256773Z","@mt":"Content root path: {ContentRoot}","ContentRoot":"D:\\MDDPlusPortal\\MDDPlusPortal","SourceContext":"Microsoft.Hosting.Lifetime","ProcessId":8940,"ProcessName":"MDDPlusPortal","ThreadId":1,"ApplicationId":"713aaee0eaa6e0965789a291adf650aecafbc4bd","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-30T11:34:02.1410778Z","@mt":"No umbracoApplicationUrl for service (yet), skip.","@l":"Warning","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.Jobs.ServerRegistration.TouchServerJob","ProcessId":8940,"ProcessName":"MDDPlusPortal","ThreadId":21,"ApplicationId":"713aaee0eaa6e0965789a291adf650aecafbc4bd","MachineName":"MDD","Log4NetLevel":"WARN "}
{"@t":"2025-07-30T11:34:55.7049021Z","@mt":"Application is shutting down...","SourceContext":"Microsoft.Hosting.Lifetime","ProcessId":8940,"ProcessName":"MDDPlusPortal","ThreadId":24,"ApplicationId":"713aaee0eaa6e0965789a291adf650aecafbc4bd","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-30T11:34:55.7059505Z","@mt":"Stopping ({SignalSource})","SignalSource":"environment","SourceContext":"Umbraco.Cms.Core.Runtime.MainDom","ProcessId":8940,"ProcessName":"MDDPlusPortal","ThreadId":24,"ApplicationId":"713aaee0eaa6e0965789a291adf650aecafbc4bd","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-30T11:34:55.7082588Z","@mt":"FileSystemMainDomLock Disposing...","SourceContext":"Umbraco.Cms.Infrastructure.Runtime.FileSystemMainDomLock","ProcessId":8940,"ProcessName":"MDDPlusPortal","ThreadId":24,"ApplicationId":"713aaee0eaa6e0965789a291adf650aecafbc4bd","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-30T11:34:55.7083801Z","@mt":"Released ({SignalSource})","SignalSource":"environment","SourceContext":"Umbraco.Cms.Core.Runtime.MainDom","ProcessId":8940,"ProcessName":"MDDPlusPortal","ThreadId":24,"ApplicationId":"713aaee0eaa6e0965789a291adf650aecafbc4bd","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-30T11:34:55.7134748Z","@mt":"Stopping recurring background jobs hosted services","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":8940,"ProcessName":"MDDPlusPortal","ThreadId":25,"ApplicationId":"713aaee0eaa6e0965789a291adf650aecafbc4bd","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-30T11:34:55.7135657Z","@mt":"Stopping background hosted service for {job}","job":"HealthCheckNotifierJob","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":8940,"ProcessName":"MDDPlusPortal","ThreadId":25,"ApplicationId":"713aaee0eaa6e0965789a291adf650aecafbc4bd","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-30T11:34:55.7158031Z","@mt":"Stopping background hosted service for {job}","job":"KeepAliveJob","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":8940,"ProcessName":"MDDPlusPortal","ThreadId":25,"ApplicationId":"713aaee0eaa6e0965789a291adf650aecafbc4bd","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-30T11:34:55.7158624Z","@mt":"Stopping background hosted service for {job}","job":"LogScrubberJob","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":8940,"ProcessName":"MDDPlusPortal","ThreadId":25,"ApplicationId":"713aaee0eaa6e0965789a291adf650aecafbc4bd","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-30T11:34:55.7158984Z","@mt":"Stopping background hosted service for {job}","job":"ContentVersionCleanupJob","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":8940,"ProcessName":"MDDPlusPortal","ThreadId":25,"ApplicationId":"713aaee0eaa6e0965789a291adf650aecafbc4bd","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-30T11:34:55.7159150Z","@mt":"Stopping background hosted service for {job}","job":"ScheduledPublishingJob","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":8940,"ProcessName":"MDDPlusPortal","ThreadId":25,"ApplicationId":"713aaee0eaa6e0965789a291adf650aecafbc4bd","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-30T11:34:55.7159328Z","@mt":"Stopping background hosted service for {job}","job":"TempFileCleanupJob","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":8940,"ProcessName":"MDDPlusPortal","ThreadId":25,"ApplicationId":"713aaee0eaa6e0965789a291adf650aecafbc4bd","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-30T11:34:55.7159679Z","@mt":"Stopping background hosted service for {job}","job":"InstructionProcessJob","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":8940,"ProcessName":"MDDPlusPortal","ThreadId":25,"ApplicationId":"713aaee0eaa6e0965789a291adf650aecafbc4bd","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-30T11:34:55.7159923Z","@mt":"Stopping background hosted service for {job}","job":"TouchServerJob","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":8940,"ProcessName":"MDDPlusPortal","ThreadId":25,"ApplicationId":"713aaee0eaa6e0965789a291adf650aecafbc4bd","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-30T11:34:55.7160158Z","@mt":"Stopping background hosted service for {job}","job":"WebhookFiring","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":8940,"ProcessName":"MDDPlusPortal","ThreadId":25,"ApplicationId":"713aaee0eaa6e0965789a291adf650aecafbc4bd","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-30T11:34:55.7160440Z","@mt":"Stopping background hosted service for {job}","job":"WebhookLoggingCleanup","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":8940,"ProcessName":"MDDPlusPortal","ThreadId":25,"ApplicationId":"713aaee0eaa6e0965789a291adf650aecafbc4bd","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-30T11:34:55.7160625Z","@mt":"Stopping background hosted service for {job}","job":"ReportSiteJob","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":8940,"ProcessName":"MDDPlusPortal","ThreadId":25,"ApplicationId":"713aaee0eaa6e0965789a291adf650aecafbc4bd","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-30T11:34:55.7160839Z","@mt":"Completed stopping recurring background jobs hosted services","SourceContext":"Umbraco.Cms.Infrastructure.BackgroundJobs.RecurringBackgroundJobHostedServiceRunner","ProcessId":8940,"ProcessName":"MDDPlusPortal","ThreadId":25,"ApplicationId":"713aaee0eaa6e0965789a291adf650aecafbc4bd","MachineName":"MDD","Log4NetLevel":"INFO "}
{"@t":"2025-07-30T11:34:55.7164614Z","@mt":"Queued Hosted Service is stopping.","SourceContext":"Umbraco.Cms.Infrastructure.HostedServices.QueuedHostedService","ProcessId":8940,"ProcessName":"MDDPlusPortal","ThreadId":25,"ApplicationId":"713aaee0eaa6e0965789a291adf650aecafbc4bd","MachineName":"MDD","Log4NetLevel":"INFO "}
