function animateDot(n,t){TweenMax.to(t,4,{x:0,z:0,ease:Back.easeOut,delay:Math.abs(t.y/radius)*2,repeat:-1,yoyo:!0,yoyoEase:Back.easeOut,onUpdate:function(){updateDot(n,t)}})}function updateDot(n,t){positions[n*3]=t.x;positions[n*3+2]=t.z}function render(){dots.geometry.verticesNeedUpdate=!0;dots.geometry.attributes.position.needsUpdate=!0;renderer.render(scene,camera)}function onResize(){canvas.style.width="";canvas.style.height="";width=canvas.offsetWidth;height=canvas.offsetHeight;camera.aspect=width/height;camera.updateProjectionMatrix();renderer.setSize(width,height)}function onMouseMove(n){mouse.x=n.clientX/window.innerWidth-.5;mouse.y=n.clientY/window.innerHeight-.5;TweenMax.to(dots.rotation,4,{x:mouse.y*Math.PI*1.5,z:mouse.x*Math.PI*.6,ease:Power1.easeOut})}function showPopup(){if($("#popup")){var n=$("#popup").attr("data-cookie");Cookies.get(n)===undefined&&(Cookies.set(n,"true",{expires:30}),$("#popup").modal("show"))}}var scene,camera,loader,i,vector,attributePositions,shaderMaterial,dots,mouse,resizeTm;{setTimeout(function(){document.getElementById("hurricane").classList.add("render");60});const n=Array.from(document.querySelectorAll("nav.demos > .demo")),i=n.length,t=n.findIndex(function(n){n.classList.contains("demo--current")}),r=function(n){document.getElementById("hurricane").classList.remove("render");document.getElementById("hurricane").addEventListener("transitionend",function(){window.location=n.href})};n.forEach(function(n){n.addEventListener("click",function(n){n.preventDefault();r(n.target)})});document.addEventListener("keydown",function(u){const e=u.keyCode||u.which;let f;if(e===37)f=t>0?n[t-1]:n[i-1];else if(e===39)f=t<i-1?n[t+1]:n[0];else return!1;r(f)})}var canvas=document.querySelector("#scene"),width=canvas.offsetWidth,height=canvas.offsetHeight,renderer=new THREE.WebGLRenderer({canvas:canvas,antialias:!0});renderer.setPixelRatio(window.devicePixelRatio>1?2:1);renderer.setSize(width,height);renderer.setClearColor(16777215);scene=new THREE.Scene;camera=new THREE.PerspectiveCamera(50,width/height,.1,2e3);camera.position.set(0,0,90);loader=new THREE.TextureLoader;loader.crossOrigin="Anonymous";var dotTexture=loader.load("/images/dotTexture.png"),radius=50,sphereGeom=new THREE.IcosahedronGeometry(radius,5),dotsGeom=new THREE.Geometry,bufferDotsGeom=new THREE.BufferGeometry,positions=new Float32Array(sphereGeom.vertices.length*3);for(i=0;i<sphereGeom.vertices.length;i++)vector=sphereGeom.vertices[i],animateDot(i,vector),dotsGeom.vertices.push(vector),vector.toArray(positions,i*3);attributePositions=new THREE.BufferAttribute(positions,3);bufferDotsGeom.addAttribute("position",attributePositions);shaderMaterial=new THREE.ShaderMaterial({uniforms:{texture:{value:dotTexture}},vertexShader:document.getElementById("wrapVertexShader").textContent,fragmentShader:document.getElementById("wrapFragmentShader").textContent,transparent:!0});dots=new THREE.Points(bufferDotsGeom,shaderMaterial);scene.add(dots);mouse=new THREE.Vector2(.8,.5);TweenMax.ticker.addEventListener("tick",render);window.addEventListener("mousemove",onMouseMove);window.addEventListener("resize",function(){resizeTm=clearTimeout(resizeTm);resizeTm=setTimeout(onResize,200)});
/* @preserve
    _____ __ _     __                _
   / ___// /(_)___/ /___  ____      (_)___
  / (_ // // // _  // -_)/ __/_    / /(_-<
  \___//_//_/ \_,_/ \__//_/  (_)__/ //___/
                              |___/
  Version: 1.7.3
  Author: Nick Piscitelli (pickykneee)
  Website: https://nickpiscitelli.com
  Documentation: http://nickpiscitelli.github.io/Glider.js
  License: MIT License
  Release Date: October 25th, 2018
*/
!function(n){"function"==typeof define&&define.amd?define(n):"object"==typeof exports?module.exports=n():n()}(function(){var t="undefined"!=typeof window?window:this,i=t.Glider=function(n,i){var r=this;if(n._glider)return n._glider;if(r.ele=n,r.ele.classList.add("glider"),(r.ele._glider=r).opt=Object.assign({},{slidesToScroll:1,slidesToShow:1,resizeLock:!0,duration:.5,easing:function(n,t,i,r,u){return r*(t/=u)*t+i}},i),r.animate_id=r.page=r.slide=0,r.arrows={},r._opt=r.opt,r.opt.skipTrack)r.track=r.ele.children[0];else for(r.track=document.createElement("div"),r.ele.appendChild(r.track);1!==r.ele.children.length;)r.track.appendChild(r.ele.children[0]);r.track.classList.add("glider-track");r.init();r.resize=r.init.bind(r,!0);r.event(r.ele,"add",{scroll:r.updateControls.bind(r)});r.event(t,"add",{resize:r.resize})},n=i.prototype;return n.init=function(n,t){var i=this,r=0,e=0,u,f;i.slides=i.track.children;[].forEach.call(i.slides,function(n){n.classList.add("glider-slide")});i.containerWidth=i.ele.clientWidth;u=i.settingsBreakpoint();(t=t||u,"auto"===i.opt.slidesToShow||void 0!==i.opt._autoSlide)&&(f=i.containerWidth/i.opt.itemWidth,i.opt._autoSlide=i.opt.slidesToShow=i.opt.exactWidth?f:Math.floor(f));"auto"===i.opt.slidesToScroll&&(i.opt.slidesToScroll=Math.floor(i.opt.slidesToShow));i.itemWidth=i.opt.exactWidth?i.opt.itemWidth:i.containerWidth/i.opt.slidesToShow;[].forEach.call(i.slides,function(n){n.style.height="auto";n.style.width=i.itemWidth+"px";r+=i.itemWidth;e=Math.max(n.offsetHeight,e)});i.track.style.width=r+"px";i.trackWidth=r;i.isDrag=!1;i.preventClick=!1;i.opt.resizeLock&&i.scrollTo(i.slide*i.itemWidth,0);(u||t)&&(i.bindArrows(),i.buildDots(),i.bindDrag());i.updateControls();i.emit(n?"refresh":"loaded")},n.bindDrag=function(){function i(){n.mouseDown=void 0;n.ele.classList.remove("drag");n.isDrag&&(n.preventClick=!0);n.isDrag=!1}var n=this,t;n.mouse=n.mouse||n.handleMouse.bind(n);t={mouseup:i,mouseleave:i,mousedown:function(t){t.preventDefault();t.stopPropagation();n.mouseDown=t.clientX;n.ele.classList.add("drag")},mousemove:n.mouse,click:function(t){n.preventClick&&(t.preventDefault(),t.stopPropagation());n.preventClick=!1}};n.ele.classList.toggle("draggable",!0===n.opt.draggable);n.event(n.ele,"remove",t);n.opt.draggable&&n.event(n.ele,"add",t)},n.buildDots=function(){var n=this,t,i;if(n.opt.dots){if(n.dots="string"==typeof n.opt.dots?document.querySelector(n.opt.dots):n.opt.dots,n.dots)for(n.dots.innerHTML="",n.dots.classList.add("glider-dots"),t=0;t<Math.ceil(n.slides.length/n.opt.slidesToShow);++t)i=document.createElement("button"),i.dataset.index=t,i.setAttribute("aria-label","Page "+(t+1)),i.className="glider-dot "+(t?"":"active"),n.event(i,"add",{click:n.scrollItem.bind(n,t,!0)}),n.dots.appendChild(i)}else n.dots&&(n.dots.innerHTML="")},n.bindArrows=function(){var n=this;n.opt.arrows?["prev","next"].forEach(function(t){var i=n.opt.arrows[t];i&&("string"==typeof i&&(i=document.querySelector(i)),i._func=i._func||n.scrollItem.bind(n,t),n.event(i,"remove",{click:i._func}),n.event(i,"add",{click:i._func}),n.arrows[t]=i)}):Object.keys(n.arrows).forEach(function(t){var i=n.arrows[t];n.event(i,"remove",{click:i._func})})},n.updateControls=function(n){var t=this,u,i,r;n&&!t.opt.scrollPropagate&&n.stopPropagation();u=t.containerWidth>=t.trackWidth;t.opt.rewind||(t.arrows.prev&&t.arrows.prev.classList.toggle("disabled",t.ele.scrollLeft<=0||u),t.arrows.next&&t.arrows.next.classList.toggle("disabled",Math.ceil(t.ele.scrollLeft+t.containerWidth)>=Math.floor(t.trackWidth)||u));t.slide=Math.round(t.ele.scrollLeft/t.itemWidth);t.page=Math.round(t.ele.scrollLeft/t.containerWidth);i=t.slide+Math.floor(Math.floor(t.opt.slidesToShow)/2);r=Math.floor(t.opt.slidesToShow)%2?0:i+1;1===Math.floor(t.opt.slidesToShow)&&(r=0);t.ele.scrollLeft+t.containerWidth>=Math.floor(t.trackWidth)&&(t.page=t.dots?t.dots.children.length-1:0);[].forEach.call(t.slides,function(n,u){var f=n.classList,s=f.contains("visible"),h=t.ele.scrollLeft,c=t.ele.scrollLeft+t.containerWidth,o=t.itemWidth*u,l=o+t.itemWidth,e;[].forEach.call(f,function(n){/^left|right/.test(n)&&f.remove(n)});f.toggle("active",t.slide===u);i===u||r&&r===u?f.add("center"):(f.remove("center"),f.add([u<i?"left":"right",Math.abs(u-(u<i?i:r||i))].join("-")));e=Math.ceil(o)>=h&&Math.floor(l)<=c;f.toggle("visible",e);e!==s&&t.emit("slide-"+(e?"visible":"hidden"),{slide:u})});t.dots&&[].forEach.call(t.dots.children,function(n,i){n.classList.toggle("active",t.page===i)});n&&t.opt.scrollLock&&(clearTimeout(t.scrollLock),t.scrollLock=setTimeout(function(){clearTimeout(t.scrollLock);.02<Math.abs(t.ele.scrollLeft/t.itemWidth-t.slide)&&(t.mouseDown||t.scrollItem(t.round(t.ele.scrollLeft/t.itemWidth)))},t.opt.scrollLockDelay||250))},n.scrollItem=function(n,t,i){var r,f,u,e;return i&&i.preventDefault(),r=this,f=n,(++r.animate_id,!0===t)?(n*=r.containerWidth,n=Math.round(n/r.itemWidth)*r.itemWidth):("string"==typeof n&&(u="prev"===n,(n=r.opt.slidesToScroll%1||r.opt.slidesToShow%1?r.round(r.ele.scrollLeft/r.itemWidth):r.slide,u?n-=r.opt.slidesToScroll:n+=r.opt.slidesToScroll,r.opt.rewind)&&(e=r.ele.scrollLeft,n=u&&!e?r.slides.length:!u&&e+r.containerWidth>=Math.floor(r.trackWidth)?0:n)),n=Math.max(Math.min(n,r.slides.length),0),r.slide=n,n=r.itemWidth*n),r.scrollTo(n,r.opt.duration*Math.abs(r.ele.scrollLeft-n),function(){r.updateControls();r.emit("animated",{value:f,type:"string"==typeof f?"arrow":t?"dot":"slide"})}),!1},n.settingsBreakpoint=function(){var n=this,r=n._opt.responsive,u,i,f;if(r)for(r.sort(function(n,t){return t.breakpoint-n.breakpoint}),u=0;u<r.length;++u)if(i=r[u],t.innerWidth>=i.breakpoint)return n.breakpoint!==i.breakpoint&&(n.opt=Object.assign({},n._opt,i.settings),n.breakpoint=i.breakpoint,!0);return f=0!==n.breakpoint,n.opt=Object.assign({},n._opt),n.breakpoint=0,f},n.scrollTo=function(n,i,r){var u=this,e=(new Date).getTime(),o=u.animate_id,f=function(){var s=(new Date).getTime()-e;u.ele.scrollLeft=u.ele.scrollLeft+(n-u.ele.scrollLeft)*u.opt.easing(0,s,0,1,i);s<i&&o===u.animate_id?t.requestAnimationFrame(f):(u.ele.scrollLeft=n,r&&r.call(u))};t.requestAnimationFrame(f)},n.removeItem=function(n){var t=this;t.slides.length&&(t.track.removeChild(t.slides[n]),t.refresh(!0),t.emit("remove"))},n.addItem=function(n){this.track.appendChild(n);this.refresh(!0);this.emit("add")},n.handleMouse=function(n){var t=this;t.mouseDown&&(t.isDrag=!0,t.ele.scrollLeft+=(t.mouseDown-n.clientX)*(t.opt.dragVelocity||3.3),t.mouseDown=n.clientX)},n.round=function(n){var t=1/(this.opt.slidesToScroll%1||1);return Math.round(n*t)/t},n.refresh=function(n){this.init(!0,n)},n.setOption=function(n,t){var i=this;i.breakpoint&&!t?i._opt.responsive.forEach(function(t){t.breakpoint===i.breakpoint&&(t.settings=Object.assign({},t.settings,n))}):i._opt=Object.assign({},i._opt,n);i.breakpoint=0;i.settingsBreakpoint()},n.destroy=function(){function r(n){n.removeAttribute("style");[].forEach.call(n.classList,function(t){/^glider/.test(t)&&n.classList.remove(t)})}var n=this,i=n.ele.cloneNode(!0);i.children[0].outerHTML=i.children[0].innerHTML;r(i);[].forEach.call(i.getElementsByTagName("*"),r);n.ele.parentNode.replaceChild(i,n.ele);n.event(t,"remove",{resize:n.resize});n.emit("destroy")},n.emit=function(n,i){var r=new t.CustomEvent("glider-"+n,{bubbles:!this.opt.eventPropagate,detail:i});this.ele.dispatchEvent(r)},n.event=function(n,t,i){var r=n[t+"EventListener"].bind(n);Object.keys(i).forEach(function(n){r(n,i[n])})},i});$(document).ready(setTimeout(function(){showPopup()},5e3));