function init(){var n,t;container=document.getElementById("animationBblock");document.body.appendChild(container);camera=new THREE.PerspectiveCamera(120,window.innerWidth/window.innerHeight,1,1e4);camera.position.z=1e3;scene=new THREE.Scene;particles=[];var i=Math.PI*2,r=new THREE.ParticleCanvasMaterial({color:235431,program:function(n){n.beginPath();n.arc(0,0,.6,0,i,!0);n.fill()}}),u=0;for(n=0;n<AMOUNTX;n++)for(t=0;t<AMOUNTY;t++)particle=particles[u++]=new THREE.Particle(r),particle.position.x=n*SEPARATION-AMOUNTX*SEPARATION/2,particle.position.z=t*SEPARATION-AMOUNTY*SEPARATION/2,scene.add(particle);renderer=new THREE.CanvasRenderer;renderer.setSize(window.innerWidth,window.innerHeight);container.appendChild(renderer.domElement);document.getElementById("animationSection").appendChild(container);document.addEventListener("mousemove",onDocumentMouseMove,!1);document.addEventListener("touchstart",onDocumentTouchStart,!1);document.addEventListener("touchmove",onDocumentTouchMove,!1);window.addEventListener("resize",onWindowResize,!1)}function onWindowResize(){windowHalfX=window.innerWidth/2;windowHalfY=window.innerHeight/2;camera.aspect=window.innerWidth/window.innerHeight;camera.updateProjectionMatrix();renderer.setSize(window.innerWidth,window.innerHeight)}function onDocumentMouseMove(n){mouseX=n.clientX-windowHalfX;mouseY=n.clientY-windowHalfY}function onDocumentTouchStart(n){n.touches.length===1&&(n.preventDefault(),mouseX=n.touches[0].pageX-windowHalfX,mouseY=n.touches[0].pageY-windowHalfY)}function onDocumentTouchMove(n){n.touches.length===1&&(n.preventDefault(),mouseX=n.touches[0].pageX-windowHalfX,mouseY=n.touches[0].pageY-windowHalfY)}function animate(){requestAnimationFrame(animate);render()}function render(){var i,n,t;for(camera.position.x+=(mouseX-camera.position.x)*.05,camera.position.y+=(-mouseY-camera.position.y)*.05,camera.lookAt(scene.position),i=0,n=0;n<AMOUNTX;n++)for(t=0;t<AMOUNTY;t++)particle=particles[i++],particle.position.y=Math.sin((n+count)*.3)*50+Math.sin((t+count)*.5)*50,particle.scale.x=particle.scale.y=(Math.sin((n+count)*.3)+1)*2+(Math.sin((t+count)*.5)+1)*2;renderer.render(scene,camera);count+=.1}var WEBGL={isWebGLAvailable:function(){try{var n=document.createElement("canvas");return!!(window.WebGLRenderingContext&&(n.getContext("webgl")||n.getContext("experimental-webgl")))}catch(t){return!1}},isWebGL2Available:function(){try{var n=document.createElement("canvas");return!!(window.WebGL2RenderingContext&&n.getContext("webgl2"))}catch(t){return!1}},getWebGLErrorMessage:function(){return this.getErrorMessage(1)},getWebGL2ErrorMessage:function(){return this.getErrorMessage(2)},getErrorMessage:function(n){var r={1:window.WebGLRenderingContext,2:window.WebGL2RenderingContext},i='Your $0 does not seem to support <a href="http://khronos.org/webgl/wiki/Getting_a_WebGL_Implementation" style="color:#000">$1<\/a>',t=document.createElement("div");return t.id="webglmessage",t.style.fontFamily="monospace",t.style.fontSize="13px",t.style.fontWeight="normal",t.style.textAlign="center",t.style.background="#fff",t.style.color="#000",t.style.padding="1.5em",t.style.width="400px",t.style.margin="5em auto 0",i=r[n]?i.replace("$0","graphics card"):i.replace("$0","browser"),i=i.replace("$1",{1:"WebGL",2:"WebGL 2"}[n]),t.innerHTML=i,t}},THREE;THREE=THREE||{REVISION:"56"};self.console=self.console||{info:function(){},log:function(){},debug:function(){},warn:function(){},error:function(){}};self.Int32Array=self.Int32Array||Array;self.Float32Array=self.Float32Array||Array;String.prototype.trim=String.prototype.trim||function(){return this.replace(/^\s+|\s+$/g,"")};THREE.extend=function(n,t){var i;if(Object.keys)for(var r=Object.keys(t),u=0,f=r.length;u<f;u++)i=r[u],Object.defineProperty(n,i,Object.getOwnPropertyDescriptor(t,i));else for(i in r={}.hasOwnProperty,t)r.call(t,i)&&(n[i]=t[i]);return n},function(){for(var i=0,t=["ms","moz","webkit","o"],n=0;n<t.length&&!window.requestAnimationFrame;++n)window.requestAnimationFrame=window[t[n]+"RequestAnimationFrame"],window.cancelAnimationFrame=window[t[n]+"CancelAnimationFrame"]||window[t[n]+"CancelRequestAnimationFrame"];void 0===window.requestAnimationFrame&&(window.requestAnimationFrame=function(n){var t=Date.now(),r=Math.max(0,16-(t-i)),u=window.setTimeout(function(){n(t+r)},r);return i=t+r,u});window.cancelAnimationFrame=window.cancelAnimationFrame||function(n){window.clearTimeout(n)}}();THREE.CullFaceNone=0;THREE.CullFaceBack=1;THREE.CullFaceFront=2;THREE.CullFaceFrontBack=3;THREE.FrontFaceDirectionCW=0;THREE.FrontFaceDirectionCCW=1;THREE.BasicShadowMap=0;THREE.PCFShadowMap=1;THREE.PCFSoftShadowMap=2;THREE.FrontSide=0;THREE.BackSide=1;THREE.DoubleSide=2;THREE.NoShading=0;THREE.FlatShading=1;THREE.SmoothShading=2;THREE.NoColors=0;THREE.FaceColors=1;THREE.VertexColors=2;THREE.NoBlending=0;THREE.NormalBlending=1;THREE.AdditiveBlending=2;THREE.SubtractiveBlending=3;THREE.MultiplyBlending=4;THREE.CustomBlending=5;THREE.AddEquation=100;THREE.SubtractEquation=101;THREE.ReverseSubtractEquation=102;THREE.ZeroFactor=200;THREE.OneFactor=201;THREE.SrcColorFactor=202;THREE.OneMinusSrcColorFactor=203;THREE.SrcAlphaFactor=204;THREE.OneMinusSrcAlphaFactor=205;THREE.DstAlphaFactor=206;THREE.OneMinusDstAlphaFactor=207;THREE.DstColorFactor=208;THREE.OneMinusDstColorFactor=209;THREE.SrcAlphaSaturateFactor=210;THREE.MultiplyOperation=0;THREE.MixOperation=1;THREE.AddOperation=2;THREE.UVMapping=function(){};THREE.CubeReflectionMapping=function(){};THREE.CubeRefractionMapping=function(){};THREE.SphericalReflectionMapping=function(){};THREE.SphericalRefractionMapping=function(){};THREE.RepeatWrapping=1e3;THREE.ClampToEdgeWrapping=1001;THREE.MirroredRepeatWrapping=1002;THREE.NearestFilter=1003;THREE.NearestMipMapNearestFilter=1004;THREE.NearestMipMapLinearFilter=1005;THREE.LinearFilter=1006;THREE.LinearMipMapNearestFilter=1007;THREE.LinearMipMapLinearFilter=1008;THREE.UnsignedByteType=1009;THREE.ByteType=1010;THREE.ShortType=1011;THREE.UnsignedShortType=1012;THREE.IntType=1013;THREE.UnsignedIntType=1014;THREE.FloatType=1015;THREE.UnsignedShort4444Type=1016;THREE.UnsignedShort5551Type=1017;THREE.UnsignedShort565Type=1018;THREE.AlphaFormat=1019;THREE.RGBFormat=1020;THREE.RGBAFormat=1021;THREE.LuminanceFormat=1022;THREE.LuminanceAlphaFormat=1023;THREE.RGB_S3TC_DXT1_Format=2001;THREE.RGBA_S3TC_DXT1_Format=2002;THREE.RGBA_S3TC_DXT3_Format=2003;THREE.RGBA_S3TC_DXT5_Format=2004;THREE.Color=function(n){return void 0!==n&&this.set(n),this};THREE.extend(THREE.Color.prototype,{r:1,g:1,b:1,set:function(n){switch(typeof n){case"number":this.setHex(n);break;case"string":this.setStyle(n)}},setHex:function(n){return n=Math.floor(n),this.r=(n>>16&255)/255,this.g=(n>>8&255)/255,this.b=(n&255)/255,this},setRGB:function(n,t,i){return this.r=n,this.g=t,this.b=i,this},setHSV:function(n,t,i){return console.log("DEPRECATED: Color's .setHSV() will be removed. Use .setHSL( h, s, l ) instead."),this.setHSL(n,t*i/(1>(n=(2-t)*i)?n:2-n),n/2)},setHSL:function(n,t,i){if(0===t)this.r=this.g=this.b=i;else{var r=function(n,t,i){return 0>i&&(i+=1),1<i&&(i-=1),i<1/6?n+6*(t-n)*i:.5>i?t:i<2/3?n+6*(t-n)*(2/3-i):n},t=.5>=i?i*(1+t):i+t-i*t,i=2*i-t;this.r=r(i,t,n+1/3);this.g=r(i,t,n);this.b=r(i,t,n-1/3)}return this},setStyle:function(n){return/^rgb\((\d+),(\d+),(\d+)\)$/i.test(n)?(n=/^rgb\((\d+),(\d+),(\d+)\)$/i.exec(n),this.r=Math.min(255,parseInt(n[1],10))/255,this.g=Math.min(255,parseInt(n[2],10))/255,this.b=Math.min(255,parseInt(n[3],10))/255,this):/^rgb\((\d+)\%,(\d+)\%,(\d+)\%\)$/i.test(n)?(n=/^rgb\((\d+)\%,(\d+)\%,(\d+)\%\)$/i.exec(n),this.r=Math.min(100,parseInt(n[1],10))/100,this.g=Math.min(100,parseInt(n[2],10))/100,this.b=Math.min(100,parseInt(n[3],10))/100,this):/^\#([0-9a-f]{6})$/i.test(n)?(n=/^\#([0-9a-f]{6})$/i.exec(n),this.setHex(parseInt(n[1],16)),this):/^\#([0-9a-f])([0-9a-f])([0-9a-f])$/i.test(n)?(n=/^\#([0-9a-f])([0-9a-f])([0-9a-f])$/i.exec(n),this.setHex(parseInt(n[1]+n[1]+n[2]+n[2]+n[3]+n[3],16)),this):/^(\w+)$/i.test(n)?(this.setHex(THREE.ColorKeywords[n]),this):void 0},copy:function(n){return this.r=n.r,this.g=n.g,this.b=n.b,this},copyGammaToLinear:function(n){return this.r=n.r*n.r,this.g=n.g*n.g,this.b=n.b*n.b,this},copyLinearToGamma:function(n){return this.r=Math.sqrt(n.r),this.g=Math.sqrt(n.g),this.b=Math.sqrt(n.b),this},convertGammaToLinear:function(){var n=this.r,t=this.g,i=this.b;return this.r=n*n,this.g=t*t,this.b=i*i,this},convertLinearToGamma:function(){return this.r=Math.sqrt(this.r),this.g=Math.sqrt(this.g),this.b=Math.sqrt(this.b),this},getHex:function(){return 255*this.r<<16^255*this.g<<8^255*this.b<<0},getHexString:function(){return("000000"+this.getHex().toString(16)).slice(-6)},getHSL:function(){var n={h:0,s:0,l:0};return function(){var o=this.r,i=this.g,r=this.b,u=Math.max(o,i,r),t=Math.min(o,i,r),f,s=(t+u)/2,e;if(t===u)t=f=0;else{e=u-t;t=.5>=s?e/(u+t):e/(2-u-t);switch(u){case o:f=(i-r)/e+(i<r?6:0);break;case i:f=(r-o)/e+2;break;case r:f=(o-i)/e+4}f/=6}return n.h=f,n.s=t,n.l=s,n}}(),getStyle:function(){return"rgb("+(255*this.r|0)+","+(255*this.g|0)+","+(255*this.b|0)+")"},offsetHSL:function(n,t,i){var r=this.getHSL();return r.h+=n,r.s+=t,r.l+=i,this.setHSL(r.h,r.s,r.l),this},add:function(n){return this.r+=n.r,this.g+=n.g,this.b+=n.b,this},addColors:function(n,t){return this.r=n.r+t.r,this.g=n.g+t.g,this.b=n.b+t.b,this},addScalar:function(n){return this.r+=n,this.g+=n,this.b+=n,this},multiply:function(n){return this.r*=n.r,this.g*=n.g,this.b*=n.b,this},multiplyScalar:function(n){return this.r*=n,this.g*=n,this.b*=n,this},lerp:function(n,t){return this.r+=(n.r-this.r)*t,this.g+=(n.g-this.g)*t,this.b+=(n.b-this.b)*t,this},clone:function(){return(new THREE.Color).setRGB(this.r,this.g,this.b)}});THREE.ColorKeywords={aliceblue:15792383,antiquewhite:16444375,aqua:65535,aquamarine:8388564,azure:15794175,beige:16119260,bisque:16770244,black:0,blanchedalmond:16772045,blue:255,blueviolet:9055202,brown:10824234,burlywood:14596231,cadetblue:6266528,chartreuse:8388352,chocolate:13789470,coral:16744272,cornflowerblue:6591981,cornsilk:16775388,crimson:14423100,cyan:65535,darkblue:139,darkcyan:35723,darkgoldenrod:12092939,darkgray:11119017,darkgreen:25600,darkgrey:11119017,darkkhaki:12433259,darkmagenta:9109643,darkolivegreen:5597999,darkorange:16747520,darkorchid:10040012,darkred:9109504,darksalmon:15308410,darkseagreen:9419919,darkslateblue:4734347,darkslategray:3100495,darkslategrey:3100495,darkturquoise:52945,darkviolet:9699539,deeppink:16716947,deepskyblue:49151,dimgray:6908265,dimgrey:6908265,dodgerblue:2003199,firebrick:11674146,floralwhite:16775920,forestgreen:2263842,fuchsia:16711935,gainsboro:14474460,ghostwhite:16316671,gold:16766720,goldenrod:14329120,gray:8421504,green:32768,greenyellow:11403055,grey:8421504,honeydew:15794160,hotpink:16738740,indianred:13458524,indigo:4915330,ivory:16777200,khaki:15787660,lavender:15132410,lavenderblush:16773365,lawngreen:8190976,lemonchiffon:16775885,lightblue:11393254,lightcoral:15761536,lightcyan:14745599,lightgoldenrodyellow:16448210,lightgray:13882323,lightgreen:9498256,lightgrey:13882323,lightpink:16758465,lightsalmon:16752762,lightseagreen:2142890,lightskyblue:8900346,lightslategray:7833753,lightslategrey:7833753,lightsteelblue:11584734,lightyellow:16777184,lime:65280,limegreen:3329330,linen:16445670,magenta:16711935,maroon:8388608,mediumaquamarine:6737322,mediumblue:205,mediumorchid:12211667,mediumpurple:9662683,mediumseagreen:3978097,mediumslateblue:8087790,mediumspringgreen:64154,mediumturquoise:4772300,mediumvioletred:13047173,midnightblue:1644912,mintcream:16121850,mistyrose:16770273,moccasin:16770229,navajowhite:16768685,navy:128,oldlace:16643558,olive:8421376,olivedrab:7048739,orange:16753920,orangered:16729344,orchid:14315734,palegoldenrod:15657130,palegreen:10025880,paleturquoise:11529966,palevioletred:14381203,papayawhip:16773077,peachpuff:16767673,peru:13468991,pink:16761035,plum:14524637,powderblue:11591910,purple:8388736,red:16711680,rosybrown:12357519,royalblue:4286945,saddlebrown:9127187,salmon:16416882,sandybrown:16032864,seagreen:3050327,seashell:16774638,sienna:10506797,silver:12632256,skyblue:8900331,slateblue:6970061,slategray:7372944,slategrey:7372944,snow:16775930,springgreen:65407,steelblue:4620980,tan:13808780,teal:32896,thistle:14204888,tomato:16737095,turquoise:4251856,violet:15631086,wheat:16113331,white:16777215,whitesmoke:16119285,yellow:16776960,yellowgreen:10145074};THREE.Quaternion=function(n,t,i,r){this.x=n||0;this.y=t||0;this.z=i||0;this.w=void 0!==r?r:1};THREE.extend(THREE.Quaternion.prototype,{set:function(n,t,i,r){return this.x=n,this.y=t,this.z=i,this.w=r,this},copy:function(n){return this.x=n.x,this.y=n.y,this.z=n.z,this.w=n.w,this},setFromEuler:function(n,t){var i=Math.cos(n.x/2),r=Math.cos(n.y/2),u=Math.cos(n.z/2),f=Math.sin(n.x/2),e=Math.sin(n.y/2),o=Math.sin(n.z/2);return void 0===t||"XYZ"===t?(this.x=f*r*u+i*e*o,this.y=i*e*u-f*r*o,this.z=i*r*o+f*e*u,this.w=i*r*u-f*e*o):"YXZ"===t?(this.x=f*r*u+i*e*o,this.y=i*e*u-f*r*o,this.z=i*r*o-f*e*u,this.w=i*r*u+f*e*o):"ZXY"===t?(this.x=f*r*u-i*e*o,this.y=i*e*u+f*r*o,this.z=i*r*o+f*e*u,this.w=i*r*u-f*e*o):"ZYX"===t?(this.x=f*r*u-i*e*o,this.y=i*e*u+f*r*o,this.z=i*r*o-f*e*u,this.w=i*r*u+f*e*o):"YZX"===t?(this.x=f*r*u+i*e*o,this.y=i*e*u+f*r*o,this.z=i*r*o-f*e*u,this.w=i*r*u-f*e*o):"XZY"===t&&(this.x=f*r*u-i*e*o,this.y=i*e*u-f*r*o,this.z=i*r*o+f*e*u,this.w=i*r*u+f*e*o),this},setFromAxisAngle:function(n,t){var r=t/2,i=Math.sin(r);return this.x=n.x*i,this.y=n.y*i,this.z=n.z*i,this.w=Math.cos(r),this},setFromRotationMatrix:function(n){var i=n.elements,t=i[0],n=i[4],u=i[8],f=i[1],r=i[5],e=i[9],o=i[2],s=i[6],i=i[10],h=t+r+i;return 0<h?(t=.5/Math.sqrt(h+1),this.w=.25/t,this.x=(s-e)*t,this.y=(u-o)*t,this.z=(f-n)*t):t>r&&t>i?(t=2*Math.sqrt(1+t-r-i),this.w=(s-e)/t,this.x=.25*t,this.y=(n+f)/t,this.z=(u+o)/t):r>i?(t=2*Math.sqrt(1+r-t-i),this.w=(u-o)/t,this.x=(n+f)/t,this.y=.25*t,this.z=(e+s)/t):(t=2*Math.sqrt(1+i-t-r),this.w=(f-n)/t,this.x=(u+o)/t,this.y=(e+s)/t,this.z=.25*t),this},inverse:function(){return this.conjugate().normalize(),this},conjugate:function(){return this.x*=-1,this.y*=-1,this.z*=-1,this},lengthSq:function(){return this.x*this.x+this.y*this.y+this.z*this.z+this.w*this.w},length:function(){return Math.sqrt(this.x*this.x+this.y*this.y+this.z*this.z+this.w*this.w)},normalize:function(){var n=this.length();return 0===n?(this.z=this.y=this.x=0,this.w=1):(n=1/n,this.x*=n,this.y*=n,this.z*=n,this.w*=n),this},multiply:function(n,t){return void 0!==t?(console.warn("DEPRECATED: Quaternion's .multiply() now only accepts one argument. Use .multiplyQuaternions( a, b ) instead."),this.multiplyQuaternions(n,t)):this.multiplyQuaternions(this,n)},multiplyQuaternions:function(n,t){var i=n.x,r=n.y,u=n.z,f=n.w,e=t.x,o=t.y,s=t.z,h=t.w;return this.x=i*h+f*e+r*s-u*o,this.y=r*h+f*o+u*e-i*s,this.z=u*h+f*s+i*o-r*e,this.w=f*h-i*e-r*o-u*s,this},multiplyVector3:function(n){return console.warn("DEPRECATED: Quaternion's .multiplyVector3() has been removed. Use is now vector.applyQuaternion( quaternion ) instead."),n.applyQuaternion(this)},slerp:function(n,t){var u=this.x,f=this.y,e=this.z,o=this.w,i=o*n.w+u*n.x+f*n.y+e*n.z,r,s;return(0>i?(this.w=-n.w,this.x=-n.x,this.y=-n.y,this.z=-n.z,i=-i):this.copy(n),1<=i)?(this.w=o,this.x=u,this.y=f,this.z=e,this):(r=Math.acos(i),s=Math.sqrt(1-i*i),.001>Math.abs(s))?(this.w=.5*(o+this.w),this.x=.5*(u+this.x),this.y=.5*(f+this.y),this.z=.5*(e+this.z),this):(i=Math.sin((1-t)*r)/s,r=Math.sin(t*r)/s,this.w=o*i+this.w*r,this.x=u*i+this.x*r,this.y=f*i+this.y*r,this.z=e*i+this.z*r,this)},equals:function(n){return n.x===this.x&&n.y===this.y&&n.z===this.z&&n.w===this.w},clone:function(){return new THREE.Quaternion(this.x,this.y,this.z,this.w)}});THREE.Quaternion.slerp=function(n,t,i,r){return i.copy(n).slerp(t,r)};THREE.Vector2=function(n,t){this.x=n||0;this.y=t||0};THREE.extend(THREE.Vector2.prototype,{set:function(n,t){return this.x=n,this.y=t,this},setX:function(n){return this.x=n,this},setY:function(n){return this.y=n,this},setComponent:function(n,t){switch(n){case 0:this.x=t;break;case 1:this.y=t;break;default:throw Error("index is out of range: "+n);}},getComponent:function(n){switch(n){case 0:return this.x;case 1:return this.y;default:throw Error("index is out of range: "+n);}},copy:function(n){return this.x=n.x,this.y=n.y,this},add:function(n,t){return void 0!==t?(console.warn("DEPRECATED: Vector2's .add() now only accepts one argument. Use .addVectors( a, b ) instead."),this.addVectors(n,t)):(this.x+=n.x,this.y+=n.y,this)},addVectors:function(n,t){return this.x=n.x+t.x,this.y=n.y+t.y,this},addScalar:function(n){return this.x+=n,this.y+=n,this},sub:function(n,t){return void 0!==t?(console.warn("DEPRECATED: Vector2's .sub() now only accepts one argument. Use .subVectors( a, b ) instead."),this.subVectors(n,t)):(this.x-=n.x,this.y-=n.y,this)},subVectors:function(n,t){return this.x=n.x-t.x,this.y=n.y-t.y,this},multiplyScalar:function(n){return this.x*=n,this.y*=n,this},divideScalar:function(n){return 0!==n?(this.x/=n,this.y/=n):this.set(0,0),this},min:function(n){return this.x>n.x&&(this.x=n.x),this.y>n.y&&(this.y=n.y),this},max:function(n){return this.x<n.x&&(this.x=n.x),this.y<n.y&&(this.y=n.y),this},clamp:function(n,t){return this.x<n.x?this.x=n.x:this.x>t.x&&(this.x=t.x),this.y<n.y?this.y=n.y:this.y>t.y&&(this.y=t.y),this},negate:function(){return this.multiplyScalar(-1)},dot:function(n){return this.x*n.x+this.y*n.y},lengthSq:function(){return this.x*this.x+this.y*this.y},length:function(){return Math.sqrt(this.x*this.x+this.y*this.y)},normalize:function(){return this.divideScalar(this.length())},distanceTo:function(n){return Math.sqrt(this.distanceToSquared(n))},distanceToSquared:function(n){var t=this.x-n.x,n=this.y-n.y;return t*t+n*n},setLength:function(n){var t=this.length();return 0!==t&&n!==t&&this.multiplyScalar(n/t),this},lerp:function(n,t){return this.x+=(n.x-this.x)*t,this.y+=(n.y-this.y)*t,this},equals:function(n){return n.x===this.x&&n.y===this.y},toArray:function(){return[this.x,this.y]},clone:function(){return new THREE.Vector2(this.x,this.y)}});THREE.Vector3=function(n,t,i){this.x=n||0;this.y=t||0;this.z=i||0};THREE.extend(THREE.Vector3.prototype,{set:function(n,t,i){return this.x=n,this.y=t,this.z=i,this},setX:function(n){return this.x=n,this},setY:function(n){return this.y=n,this},setZ:function(n){return this.z=n,this},setComponent:function(n,t){switch(n){case 0:this.x=t;break;case 1:this.y=t;break;case 2:this.z=t;break;default:throw Error("index is out of range: "+n);}},getComponent:function(n){switch(n){case 0:return this.x;case 1:return this.y;case 2:return this.z;default:throw Error("index is out of range: "+n);}},copy:function(n){return this.x=n.x,this.y=n.y,this.z=n.z,this},add:function(n,t){return void 0!==t?(console.warn("DEPRECATED: Vector3's .add() now only accepts one argument. Use .addVectors( a, b ) instead."),this.addVectors(n,t)):(this.x+=n.x,this.y+=n.y,this.z+=n.z,this)},addScalar:function(n){return this.x+=n,this.y+=n,this.z+=n,this},addVectors:function(n,t){return this.x=n.x+t.x,this.y=n.y+t.y,this.z=n.z+t.z,this},sub:function(n,t){return void 0!==t?(console.warn("DEPRECATED: Vector3's .sub() now only accepts one argument. Use .subVectors( a, b ) instead."),this.subVectors(n,t)):(this.x-=n.x,this.y-=n.y,this.z-=n.z,this)},subVectors:function(n,t){return this.x=n.x-t.x,this.y=n.y-t.y,this.z=n.z-t.z,this},multiply:function(n,t){return void 0!==t?(console.warn("DEPRECATED: Vector3's .multiply() now only accepts one argument. Use .multiplyVectors( a, b ) instead."),this.multiplyVectors(n,t)):(this.x*=n.x,this.y*=n.y,this.z*=n.z,this)},multiplyScalar:function(n){return this.x*=n,this.y*=n,this.z*=n,this},multiplyVectors:function(n,t){return this.x=n.x*t.x,this.y=n.y*t.y,this.z=n.z*t.z,this},applyMatrix3:function(n){var t=this.x,i=this.y,r=this.z,n=n.elements;return this.x=n[0]*t+n[3]*i+n[6]*r,this.y=n[1]*t+n[4]*i+n[7]*r,this.z=n[2]*t+n[5]*i+n[8]*r,this},applyMatrix4:function(n){var t=this.x,i=this.y,r=this.z,n=n.elements;return this.x=n[0]*t+n[4]*i+n[8]*r+n[12],this.y=n[1]*t+n[5]*i+n[9]*r+n[13],this.z=n[2]*t+n[6]*i+n[10]*r+n[14],this},applyProjection:function(n){var t=this.x,i=this.y,r=this.z,n=n.elements,u=1/(n[3]*t+n[7]*i+n[11]*r+n[15]);return this.x=(n[0]*t+n[4]*i+n[8]*r+n[12])*u,this.y=(n[1]*t+n[5]*i+n[9]*r+n[13])*u,this.z=(n[2]*t+n[6]*i+n[10]*r+n[14])*u,this},applyQuaternion:function(n){var t=this.x,f=this.y,e=this.z,i=n.x,r=n.y,u=n.z,n=n.w,o=n*t+r*e-u*f,s=n*f+u*t-i*e,h=n*e+i*f-r*t,t=-i*t-r*f-u*e;return this.x=o*n+t*-i+s*-u-h*-r,this.y=s*n+t*-r+h*-i-o*-u,this.z=h*n+t*-u+o*-r-s*-i,this},applyEuler:function(){var n=new THREE.Quaternion;return function(t,i){var r=n.setFromEuler(t,i);return this.applyQuaternion(r),this}}(),applyAxisAngle:function(){var n=new THREE.Quaternion;return function(t,i){var r=n.setFromAxisAngle(t,i);return this.applyQuaternion(r),this}}(),transformDirection:function(n){var t=this.x,i=this.y,r=this.z,n=n.elements;return this.x=n[0]*t+n[4]*i+n[8]*r,this.y=n[1]*t+n[5]*i+n[9]*r,this.z=n[2]*t+n[6]*i+n[10]*r,this.normalize(),this},divide:function(n){return this.x/=n.x,this.y/=n.y,this.z/=n.z,this},divideScalar:function(n){return 0!==n?(this.x/=n,this.y/=n,this.z/=n):this.z=this.y=this.x=0,this},min:function(n){return this.x>n.x&&(this.x=n.x),this.y>n.y&&(this.y=n.y),this.z>n.z&&(this.z=n.z),this},max:function(n){return this.x<n.x&&(this.x=n.x),this.y<n.y&&(this.y=n.y),this.z<n.z&&(this.z=n.z),this},clamp:function(n,t){return this.x<n.x?this.x=n.x:this.x>t.x&&(this.x=t.x),this.y<n.y?this.y=n.y:this.y>t.y&&(this.y=t.y),this.z<n.z?this.z=n.z:this.z>t.z&&(this.z=t.z),this},negate:function(){return this.multiplyScalar(-1)},dot:function(n){return this.x*n.x+this.y*n.y+this.z*n.z},lengthSq:function(){return this.x*this.x+this.y*this.y+this.z*this.z},length:function(){return Math.sqrt(this.x*this.x+this.y*this.y+this.z*this.z)},lengthManhattan:function(){return Math.abs(this.x)+Math.abs(this.y)+Math.abs(this.z)},normalize:function(){return this.divideScalar(this.length())},setLength:function(n){var t=this.length();return 0!==t&&n!==t&&this.multiplyScalar(n/t),this},lerp:function(n,t){return this.x+=(n.x-this.x)*t,this.y+=(n.y-this.y)*t,this.z+=(n.z-this.z)*t,this},cross:function(n,t){if(void 0!==t)return console.warn("DEPRECATED: Vector3's .cross() now only accepts one argument. Use .crossVectors( a, b ) instead."),this.crossVectors(n,t);var i=this.x,r=this.y,u=this.z;return this.x=r*n.z-u*n.y,this.y=u*n.x-i*n.z,this.z=i*n.y-r*n.x,this},crossVectors:function(n,t){return this.x=n.y*t.z-n.z*t.y,this.y=n.z*t.x-n.x*t.z,this.z=n.x*t.y-n.y*t.x,this},projectOnVector:function(){var n=new THREE.Vector3;return function(t){return n.copy(t).normalize(),t=this.dot(n),this.copy(n).multiplyScalar(t)}}(),projectOnPlane:function(){var n=new THREE.Vector3;return function(t){return n.copy(this).projectOnVector(t),this.sub(n)}}(),reflect:function(){var n=new THREE.Vector3;return function(t){return n.copy(this).projectOnVector(t).multiplyScalar(2),this.subVectors(n,this)}}(),angleTo:function(n){return n=this.dot(n)/(this.length()*n.length()),Math.acos(THREE.Math.clamp(n,-1,1))},distanceTo:function(n){return Math.sqrt(this.distanceToSquared(n))},distanceToSquared:function(n){var t=this.x-n.x,i=this.y-n.y,n=this.z-n.z;return t*t+i*i+n*n},getPositionFromMatrix:function(n){return this.x=n.elements[12],this.y=n.elements[13],this.z=n.elements[14],this},setEulerFromRotationMatrix:function(n,t){function r(n){return Math.min(Math.max(n,-1),1)}var i=n.elements,u=i[0],e=i[4],o=i[8],s=i[1],f=i[5],h=i[9],c=i[2],l=i[6],i=i[10];return void 0===t||"XYZ"===t?(this.y=Math.asin(r(o)),.99999>Math.abs(o)?(this.x=Math.atan2(-h,i),this.z=Math.atan2(-e,u)):(this.x=Math.atan2(l,f),this.z=0)):"YXZ"===t?(this.x=Math.asin(-r(h)),.99999>Math.abs(h)?(this.y=Math.atan2(o,i),this.z=Math.atan2(s,f)):(this.y=Math.atan2(-c,u),this.z=0)):"ZXY"===t?(this.x=Math.asin(r(l)),.99999>Math.abs(l)?(this.y=Math.atan2(-c,i),this.z=Math.atan2(-e,f)):(this.y=0,this.z=Math.atan2(s,u))):"ZYX"===t?(this.y=Math.asin(-r(c)),.99999>Math.abs(c)?(this.x=Math.atan2(l,i),this.z=Math.atan2(s,u)):(this.x=0,this.z=Math.atan2(-e,f))):"YZX"===t?(this.z=Math.asin(r(s)),.99999>Math.abs(s)?(this.x=Math.atan2(-h,f),this.y=Math.atan2(-c,u)):(this.x=0,this.y=Math.atan2(o,i))):"XZY"===t&&(this.z=Math.asin(-r(e)),.99999>Math.abs(e)?(this.x=Math.atan2(l,f),this.y=Math.atan2(o,u)):(this.x=Math.atan2(-h,i),this.y=0)),this},setEulerFromQuaternion:function(n,t){function e(n){return Math.min(Math.max(n,-1),1)}var i=n.x*n.x,r=n.y*n.y,u=n.z*n.z,f=n.w*n.w;return void 0===t||"XYZ"===t?(this.x=Math.atan2(2*(n.x*n.w-n.y*n.z),f-i-r+u),this.y=Math.asin(e(2*(n.x*n.z+n.y*n.w))),this.z=Math.atan2(2*(n.z*n.w-n.x*n.y),f+i-r-u)):"YXZ"===t?(this.x=Math.asin(e(2*(n.x*n.w-n.y*n.z))),this.y=Math.atan2(2*(n.x*n.z+n.y*n.w),f-i-r+u),this.z=Math.atan2(2*(n.x*n.y+n.z*n.w),f-i+r-u)):"ZXY"===t?(this.x=Math.asin(e(2*(n.x*n.w+n.y*n.z))),this.y=Math.atan2(2*(n.y*n.w-n.z*n.x),f-i-r+u),this.z=Math.atan2(2*(n.z*n.w-n.x*n.y),f-i+r-u)):"ZYX"===t?(this.x=Math.atan2(2*(n.x*n.w+n.z*n.y),f-i-r+u),this.y=Math.asin(e(2*(n.y*n.w-n.x*n.z))),this.z=Math.atan2(2*(n.x*n.y+n.z*n.w),f+i-r-u)):"YZX"===t?(this.x=Math.atan2(2*(n.x*n.w-n.z*n.y),f-i+r-u),this.y=Math.atan2(2*(n.y*n.w-n.x*n.z),f+i-r-u),this.z=Math.asin(e(2*(n.x*n.y+n.z*n.w)))):"XZY"===t&&(this.x=Math.atan2(2*(n.x*n.w+n.y*n.z),f-i+r-u),this.y=Math.atan2(2*(n.x*n.z+n.y*n.w),f+i-r-u),this.z=Math.asin(e(2*(n.z*n.w-n.x*n.y)))),this},getScaleFromMatrix:function(n){var t=this.set(n.elements[0],n.elements[1],n.elements[2]).length(),i=this.set(n.elements[4],n.elements[5],n.elements[6]).length(),n=this.set(n.elements[8],n.elements[9],n.elements[10]).length();return this.x=t,this.y=i,this.z=n,this},equals:function(n){return n.x===this.x&&n.y===this.y&&n.z===this.z},toArray:function(){return[this.x,this.y,this.z]},clone:function(){return new THREE.Vector3(this.x,this.y,this.z)}});THREE.Vector4=function(n,t,i,r){this.x=n||0;this.y=t||0;this.z=i||0;this.w=void 0!==r?r:1};THREE.extend(THREE.Vector4.prototype,{set:function(n,t,i,r){return this.x=n,this.y=t,this.z=i,this.w=r,this},setX:function(n){return this.x=n,this},setY:function(n){return this.y=n,this},setZ:function(n){return this.z=n,this},setW:function(n){return this.w=n,this},setComponent:function(n,t){switch(n){case 0:this.x=t;break;case 1:this.y=t;break;case 2:this.z=t;break;case 3:this.w=t;break;default:throw Error("index is out of range: "+n);}},getComponent:function(n){switch(n){case 0:return this.x;case 1:return this.y;case 2:return this.z;case 3:return this.w;default:throw Error("index is out of range: "+n);}},copy:function(n){return this.x=n.x,this.y=n.y,this.z=n.z,this.w=void 0!==n.w?n.w:1,this},add:function(n,t){return void 0!==t?(console.warn("DEPRECATED: Vector4's .add() now only accepts one argument. Use .addVectors( a, b ) instead."),this.addVectors(n,t)):(this.x+=n.x,this.y+=n.y,this.z+=n.z,this.w+=n.w,this)},addScalar:function(n){return this.x+=n,this.y+=n,this.z+=n,this.w+=n,this},addVectors:function(n,t){return this.x=n.x+t.x,this.y=n.y+t.y,this.z=n.z+t.z,this.w=n.w+t.w,this},sub:function(n,t){return void 0!==t?(console.warn("DEPRECATED: Vector4's .sub() now only accepts one argument. Use .subVectors( a, b ) instead."),this.subVectors(n,t)):(this.x-=n.x,this.y-=n.y,this.z-=n.z,this.w-=n.w,this)},subVectors:function(n,t){return this.x=n.x-t.x,this.y=n.y-t.y,this.z=n.z-t.z,this.w=n.w-t.w,this},multiplyScalar:function(n){return this.x*=n,this.y*=n,this.z*=n,this.w*=n,this},applyMatrix4:function(n){var t=this.x,i=this.y,r=this.z,u=this.w,n=n.elements;return this.x=n[0]*t+n[4]*i+n[8]*r+n[12]*u,this.y=n[1]*t+n[5]*i+n[9]*r+n[13]*u,this.z=n[2]*t+n[6]*i+n[10]*r+n[14]*u,this.w=n[3]*t+n[7]*i+n[11]*r+n[15]*u,this},divideScalar:function(n){return 0!==n?(this.x/=n,this.y/=n,this.z/=n,this.w/=n):(this.z=this.y=this.x=0,this.w=1),this},setAxisAngleFromQuaternion:function(n){this.w=2*Math.acos(n.w);var t=Math.sqrt(1-n.w*n.w);return.0001>t?(this.x=1,this.z=this.y=0):(this.x=n.x/t,this.y=n.y/t,this.z=n.z/t),this},setAxisAngleFromRotationMatrix:function(n){var i,r,t,n=n.elements,o=n[0],e;t=n[4];var u=n[8],h=n[1],s=n[5],f=n[9];return(r=n[2],i=n[6],e=n[10],.01>Math.abs(t-h)&&.01>Math.abs(u-r)&&.01>Math.abs(f-i))?.1>Math.abs(t+h)&&.1>Math.abs(u+r)&&.1>Math.abs(f+i)&&.1>Math.abs(o+s+e-3)?(this.set(1,0,0,0),this):(n=Math.PI,o=(o+1)/2,s=(s+1)/2,e=(e+1)/2,t=(t+h)/4,u=(u+r)/4,f=(f+i)/4,o>s&&o>e?.01>o?(i=0,t=r=.*********):(i=Math.sqrt(o),r=t/i,t=u/i):s>e?.01>s?(i=.*********,r=0,t=.*********):(r=Math.sqrt(s),i=t/r,t=f/r):.01>e?(r=i=.*********,t=0):(t=Math.sqrt(e),i=u/t,r=f/t),this.set(i,r,t,n),this):(n=Math.sqrt((i-f)*(i-f)+(u-r)*(u-r)+(h-t)*(h-t)),.001>Math.abs(n)&&(n=1),this.x=(i-f)/n,this.y=(u-r)/n,this.z=(h-t)/n,this.w=Math.acos((o+s+e-1)/2),this)},min:function(n){return this.x>n.x&&(this.x=n.x),this.y>n.y&&(this.y=n.y),this.z>n.z&&(this.z=n.z),this.w>n.w&&(this.w=n.w),this},max:function(n){return this.x<n.x&&(this.x=n.x),this.y<n.y&&(this.y=n.y),this.z<n.z&&(this.z=n.z),this.w<n.w&&(this.w=n.w),this},clamp:function(n,t){return this.x<n.x?this.x=n.x:this.x>t.x&&(this.x=t.x),this.y<n.y?this.y=n.y:this.y>t.y&&(this.y=t.y),this.z<n.z?this.z=n.z:this.z>t.z&&(this.z=t.z),this.w<n.w?this.w=n.w:this.w>t.w&&(this.w=t.w),this},negate:function(){return this.multiplyScalar(-1)},dot:function(n){return this.x*n.x+this.y*n.y+this.z*n.z+this.w*n.w},lengthSq:function(){return this.x*this.x+this.y*this.y+this.z*this.z+this.w*this.w},length:function(){return Math.sqrt(this.x*this.x+this.y*this.y+this.z*this.z+this.w*this.w)},lengthManhattan:function(){return Math.abs(this.x)+Math.abs(this.y)+Math.abs(this.z)+Math.abs(this.w)},normalize:function(){return this.divideScalar(this.length())},setLength:function(n){var t=this.length();return 0!==t&&n!==t&&this.multiplyScalar(n/t),this},lerp:function(n,t){return this.x+=(n.x-this.x)*t,this.y+=(n.y-this.y)*t,this.z+=(n.z-this.z)*t,this.w+=(n.w-this.w)*t,this},equals:function(n){return n.x===this.x&&n.y===this.y&&n.z===this.z&&n.w===this.w},toArray:function(){return[this.x,this.y,this.z,this.w]},clone:function(){return new THREE.Vector4(this.x,this.y,this.z,this.w)}});THREE.Line3=function(n,t){this.start=void 0!==n?n:new THREE.Vector3;this.end=void 0!==t?t:new THREE.Vector3};THREE.extend(THREE.Line3.prototype,{set:function(n,t){return this.start.copy(n),this.end.copy(t),this},copy:function(n){return this.start.copy(n.start),this.end.copy(n.end),this},center:function(n){return(n||new THREE.Vector3).addVectors(this.start,this.end).multiplyScalar(.5)},delta:function(n){return(n||new THREE.Vector3).subVectors(this.end,this.start)},distanceSq:function(){return this.start.distanceToSquared(this.end)},distance:function(){return this.start.distanceTo(this.end)},at:function(n,t){var i=t||new THREE.Vector3;return this.delta(i).multiplyScalar(n).add(this.start)},closestPointToPointParameter:function(){var t=new THREE.Vector3,n=new THREE.Vector3;return function(i,r){t.subVectors(i,this.start);n.subVectors(this.end,this.start);var u=n.dot(n),u=n.dot(t)/u;return r&&(u=THREE.Math.clamp(u,0,1)),u}}(),closestPointToPoint:function(n,t,i){return n=this.closestPointToPointParameter(n,t),i=i||new THREE.Vector3,this.delta(i).multiplyScalar(n).add(this.start)},applyMatrix4:function(n){return this.start.applyMatrix4(n),this.end.applyMatrix4(n),this},equals:function(n){return n.start.equals(this.start)&&n.end.equals(this.end)},clone:function(){return(new THREE.Line3).copy(this)}});THREE.Box2=function(n,t){this.min=void 0!==n?n:new THREE.Vector2(Infinity,Infinity);this.max=void 0!==t?t:new THREE.Vector2(-Infinity,-Infinity)};THREE.extend(THREE.Box2.prototype,{set:function(n,t){return this.min.copy(n),this.max.copy(t),this},setFromPoints:function(n){var t,i,r;if(0<n.length)for(t=n[0],this.min.copy(t),this.max.copy(t),i=1,r=n.length;i<r;i++)t=n[i],t.x<this.min.x?this.min.x=t.x:t.x>this.max.x&&(this.max.x=t.x),t.y<this.min.y?this.min.y=t.y:t.y>this.max.y&&(this.max.y=t.y);else this.makeEmpty();return this},setFromCenterAndSize:function(){var n=new THREE.Vector2;return function(t,i){var r=n.copy(i).multiplyScalar(.5);return this.min.copy(t).sub(r),this.max.copy(t).add(r),this}}(),copy:function(n){return this.min.copy(n.min),this.max.copy(n.max),this},makeEmpty:function(){return this.min.x=this.min.y=Infinity,this.max.x=this.max.y=-Infinity,this},empty:function(){return this.max.x<this.min.x||this.max.y<this.min.y},center:function(n){return(n||new THREE.Vector2).addVectors(this.min,this.max).multiplyScalar(.5)},size:function(n){return(n||new THREE.Vector2).subVectors(this.max,this.min)},expandByPoint:function(n){return this.min.min(n),this.max.max(n),this},expandByVector:function(n){return this.min.sub(n),this.max.add(n),this},expandByScalar:function(n){return this.min.addScalar(-n),this.max.addScalar(n),this},containsPoint:function(n){return n.x<this.min.x||n.x>this.max.x||n.y<this.min.y||n.y>this.max.y?!1:!0},containsBox:function(n){return this.min.x<=n.min.x&&n.max.x<=this.max.x&&this.min.y<=n.min.y&&n.max.y<=this.max.y?!0:!1},getParameter:function(n){return new THREE.Vector2((n.x-this.min.x)/(this.max.x-this.min.x),(n.y-this.min.y)/(this.max.y-this.min.y))},isIntersectionBox:function(n){return n.max.x<this.min.x||n.min.x>this.max.x||n.max.y<this.min.y||n.min.y>this.max.y?!1:!0},clampPoint:function(n,t){return(t||new THREE.Vector2).copy(n).clamp(this.min,this.max)},distanceToPoint:function(){var n=new THREE.Vector2;return function(t){return n.copy(t).clamp(this.min,this.max).sub(t).length()}}(),intersect:function(n){return this.min.max(n.min),this.max.min(n.max),this},union:function(n){return this.min.min(n.min),this.max.max(n.max),this},translate:function(n){return this.min.add(n),this.max.add(n),this},equals:function(n){return n.min.equals(this.min)&&n.max.equals(this.max)},clone:function(){return(new THREE.Box2).copy(this)}});THREE.Box3=function(n,t){this.min=void 0!==n?n:new THREE.Vector3(Infinity,Infinity,Infinity);this.max=void 0!==t?t:new THREE.Vector3(-Infinity,-Infinity,-Infinity)};THREE.extend(THREE.Box3.prototype,{set:function(n,t){return this.min.copy(n),this.max.copy(t),this},setFromPoints:function(n){var t,i,r;if(0<n.length)for(t=n[0],this.min.copy(t),this.max.copy(t),i=1,r=n.length;i<r;i++)t=n[i],t.x<this.min.x?this.min.x=t.x:t.x>this.max.x&&(this.max.x=t.x),t.y<this.min.y?this.min.y=t.y:t.y>this.max.y&&(this.max.y=t.y),t.z<this.min.z?this.min.z=t.z:t.z>this.max.z&&(this.max.z=t.z);else this.makeEmpty();return this},setFromCenterAndSize:function(){var n=new THREE.Vector3;return function(t,i){var r=n.copy(i).multiplyScalar(.5);return this.min.copy(t).sub(r),this.max.copy(t).add(r),this}}(),copy:function(n){return this.min.copy(n.min),this.max.copy(n.max),this},makeEmpty:function(){return this.min.x=this.min.y=this.min.z=Infinity,this.max.x=this.max.y=this.max.z=-Infinity,this},empty:function(){return this.max.x<this.min.x||this.max.y<this.min.y||this.max.z<this.min.z},center:function(n){return(n||new THREE.Vector3).addVectors(this.min,this.max).multiplyScalar(.5)},size:function(n){return(n||new THREE.Vector3).subVectors(this.max,this.min)},expandByPoint:function(n){return this.min.min(n),this.max.max(n),this},expandByVector:function(n){return this.min.sub(n),this.max.add(n),this},expandByScalar:function(n){return this.min.addScalar(-n),this.max.addScalar(n),this},containsPoint:function(n){return n.x<this.min.x||n.x>this.max.x||n.y<this.min.y||n.y>this.max.y||n.z<this.min.z||n.z>this.max.z?!1:!0},containsBox:function(n){return this.min.x<=n.min.x&&n.max.x<=this.max.x&&this.min.y<=n.min.y&&n.max.y<=this.max.y&&this.min.z<=n.min.z&&n.max.z<=this.max.z?!0:!1},getParameter:function(n){return new THREE.Vector3((n.x-this.min.x)/(this.max.x-this.min.x),(n.y-this.min.y)/(this.max.y-this.min.y),(n.z-this.min.z)/(this.max.z-this.min.z))},isIntersectionBox:function(n){return n.max.x<this.min.x||n.min.x>this.max.x||n.max.y<this.min.y||n.min.y>this.max.y||n.max.z<this.min.z||n.min.z>this.max.z?!1:!0},clampPoint:function(n,t){return(t||new THREE.Vector3).copy(n).clamp(this.min,this.max)},distanceToPoint:function(){var n=new THREE.Vector3;return function(t){return n.copy(t).clamp(this.min,this.max).sub(t).length()}}(),getBoundingSphere:function(){var n=new THREE.Vector3;return function(t){return t=t||new THREE.Sphere,t.center=this.center(),t.radius=.5*this.size(n).length(),t}}(),intersect:function(n){return this.min.max(n.min),this.max.min(n.max),this},union:function(n){return this.min.min(n.min),this.max.max(n.max),this},applyMatrix4:function(){var n=[new THREE.Vector3,new THREE.Vector3,new THREE.Vector3,new THREE.Vector3,new THREE.Vector3,new THREE.Vector3,new THREE.Vector3,new THREE.Vector3];return function(t){return n[0].set(this.min.x,this.min.y,this.min.z).applyMatrix4(t),n[1].set(this.min.x,this.min.y,this.max.z).applyMatrix4(t),n[2].set(this.min.x,this.max.y,this.min.z).applyMatrix4(t),n[3].set(this.min.x,this.max.y,this.max.z).applyMatrix4(t),n[4].set(this.max.x,this.min.y,this.min.z).applyMatrix4(t),n[5].set(this.max.x,this.min.y,this.max.z).applyMatrix4(t),n[6].set(this.max.x,this.max.y,this.min.z).applyMatrix4(t),n[7].set(this.max.x,this.max.y,this.max.z).applyMatrix4(t),this.makeEmpty(),this.setFromPoints(n),this}}(),translate:function(n){return this.min.add(n),this.max.add(n),this},equals:function(n){return n.min.equals(this.min)&&n.max.equals(this.max)},clone:function(){return(new THREE.Box3).copy(this)}});THREE.Matrix3=function(n,t,i,r,u,f,e,o,s){this.elements=new Float32Array(9);this.set(void 0!==n?n:1,t||0,i||0,r||0,void 0!==u?u:1,f||0,e||0,o||0,void 0!==s?s:1)};THREE.extend(THREE.Matrix3.prototype,{set:function(n,t,i,r,u,f,e,o,s){var h=this.elements;return h[0]=n,h[3]=t,h[6]=i,h[1]=r,h[4]=u,h[7]=f,h[2]=e,h[5]=o,h[8]=s,this},identity:function(){return this.set(1,0,0,0,1,0,0,0,1),this},copy:function(n){return n=n.elements,this.set(n[0],n[3],n[6],n[1],n[4],n[7],n[2],n[5],n[8]),this},multiplyVector3:function(n){return console.warn("DEPRECATED: Matrix3's .multiplyVector3() has been removed. Use vector.applyMatrix3( matrix ) instead."),n.applyMatrix3(this)},multiplyVector3Array:function(){var n=new THREE.Vector3;return function(t){for(var i=0,r=t.length;i<r;i+=3)n.x=t[i],n.y=t[i+1],n.z=t[i+2],n.applyMatrix3(this),t[i]=n.x,t[i+1]=n.y,t[i+2]=n.z;return t}}(),multiplyScalar:function(n){var t=this.elements;return t[0]*=n,t[3]*=n,t[6]*=n,t[1]*=n,t[4]*=n,t[7]*=n,t[2]*=n,t[5]*=n,t[8]*=n,this},determinant:function(){var n=this.elements,t=n[0],i=n[1],r=n[2],u=n[3],f=n[4],e=n[5],o=n[6],s=n[7],n=n[8];return t*f*n-t*e*s-i*u*n+i*e*o+r*u*s-r*f*o},getInverse:function(n,t){var i=n.elements,r=this.elements;if(r[0]=i[10]*i[5]-i[6]*i[9],r[1]=-i[10]*i[1]+i[2]*i[9],r[2]=i[6]*i[1]-i[2]*i[5],r[3]=-i[10]*i[4]+i[6]*i[8],r[4]=i[10]*i[0]-i[2]*i[8],r[5]=-i[6]*i[0]+i[2]*i[4],r[6]=i[9]*i[4]-i[5]*i[8],r[7]=-i[9]*i[0]+i[1]*i[8],r[8]=i[5]*i[0]-i[1]*i[4],i=i[0]*r[0]+i[1]*r[3]+i[2]*r[6],0===i){if(t)throw Error("Matrix3.getInverse(): can't invert matrix, determinant is 0");return console.warn("Matrix3.getInverse(): can't invert matrix, determinant is 0"),this.identity(),this}return this.multiplyScalar(1/i),this},transpose:function(){var t,n=this.elements;return t=n[1],n[1]=n[3],n[3]=t,t=n[2],n[2]=n[6],n[6]=t,t=n[5],n[5]=n[7],n[7]=t,this},getNormalMatrix:function(n){return this.getInverse(n).transpose(),this},transposeIntoArray:function(n){var t=this.elements;return n[0]=t[0],n[1]=t[3],n[2]=t[6],n[3]=t[1],n[4]=t[4],n[5]=t[7],n[6]=t[2],n[7]=t[5],n[8]=t[8],this},clone:function(){var n=this.elements;return new THREE.Matrix3(n[0],n[3],n[6],n[1],n[4],n[7],n[2],n[5],n[8])}});THREE.Matrix4=function(n,t,i,r,u,f,e,o,s,h,c,l,a,v,y,p){var w=this.elements=new Float32Array(16);w[0]=void 0!==n?n:1;w[4]=t||0;w[8]=i||0;w[12]=r||0;w[1]=u||0;w[5]=void 0!==f?f:1;w[9]=e||0;w[13]=o||0;w[2]=s||0;w[6]=h||0;w[10]=void 0!==c?c:1;w[14]=l||0;w[3]=a||0;w[7]=v||0;w[11]=y||0;w[15]=void 0!==p?p:1};THREE.extend(THREE.Matrix4.prototype,{set:function(n,t,i,r,u,f,e,o,s,h,c,l,a,v,y,p){var w=this.elements;return w[0]=n,w[4]=t,w[8]=i,w[12]=r,w[1]=u,w[5]=f,w[9]=e,w[13]=o,w[2]=s,w[6]=h,w[10]=c,w[14]=l,w[3]=a,w[7]=v,w[11]=y,w[15]=p,this},identity:function(){return this.set(1,0,0,0,0,1,0,0,0,0,1,0,0,0,0,1),this},copy:function(n){return n=n.elements,this.set(n[0],n[4],n[8],n[12],n[1],n[5],n[9],n[13],n[2],n[6],n[10],n[14],n[3],n[7],n[11],n[15]),this},setRotationFromEuler:function(n,t){var i=this.elements,r=n.x,u=n.y,f=n.z,e=Math.cos(r),r=Math.sin(r),o=Math.cos(u),u=Math.sin(u),s=Math.cos(f),f=Math.sin(f);if(void 0===t||"XYZ"===t){var h=e*s,c=e*f,l=r*s,a=r*f;i[0]=o*s;i[4]=-o*f;i[8]=u;i[1]=c+l*u;i[5]=h-a*u;i[9]=-r*o;i[2]=a-h*u;i[6]=l+c*u;i[10]=e*o}else"YXZ"===t?(h=o*s,c=o*f,l=u*s,a=u*f,i[0]=h+a*r,i[4]=l*r-c,i[8]=e*u,i[1]=e*f,i[5]=e*s,i[9]=-r,i[2]=c*r-l,i[6]=a+h*r,i[10]=e*o):"ZXY"===t?(h=o*s,c=o*f,l=u*s,a=u*f,i[0]=h-a*r,i[4]=-e*f,i[8]=l+c*r,i[1]=c+l*r,i[5]=e*s,i[9]=a-h*r,i[2]=-e*u,i[6]=r,i[10]=e*o):"ZYX"===t?(h=e*s,c=e*f,l=r*s,a=r*f,i[0]=o*s,i[4]=l*u-c,i[8]=h*u+a,i[1]=o*f,i[5]=a*u+h,i[9]=c*u-l,i[2]=-u,i[6]=r*o,i[10]=e*o):"YZX"===t?(h=e*o,c=e*u,l=r*o,a=r*u,i[0]=o*s,i[4]=a-h*f,i[8]=l*f+c,i[1]=f,i[5]=e*s,i[9]=-r*s,i[2]=-u*s,i[6]=c*f+l,i[10]=h-a*f):"XZY"===t&&(h=e*o,c=e*u,l=r*o,a=r*u,i[0]=o*s,i[4]=-f,i[8]=u*s,i[1]=h*f+a,i[5]=e*s,i[9]=c*f-l,i[2]=l*f-c,i[6]=r*s,i[10]=a*f+h);return this},setRotationFromQuaternion:function(n){var t=this.elements,i=n.x,r=n.y,u=n.z,f=n.w,o=i+i,e=r+r,s=u+u,n=i*o,h=i*e,i=i*s,c=r*e,r=r*s,u=u*s,o=f*o,e=f*e,f=f*s;return t[0]=1-(c+u),t[4]=h-f,t[8]=i+e,t[1]=h+f,t[5]=1-(n+u),t[9]=r-o,t[2]=i-e,t[6]=r+o,t[10]=1-(n+c),this},lookAt:function(){var t=new THREE.Vector3,i=new THREE.Vector3,n=new THREE.Vector3;return function(r,u,f){var e=this.elements;return n.subVectors(r,u).normalize(),0===n.length()&&(n.z=1),t.crossVectors(f,n).normalize(),0===t.length()&&(n.x+=.0001,t.crossVectors(f,n).normalize()),i.crossVectors(n,t),e[0]=t.x,e[4]=i.x,e[8]=n.x,e[1]=t.y,e[5]=i.y,e[9]=n.y,e[2]=t.z,e[6]=i.z,e[10]=n.z,this}}(),multiply:function(n,t){return void 0!==t?(console.warn("DEPRECATED: Matrix4's .multiply() now only accepts one argument. Use .multiplyMatrices( a, b ) instead."),this.multiplyMatrices(n,t)):this.multiplyMatrices(this,n)},multiplyMatrices:function(n,t){var i=n.elements,r=t.elements,u=this.elements,f=i[0],e=i[4],o=i[8],s=i[12],h=i[1],c=i[5],l=i[9],a=i[13],v=i[2],y=i[6],p=i[10],w=i[14],b=i[3],k=i[7],d=i[11],i=i[15],g=r[0],nt=r[4],tt=r[8],it=r[12],rt=r[1],ut=r[5],ft=r[9],et=r[13],ot=r[2],st=r[6],ht=r[10],ct=r[14],lt=r[3],at=r[7],vt=r[11],r=r[15];return u[0]=f*g+e*rt+o*ot+s*lt,u[4]=f*nt+e*ut+o*st+s*at,u[8]=f*tt+e*ft+o*ht+s*vt,u[12]=f*it+e*et+o*ct+s*r,u[1]=h*g+c*rt+l*ot+a*lt,u[5]=h*nt+c*ut+l*st+a*at,u[9]=h*tt+c*ft+l*ht+a*vt,u[13]=h*it+c*et+l*ct+a*r,u[2]=v*g+y*rt+p*ot+w*lt,u[6]=v*nt+y*ut+p*st+w*at,u[10]=v*tt+y*ft+p*ht+w*vt,u[14]=v*it+y*et+p*ct+w*r,u[3]=b*g+k*rt+d*ot+i*lt,u[7]=b*nt+k*ut+d*st+i*at,u[11]=b*tt+k*ft+d*ht+i*vt,u[15]=b*it+k*et+d*ct+i*r,this},multiplyToArray:function(n,t,i){var r=this.elements;return this.multiplyMatrices(n,t),i[0]=r[0],i[1]=r[1],i[2]=r[2],i[3]=r[3],i[4]=r[4],i[5]=r[5],i[6]=r[6],i[7]=r[7],i[8]=r[8],i[9]=r[9],i[10]=r[10],i[11]=r[11],i[12]=r[12],i[13]=r[13],i[14]=r[14],i[15]=r[15],this},multiplyScalar:function(n){var t=this.elements;return t[0]*=n,t[4]*=n,t[8]*=n,t[12]*=n,t[1]*=n,t[5]*=n,t[9]*=n,t[13]*=n,t[2]*=n,t[6]*=n,t[10]*=n,t[14]*=n,t[3]*=n,t[7]*=n,t[11]*=n,t[15]*=n,this},multiplyVector3:function(n){return console.warn("DEPRECATED: Matrix4's .multiplyVector3() has been removed. Use vector.applyMatrix4( matrix ) or vector.applyProjection( matrix ) instead."),n.applyProjection(this)},multiplyVector4:function(n){return console.warn("DEPRECATED: Matrix4's .multiplyVector4() has been removed. Use vector.applyMatrix4( matrix ) instead."),n.applyMatrix4(this)},multiplyVector3Array:function(){var n=new THREE.Vector3;return function(t){for(var i=0,r=t.length;i<r;i+=3)n.x=t[i],n.y=t[i+1],n.z=t[i+2],n.applyProjection(this),t[i]=n.x,t[i+1]=n.y,t[i+2]=n.z;return t}}(),rotateAxis:function(n){var t=this.elements,i=n.x,r=n.y,u=n.z;return n.x=i*t[0]+r*t[4]+u*t[8],n.y=i*t[1]+r*t[5]+u*t[9],n.z=i*t[2]+r*t[6]+u*t[10],n.normalize(),n},crossVector:function(n){var t=this.elements,i=new THREE.Vector4;return i.x=t[0]*n.x+t[4]*n.y+t[8]*n.z+t[12]*n.w,i.y=t[1]*n.x+t[5]*n.y+t[9]*n.z+t[13]*n.w,i.z=t[2]*n.x+t[6]*n.y+t[10]*n.z+t[14]*n.w,i.w=n.w?t[3]*n.x+t[7]*n.y+t[11]*n.z+t[15]*n.w:1,i},determinant:function(){var n=this.elements,t=n[0],i=n[4],r=n[8],u=n[12],f=n[1],e=n[5],o=n[9],s=n[13],h=n[2],c=n[6],l=n[10],a=n[14];return n[3]*(+u*o*c-r*s*c-u*e*l+i*s*l+r*e*a-i*o*a)+n[7]*(+t*o*a-t*s*l+u*f*l-r*f*a+r*s*h-u*o*h)+n[11]*(+t*s*c-t*e*a-u*f*c+i*f*a+u*e*h-i*s*h)+n[15]*(-r*e*h-t*o*c+t*e*l+r*f*c-i*f*l+i*o*h)},transpose:function(){var n=this.elements,t;return t=n[1],n[1]=n[4],n[4]=t,t=n[2],n[2]=n[8],n[8]=t,t=n[6],n[6]=n[9],n[9]=t,t=n[3],n[3]=n[12],n[12]=t,t=n[7],n[7]=n[13],n[13]=t,t=n[11],n[11]=n[14],n[14]=t,this},flattenToArray:function(n){var t=this.elements;return n[0]=t[0],n[1]=t[1],n[2]=t[2],n[3]=t[3],n[4]=t[4],n[5]=t[5],n[6]=t[6],n[7]=t[7],n[8]=t[8],n[9]=t[9],n[10]=t[10],n[11]=t[11],n[12]=t[12],n[13]=t[13],n[14]=t[14],n[15]=t[15],n},flattenToArrayOffset:function(n,t){var i=this.elements;return n[t]=i[0],n[t+1]=i[1],n[t+2]=i[2],n[t+3]=i[3],n[t+4]=i[4],n[t+5]=i[5],n[t+6]=i[6],n[t+7]=i[7],n[t+8]=i[8],n[t+9]=i[9],n[t+10]=i[10],n[t+11]=i[11],n[t+12]=i[12],n[t+13]=i[13],n[t+14]=i[14],n[t+15]=i[15],n},getPosition:function(){var n=new THREE.Vector3;return function(){console.warn("DEPRECATED: Matrix4's .getPosition() has been removed. Use Vector3.getPositionFromMatrix( matrix ) instead.");var t=this.elements;return n.set(t[12],t[13],t[14])}}(),setPosition:function(n){var t=this.elements;return t[12]=n.x,t[13]=n.y,t[14]=n.z,this},getInverse:function(n,t){var i=this.elements,r=n.elements,u=r[0],f=r[4],e=r[8],o=r[12],s=r[1],h=r[5],c=r[9],l=r[13],a=r[2],v=r[6],y=r[10],p=r[14],w=r[3],b=r[7],k=r[11],d=r[15];if(i[0]=c*p*b-l*y*b+l*v*k-h*p*k-c*v*d+h*y*d,i[4]=o*y*b-e*p*b-o*v*k+f*p*k+e*v*d-f*y*d,i[8]=e*l*b-o*c*b+o*h*k-f*l*k-e*h*d+f*c*d,i[12]=o*c*v-e*l*v-o*h*y+f*l*y+e*h*p-f*c*p,i[1]=l*y*w-c*p*w-l*a*k+s*p*k+c*a*d-s*y*d,i[5]=e*p*w-o*y*w+o*a*k-u*p*k-e*a*d+u*y*d,i[9]=o*c*w-e*l*w-o*s*k+u*l*k+e*s*d-u*c*d,i[13]=e*l*a-o*c*a+o*s*y-u*l*y-e*s*p+u*c*p,i[2]=h*p*w-l*v*w+l*a*b-s*p*b-h*a*d+s*v*d,i[6]=o*v*w-f*p*w-o*a*b+u*p*b+f*a*d-u*v*d,i[10]=f*l*w-o*h*w+o*s*b-u*l*b-f*s*d+u*h*d,i[14]=o*h*a-f*l*a-o*s*v+u*l*v+f*s*p-u*h*p,i[3]=c*v*w-h*y*w-c*a*b+s*y*b+h*a*k-s*v*k,i[7]=f*y*w-e*v*w+e*a*b-u*y*b-f*a*k+u*v*k,i[11]=e*h*w-f*c*w-e*s*b+u*c*b+f*s*k-u*h*k,i[15]=f*c*a-e*h*a+e*s*v-u*c*v-f*s*y+u*h*y,i=r[0]*i[0]+r[1]*i[4]+r[2]*i[8]+r[3]*i[12],0==i){if(t)throw Error("Matrix4.getInverse(): can't invert matrix, determinant is 0");return console.warn("Matrix4.getInverse(): can't invert matrix, determinant is 0"),this.identity(),this}return this.multiplyScalar(1/i),this},compose:function(){var n=new THREE.Matrix4,t=new THREE.Matrix4;return function(i,r,u){var f=this.elements;return n.identity(),n.setRotationFromQuaternion(r),t.makeScale(u.x,u.y,u.z),this.multiplyMatrices(n,t),f[12]=i.x,f[13]=i.y,f[14]=i.z,this}}(),decompose:function(){var t=new THREE.Vector3,i=new THREE.Vector3,r=new THREE.Vector3,n=new THREE.Matrix4;return function(u,f,e){var o=this.elements;return t.set(o[0],o[1],o[2]),i.set(o[4],o[5],o[6]),r.set(o[8],o[9],o[10]),u=u instanceof THREE.Vector3?u:new THREE.Vector3,f=f instanceof THREE.Quaternion?f:new THREE.Quaternion,e=e instanceof THREE.Vector3?e:new THREE.Vector3,e.x=t.length(),e.y=i.length(),e.z=r.length(),u.x=o[12],u.y=o[13],u.z=o[14],n.copy(this),n.elements[0]/=e.x,n.elements[1]/=e.x,n.elements[2]/=e.x,n.elements[4]/=e.y,n.elements[5]/=e.y,n.elements[6]/=e.y,n.elements[8]/=e.z,n.elements[9]/=e.z,n.elements[10]/=e.z,f.setFromRotationMatrix(n),[u,f,e]}}(),extractPosition:function(n){var t=this.elements,n=n.elements;return t[12]=n[12],t[13]=n[13],t[14]=n[14],this},extractRotation:function(){var n=new THREE.Vector3;return function(t){var i=this.elements,t=t.elements,r=1/n.set(t[0],t[1],t[2]).length(),u=1/n.set(t[4],t[5],t[6]).length(),f=1/n.set(t[8],t[9],t[10]).length();return i[0]=t[0]*r,i[1]=t[1]*r,i[2]=t[2]*r,i[4]=t[4]*u,i[5]=t[5]*u,i[6]=t[6]*u,i[8]=t[8]*f,i[9]=t[9]*f,i[10]=t[10]*f,this}}(),translate:function(n){var t=this.elements,i=n.x,r=n.y,n=n.z;return t[12]=t[0]*i+t[4]*r+t[8]*n+t[12],t[13]=t[1]*i+t[5]*r+t[9]*n+t[13],t[14]=t[2]*i+t[6]*r+t[10]*n+t[14],t[15]=t[3]*i+t[7]*r+t[11]*n+t[15],this},rotateX:function(n){var t=this.elements,r=t[4],u=t[5],f=t[6],e=t[7],o=t[8],s=t[9],h=t[10],c=t[11],i=Math.cos(n),n=Math.sin(n);return t[4]=i*r+n*o,t[5]=i*u+n*s,t[6]=i*f+n*h,t[7]=i*e+n*c,t[8]=i*o-n*r,t[9]=i*s-n*u,t[10]=i*h-n*f,t[11]=i*c-n*e,this},rotateY:function(n){var t=this.elements,r=t[0],u=t[1],f=t[2],e=t[3],o=t[8],s=t[9],h=t[10],c=t[11],i=Math.cos(n),n=Math.sin(n);return t[0]=i*r-n*o,t[1]=i*u-n*s,t[2]=i*f-n*h,t[3]=i*e-n*c,t[8]=i*o+n*r,t[9]=i*s+n*u,t[10]=i*h+n*f,t[11]=i*c+n*e,this},rotateZ:function(n){var t=this.elements,r=t[0],u=t[1],f=t[2],e=t[3],o=t[4],s=t[5],h=t[6],c=t[7],i=Math.cos(n),n=Math.sin(n);return t[0]=i*r+n*o,t[1]=i*u+n*s,t[2]=i*f+n*h,t[3]=i*e+n*c,t[4]=i*o-n*r,t[5]=i*s-n*u,t[6]=i*h-n*f,t[7]=i*c-n*e,this},rotateByAxis:function(n,t){var i=this.elements;if(1===n.x&&0===n.y&&0===n.z)return this.rotateX(t);if(0===n.x&&1===n.y&&0===n.z)return this.rotateY(t);if(0===n.x&&0===n.y&&1===n.z)return this.rotateZ(t);var r=n.x,u=n.y,f=n.z,e=Math.sqrt(r*r+u*u+f*f),r=r/e,u=u/e,f=f/e,e=r*r,c=u*u,l=f*f,h=Math.cos(t),o=Math.sin(t),s=1-h,a=r*u*s,v=r*f*s,s=u*f*s,r=r*o,y=u*o,o=f*o,f=e+(1-e)*h,e=a+o,u=v-y,a=a-o,c=c+(1-c)*h,o=s+r,v=v+y,s=s-r,l=l+(1-l)*h,h=i[0],r=i[1],y=i[2],p=i[3],w=i[4],b=i[5],k=i[6],d=i[7],g=i[8],nt=i[9],tt=i[10],it=i[11];return i[0]=f*h+e*w+u*g,i[1]=f*r+e*b+u*nt,i[2]=f*y+e*k+u*tt,i[3]=f*p+e*d+u*it,i[4]=a*h+c*w+o*g,i[5]=a*r+c*b+o*nt,i[6]=a*y+c*k+o*tt,i[7]=a*p+c*d+o*it,i[8]=v*h+s*w+l*g,i[9]=v*r+s*b+l*nt,i[10]=v*y+s*k+l*tt,i[11]=v*p+s*d+l*it,this},scale:function(n){var t=this.elements,i=n.x,r=n.y,n=n.z;return t[0]*=i,t[4]*=r,t[8]*=n,t[1]*=i,t[5]*=r,t[9]*=n,t[2]*=i,t[6]*=r,t[10]*=n,t[3]*=i,t[7]*=r,t[11]*=n,this},getMaxScaleOnAxis:function(){var n=this.elements;return Math.sqrt(Math.max(n[0]*n[0]+n[1]*n[1]+n[2]*n[2],Math.max(n[4]*n[4]+n[5]*n[5]+n[6]*n[6],n[8]*n[8]+n[9]*n[9]+n[10]*n[10])))},makeTranslation:function(n,t,i){return this.set(1,0,0,n,0,1,0,t,0,0,1,i,0,0,0,1),this},makeRotationX:function(n){var t=Math.cos(n),n=Math.sin(n);return this.set(1,0,0,0,0,t,-n,0,0,n,t,0,0,0,0,1),this},makeRotationY:function(n){var t=Math.cos(n),n=Math.sin(n);return this.set(t,0,n,0,0,1,0,0,-n,0,t,0,0,0,0,1),this},makeRotationZ:function(n){var t=Math.cos(n),n=Math.sin(n);return this.set(t,-n,0,0,n,t,0,0,0,0,1,0,0,0,0,1),this},makeRotationAxis:function(n,t){var e=Math.cos(t),r=Math.sin(t),s=1-e,o=n.x,u=n.y,i=n.z,f=s*o,h=s*u;return this.set(f*o+e,f*u-r*i,f*i+r*u,0,f*u+r*i,h*u+e,h*i-r*o,0,f*i-r*u,h*i+r*o,s*i*i+e,0,0,0,0,1),this},makeScale:function(n,t,i){return this.set(n,0,0,0,0,t,0,0,0,0,i,0,0,0,0,1),this},makeFrustum:function(n,t,i,r,u,f){var e=this.elements;return e[0]=2*u/(t-n),e[4]=0,e[8]=(t+n)/(t-n),e[12]=0,e[1]=0,e[5]=2*u/(r-i),e[9]=(r+i)/(r-i),e[13]=0,e[2]=0,e[6]=0,e[10]=-(f+u)/(f-u),e[14]=-2*f*u/(f-u),e[3]=0,e[7]=0,e[11]=-1,e[15]=0,this},makePerspective:function(n,t,i,r){var n=i*Math.tan(THREE.Math.degToRad(.5*n)),u=-n;return this.makeFrustum(u*t,n*t,u,n,i,r)},makeOrthographic:function(n,t,i,r,u,f){var e=this.elements,o=t-n,s=i-r,h=f-u;return e[0]=2/o,e[4]=0,e[8]=0,e[12]=-((t+n)/o),e[1]=0,e[5]=2/s,e[9]=0,e[13]=-((i+r)/s),e[2]=0,e[6]=0,e[10]=-2/h,e[14]=-((f+u)/h),e[3]=0,e[7]=0,e[11]=0,e[15]=1,this},clone:function(){var n=this.elements;return new THREE.Matrix4(n[0],n[4],n[8],n[12],n[1],n[5],n[9],n[13],n[2],n[6],n[10],n[14],n[3],n[7],n[11],n[15])}});THREE.Ray=function(n,t){this.origin=void 0!==n?n:new THREE.Vector3;this.direction=void 0!==t?t:new THREE.Vector3};THREE.extend(THREE.Ray.prototype,{set:function(n,t){return this.origin.copy(n),this.direction.copy(t),this},copy:function(n){return this.origin.copy(n.origin),this.direction.copy(n.direction),this},at:function(n,t){return(t||new THREE.Vector3).copy(this.direction).multiplyScalar(n).add(this.origin)},recast:function(){var n=new THREE.Vector3;return function(t){return this.origin.copy(this.at(t,n)),this}}(),closestPointToPoint:function(n,t){var i=t||new THREE.Vector3,r;return i.subVectors(n,this.origin),r=i.dot(this.direction),i.copy(this.direction).multiplyScalar(r).add(this.origin)},distanceToPoint:function(){var n=new THREE.Vector3;return function(t){var i=n.subVectors(t,this.origin).dot(this.direction);return n.copy(this.direction).multiplyScalar(i).add(this.origin),n.distanceTo(t)}}(),isIntersectionSphere:function(n){return this.distanceToPoint(n.center)<=n.radius},isIntersectionPlane:function(n){return 0!=n.normal.dot(this.direction)||0==n.distanceToPoint(this.origin)?!0:!1},distanceToPlane:function(n){var t=n.normal.dot(this.direction);if(0==t){if(0==n.distanceToPoint(this.origin))return 0}else return-(this.origin.dot(n.normal)+n.constant)/t},intersectPlane:function(n,t){var i=this.distanceToPlane(n);if(void 0!==i)return this.at(i,t)},applyMatrix4:function(n){return this.direction.add(this.origin).applyMatrix4(n),this.origin.applyMatrix4(n),this.direction.sub(this.origin),this},equals:function(n){return n.origin.equals(this.origin)&&n.direction.equals(this.direction)},clone:function(){return(new THREE.Ray).copy(this)}});THREE.Sphere=function(n,t){this.center=void 0!==n?n:new THREE.Vector3;this.radius=void 0!==t?t:0};THREE.extend(THREE.Sphere.prototype,{set:function(n,t){return this.center.copy(n),this.radius=t,this},setFromCenterAndPoints:function(n,t){for(var u,i=0,r=0,f=t.length;r<f;r++)u=n.distanceToSquared(t[r]),i=Math.max(i,u);return this.center=n,this.radius=Math.sqrt(i),this},copy:function(n){return this.center.copy(n.center),this.radius=n.radius,this},empty:function(){return 0>=this.radius},containsPoint:function(n){return n.distanceToSquared(this.center)<=this.radius*this.radius},distanceToPoint:function(n){return n.distanceTo(this.center)-this.radius},intersectsSphere:function(n){var t=this.radius+n.radius;return n.center.distanceToSquared(this.center)<=t*t},clampPoint:function(n,t){var r=this.center.distanceToSquared(n),i=t||new THREE.Vector3;return i.copy(n),r>this.radius*this.radius&&(i.sub(this.center).normalize(),i.multiplyScalar(this.radius).add(this.center)),i},getBoundingBox:function(n){return n=n||new THREE.Box3,n.set(this.center,this.center),n.expandByScalar(this.radius),n},applyMatrix4:function(n){return this.center.applyMatrix4(n),this.radius*=n.getMaxScaleOnAxis(),this},translate:function(n){return this.center.add(n),this},equals:function(n){return n.center.equals(this.center)&&n.radius===this.radius},clone:function(){return(new THREE.Sphere).copy(this)}});THREE.Frustum=function(n,t,i,r,u,f){this.planes=[void 0!==n?n:new THREE.Plane,void 0!==t?t:new THREE.Plane,void 0!==i?i:new THREE.Plane,void 0!==r?r:new THREE.Plane,void 0!==u?u:new THREE.Plane,void 0!==f?f:new THREE.Plane]};THREE.extend(THREE.Frustum.prototype,{set:function(n,t,i,r,u,f){var e=this.planes;return e[0].copy(n),e[1].copy(t),e[2].copy(i),e[3].copy(r),e[4].copy(u),e[5].copy(f),this},copy:function(n){for(var i=this.planes,t=0;6>t;t++)i[t].copy(n.planes[t]);return this},setFromMatrix:function(n){var i=this.planes,t=n.elements,n=t[0],e=t[1],o=t[2],r=t[3],s=t[4],h=t[5],c=t[6],u=t[7],l=t[8],a=t[9],v=t[10],f=t[11],y=t[12],p=t[13],w=t[14],t=t[15];return i[0].setComponents(r-n,u-s,f-l,t-y).normalize(),i[1].setComponents(r+n,u+s,f+l,t+y).normalize(),i[2].setComponents(r+e,u+h,f+a,t+p).normalize(),i[3].setComponents(r-e,u-h,f-a,t-p).normalize(),i[4].setComponents(r-o,u-c,f-v,t-w).normalize(),i[5].setComponents(r+o,u+c,f+v,t+w).normalize(),this},intersectsObject:function(){var n=new THREE.Vector3;return function(t){var i=t.matrixWorld,r=this.planes,t=-t.geometry.boundingSphere.radius*i.getMaxScaleOnAxis();for(n.getPositionFromMatrix(i),i=0;6>i;i++)if(r[i].distanceToPoint(n)<t)return!1;return!0}}(),intersectsSphere:function(n){for(var i=this.planes,r=n.center,n=-n.radius,t=0;6>t;t++)if(i[t].distanceToPoint(r)<n)return!1;return!0},containsPoint:function(n){for(var i=this.planes,t=0;6>t;t++)if(0>i[t].distanceToPoint(n))return!1;return!0},clone:function(){return(new THREE.Frustum).copy(this)}});THREE.Plane=function(n,t){this.normal=void 0!==n?n:new THREE.Vector3(1,0,0);this.constant=void 0!==t?t:0};THREE.extend(THREE.Plane.prototype,{set:function(n,t){return this.normal.copy(n),this.constant=t,this},setComponents:function(n,t,i,r){return this.normal.set(n,t,i),this.constant=r,this},setFromNormalAndCoplanarPoint:function(n,t){return this.normal.copy(n),this.constant=-t.dot(this.normal),this},setFromCoplanarPoints:function(){var n=new THREE.Vector3,t=new THREE.Vector3;return function(i,r,u){return r=n.subVectors(u,r).cross(t.subVectors(i,r)).normalize(),this.setFromNormalAndCoplanarPoint(r,i),this}}(),copy:function(n){return this.normal.copy(n.normal),this.constant=n.constant,this},normalize:function(){var n=1/this.normal.length();return this.normal.multiplyScalar(n),this.constant*=n,this},negate:function(){return this.constant*=-1,this.normal.negate(),this},distanceToPoint:function(n){return this.normal.dot(n)+this.constant},distanceToSphere:function(n){return this.distanceToPoint(n.center)-n.radius},projectPoint:function(n,t){return this.orthoPoint(n,t).sub(n).negate()},orthoPoint:function(n,t){var i=this.distanceToPoint(n);return(t||new THREE.Vector3).copy(this.normal).multiplyScalar(i)},isIntersectionLine:function(n){var t=this.distanceToPoint(n.start),n=this.distanceToPoint(n.end);return 0>t&&0<n||0>n&&0<t},intersectLine:function(){var n=new THREE.Vector3;return function(t,i){var u=i||new THREE.Vector3,f=t.delta(n),r=this.normal.dot(f);if(0==r){if(0==this.distanceToPoint(t.start))return u.copy(t.start)}else return r=-(t.start.dot(this.normal)+this.constant)/r,0>r||1<r?void 0:u.copy(f).multiplyScalar(r).add(t.start)}}(),coplanarPoint:function(n){return(n||new THREE.Vector3).copy(this.normal).multiplyScalar(-this.constant)},applyMatrix4:function(){var n=new THREE.Vector3,t=new THREE.Vector3;return function(i,r){var r=r||(new THREE.Matrix3).getInverse(i).transpose(),f=n.copy(this.normal).applyMatrix3(r),u=this.coplanarPoint(t);return u.applyMatrix4(i),this.setFromNormalAndCoplanarPoint(f,u),this}}(),translate:function(n){return this.constant-=n.dot(this.normal),this},equals:function(n){return n.normal.equals(this.normal)&&n.constant==this.constant},clone:function(){return(new THREE.Plane).copy(this)}});THREE.Math={clamp:function(n,t,i){return n<t?t:n>i?i:n},clampBottom:function(n,t){return n<t?t:n},mapLinear:function(n,t,i,r,u){return r+(n-t)*(u-r)/(i-t)},smoothstep:function(n,t,i){return n<=t?0:n>=i?1:(n=(n-t)/(i-t),n*n*(3-2*n))},smootherstep:function(n,t,i){return n<=t?0:n>=i?1:(n=(n-t)/(i-t),n*n*n*(n*(6*n-15)+10))},random16:function(){return(65280*Math.random()+255*Math.random())/65535},randInt:function(n,t){return n+Math.floor(Math.random()*(t-n+1))},randFloat:function(n,t){return n+Math.random()*(t-n)},randFloatSpread:function(n){return n*(.5-Math.random())},sign:function(n){return 0>n?-1:0<n?1:0},degToRad:function(){var n=Math.PI/180;return function(t){return t*n}}(),radToDeg:function(){var n=180/Math.PI;return function(t){return t*n}}()};THREE.Spline=function(n){function l(n,t,i,r,u,f,e){return n=.5*(i-n),r=.5*(r-t),(2*(t-i)+n+r)*e+(-3*(t-i)-2*n-r)*f+n*u+t}this.points=n;var i=[],f={x:0,y:0,z:0},a,t,r,u,e,o,s,h,c;this.initFromArray=function(n){this.points=[];for(var t=0;t<n.length;t++)this.points[t]={x:n[t][0],y:n[t][1],z:n[t][2]}};this.getPoint=function(n){return a=(this.points.length-1)*n,t=Math.floor(a),r=a-t,i[0]=0===t?t:t-1,i[1]=t,i[2]=t>this.points.length-2?this.points.length-1:t+1,i[3]=t>this.points.length-3?this.points.length-1:t+2,o=this.points[i[0]],s=this.points[i[1]],h=this.points[i[2]],c=this.points[i[3]],u=r*r,e=r*u,f.x=l(o.x,s.x,h.x,c.x,r,u,e),f.y=l(o.y,s.y,h.y,c.y,r,u,e),f.z=l(o.z,s.z,h.z,c.z,r,u,e),f};this.getControlPointsArray=function(){for(var t,r=this.points.length,i=[],n=0;n<r;n++)t=this.points[n],i[n]=[t.x,t.y,t.z];return i};this.getLength=function(n){var t,u,f,o=t=t=0,e=new THREE.Vector3,s=new THREE.Vector3,i=[],r=0;for(i[0]=0,n||(n=100),u=this.points.length*n,e.copy(this.points[0]),n=1;n<u;n++)t=n/u,f=this.getPoint(t),s.copy(f),r+=s.distanceTo(e),e.copy(f),t*=this.points.length-1,t=Math.floor(t),t!=o&&(i[t]=r,o=t);return i[i.length]=r,{chunks:i,total:r}};this.reparametrizeByArcLength=function(n){var t,i,r,f,h,e,u=[],o=new THREE.Vector3,s=this.getLength();for(u.push(o.copy(this.points[0]).clone()),t=1;t<this.points.length;t++){for(i=s.chunks[t]-s.chunks[t-1],e=Math.ceil(n*i/s.total),f=(t-1)/(this.points.length-1),h=t/(this.points.length-1),i=1;i<e-1;i++)r=f+i*(1/e)*(h-f),r=this.getPoint(r),u.push(o.copy(r).clone());u.push(o.copy(this.points[t]).clone())}this.points=u}};THREE.Triangle=function(n,t,i){this.a=void 0!==n?n:new THREE.Vector3;this.b=void 0!==t?t:new THREE.Vector3;this.c=void 0!==i?i:new THREE.Vector3};THREE.Triangle.normal=function(){var n=new THREE.Vector3;return function(t,i,r,u){return u=u||new THREE.Vector3,u.subVectors(r,i),n.subVectors(t,i),u.cross(n),t=u.lengthSq(),0<t?u.multiplyScalar(1/Math.sqrt(t)):u.set(0,0,0)}}();THREE.Triangle.barycoordFromPoint=function(){var n=new THREE.Vector3,t=new THREE.Vector3,i=new THREE.Vector3;return function(r,u,f,e,o){n.subVectors(e,u);t.subVectors(f,u);i.subVectors(r,u);var r=n.dot(n),u=n.dot(t),f=n.dot(i),s=t.dot(t),e=t.dot(i),h=r*s-u*u,o=o||new THREE.Vector3;return 0==h?o.set(-2,-1,-1):(h=1/h,s=(s*f-u*e)*h,r=(r*e-u*f)*h,o.set(1-s-r,r,s))}}();THREE.Triangle.containsPoint=function(){var n=new THREE.Vector3;return function(t,i,r,u){return t=THREE.Triangle.barycoordFromPoint(t,i,r,u,n),0<=t.x&&0<=t.y&&1>=t.x+t.y}}();THREE.extend(THREE.Triangle.prototype,{constructor:THREE.Triangle,set:function(n,t,i){return this.a.copy(n),this.b.copy(t),this.c.copy(i),this},setFromPointsAndIndices:function(n,t,i,r){return this.a.copy(n[t]),this.b.copy(n[i]),this.c.copy(n[r]),this},copy:function(n){return this.a.copy(n.a),this.b.copy(n.b),this.c.copy(n.c),this},area:function(){var n=new THREE.Vector3,t=new THREE.Vector3;return function(){return n.subVectors(this.c,this.b),t.subVectors(this.a,this.b),.5*n.cross(t).length()}}(),midpoint:function(n){return(n||new THREE.Vector3).addVectors(this.a,this.b).add(this.c).multiplyScalar(1/3)},normal:function(n){return THREE.Triangle.normal(this.a,this.b,this.c,n)},plane:function(n){return(n||new THREE.Plane).setFromCoplanarPoints(this.a,this.b,this.c)},barycoordFromPoint:function(n,t){return THREE.Triangle.barycoordFromPoint(n,this.a,this.b,this.c,t)},containsPoint:function(n){return THREE.Triangle.containsPoint(n,this.a,this.b,this.c)},equals:function(n){return n.a.equals(this.a)&&n.b.equals(this.b)&&n.c.equals(this.c)},clone:function(){return(new THREE.Triangle).copy(this)}});THREE.Vertex=function(n){return console.warn("THREE.Vertex has been DEPRECATED. Use THREE.Vector3 instead."),n};THREE.UV=function(n,t){return console.warn("THREE.UV has been DEPRECATED. Use THREE.Vector2 instead."),new THREE.Vector2(n,t)};THREE.Clock=function(n){this.autoStart=void 0!==n?n:!0;this.elapsedTime=this.oldTime=this.startTime=0;this.running=!1};THREE.extend(THREE.Clock.prototype,{start:function(){this.oldTime=this.startTime=void 0!==window.performance&&void 0!==window.performance.now?window.performance.now():Date.now();this.running=!0},stop:function(){this.getElapsedTime();this.running=!1},getElapsedTime:function(){return this.getDelta(),this.elapsedTime},getDelta:function(){var n=0,t;return this.autoStart&&!this.running&&this.start(),this.running&&(t=void 0!==window.performance&&void 0!==window.performance.now?window.performance.now():Date.now(),n=.001*(t-this.oldTime),this.oldTime=t,this.elapsedTime+=n),n}});THREE.EventDispatcher=function(){var n={};this.addEventListener=function(t,i){void 0===n[t]&&(n[t]=[]);-1===n[t].indexOf(i)&&n[t].push(i)};this.removeEventListener=function(t,i){var r=n[t].indexOf(i);-1!==r&&n[t].splice(r,1)};this.dispatchEvent=function(t){var r=n[t.type],i,u;if(void 0!==r)for(t.target=this,i=0,u=r.length;i<u;i++)r[i].call(this,t)}},function(n){n.Raycaster=function(t,i,r,u){this.ray=new n.Ray(t,i);0<this.ray.direction.lengthSq()&&this.ray.direction.normalize();this.near=r||0;this.far=u||Infinity};var e=new n.Sphere,i=new n.Ray,u=new n.Plane,t=new n.Vector3,r=new n.Vector3,o=new n.Matrix4,s=function(n,t){return n.distance-t.distance},f=function(f,s,h){var p,g,c,l,y;if(f instanceof n.Particle){if(r.getPositionFromMatrix(f.matrixWorld),s=s.ray.distanceToPoint(r),s>f.scale.x)return h;h.push({distance:s,point:f.position,face:null,object:f})}else if(f instanceof n.Mesh){if(r.getPositionFromMatrix(f.matrixWorld),e.set(r,f.geometry.boundingSphere.radius*f.matrixWorld.getMaxScaleOnAxis()),!s.ray.isIntersectionSphere(e))return h;var b=f.geometry,a=b.vertices,d=f.material instanceof n.MeshFaceMaterial,nt=!0===d?f.material.materials:null,l=f.material.side,v,w,k,tt=s.precision;for(f.matrixRotationWorld.extractRotation(f.matrixWorld),o.getInverse(f.matrixWorld),i.copy(s.ray).applyMatrix4(o),p=0,g=b.faces.length;p<g;p++)if(c=b.faces[p],l=!0===d?nt[c.materialIndex]:f.material,void 0!==l&&(u.setFromNormalAndCoplanarPoint(c.normal,a[c.a]),y=i.distanceToPlane(u),!(Math.abs(y)<tt)&&!(0>y))){if(l=l.side,l!==n.DoubleSide&&(v=i.direction.dot(u.normal),!(l===n.FrontSide?0>v:0<v)))continue;if(!(y<s.near||y>s.far)){if(t=i.at(y,t),c instanceof n.Face3){if(l=a[c.a],v=a[c.b],w=a[c.c],!n.Triangle.containsPoint(t,l,v,w))continue}else if(c instanceof n.Face4){if(l=a[c.a],v=a[c.b],w=a[c.c],k=a[c.d],!n.Triangle.containsPoint(t,l,v,k)&&!n.Triangle.containsPoint(t,v,w,k))continue}else throw Error("face type not supported");h.push({distance:y,point:s.ray.at(y),face:c,faceIndex:p,object:f})}}}},h=function(n,t,i){for(var n=n.getDescendants(),r=0,u=n.length;r<u;r++)f(n[r],t,i)};n.Raycaster.prototype.precision=.0001;n.Raycaster.prototype.set=function(n,t){this.ray.set(n,t);0<this.ray.direction.length()&&this.ray.direction.normalize()};n.Raycaster.prototype.intersectObject=function(n,t){var i=[];return!0===t&&h(n,this,i),f(n,this,i),i.sort(s),i};n.Raycaster.prototype.intersectObjects=function(n,t){for(var i=[],r=0,u=n.length;r<u;r++)f(n[r],this,i),!0===t&&h(n[r],this,i);return i.sort(s),i}}(THREE);THREE.Object3D=function(){this.id=THREE.Object3DIdCount++;this.name="";this.properties={};this.parent=void 0;this.children=[];this.up=new THREE.Vector3(0,1,0);this.position=new THREE.Vector3;this.rotation=new THREE.Vector3;this.eulerOrder=THREE.Object3D.defaultEulerOrder;this.scale=new THREE.Vector3(1,1,1);this.renderDepth=null;this.rotationAutoUpdate=!0;this.matrix=new THREE.Matrix4;this.matrixWorld=new THREE.Matrix4;this.matrixRotationWorld=new THREE.Matrix4;this.matrixWorldNeedsUpdate=this.matrixAutoUpdate=!0;this.quaternion=new THREE.Quaternion;this.useQuaternion=!1;this.visible=!0;this.receiveShadow=this.castShadow=!1;this.frustumCulled=!0;this._vector=new THREE.Vector3};THREE.Object3D.prototype={constructor:THREE.Object3D,applyMatrix:function(n){this.matrix.multiplyMatrices(n,this.matrix);this.scale.getScaleFromMatrix(this.matrix);n=(new THREE.Matrix4).extractRotation(this.matrix);this.rotation.setEulerFromRotationMatrix(n,this.eulerOrder);this.position.getPositionFromMatrix(this.matrix)},translate:function(n,t){this.matrix.rotateAxis(t);this.position.add(t.multiplyScalar(n))},translateX:function(n){this.translate(n,this._vector.set(1,0,0))},translateY:function(n){this.translate(n,this._vector.set(0,1,0))},translateZ:function(n){this.translate(n,this._vector.set(0,0,1))},localToWorld:function(n){return n.applyMatrix4(this.matrixWorld)},worldToLocal:function(n){return n.applyMatrix4(THREE.Object3D.__m1.getInverse(this.matrixWorld))},lookAt:function(n){this.matrix.lookAt(n,this.position,this.up);this.rotationAutoUpdate&&(!1===this.useQuaternion?this.rotation.setEulerFromRotationMatrix(this.matrix,this.eulerOrder):this.quaternion.copy(this.matrix.decompose()[1]))},add:function(n){if(n===this)console.warn("THREE.Object3D.add: An object can't be added as a child of itself.");else if(n instanceof THREE.Object3D){void 0!==n.parent&&n.parent.remove(n);n.parent=this;this.children.push(n);for(var t=this;void 0!==t.parent;)t=t.parent;void 0!==t&&t instanceof THREE.Scene&&t.__addObject(n)}},remove:function(n){var t=this.children.indexOf(n);if(-1!==t){for(n.parent=void 0,this.children.splice(t,1),t=this;void 0!==t.parent;)t=t.parent;void 0!==t&&t instanceof THREE.Scene&&t.__removeObject(n)}},traverse:function(n){n(this);for(var t=0,i=this.children.length;t<i;t++)this.children[t].traverse(n)},getChildByName:function(n,t){for(var i,r=0,u=this.children.length;r<u;r++)if(i=this.children[r],i.name===n||!0===t&&(i=i.getChildByName(n,t),void 0!==i))return i},getDescendants:function(n){void 0===n&&(n=[]);Array.prototype.push.apply(n,this.children);for(var t=0,i=this.children.length;t<i;t++)this.children[t].getDescendants(n);return n},updateMatrix:function(){this.matrix.setPosition(this.position);!1===this.useQuaternion?this.matrix.setRotationFromEuler(this.rotation,this.eulerOrder):this.matrix.setRotationFromQuaternion(this.quaternion);(1!==this.scale.x||1!==this.scale.y||1!==this.scale.z)&&this.matrix.scale(this.scale);this.matrixWorldNeedsUpdate=!0},updateMatrixWorld:function(n){!0===this.matrixAutoUpdate&&this.updateMatrix();(!0===this.matrixWorldNeedsUpdate||!0===n)&&(void 0===this.parent?this.matrixWorld.copy(this.matrix):this.matrixWorld.multiplyMatrices(this.parent.matrixWorld,this.matrix),this.matrixWorldNeedsUpdate=!1,n=!0);for(var t=0,i=this.children.length;t<i;t++)this.children[t].updateMatrixWorld(n)},clone:function(n){void 0===n&&(n=new THREE.Object3D);n.name=this.name;n.up.copy(this.up);n.position.copy(this.position);n.rotation instanceof THREE.Vector3&&n.rotation.copy(this.rotation);n.eulerOrder=this.eulerOrder;n.scale.copy(this.scale);n.renderDepth=this.renderDepth;n.rotationAutoUpdate=this.rotationAutoUpdate;n.matrix.copy(this.matrix);n.matrixWorld.copy(this.matrixWorld);n.matrixRotationWorld.copy(this.matrixRotationWorld);n.matrixAutoUpdate=this.matrixAutoUpdate;n.matrixWorldNeedsUpdate=this.matrixWorldNeedsUpdate;n.quaternion.copy(this.quaternion);n.useQuaternion=this.useQuaternion;n.visible=this.visible;n.castShadow=this.castShadow;n.receiveShadow=this.receiveShadow;n.frustumCulled=this.frustumCulled;for(var t=0;t<this.children.length;t++)n.add(this.children[t].clone());return n}};THREE.Object3D.__m1=new THREE.Matrix4;THREE.Object3D.defaultEulerOrder="XYZ";THREE.Object3DIdCount=0;THREE.Projector=function(){function it(){if(p===st){var n=new THREE.RenderableObject;return ot.push(n),st++,p++,n}return ot[p++]}function rt(){if(v===ht){var n=new THREE.RenderableVertex;return o.push(n),ht++,v++,n}return o[v++]}function et(n,t){return t.z-n.z}function ti(n,t){var u=0,f=1,i=n.z+n.w,e=t.z+t.w,r=-n.z+n.w,o=-t.z+t.w;return 0<=i&&0<=e&&0<=r&&0<=o?!0:0>i&&0>e||0>r&&0>o?!1:(0>i?u=Math.max(u,i/(i-e)):0>e&&(f=Math.min(f,i/(i-e))),0>r?u=Math.max(u,r/(r-o)):0>o&&(f=Math.min(f,r/(r-o))),f<u)?!1:(n.lerp(t,u),t.lerp(n,1-f),!0)}var r,p,ot=[],st=0,i,v,o=[],ht=0,n,w,ct=[],lt=0,b,at=[],vt=0,c,k,yt=[],pt=0,f,d,wt=[],bt=0,t={objects:[],sprites:[],lights:[],elements:[]},s=new THREE.Vector3,u=new THREE.Vector4,kt=new THREE.Box3(new THREE.Vector3(-1,-1,-1),new THREE.Vector3(1,1,1)),dt=new THREE.Box3,g=Array(3),y=Array(4),ut=new THREE.Matrix4,e=new THREE.Matrix4,h,ft=new THREE.Matrix4,nt=new THREE.Matrix3,tt=new THREE.Matrix3,gt=new THREE.Vector3,ni=new THREE.Frustum,l=new THREE.Vector4,a=new THREE.Vector4;this.projectVector=function(n,t){return t.matrixWorldInverse.getInverse(t.matrixWorld),e.multiplyMatrices(t.projectionMatrix,t.matrixWorldInverse),n.applyProjection(e)};this.unprojectVector=function(n,t){return t.projectionMatrixInverse.getInverse(t.projectionMatrix),e.multiplyMatrices(t.matrixWorld,t.projectionMatrixInverse),n.applyProjection(e)};this.pickingRay=function(n,t){n.z=-1;var i=new THREE.Vector3(n.x,n.y,1);return this.unprojectVector(n,t),this.unprojectVector(i,t),i.sub(n).normalize(),new THREE.Raycaster(n,i)};this.projectScene=function(ot,st,ht,ii){var vi=!1,li,oi,ci,ri,ui,hi,fi,si,ai,yi,ei,wi,di,bi,pi,ki;for(d=k=b=w=0,t.elements.length=0,ot.updateMatrixWorld(),void 0===st.parent&&st.updateMatrixWorld(),ut.copy(st.matrixWorldInverse.getInverse(st.matrixWorld)),e.multiplyMatrices(st.projectionMatrix,ut),tt.getInverse(ut),tt.transpose(),ni.setFromMatrix(e),p=0,t.objects.length=0,t.sprites.length=0,t.lights.length=0,bi=function(n){for(var i,u=0,f=n.children.length;u<f;u++)i=n.children[u],!1!==i.visible&&(i instanceof THREE.Light?t.lights.push(i):i instanceof THREE.Mesh||i instanceof THREE.Line?(!1===i.frustumCulled||!0===ni.intersectsObject(i))&&(r=it(),r.object=i,null!==i.renderDepth?r.z=i.renderDepth:(s.getPositionFromMatrix(i.matrixWorld),s.applyProjection(e),r.z=s.z),t.objects.push(r)):i instanceof THREE.Sprite||i instanceof THREE.Particle?(r=it(),r.object=i,null!==i.renderDepth?r.z=i.renderDepth:(s.getPositionFromMatrix(i.matrixWorld),s.applyProjection(e),r.z=s.z),t.sprites.push(r)):(r=it(),r.object=i,null!==i.renderDepth?r.z=i.renderDepth:(s.getPositionFromMatrix(i.matrixWorld),s.applyProjection(e),r.z=s.z),t.objects.push(r)),bi(i))},bi(ot),!0===ht&&t.objects.sort(et),ot=0,ht=t.objects.length;ot<ht;ot++)if(si=t.objects[ot].object,h=si.matrixWorld,v=0,si instanceof THREE.Mesh){for(ai=si.geometry,ci=ai.vertices,yi=ai.faces,ai=ai.faceVertexUvs,nt.getInverse(h),nt.transpose(),wi=si.material instanceof THREE.MeshFaceMaterial,di=!0===wi?si.material:null,li=0,oi=ci.length;li<oi;li++)i=rt(),i.positionWorld.copy(ci[li]).applyMatrix4(h),i.positionScreen.copy(i.positionWorld).applyMatrix4(e),i.positionScreen.x/=i.positionScreen.w,i.positionScreen.y/=i.positionScreen.w,i.positionScreen.z/=i.positionScreen.w,i.visible=!(-1>i.positionScreen.x||1<i.positionScreen.x||-1>i.positionScreen.y||1<i.positionScreen.y||-1>i.positionScreen.z||1<i.positionScreen.z);for(ci=0,li=yi.length;ci<li;ci++)if(oi=yi[ci],pi=!0===wi?di.materials[oi.materialIndex]:si.material,void 0!==pi){if(hi=pi.side,oi instanceof THREE.Face3)if(ri=o[oi.a],ui=o[oi.b],fi=o[oi.c],g[0]=ri.positionScreen,g[1]=ui.positionScreen,g[2]=fi.positionScreen,!0===ri.visible||!0===ui.visible||!0===fi.visible||kt.isIntersectionBox(dt.setFromPoints(g)))if(vi=0>(fi.positionScreen.x-ri.positionScreen.x)*(ui.positionScreen.y-ri.positionScreen.y)-(fi.positionScreen.y-ri.positionScreen.y)*(ui.positionScreen.x-ri.positionScreen.x),hi===THREE.DoubleSide||vi===(hi===THREE.FrontSide))w===lt?(ei=new THREE.RenderableFace3,ct.push(ei),lt++,w++,n=ei):n=ct[w++],n.v1.copy(ri),n.v2.copy(ui),n.v3.copy(fi);else continue;else continue;else if(oi instanceof THREE.Face4)if(ri=o[oi.a],ui=o[oi.b],fi=o[oi.c],ei=o[oi.d],y[0]=ri.positionScreen,y[1]=ui.positionScreen,y[2]=fi.positionScreen,y[3]=ei.positionScreen,!0===ri.visible||!0===ui.visible||!0===fi.visible||!0===ei.visible||kt.isIntersectionBox(dt.setFromPoints(y)))if(vi=0>(ei.positionScreen.x-ri.positionScreen.x)*(ui.positionScreen.y-ri.positionScreen.y)-(ei.positionScreen.y-ri.positionScreen.y)*(ui.positionScreen.x-ri.positionScreen.x)||0>(ui.positionScreen.x-fi.positionScreen.x)*(ei.positionScreen.y-fi.positionScreen.y)-(ui.positionScreen.y-fi.positionScreen.y)*(ei.positionScreen.x-fi.positionScreen.x),hi===THREE.DoubleSide||vi===(hi===THREE.FrontSide))b===vt?(ki=new THREE.RenderableFace4,at.push(ki),vt++,b++,n=ki):n=at[b++],n.v1.copy(ri),n.v2.copy(ui),n.v3.copy(fi),n.v4.copy(ei);else continue;else continue;for(n.normalModel.copy(oi.normal),!1===vi&&(hi===THREE.BackSide||hi===THREE.DoubleSide)&&n.normalModel.negate(),n.normalModel.applyMatrix3(nt).normalize(),n.normalModelView.copy(n.normalModel).applyMatrix3(tt),n.centroidModel.copy(oi.centroid).applyMatrix4(h),fi=oi.vertexNormals,ri=0,ui=fi.length;ri<ui;ri++)ei=n.vertexNormalsModel[ri],ei.copy(fi[ri]),!1===vi&&(hi===THREE.BackSide||hi===THREE.DoubleSide)&&ei.negate(),ei.applyMatrix3(nt).normalize(),n.vertexNormalsModelView[ri].copy(ei).applyMatrix3(tt);for(n.vertexNormalsLength=fi.length,ri=0,ui=ai.length;ri<ui;ri++)if(ei=ai[ri][ci],void 0!==ei)for(hi=0,fi=ei.length;hi<fi;hi++)n.uvs[ri][hi]=ei[hi];n.color=oi.color;n.material=pi;gt.copy(n.centroidModel).applyProjection(e);n.z=gt.z;t.elements.push(n)}}else if(si instanceof THREE.Line)for(ft.multiplyMatrices(e,h),ci=si.geometry.vertices,ri=rt(),ri.positionScreen.copy(ci[0]).applyMatrix4(ft),yi=si.type===THREE.LinePieces?2:1,li=1,oi=ci.length;li<oi;li++)ri=rt(),ri.positionScreen.copy(ci[li]).applyMatrix4(ft),0<(li+1)%yi||(ui=o[v-2],l.copy(ri.positionScreen),a.copy(ui.positionScreen),!0===ti(l,a)&&(l.multiplyScalar(1/l.w),a.multiplyScalar(1/a.w),k===pt?(ai=new THREE.RenderableLine,yt.push(ai),pt++,k++,c=ai):c=yt[k++],c.v1.positionScreen.copy(l),c.v2.positionScreen.copy(a),c.z=Math.max(l.z,a.z),c.material=si.material,t.elements.push(c)));for(ot=0,ht=t.sprites.length;ot<ht;ot++)si=t.sprites[ot].object,h=si.matrixWorld,si instanceof THREE.Particle&&(u.set(h.elements[12],h.elements[13],h.elements[14],1),u.applyMatrix4(e),u.z/=u.w,0<u.z&&1>u.z&&(d===bt?(vi=new THREE.RenderableParticle,wt.push(vi),bt++,d++,f=vi):f=wt[d++],f.object=si,f.x=u.x/u.w,f.y=u.y/u.w,f.z=u.z,f.rotation=si.rotation.z,f.scale.x=si.scale.x*Math.abs(f.x-(u.x+st.projectionMatrix.elements[0])/(u.w+st.projectionMatrix.elements[12])),f.scale.y=si.scale.y*Math.abs(f.y-(u.y+st.projectionMatrix.elements[5])/(u.w+st.projectionMatrix.elements[13])),f.material=si.material,t.elements.push(f)));return!0===ii&&t.elements.sort(et),t}};THREE.Face3=function(n,t,i,r,u,f){this.a=n;this.b=t;this.c=i;this.normal=r instanceof THREE.Vector3?r:new THREE.Vector3;this.vertexNormals=r instanceof Array?r:[];this.color=u instanceof THREE.Color?u:new THREE.Color;this.vertexColors=u instanceof Array?u:[];this.vertexTangents=[];this.materialIndex=void 0!==f?f:0;this.centroid=new THREE.Vector3};THREE.Face3.prototype={constructor:THREE.Face3,clone:function(){var t=new THREE.Face3(this.a,this.b,this.c),n,i;for(t.normal.copy(this.normal),t.color.copy(this.color),t.centroid.copy(this.centroid),t.materialIndex=this.materialIndex,n=0,i=this.vertexNormals.length;n<i;n++)t.vertexNormals[n]=this.vertexNormals[n].clone();for(n=0,i=this.vertexColors.length;n<i;n++)t.vertexColors[n]=this.vertexColors[n].clone();for(n=0,i=this.vertexTangents.length;n<i;n++)t.vertexTangents[n]=this.vertexTangents[n].clone();return t}};THREE.Face4=function(n,t,i,r,u,f,e){this.a=n;this.b=t;this.c=i;this.d=r;this.normal=u instanceof THREE.Vector3?u:new THREE.Vector3;this.vertexNormals=u instanceof Array?u:[];this.color=f instanceof THREE.Color?f:new THREE.Color;this.vertexColors=f instanceof Array?f:[];this.vertexTangents=[];this.materialIndex=void 0!==e?e:0;this.centroid=new THREE.Vector3};THREE.Face4.prototype={constructor:THREE.Face4,clone:function(){var t=new THREE.Face4(this.a,this.b,this.c,this.d),n,i;for(t.normal.copy(this.normal),t.color.copy(this.color),t.centroid.copy(this.centroid),t.materialIndex=this.materialIndex,n=0,i=this.vertexNormals.length;n<i;n++)t.vertexNormals[n]=this.vertexNormals[n].clone();for(n=0,i=this.vertexColors.length;n<i;n++)t.vertexColors[n]=this.vertexColors[n].clone();for(n=0,i=this.vertexTangents.length;n<i;n++)t.vertexTangents[n]=this.vertexTangents[n].clone();return t}};THREE.Geometry=function(){THREE.EventDispatcher.call(this);this.id=THREE.GeometryIdCount++;this.name="";this.vertices=[];this.colors=[];this.normals=[];this.faces=[];this.faceUvs=[[]];this.faceVertexUvs=[[]];this.morphTargets=[];this.morphColors=[];this.morphNormals=[];this.skinWeights=[];this.skinIndices=[];this.lineDistances=[];this.boundingSphere=this.boundingBox=null;this.hasTangents=!1;this.dynamic=!0;this.buffersNeedUpdate=this.lineDistancesNeedUpdate=this.colorsNeedUpdate=this.tangentsNeedUpdate=this.normalsNeedUpdate=this.uvsNeedUpdate=this.elementsNeedUpdate=this.verticesNeedUpdate=!1};THREE.Geometry.prototype={constructor:THREE.Geometry,applyMatrix:function(n){for(var i,r,e,f=(new THREE.Matrix3).getInverse(n).transpose(),t=0,u=this.vertices.length;t<u;t++)this.vertices[t].applyMatrix4(n);for(t=0,u=this.faces.length;t<u;t++){for(i=this.faces[t],i.normal.applyMatrix3(f).normalize(),r=0,e=i.vertexNormals.length;r<e;r++)i.vertexNormals[r].applyMatrix3(f).normalize();i.centroid.applyMatrix4(n)}},computeCentroids:function(){for(var n,t=0,i=this.faces.length;t<i;t++)n=this.faces[t],n.centroid.set(0,0,0),n instanceof THREE.Face3?(n.centroid.add(this.vertices[n.a]),n.centroid.add(this.vertices[n.b]),n.centroid.add(this.vertices[n.c]),n.centroid.divideScalar(3)):n instanceof THREE.Face4&&(n.centroid.add(this.vertices[n.a]),n.centroid.add(this.vertices[n.b]),n.centroid.add(this.vertices[n.c]),n.centroid.add(this.vertices[n.d]),n.centroid.divideScalar(4))},computeFaceNormals:function(){for(var n=new THREE.Vector3,r=new THREE.Vector3,i=0,f=this.faces.length;i<f;i++){var t=this.faces[i],e=this.vertices[t.a],u=this.vertices[t.b];n.subVectors(this.vertices[t.c],u);r.subVectors(e,u);n.cross(r);n.normalize();t.normal.copy(n)}},computeVertexNormals:function(n){var i,u,t,r;if(void 0===this.__tmpVertices){for(r=this.__tmpVertices=Array(this.vertices.length),i=0,u=this.vertices.length;i<u;i++)r[i]=new THREE.Vector3;for(i=0,u=this.faces.length;i<u;i++)t=this.faces[i],t instanceof THREE.Face3?t.vertexNormals=[new THREE.Vector3,new THREE.Vector3,new THREE.Vector3]:t instanceof THREE.Face4&&(t.vertexNormals=[new THREE.Vector3,new THREE.Vector3,new THREE.Vector3,new THREE.Vector3])}else for(r=this.__tmpVertices,i=0,u=this.vertices.length;i<u;i++)r[i].set(0,0,0);if(n){var f,e,l,o=new THREE.Vector3,c=new THREE.Vector3,s=new THREE.Vector3,h=new THREE.Vector3,a=new THREE.Vector3;for(i=0,u=this.faces.length;i<u;i++)t=this.faces[i],t instanceof THREE.Face3?(n=this.vertices[t.a],f=this.vertices[t.b],e=this.vertices[t.c],o.subVectors(e,f),c.subVectors(n,f),o.cross(c),r[t.a].add(o),r[t.b].add(o),r[t.c].add(o)):t instanceof THREE.Face4&&(n=this.vertices[t.a],f=this.vertices[t.b],e=this.vertices[t.c],l=this.vertices[t.d],s.subVectors(l,f),c.subVectors(n,f),s.cross(c),r[t.a].add(s),r[t.b].add(s),r[t.d].add(s),h.subVectors(l,e),a.subVectors(f,e),h.cross(a),r[t.b].add(h),r[t.c].add(h),r[t.d].add(h))}else for(i=0,u=this.faces.length;i<u;i++)t=this.faces[i],t instanceof THREE.Face3?(r[t.a].add(t.normal),r[t.b].add(t.normal),r[t.c].add(t.normal)):t instanceof THREE.Face4&&(r[t.a].add(t.normal),r[t.b].add(t.normal),r[t.c].add(t.normal),r[t.d].add(t.normal));for(i=0,u=this.vertices.length;i<u;i++)r[i].normalize();for(i=0,u=this.faces.length;i<u;i++)t=this.faces[i],t instanceof THREE.Face3?(t.vertexNormals[0].copy(r[t.a]),t.vertexNormals[1].copy(r[t.b]),t.vertexNormals[2].copy(r[t.c])):t instanceof THREE.Face4&&(t.vertexNormals[0].copy(r[t.a]),t.vertexNormals[1].copy(r[t.b]),t.vertexNormals[2].copy(r[t.c]),t.vertexNormals[3].copy(r[t.d]))},computeMorphNormals:function(){for(var t,o,n,f,e,h,s,r,i=0,u=this.faces.length;i<u;i++)for(n=this.faces[i],n.__originalFaceNormal?n.__originalFaceNormal.copy(n.normal):n.__originalFaceNormal=n.normal.clone(),n.__originalVertexNormals||(n.__originalVertexNormals=[]),t=0,o=n.vertexNormals.length;t<o;t++)n.__originalVertexNormals[t]?n.__originalVertexNormals[t].copy(n.vertexNormals[t]):n.__originalVertexNormals[t]=n.vertexNormals[t].clone();for(f=new THREE.Geometry,f.faces=this.faces,t=0,o=this.morphTargets.length;t<o;t++){if(!this.morphNormals[t])for(this.morphNormals[t]={},this.morphNormals[t].faceNormals=[],this.morphNormals[t].vertexNormals=[],e=this.morphNormals[t].faceNormals,h=this.morphNormals[t].vertexNormals,i=0,u=this.faces.length;i<u;i++)n=this.faces[i],s=new THREE.Vector3,r=n instanceof THREE.Face3?{a:new THREE.Vector3,b:new THREE.Vector3,c:new THREE.Vector3}:{a:new THREE.Vector3,b:new THREE.Vector3,c:new THREE.Vector3,d:new THREE.Vector3},e.push(s),h.push(r);for(e=this.morphNormals[t],f.vertices=this.morphTargets[t].vertices,f.computeFaceNormals(),f.computeVertexNormals(),i=0,u=this.faces.length;i<u;i++)n=this.faces[i],s=e.faceNormals[i],r=e.vertexNormals[i],s.copy(n.normal),n instanceof THREE.Face3?(r.a.copy(n.vertexNormals[0]),r.b.copy(n.vertexNormals[1]),r.c.copy(n.vertexNormals[2])):(r.a.copy(n.vertexNormals[0]),r.b.copy(n.vertexNormals[1]),r.c.copy(n.vertexNormals[2]),r.d.copy(n.vertexNormals[3]))}for(i=0,u=this.faces.length;i<u;i++)n=this.faces[i],n.normal=n.__originalFaceNormal,n.vertexNormals=n.__originalVertexNormals},computeTangents:function(){function nt(n,t,i,r,e,y,k){u=n.vertices[t];w=n.vertices[i];b=n.vertices[r];o=p[e];tt=p[y];it=p[k];rt=w.x-u.x;ut=b.x-u.x;ft=w.y-u.y;et=b.y-u.y;ot=w.z-u.z;st=b.z-u.z;s=tt.x-o.x;h=it.x-o.x;c=tt.y-o.y;l=it.y-o.y;f=1/(s*l-h*c);d.set((l*rt-c*ut)*f,(l*ft-c*et)*f,(l*ot-c*st)*f);g.set((s*ut-h*rt)*f,(s*et-h*ft)*f,(s*st-h*ot)*f);a[t].add(d);a[i].add(d);a[r].add(d);v[t].add(g);v[i].add(g);v[r].add(g)}for(var i,r,n,p,u,w,b,o,tt,it,rt,ut,ft,et,ot,st,s,h,c,l,f,k,a=[],v=[],d=new THREE.Vector3,g=new THREE.Vector3,y=new THREE.Vector3,ct=new THREE.Vector3,ht=new THREE.Vector3,lt,t=0,e=this.vertices.length;t<e;t++)a[t]=new THREE.Vector3,v[t]=new THREE.Vector3;for(t=0,e=this.faces.length;t<e;t++)n=this.faces[t],p=this.faceVertexUvs[0][t],n instanceof THREE.Face3?nt(this,n.a,n.b,n.c,0,1,2):n instanceof THREE.Face4&&(nt(this,n.a,n.b,n.d,0,1,3),nt(this,n.b,n.c,n.d,1,2,3));for(lt=["a","b","c","d"],t=0,e=this.faces.length;t<e;t++)for(n=this.faces[t],i=0;i<n.vertexNormals.length;i++)ht.copy(n.vertexNormals[i]),r=n[lt[i]],k=a[r],y.copy(k),y.sub(ht.multiplyScalar(ht.dot(k))).normalize(),ct.crossVectors(n.vertexNormals[i],k),r=ct.dot(v[r]),r=0>r?-1:1,n.vertexTangents[i]=new THREE.Vector4(y.x,y.y,y.z,r);this.hasTangents=!0},computeLineDistances:function(){for(var i=0,t=this.vertices,n=0,r=t.length;n<r;n++)0<n&&(i+=t[n].distanceTo(t[n-1])),this.lineDistances[n]=i},computeBoundingBox:function(){null===this.boundingBox&&(this.boundingBox=new THREE.Box3);this.boundingBox.setFromPoints(this.vertices)},computeBoundingSphere:function(){null===this.boundingSphere&&(this.boundingSphere=new THREE.Sphere);this.boundingSphere.setFromCenterAndPoints(this.boundingSphere.center,this.vertices)},mergeVertices:function(){var n={},s=[],f=[],r,e=Math.pow(10,4),t,h,i,u,c,o;for(this.__tmpVertices=void 0,t=0,h=this.vertices.length;t<h;t++)r=this.vertices[t],r=[Math.round(r.x*e),Math.round(r.y*e),Math.round(r.z*e)].join("_"),void 0===n[r]?(n[r]=t,s.push(this.vertices[t]),f[t]=s.length-1):f[t]=f[n[r]];for(e=[],t=0,h=this.faces.length;t<h;t++)if(n=this.faces[t],n instanceof THREE.Face3){for(n.a=f[n.a],n.b=f[n.b],n.c=f[n.c],i=[n.a,n.b,n.c],r=-1,u=0;3>u;u++)if(i[u]==i[(u+1)%3]){e.push(t);break}}else if(n instanceof THREE.Face4){for(n.a=f[n.a],n.b=f[n.b],n.c=f[n.c],n.d=f[n.d],i=[n.a,n.b,n.c,n.d],r=-1,u=0;4>u;u++)i[u]==i[(u+1)%4]&&(0<=r&&e.push(t),r=u);if(0<=r){for(i.splice(r,1),o=new THREE.Face3(i[0],i[1],i[2],n.normal,n.color,n.materialIndex),i=0,u=this.faceVertexUvs.length;i<u;i++)(c=this.faceVertexUvs[i][t])&&c.splice(r,1);n.vertexNormals&&0<n.vertexNormals.length&&(o.vertexNormals=n.vertexNormals,o.vertexNormals.splice(r,1));n.vertexColors&&0<n.vertexColors.length&&(o.vertexColors=n.vertexColors,o.vertexColors.splice(r,1));this.faces[t]=o}}for(t=e.length-1;0<=t;t--)for(this.faces.splice(t,1),i=0,u=this.faceVertexUvs.length;i<u;i++)this.faceVertexUvs[i].splice(t,1);return f=this.vertices.length-s.length,this.vertices=s,f},clone:function(){for(var r=new THREE.Geometry,t=this.vertices,n=0,i=t.length;n<i;n++)r.vertices.push(t[n].clone());for(t=this.faces,n=0,i=t.length;n<i;n++)r.faces.push(t[n].clone());for(t=this.faceVertexUvs[0],n=0,i=t.length;n<i;n++){for(var f=t[n],e=[],u=0,o=f.length;u<o;u++)e.push(new THREE.Vector2(f[u].x,f[u].y));r.faceVertexUvs[0].push(e)}return r},dispose:function(){this.dispatchEvent({type:"dispose"})}};THREE.GeometryIdCount=0;THREE.BufferGeometry=function(){THREE.EventDispatcher.call(this);this.id=THREE.GeometryIdCount++;this.attributes={};this.dynamic=!1;this.offsets=[];this.boundingSphere=this.boundingBox=null;this.hasTangents=!1;this.morphTargets=[]};THREE.BufferGeometry.prototype={constructor:THREE.BufferGeometry,applyMatrix:function(n){var t,i;this.attributes.position&&(t=this.attributes.position.array);this.attributes.normal&&(i=this.attributes.normal.array);void 0!==t&&(n.multiplyVector3Array(t),this.verticesNeedUpdate=!0);void 0!==i&&(t=new THREE.Matrix3,t.getInverse(n).transpose(),t.multiplyVector3Array(i),this.normalizeNormals(),this.normalsNeedUpdate=!0)},computeBoundingBox:function(){var t,n,r,u,f,i,e;if(null===this.boundingBox&&(this.boundingBox=new THREE.Box3),t=this.attributes.position.array,t)for(n=this.boundingBox,3<=t.length&&(n.min.x=n.max.x=t[0],n.min.y=n.max.y=t[1],n.min.z=n.max.z=t[2]),i=3,e=t.length;i<e;i+=3)r=t[i],u=t[i+1],f=t[i+2],r<n.min.x?n.min.x=r:r>n.max.x&&(n.max.x=r),u<n.min.y?n.min.y=u:u>n.max.y&&(n.max.y=u),f<n.min.z?n.min.z=f:f>n.max.z&&(n.max.z=f);(void 0===t||0===t.length)&&(this.boundingBox.min.set(0,0,0),this.boundingBox.max.set(0,0,0))},computeBoundingSphere:function(){var n;if(null===this.boundingSphere&&(this.boundingSphere=new THREE.Sphere),n=this.attributes.position.array,n){for(var t,r=0,u,f,i=0,e=n.length;i<e;i+=3)t=n[i],u=n[i+1],f=n[i+2],t=t*t+u*u+f*f,t>r&&(r=t);this.boundingSphere.radius=Math.sqrt(r)}},computeVertexNormals:function(){var n,s,l,d,b,v,k;if(this.attributes.position){if(n=this.attributes.position.array.length,void 0===this.attributes.normal)this.attributes.normal={itemSize:3,array:new Float32Array(n),numItems:n};else for(n=0,s=this.attributes.normal.array.length;n<s;n++)this.attributes.normal.array[n]=0;var i=this.attributes.position.array,r=this.attributes.normal.array,o,h,c,u,f,e,y=new THREE.Vector3,a=new THREE.Vector3,p=new THREE.Vector3,t=new THREE.Vector3,w=new THREE.Vector3;if(this.attributes.index)for(b=this.attributes.index.array,v=this.offsets,l=0,d=v.length;l<d;++l)for(s=v[l].start,o=v[l].count,k=v[l].index,n=s,s+=o;n<s;n+=3)o=k+b[n],h=k+b[n+1],c=k+b[n+2],u=i[3*o],f=i[3*o+1],e=i[3*o+2],y.set(u,f,e),u=i[3*h],f=i[3*h+1],e=i[3*h+2],a.set(u,f,e),u=i[3*c],f=i[3*c+1],e=i[3*c+2],p.set(u,f,e),t.subVectors(p,a),w.subVectors(y,a),t.cross(w),r[3*o]+=t.x,r[3*o+1]+=t.y,r[3*o+2]+=t.z,r[3*h]+=t.x,r[3*h+1]+=t.y,r[3*h+2]+=t.z,r[3*c]+=t.x,r[3*c+1]+=t.y,r[3*c+2]+=t.z;else for(n=0,s=i.length;n<s;n+=9)u=i[n],f=i[n+1],e=i[n+2],y.set(u,f,e),u=i[n+3],f=i[n+4],e=i[n+5],a.set(u,f,e),u=i[n+6],f=i[n+7],e=i[n+8],p.set(u,f,e),t.subVectors(p,a),w.subVectors(y,a),t.cross(w),r[n]=t.x,r[n+1]=t.y,r[n+2]=t.z,r[n+3]=t.x,r[n+4]=t.y,r[n+5]=t.z,r[n+6]=t.x,r[n+7]=t.y,r[n+8]=t.z;this.normalizeNormals();this.normalsNeedUpdate=!0}},normalizeNormals:function(){for(var t=this.attributes.normal.array,i,r,u,n=0,f=t.length;n<f;n+=3)i=t[n],r=t[n+1],u=t[n+2],i=1/Math.sqrt(i*i+r*r+u*u),t[n]*=i,t[n+1]*=i,t[n+2]*=i},computeTangents:function(){function ct(n){nt.x=lt[3*n];nt.y=lt[3*n+1];nt.z=lt[3*n+2];bt.copy(nt);ht=tt[n];rt.copy(ht);rt.sub(nt.multiplyScalar(nt.dot(ht))).normalize();wt.crossVectors(bt,ht);dt=wt.dot(it[n]);kt=0>dt?-1:1;ut[4*n]=rt.x;ut[4*n+1]=rt.y;ut[4*n+2]=rt.z;ut[4*n+3]=kt}var r,a;if(void 0===this.attributes.index||void 0===this.attributes.position||void 0===this.attributes.normal||void 0===this.attributes.uv)console.warn("Missing required attributes (index, position, normal or uv) in BufferGeometry.computeTangents()");else{var v=this.attributes.index.array,u=this.attributes.position.array,lt=this.attributes.normal.array,y=this.attributes.uv.array,p=u.length/3;void 0===this.attributes.tangent&&(r=4*p,this.attributes.tangent={itemSize:4,array:new Float32Array(r),numItems:r});for(var ut=this.attributes.tangent.array,tt=[],it=[],r=0;r<p;r++)tt[r]=new THREE.Vector3,it[r]=new THREE.Vector3;for(var w,b,k,ft,et,ot,at,vt,yt,s,h,d,g,pt,o,p=new THREE.Vector3,r=new THREE.Vector3,t,c,n,f,e,l=this.offsets,i=0,st=l.length;i<st;++i)for(c=l[i].start,n=l[i].count,a=l[i].index,t=c,c+=n;t<c;t+=3)n=a+v[t],f=a+v[t+1],e=a+v[t+2],w=u[3*n],b=u[3*n+1],k=u[3*n+2],ft=u[3*f],et=u[3*f+1],ot=u[3*f+2],at=u[3*e],vt=u[3*e+1],yt=u[3*e+2],s=y[2*n],h=y[2*n+1],d=y[2*f],g=y[2*f+1],pt=y[2*e],o=y[2*e+1],ft-=w,w=at-w,et-=b,b=vt-b,ot-=k,k=yt-k,d-=s,s=pt-s,g-=h,h=o-h,o=1/(d*h-s*g),p.set((h*ft-g*w)*o,(h*et-g*b)*o,(h*ot-g*k)*o),r.set((d*w-s*ft)*o,(d*b-s*et)*o,(d*k-s*ot)*o),tt[n].add(p),tt[f].add(p),tt[e].add(p),it[n].add(r),it[f].add(r),it[e].add(r);var rt=new THREE.Vector3,wt=new THREE.Vector3,nt=new THREE.Vector3,bt=new THREE.Vector3,kt,ht,dt;for(i=0,st=l.length;i<st;++i)for(c=l[i].start,n=l[i].count,a=l[i].index,t=c,c+=n;t<c;t+=3)n=a+v[t],f=a+v[t+1],e=a+v[t+2],ct(n),ct(f),ct(e);this.tangentsNeedUpdate=this.hasTangents=!0}},dispose:function(){this.dispatchEvent({type:"dispose"})}};THREE.Camera=function(){THREE.Object3D.call(this);this.matrixWorldInverse=new THREE.Matrix4;this.projectionMatrix=new THREE.Matrix4;this.projectionMatrixInverse=new THREE.Matrix4};THREE.Camera.prototype=Object.create(THREE.Object3D.prototype);THREE.Camera.prototype.lookAt=function(n){this.matrix.lookAt(this.position,n,this.up);!0===this.rotationAutoUpdate&&(!1===this.useQuaternion?this.rotation.setEulerFromRotationMatrix(this.matrix,this.eulerOrder):this.quaternion.copy(this.matrix.decompose()[1]))};THREE.OrthographicCamera=function(n,t,i,r,u,f){THREE.Camera.call(this);this.left=n;this.right=t;this.top=i;this.bottom=r;this.near=void 0!==u?u:.1;this.far=void 0!==f?f:2e3;this.updateProjectionMatrix()};THREE.OrthographicCamera.prototype=Object.create(THREE.Camera.prototype);THREE.OrthographicCamera.prototype.updateProjectionMatrix=function(){this.projectionMatrix.makeOrthographic(this.left,this.right,this.top,this.bottom,this.near,this.far)};THREE.PerspectiveCamera=function(n,t,i,r){THREE.Camera.call(this);this.fov=void 0!==n?n:50;this.aspect=void 0!==t?t:1;this.near=void 0!==i?i:.1;this.far=void 0!==r?r:2e3;this.updateProjectionMatrix()};THREE.PerspectiveCamera.prototype=Object.create(THREE.Camera.prototype);THREE.PerspectiveCamera.prototype.setLens=function(n,t){void 0===t&&(t=24);this.fov=2*THREE.Math.radToDeg(Math.atan(t/(2*n)));this.updateProjectionMatrix()};THREE.PerspectiveCamera.prototype.setViewOffset=function(n,t,i,r,u,f){this.fullWidth=n;this.fullHeight=t;this.x=i;this.y=r;this.width=u;this.height=f;this.updateProjectionMatrix()};THREE.PerspectiveCamera.prototype.updateProjectionMatrix=function(){if(this.fullWidth){var t=this.fullWidth/this.fullHeight,n=Math.tan(THREE.Math.degToRad(.5*this.fov))*this.near,i=-n,r=t*i,t=Math.abs(t*n-r),i=Math.abs(n-i);this.projectionMatrix.makeFrustum(r+this.x*t/this.fullWidth,r+(this.x+this.width)*t/this.fullWidth,n-(this.y+this.height)*i/this.fullHeight,n-this.y*i/this.fullHeight,this.near,this.far)}else this.projectionMatrix.makePerspective(this.fov,this.aspect,this.near,this.far)};THREE.Light=function(n){THREE.Object3D.call(this);this.color=new THREE.Color(n)};THREE.Light.prototype=Object.create(THREE.Object3D.prototype);THREE.AmbientLight=function(n){THREE.Light.call(this,n)};THREE.AmbientLight.prototype=Object.create(THREE.Light.prototype);THREE.AreaLight=function(n,t){THREE.Light.call(this,n);this.normal=new THREE.Vector3(0,-1,0);this.right=new THREE.Vector3(1,0,0);this.intensity=void 0!==t?t:1;this.height=this.width=1;this.constantAttenuation=1.5;this.linearAttenuation=.5;this.quadraticAttenuation=.1};THREE.AreaLight.prototype=Object.create(THREE.Light.prototype);THREE.DirectionalLight=function(n,t){THREE.Light.call(this,n);this.position=new THREE.Vector3(0,1,0);this.target=new THREE.Object3D;this.intensity=void 0!==t?t:1;this.onlyShadow=this.castShadow=!1;this.shadowCameraNear=50;this.shadowCameraFar=5e3;this.shadowCameraLeft=-500;this.shadowCameraTop=this.shadowCameraRight=500;this.shadowCameraBottom=-500;this.shadowCameraVisible=!1;this.shadowBias=0;this.shadowDarkness=.5;this.shadowMapHeight=this.shadowMapWidth=512;this.shadowCascade=!1;this.shadowCascadeOffset=new THREE.Vector3(0,0,-1e3);this.shadowCascadeCount=2;this.shadowCascadeBias=[0,0,0];this.shadowCascadeWidth=[512,512,512];this.shadowCascadeHeight=[512,512,512];this.shadowCascadeNearZ=[-1,.99,.998];this.shadowCascadeFarZ=[.99,.998,1];this.shadowCascadeArray=[];this.shadowMatrix=this.shadowCamera=this.shadowMapSize=this.shadowMap=null};THREE.DirectionalLight.prototype=Object.create(THREE.Light.prototype);THREE.HemisphereLight=function(n,t,i){THREE.Light.call(this,n);this.groundColor=new THREE.Color(t);this.position=new THREE.Vector3(0,100,0);this.intensity=void 0!==i?i:1};THREE.HemisphereLight.prototype=Object.create(THREE.Light.prototype);THREE.PointLight=function(n,t,i){THREE.Light.call(this,n);this.position=new THREE.Vector3(0,0,0);this.intensity=void 0!==t?t:1;this.distance=void 0!==i?i:0};THREE.PointLight.prototype=Object.create(THREE.Light.prototype);THREE.SpotLight=function(n,t,i,r,u){THREE.Light.call(this,n);this.position=new THREE.Vector3(0,1,0);this.target=new THREE.Object3D;this.intensity=void 0!==t?t:1;this.distance=void 0!==i?i:0;this.angle=void 0!==r?r:Math.PI/2;this.exponent=void 0!==u?u:10;this.onlyShadow=this.castShadow=!1;this.shadowCameraNear=50;this.shadowCameraFar=5e3;this.shadowCameraFov=50;this.shadowCameraVisible=!1;this.shadowBias=0;this.shadowDarkness=.5;this.shadowMapHeight=this.shadowMapWidth=512;this.shadowMatrix=this.shadowCamera=this.shadowMapSize=this.shadowMap=null};THREE.SpotLight.prototype=Object.create(THREE.Light.prototype);THREE.Loader=function(n){this.statusDomElement=(this.showStatus=n)?THREE.Loader.prototype.addStatusElement():null;this.onLoadStart=function(){};this.onLoadProgress=function(){};this.onLoadComplete=function(){}};THREE.Loader.prototype={constructor:THREE.Loader,crossOrigin:"anonymous",addStatusElement:function(){var n=document.createElement("div");return n.style.position="absolute",n.style.right="0px",n.style.top="0px",n.style.fontSize="0.8em",n.style.textAlign="left",n.style.background="rgba(0,0,0,0.25)",n.style.color="#fff",n.style.width="120px",n.style.padding="0.5em 0.5em 0.5em 0.5em",n.style.zIndex=1e3,n.innerHTML="Loading ...",n},updateProgress:function(n){var t="Loaded ",t=n.total?t+((100*n.loaded/n.total).toFixed(0)+"%"):t+((n.loaded/1e3).toFixed(2)+" KB");this.statusDomElement.innerHTML=t},extractUrlBase:function(n){return n=n.split("/"),n.pop(),(1>n.length?".":n.join("/"))+"/"},initMaterials:function(n,t){for(var r=[],i=0;i<n.length;++i)r[i]=THREE.Loader.prototype.createMaterial(n[i],t);return r},needsTangents:function(n){for(var t=0,i=n.length;t<i;t++)if(n[t]instanceof THREE.ShaderMaterial)return!0;return!1},createMaterial:function(n,t){function o(n){return n=Math.log(n)/Math.LN2,Math.floor(n)==n}function s(n){return n=Math.log(n)/Math.LN2,Math.pow(2,Math.round(n))}function f(n,i,r,u,f,e,c){var v=/\.dds$/i.test(r),y=t+"/"+r,a,l,n;v?(a=THREE.ImageUtils.loadCompressedTexture(y),n[i]=a):(a=document.createElement("canvas"),n[i]=new THREE.Texture(a));n[i].sourceFile=r;u&&(n[i].repeat.set(u[0],u[1]),1!==u[0]&&(n[i].wrapS=THREE.RepeatWrapping),1!==u[1]&&(n[i].wrapT=THREE.RepeatWrapping));f&&n[i].offset.set(f[0],f[1]);e&&(r={repeat:THREE.RepeatWrapping,mirror:THREE.MirroredRepeatWrapping},void 0!==r[e[0]]&&(n[i].wrapS=r[e[0]]),void 0!==r[e[1]]&&(n[i].wrapT=r[e[1]]));c&&(n[i].anisotropy=c);v||(l=n[i],n=new Image,n.onload=function(){if(o(this.width)&&o(this.height))l.image=this;else{var n=s(this.width),t=s(this.height);l.image.width=n;l.image.height=t;l.image.getContext("2d").drawImage(this,0,0,n,t)}l.needsUpdate=!0},n.crossOrigin=h.crossOrigin,n.src=y)}function e(n){return(255*n[0]<<16)+(255*n[1]<<8)+255*n[2]}var h=this,u="MeshLambertMaterial",i={color:15658734,opacity:1,map:null,lightMap:null,normalMap:null,bumpMap:null,wireframe:!1},r;return n.shading&&(r=n.shading.toLowerCase(),"phong"===r?u="MeshPhongMaterial":"basic"===r&&(u="MeshBasicMaterial")),void 0!==n.blending&&void 0!==THREE[n.blending]&&(i.blending=THREE[n.blending]),(void 0!==n.transparent||1>n.opacity)&&(i.transparent=n.transparent),void 0!==n.depthTest&&(i.depthTest=n.depthTest),void 0!==n.depthWrite&&(i.depthWrite=n.depthWrite),void 0!==n.visible&&(i.visible=n.visible),void 0!==n.flipSided&&(i.side=THREE.BackSide),void 0!==n.doubleSided&&(i.side=THREE.DoubleSide),void 0!==n.wireframe&&(i.wireframe=n.wireframe),void 0!==n.vertexColors&&("face"===n.vertexColors?i.vertexColors=THREE.FaceColors:n.vertexColors&&(i.vertexColors=THREE.VertexColors)),n.colorDiffuse?i.color=e(n.colorDiffuse):n.DbgColor&&(i.color=n.DbgColor),n.colorSpecular&&(i.specular=e(n.colorSpecular)),n.colorAmbient&&(i.ambient=e(n.colorAmbient)),n.transparency&&(i.opacity=n.transparency),n.specularCoef&&(i.shininess=n.specularCoef),n.mapDiffuse&&t&&f(i,"map",n.mapDiffuse,n.mapDiffuseRepeat,n.mapDiffuseOffset,n.mapDiffuseWrap,n.mapDiffuseAnisotropy),n.mapLight&&t&&f(i,"lightMap",n.mapLight,n.mapLightRepeat,n.mapLightOffset,n.mapLightWrap,n.mapLightAnisotropy),n.mapBump&&t&&f(i,"bumpMap",n.mapBump,n.mapBumpRepeat,n.mapBumpOffset,n.mapBumpWrap,n.mapBumpAnisotropy),n.mapNormal&&t&&f(i,"normalMap",n.mapNormal,n.mapNormalRepeat,n.mapNormalOffset,n.mapNormalWrap,n.mapNormalAnisotropy),n.mapSpecular&&t&&f(i,"specularMap",n.mapSpecular,n.mapSpecularRepeat,n.mapSpecularOffset,n.mapSpecularWrap,n.mapSpecularAnisotropy),n.mapBumpScale&&(i.bumpScale=n.mapBumpScale),n.mapNormal?(u=THREE.ShaderLib.normalmap,r=THREE.UniformsUtils.clone(u.uniforms),r.tNormal.value=i.normalMap,n.mapNormalFactor&&r.uNormalScale.value.set(n.mapNormalFactor,n.mapNormalFactor),i.map&&(r.tDiffuse.value=i.map,r.enableDiffuse.value=!0),i.specularMap&&(r.tSpecular.value=i.specularMap,r.enableSpecular.value=!0),i.lightMap&&(r.tAO.value=i.lightMap,r.enableAO.value=!0),r.uDiffuseColor.value.setHex(i.color),r.uSpecularColor.value.setHex(i.specular),r.uAmbientColor.value.setHex(i.ambient),r.uShininess.value=i.shininess,void 0!==i.opacity&&(r.uOpacity.value=i.opacity),u=new THREE.ShaderMaterial({fragmentShader:u.fragmentShader,vertexShader:u.vertexShader,uniforms:r,lights:!0,fog:!0}),i.transparent&&(u.transparent=!0)):u=new THREE[u](i),void 0!==n.DbgName&&(u.name=n.DbgName),u}};THREE.ImageLoader=function(){THREE.EventDispatcher.call(this);this.crossOrigin=null};THREE.ImageLoader.prototype={constructor:THREE.ImageLoader,load:function(n,t){var i=this;void 0===t&&(t=new Image);t.addEventListener("load",function(){i.dispatchEvent({type:"load",content:t})},!1);t.addEventListener("error",function(){i.dispatchEvent({type:"error",message:"Couldn't load URL ["+n+"]"})},!1);i.crossOrigin&&(t.crossOrigin=i.crossOrigin);t.src=n}};THREE.JSONLoader=function(n){THREE.Loader.call(this,n);this.withCredentials=!1};THREE.JSONLoader.prototype=Object.create(THREE.Loader.prototype);THREE.JSONLoader.prototype.load=function(n,t,i){i=i&&"string"==typeof i?i:this.extractUrlBase(n);this.onLoadStart();this.loadAjaxJSON(this,n,t,i)};THREE.JSONLoader.prototype.loadAjaxJSON=function(n,t,i,r,u){var f=new XMLHttpRequest,e=0;f.onreadystatechange=function(){if(f.readyState===f.DONE)if(200===f.status||0===f.status){if(f.responseText){var o=JSON.parse(f.responseText);n.createModel(o,i,r)}else console.warn("THREE.JSONLoader: ["+t+"] seems to be unreachable or file there is empty");n.onLoadComplete()}else console.error("THREE.JSONLoader: Couldn't load ["+t+"] ["+f.status+"]");else f.readyState===f.LOADING?u&&(0===e&&(e=f.getResponseHeader("Content-Length")),u({total:e,loaded:f.responseText.length})):f.readyState===f.HEADERS_RECEIVED&&(e=f.getResponseHeader("Content-Length"))};f.open("GET",t,!0);f.withCredentials=this.withCredentials;f.send(null)};THREE.JSONLoader.prototype.createModel=function(n,t,i){var e=new THREE.Geometry,l=void 0!==n.scale?1/n.scale:1,f,s,d,r,a,v,p,w,b,o,it,h,g,tt,nt,u=n.faces;o=n.vertices;var c=n.normals,k=n.colors,y=0;for(f=0;f<n.uvs.length;f++)n.uvs[f].length&&y++;for(f=0;f<y;f++)e.faceUvs[f]=[],e.faceVertexUvs[f]=[];for(r=0,a=o.length;r<a;)v=new THREE.Vector3,v.x=o[r++]*l,v.y=o[r++]*l,v.z=o[r++]*l,e.vertices.push(v);for(r=0,a=u.length;r<a;){if(o=u[r++],v=o&1,d=o&2,f=o&4,s=o&8,w=o&16,p=o&32,it=o&64,o&=128,v?(h=new THREE.Face4,h.a=u[r++],h.b=u[r++],h.c=u[r++],h.d=u[r++],v=4):(h=new THREE.Face3,h.a=u[r++],h.b=u[r++],h.c=u[r++],v=3),d&&(d=u[r++],h.materialIndex=d),d=e.faces.length,f)for(f=0;f<y;f++)g=n.uvs[f],b=u[r++],nt=g[2*b],b=g[2*b+1],e.faceUvs[f][d]=new THREE.Vector2(nt,b);if(s)for(f=0;f<y;f++){for(g=n.uvs[f],tt=[],s=0;s<v;s++)b=u[r++],nt=g[2*b],b=g[2*b+1],tt[s]=new THREE.Vector2(nt,b);e.faceVertexUvs[f][d]=tt}if(w&&(w=3*u[r++],s=new THREE.Vector3,s.x=c[w++],s.y=c[w++],s.z=c[w],h.normal=s),p)for(f=0;f<v;f++)w=3*u[r++],s=new THREE.Vector3,s.x=c[w++],s.y=c[w++],s.z=c[w],h.vertexNormals.push(s);if(it&&(p=u[r++],p=new THREE.Color(k[p]),h.color=p),o)for(f=0;f<v;f++)p=u[r++],p=new THREE.Color(k[p]),h.vertexColors.push(p);e.faces.push(h)}if(n.skinWeights)for(r=0,a=n.skinWeights.length;r<a;r+=2)u=n.skinWeights[r],c=n.skinWeights[r+1],e.skinWeights.push(new THREE.Vector4(u,c,0,0));if(n.skinIndices)for(r=0,a=n.skinIndices.length;r<a;r+=2)u=n.skinIndices[r],c=n.skinIndices[r+1],e.skinIndices.push(new THREE.Vector4(u,c,0,0));if(e.bones=n.bones,e.animation=n.animation,void 0!==n.morphTargets)for(r=0,a=n.morphTargets.length;r<a;r++)for(e.morphTargets[r]={},e.morphTargets[r].name=n.morphTargets[r].name,e.morphTargets[r].vertices=[],k=e.morphTargets[r].vertices,y=n.morphTargets[r].vertices,u=0,c=y.length;u<c;u+=3)o=new THREE.Vector3,o.x=y[u]*l,o.y=y[u+1]*l,o.z=y[u+2]*l,k.push(o);if(void 0!==n.morphColors)for(r=0,a=n.morphColors.length;r<a;r++)for(e.morphColors[r]={},e.morphColors[r].name=n.morphColors[r].name,e.morphColors[r].colors=[],c=e.morphColors[r].colors,k=n.morphColors[r].colors,l=0,u=k.length;l<u;l+=3)y=new THREE.Color(16755200),y.setRGB(k[l],k[l+1],k[l+2]),c.push(y);e.computeCentroids();e.computeFaceNormals();n=this.initMaterials(n.materials,i);this.needsTangents(n)&&e.computeTangents();t(e,n)};THREE.LoadingMonitor=function(){THREE.EventDispatcher.call(this);var i=this,n=0,t=0,r=function(){n++;i.dispatchEvent({type:"progress",loaded:n,total:t});n===t&&i.dispatchEvent({type:"load"})};this.add=function(n){t++;n.addEventListener("load",r,!1)}};THREE.SceneLoader=function(){this.onLoadStart=function(){};this.onLoadProgress=function(){};this.onLoadComplete=function(){};this.callbackSync=function(){};this.callbackProgress=function(){};this.geometryHandlerMap={};this.hierarchyHandlerMap={};this.addGeometryHandler("ascii",THREE.JSONLoader)};THREE.SceneLoader.prototype.constructor=THREE.SceneLoader;THREE.SceneLoader.prototype.load=function(n,t){var r=this,i=new XMLHttpRequest;i.onreadystatechange=function(){if(4===i.readyState)if(200===i.status||0===i.status){var u=JSON.parse(i.responseText);r.parse(u,t,n)}else console.error("THREE.SceneLoader: Couldn't load ["+n+"] ["+i.status+"]")};i.open("GET",n,!0);i.send(null)};THREE.SceneLoader.prototype.addGeometryHandler=function(n,t){this.geometryHandlerMap[n]={loaderClass:t}};THREE.SceneLoader.prototype.addHierarchyHandler=function(n,t){this.hierarchyHandlerMap[n]={loaderClass:t}};THREE.SceneLoader.prototype.parse=function(n,t,i){function g(n,t){return"relativeToHTML"==t?n:at+"/"+n}function rt(){st(e.scene,h.objects)}function st(n,t){var f,s,u,l,w,c,o,i,r,k,d;for(o in t)if(void 0===e.objects[o]){if(i=t[o],r=null,i.type&&i.type in y.hierarchyHandlerMap){if(void 0===i.loading){s={type:1,url:1,material:1,position:1,rotation:1,scale:1,visible:1,children:1,properties:1,skin:1,morph:1,mirroredLoop:1,duration:1};u={};for(k in i)k in s||(u[k]=i[k]);a=e.materials[i.material];i.loading=!0;s=y.hierarchyHandlerMap[i.type].loaderObject;s.options?s.load(g(i.url,h.urlBaseType),ht(o,n,a,i)):s.load(g(i.url,h.urlBaseType),ht(o,n,a,i),u)}}else if(void 0!==i.geometry){if(v=e.geometries[i.geometry]){if(r=!1,a=e.materials[i.material],r=a instanceof THREE.ShaderMaterial,u=i.position,l=i.rotation,w=i.scale,f=i.matrix,c=i.quaternion,i.material||(a=new THREE.MeshFaceMaterial(e.face_materials[i.geometry])),a instanceof THREE.MeshFaceMaterial&&0===a.materials.length&&(a=new THREE.MeshFaceMaterial(e.face_materials[i.geometry])),a instanceof THREE.MeshFaceMaterial)for(s=0;s<a.materials.length;s++)r=r||a.materials[s]instanceof THREE.ShaderMaterial;r&&v.computeTangents();i.skin?r=new THREE.SkinnedMesh(v,a):i.morph?(r=new THREE.MorphAnimMesh(v,a),void 0!==i.duration&&(r.duration=i.duration),void 0!==i.time&&(r.time=i.time),void 0!==i.mirroredLoop&&(r.mirroredLoop=i.mirroredLoop),a.morphNormals&&v.computeMorphNormals()):r=new THREE.Mesh(v,a);r.name=o;f?(r.matrixAutoUpdate=!1,r.matrix.set(f[0],f[1],f[2],f[3],f[4],f[5],f[6],f[7],f[8],f[9],f[10],f[11],f[12],f[13],f[14],f[15])):(r.position.set(u[0],u[1],u[2]),c?(r.quaternion.set(c[0],c[1],c[2],c[3]),r.useQuaternion=!0):r.rotation.set(l[0],l[1],l[2]),r.scale.set(w[0],w[1],w[2]));r.visible=i.visible;r.castShadow=i.castShadow;r.receiveShadow=i.receiveShadow;n.add(r);e.objects[o]=r}}else"DirectionalLight"===i.type||"PointLight"===i.type||"AmbientLight"===i.type?(it=void 0!==i.color?i.color:16777215,ut=void 0!==i.intensity?i.intensity:1,"DirectionalLight"===i.type?(u=i.direction,p=new THREE.DirectionalLight(it,ut),p.position.set(u[0],u[1],u[2]),i.target&&(ot.push({object:p,targetName:i.target}),p.target=null)):"PointLight"===i.type?(u=i.position,s=i.distance,p=new THREE.PointLight(it,ut,s),p.position.set(u[0],u[1],u[2])):"AmbientLight"===i.type&&(p=new THREE.AmbientLight(it)),n.add(p),p.name=o,e.lights[o]=p,e.objects[o]=p):"PerspectiveCamera"===i.type||"OrthographicCamera"===i.type?("PerspectiveCamera"===i.type?b=new THREE.PerspectiveCamera(i.fov,i.aspect,i.near,i.far):"OrthographicCamera"===i.type&&(b=new THREE.OrthographicCamera(i.left,i.right,i.top,i.bottom,i.near,i.far)),u=i.position,b.position.set(u[0],u[1],u[2]),n.add(b),b.name=o,e.cameras[o]=b,e.objects[o]=b):(u=i.position,l=i.rotation,w=i.scale,c=i.quaternion,r=new THREE.Object3D,r.name=o,r.position.set(u[0],u[1],u[2]),c?(r.quaternion.set(c[0],c[1],c[2],c[3]),r.useQuaternion=!0):r.rotation.set(l[0],l[1],l[2]),r.scale.set(w[0],w[1],w[2]),r.visible=void 0!==i.visible?i.visible:!1,n.add(r),e.objects[o]=r,e.empties[o]=r);if(r){if(void 0!==i.properties)for(d in i.properties)r.properties[d]=i.properties[d];if(void 0!==i.groups)for(s=0;s<i.groups.length;s++)u=i.groups[s],void 0===e.groups[u]&&(e.groups[u]=[]),e.groups[u].push(o);void 0!==i.children&&st(r,i.children)}}}function ct(n){return function(t,i){e.geometries[n]=t;e.face_materials[n]=i;rt();w-=1;y.onLoadComplete();tt()}}function ht(n,t,i,r){return function(u){var u=u.content?u.content:u.dae?u.scene:u,o=r.position,s=r.rotation,f=r.quaternion,h=r.scale,c;u.position.set(o[0],o[1],o[2]);f?(u.quaternion.set(f[0],f[1],f[2],f[3]),u.useQuaternion=!0):u.rotation.set(s[0],s[1],s[2]);u.scale.set(h[0],h[1],h[2]);i&&u.traverse(function(n){n.material=i});c=void 0!==r.visible?r.visible:!0;u.traverse(function(n){n.visible=c});t.add(u);u.name=n;e.objects[n]=u;rt();w-=1;y.onLoadComplete();tt()}}function lt(n){return function(t,i){e.geometries[n]=t;e.face_materials[n]=i}}function tt(){var i,n,r;if(y.callbackProgress({totalModels:ft,totalTextures:et,loadedModels:ft-w,loadedTextures:et-k},e),y.onLoadProgress(),0===w&&0===k){for(i=0;i<ot.length;i++)n=ot[i],r=e.objects[n.targetName],r?n.object.target=r:(n.object.target=new THREE.Object3D,e.scene.add(n.object.target)),n.object.target.properties.targetInverse=n.object;t(e)}}var y=this,at=THREE.Loader.prototype.extractUrlBase(i),v,a,b,f,o,p,it,ut,w,k,ft,et,e,ot=[],h=n,c,nt,r,l,u,d,s;for(c in this.geometryHandlerMap)n=this.geometryHandlerMap[c].loaderClass,this.geometryHandlerMap[c].loaderObject=new n;for(c in this.hierarchyHandlerMap)n=this.hierarchyHandlerMap[c].loaderClass,this.hierarchyHandlerMap[c].loaderObject=new n;k=w=0;e={scene:new THREE.Scene,geometries:{},face_materials:{},materials:{},textures:{},objects:{},cameras:{},lights:{},fogs:{},empties:{},groups:{}};h.transform&&(c=h.transform.position,n=h.transform.rotation,i=h.transform.scale,c&&e.scene.position.set(c[0],c[1],c[2]),n&&e.scene.rotation.set(n[0],n[1],n[2]),i&&e.scene.scale.set(i[0],i[1],i[2]),c||n||i)&&(e.scene.updateMatrix(),e.scene.updateMatrixWorld());c=function(n){return function(){k-=n;tt();y.onLoadComplete()}};for(nt in h.fogs)n=h.fogs[nt],"linear"===n.type?f=new THREE.Fog(0,n.near,n.far):"exp2"===n.type&&(f=new THREE.FogExp2(0,n.density)),n=n.color,f.color.setRGB(n[0],n[1],n[2]),e.fogs[nt]=f;for(r in h.geometries)f=h.geometries[r],f.type in this.geometryHandlerMap&&(w+=1,y.onLoadStart());for(l in h.objects)f=h.objects[l],f.type&&f.type in this.hierarchyHandlerMap&&(w+=1,y.onLoadStart());ft=w;for(r in h.geometries)if(f=h.geometries[r],"cube"===f.type)v=new THREE.CubeGeometry(f.width,f.height,f.depth,f.widthSegments,f.heightSegments,f.depthSegments),e.geometries[r]=v;else if("plane"===f.type)v=new THREE.PlaneGeometry(f.width,f.height,f.widthSegments,f.heightSegments),e.geometries[r]=v;else if("sphere"===f.type)v=new THREE.SphereGeometry(f.radius,f.widthSegments,f.heightSegments),e.geometries[r]=v;else if("cylinder"===f.type)v=new THREE.CylinderGeometry(f.topRad,f.botRad,f.height,f.radSegs,f.heightSegs),e.geometries[r]=v;else if("torus"===f.type)v=new THREE.TorusGeometry(f.radius,f.tube,f.segmentsR,f.segmentsT),e.geometries[r]=v;else if("icosahedron"===f.type)v=new THREE.IcosahedronGeometry(f.radius,f.subdivisions),e.geometries[r]=v;else if(f.type in this.geometryHandlerMap){l={};for(o in f)"type"!==o&&"url"!==o&&(l[o]=f[o]);this.geometryHandlerMap[f.type].loaderObject.load(g(f.url,h.urlBaseType),ct(r),l)}else"embedded"===f.type&&(l=h.embeds[f.id],l.metadata=h.metadata,l&&this.geometryHandlerMap.ascii.loaderObject.createModel(l,lt(r),""));for(u in h.textures)if(r=h.textures[u],r.url instanceof Array)for(k+=r.url.length,o=0;o<r.url.length;o++)y.onLoadStart();else k+=1,y.onLoadStart();et=k;for(u in h.textures){if(r=h.textures[u],void 0!==r.mapping&&void 0!==THREE[r.mapping]&&(r.mapping=new THREE[r.mapping]),r.url instanceof Array){for(l=r.url.length,f=[],o=0;o<l;o++)f[o]=g(r.url[o],h.urlBaseType);o=(o=/\.dds$/i.test(f[0]))?THREE.ImageUtils.loadCompressedTextureCube(f,r.mapping,c(l)):THREE.ImageUtils.loadTextureCube(f,r.mapping,c(l))}else o=/\.dds$/i.test(r.url),l=g(r.url,h.urlBaseType),f=c(1),o=o?THREE.ImageUtils.loadCompressedTexture(l,r.mapping,f):THREE.ImageUtils.loadTexture(l,r.mapping,f),void 0!==THREE[r.minFilter]&&(o.minFilter=THREE[r.minFilter]),void 0!==THREE[r.magFilter]&&(o.magFilter=THREE[r.magFilter]),r.anisotropy&&(o.anisotropy=r.anisotropy),r.repeat&&(o.repeat.set(r.repeat[0],r.repeat[1]),1!==r.repeat[0]&&(o.wrapS=THREE.RepeatWrapping),1!==r.repeat[1]&&(o.wrapT=THREE.RepeatWrapping)),r.offset&&o.offset.set(r.offset[0],r.offset[1]),r.wrap&&(l={repeat:THREE.RepeatWrapping,mirror:THREE.MirroredRepeatWrapping},void 0!==l[r.wrap[0]]&&(o.wrapS=l[r.wrap[0]]),void 0!==l[r.wrap[1]]&&(o.wrapT=l[r.wrap[1]]));e.textures[u]=o}for(d in h.materials){u=h.materials[d];for(s in u.parameters)"envMap"===s||"map"===s||"lightMap"===s||"bumpMap"===s?u.parameters[s]=e.textures[u.parameters[s]]:"shading"===s?u.parameters[s]="flat"===u.parameters[s]?THREE.FlatShading:THREE.SmoothShading:"side"===s?u.parameters[s]="double"==u.parameters[s]?THREE.DoubleSide:"back"==u.parameters[s]?THREE.BackSide:THREE.FrontSide:"blending"===s?u.parameters[s]=u.parameters[s]in THREE?THREE[u.parameters[s]]:THREE.NormalBlending:"combine"===s?u.parameters[s]=u.parameters[s]in THREE?THREE[u.parameters[s]]:THREE.MultiplyOperation:"vertexColors"===s?"face"==u.parameters[s]?u.parameters[s]=THREE.FaceColors:u.parameters[s]&&(u.parameters[s]=THREE.VertexColors):"wrapRGB"===s&&(c=u.parameters[s],u.parameters[s]=new THREE.Vector3(c[0],c[1],c[2]));void 0!==u.parameters.opacity&&1>u.parameters.opacity&&(u.parameters.transparent=!0);u.parameters.normalMap?(c=THREE.ShaderLib.normalmap,r=THREE.UniformsUtils.clone(c.uniforms),o=u.parameters.color,l=u.parameters.specular,f=u.parameters.ambient,nt=u.parameters.shininess,r.tNormal.value=e.textures[u.parameters.normalMap],u.parameters.normalScale&&r.uNormalScale.value.set(u.parameters.normalScale[0],u.parameters.normalScale[1]),u.parameters.map&&(r.tDiffuse.value=u.parameters.map,r.enableDiffuse.value=!0),u.parameters.envMap&&(r.tCube.value=u.parameters.envMap,r.enableReflection.value=!0,r.uReflectivity.value=u.parameters.reflectivity),u.parameters.lightMap&&(r.tAO.value=u.parameters.lightMap,r.enableAO.value=!0),u.parameters.specularMap&&(r.tSpecular.value=e.textures[u.parameters.specularMap],r.enableSpecular.value=!0),u.parameters.displacementMap&&(r.tDisplacement.value=e.textures[u.parameters.displacementMap],r.enableDisplacement.value=!0,r.uDisplacementBias.value=u.parameters.displacementBias,r.uDisplacementScale.value=u.parameters.displacementScale),r.uDiffuseColor.value.setHex(o),r.uSpecularColor.value.setHex(l),r.uAmbientColor.value.setHex(f),r.uShininess.value=nt,u.parameters.opacity&&(r.uOpacity.value=u.parameters.opacity),a=new THREE.ShaderMaterial({fragmentShader:c.fragmentShader,vertexShader:c.vertexShader,uniforms:r,lights:!0,fog:!0})):a=new THREE[u.type](u.parameters);e.materials[d]=a}for(d in h.materials)if(u=h.materials[d],u.parameters.materials){for(s=[],o=0;o<u.parameters.materials.length;o++)s.push(e.materials[u.parameters.materials[o]]);e.materials[d].materials=s}rt();e.cameras&&h.defaults.camera&&(e.currentCamera=e.cameras[h.defaults.camera]);e.fogs&&h.defaults.fog&&(e.scene.fog=e.fogs[h.defaults.fog]);y.callbackSync(e);tt()};THREE.TextureLoader=function(){THREE.EventDispatcher.call(this);this.crossOrigin=null};THREE.TextureLoader.prototype={constructor:THREE.TextureLoader,load:function(n){var i=this,t=new Image;t.addEventListener("load",function(){var n=new THREE.Texture(t);n.needsUpdate=!0;i.dispatchEvent({type:"load",content:n})},!1);t.addEventListener("error",function(){i.dispatchEvent({type:"error",message:"Couldn't load URL ["+n+"]"})},!1);i.crossOrigin&&(t.crossOrigin=i.crossOrigin);t.src=n}};THREE.Material=function(){THREE.EventDispatcher.call(this);this.id=THREE.MaterialIdCount++;this.name="";this.side=THREE.FrontSide;this.opacity=1;this.transparent=!1;this.blending=THREE.NormalBlending;this.blendSrc=THREE.SrcAlphaFactor;this.blendDst=THREE.OneMinusSrcAlphaFactor;this.blendEquation=THREE.AddEquation;this.depthWrite=this.depthTest=!0;this.polygonOffset=!1;this.alphaTest=this.polygonOffsetUnits=this.polygonOffsetFactor=0;this.overdraw=!1;this.needsUpdate=this.visible=!0};THREE.Material.prototype.setValues=function(n){var r,t,i;if(void 0!==n)for(r in n)t=n[r],void 0===t?console.warn("THREE.Material: '"+r+"' parameter is undefined."):r in this&&(i=this[r],i instanceof THREE.Color&&t instanceof THREE.Color?i.copy(t):i instanceof THREE.Color?i.set(t):i instanceof THREE.Vector3&&t instanceof THREE.Vector3?i.copy(t):this[r]=t)};THREE.Material.prototype.clone=function(n){return void 0===n&&(n=new THREE.Material),n.name=this.name,n.side=this.side,n.opacity=this.opacity,n.transparent=this.transparent,n.blending=this.blending,n.blendSrc=this.blendSrc,n.blendDst=this.blendDst,n.blendEquation=this.blendEquation,n.depthTest=this.depthTest,n.depthWrite=this.depthWrite,n.polygonOffset=this.polygonOffset,n.polygonOffsetFactor=this.polygonOffsetFactor,n.polygonOffsetUnits=this.polygonOffsetUnits,n.alphaTest=this.alphaTest,n.overdraw=this.overdraw,n.visible=this.visible,n};THREE.Material.prototype.dispose=function(){this.dispatchEvent({type:"dispose"})};THREE.MaterialIdCount=0;THREE.LineBasicMaterial=function(n){THREE.Material.call(this);this.color=new THREE.Color(16777215);this.linewidth=1;this.linejoin=this.linecap="round";this.vertexColors=!1;this.fog=!0;this.setValues(n)};THREE.LineBasicMaterial.prototype=Object.create(THREE.Material.prototype);THREE.LineBasicMaterial.prototype.clone=function(){var n=new THREE.LineBasicMaterial;return THREE.Material.prototype.clone.call(this,n),n.color.copy(this.color),n.linewidth=this.linewidth,n.linecap=this.linecap,n.linejoin=this.linejoin,n.vertexColors=this.vertexColors,n.fog=this.fog,n};THREE.LineDashedMaterial=function(n){THREE.Material.call(this);this.color=new THREE.Color(16777215);this.scale=this.linewidth=1;this.dashSize=3;this.gapSize=1;this.vertexColors=!1;this.fog=!0;this.setValues(n)};THREE.LineDashedMaterial.prototype=Object.create(THREE.Material.prototype);THREE.LineDashedMaterial.prototype.clone=function(){var n=new THREE.LineDashedMaterial;return THREE.Material.prototype.clone.call(this,n),n.color.copy(this.color),n.linewidth=this.linewidth,n.scale=this.scale,n.dashSize=this.dashSize,n.gapSize=this.gapSize,n.vertexColors=this.vertexColors,n.fog=this.fog,n};THREE.MeshBasicMaterial=function(n){THREE.Material.call(this);this.color=new THREE.Color(16777215);this.envMap=this.specularMap=this.lightMap=this.map=null;this.combine=THREE.MultiplyOperation;this.reflectivity=1;this.refractionRatio=.98;this.fog=!0;this.shading=THREE.SmoothShading;this.wireframe=!1;this.wireframeLinewidth=1;this.wireframeLinejoin=this.wireframeLinecap="round";this.vertexColors=THREE.NoColors;this.morphTargets=this.skinning=!1;this.setValues(n)};THREE.MeshBasicMaterial.prototype=Object.create(THREE.Material.prototype);THREE.MeshBasicMaterial.prototype.clone=function(){var n=new THREE.MeshBasicMaterial;return THREE.Material.prototype.clone.call(this,n),n.color.copy(this.color),n.map=this.map,n.lightMap=this.lightMap,n.specularMap=this.specularMap,n.envMap=this.envMap,n.combine=this.combine,n.reflectivity=this.reflectivity,n.refractionRatio=this.refractionRatio,n.fog=this.fog,n.shading=this.shading,n.wireframe=this.wireframe,n.wireframeLinewidth=this.wireframeLinewidth,n.wireframeLinecap=this.wireframeLinecap,n.wireframeLinejoin=this.wireframeLinejoin,n.vertexColors=this.vertexColors,n.skinning=this.skinning,n.morphTargets=this.morphTargets,n};THREE.MeshLambertMaterial=function(n){THREE.Material.call(this);this.color=new THREE.Color(16777215);this.ambient=new THREE.Color(16777215);this.emissive=new THREE.Color(0);this.wrapAround=!1;this.wrapRGB=new THREE.Vector3(1,1,1);this.envMap=this.specularMap=this.lightMap=this.map=null;this.combine=THREE.MultiplyOperation;this.reflectivity=1;this.refractionRatio=.98;this.fog=!0;this.shading=THREE.SmoothShading;this.wireframe=!1;this.wireframeLinewidth=1;this.wireframeLinejoin=this.wireframeLinecap="round";this.vertexColors=THREE.NoColors;this.morphNormals=this.morphTargets=this.skinning=!1;this.setValues(n)};THREE.MeshLambertMaterial.prototype=Object.create(THREE.Material.prototype);THREE.MeshLambertMaterial.prototype.clone=function(){var n=new THREE.MeshLambertMaterial;return THREE.Material.prototype.clone.call(this,n),n.color.copy(this.color),n.ambient.copy(this.ambient),n.emissive.copy(this.emissive),n.wrapAround=this.wrapAround,n.wrapRGB.copy(this.wrapRGB),n.map=this.map,n.lightMap=this.lightMap,n.specularMap=this.specularMap,n.envMap=this.envMap,n.combine=this.combine,n.reflectivity=this.reflectivity,n.refractionRatio=this.refractionRatio,n.fog=this.fog,n.shading=this.shading,n.wireframe=this.wireframe,n.wireframeLinewidth=this.wireframeLinewidth,n.wireframeLinecap=this.wireframeLinecap,n.wireframeLinejoin=this.wireframeLinejoin,n.vertexColors=this.vertexColors,n.skinning=this.skinning,n.morphTargets=this.morphTargets,n.morphNormals=this.morphNormals,n};THREE.MeshPhongMaterial=function(n){THREE.Material.call(this);this.color=new THREE.Color(16777215);this.ambient=new THREE.Color(16777215);this.emissive=new THREE.Color(0);this.specular=new THREE.Color(1118481);this.shininess=30;this.metal=!1;this.perPixel=!0;this.wrapAround=!1;this.wrapRGB=new THREE.Vector3(1,1,1);this.bumpMap=this.lightMap=this.map=null;this.bumpScale=1;this.normalMap=null;this.normalScale=new THREE.Vector2(1,1);this.envMap=this.specularMap=null;this.combine=THREE.MultiplyOperation;this.reflectivity=1;this.refractionRatio=.98;this.fog=!0;this.shading=THREE.SmoothShading;this.wireframe=!1;this.wireframeLinewidth=1;this.wireframeLinejoin=this.wireframeLinecap="round";this.vertexColors=THREE.NoColors;this.morphNormals=this.morphTargets=this.skinning=!1;this.setValues(n)};THREE.MeshPhongMaterial.prototype=Object.create(THREE.Material.prototype);THREE.MeshPhongMaterial.prototype.clone=function(){var n=new THREE.MeshPhongMaterial;return THREE.Material.prototype.clone.call(this,n),n.color.copy(this.color),n.ambient.copy(this.ambient),n.emissive.copy(this.emissive),n.specular.copy(this.specular),n.shininess=this.shininess,n.metal=this.metal,n.perPixel=this.perPixel,n.wrapAround=this.wrapAround,n.wrapRGB.copy(this.wrapRGB),n.map=this.map,n.lightMap=this.lightMap,n.bumpMap=this.bumpMap,n.bumpScale=this.bumpScale,n.normalMap=this.normalMap,n.normalScale.copy(this.normalScale),n.specularMap=this.specularMap,n.envMap=this.envMap,n.combine=this.combine,n.reflectivity=this.reflectivity,n.refractionRatio=this.refractionRatio,n.fog=this.fog,n.shading=this.shading,n.wireframe=this.wireframe,n.wireframeLinewidth=this.wireframeLinewidth,n.wireframeLinecap=this.wireframeLinecap,n.wireframeLinejoin=this.wireframeLinejoin,n.vertexColors=this.vertexColors,n.skinning=this.skinning,n.morphTargets=this.morphTargets,n.morphNormals=this.morphNormals,n};THREE.MeshDepthMaterial=function(n){THREE.Material.call(this);this.wireframe=!1;this.wireframeLinewidth=1;this.setValues(n)};THREE.MeshDepthMaterial.prototype=Object.create(THREE.Material.prototype);THREE.MeshDepthMaterial.prototype.clone=function(){var n=new THREE.LineBasicMaterial;return THREE.Material.prototype.clone.call(this,n),n.wireframe=this.wireframe,n.wireframeLinewidth=this.wireframeLinewidth,n};THREE.MeshNormalMaterial=function(n){THREE.Material.call(this,n);this.shading=THREE.FlatShading;this.wireframe=!1;this.wireframeLinewidth=1;this.setValues(n)};THREE.MeshNormalMaterial.prototype=Object.create(THREE.Material.prototype);THREE.MeshNormalMaterial.prototype.clone=function(){var n=new THREE.MeshNormalMaterial;return THREE.Material.prototype.clone.call(this,n),n.shading=this.shading,n.wireframe=this.wireframe,n.wireframeLinewidth=this.wireframeLinewidth,n};THREE.MeshFaceMaterial=function(n){this.materials=n instanceof Array?n:[]};THREE.MeshFaceMaterial.prototype.clone=function(){return new THREE.MeshFaceMaterial(this.materials.slice(0))};THREE.ParticleBasicMaterial=function(n){THREE.Material.call(this);this.color=new THREE.Color(16777215);this.map=null;this.size=1;this.sizeAttenuation=!0;this.vertexColors=!1;this.fog=!0;this.setValues(n)};THREE.ParticleBasicMaterial.prototype=Object.create(THREE.Material.prototype);THREE.ParticleBasicMaterial.prototype.clone=function(){var n=new THREE.ParticleBasicMaterial;return THREE.Material.prototype.clone.call(this,n),n.color.copy(this.color),n.map=this.map,n.size=this.size,n.sizeAttenuation=this.sizeAttenuation,n.vertexColors=this.vertexColors,n.fog=this.fog,n};THREE.ParticleCanvasMaterial=function(n){THREE.Material.call(this);this.color=new THREE.Color(16777215);this.program=function(){};this.setValues(n)};THREE.ParticleCanvasMaterial.prototype=Object.create(THREE.Material.prototype);THREE.ParticleCanvasMaterial.prototype.clone=function(){var n=new THREE.ParticleCanvasMaterial;return THREE.Material.prototype.clone.call(this,n),n.color.copy(this.color),n.program=this.program,n};THREE.ShaderMaterial=function(n){THREE.Material.call(this);this.vertexShader=this.fragmentShader="void main() {}";this.uniforms={};this.defines={};this.attributes=null;this.shading=THREE.SmoothShading;this.wireframe=!1;this.wireframeLinewidth=1;this.lights=this.fog=!1;this.vertexColors=THREE.NoColors;this.morphNormals=this.morphTargets=this.skinning=!1;this.setValues(n)};THREE.ShaderMaterial.prototype=Object.create(THREE.Material.prototype);THREE.ShaderMaterial.prototype.clone=function(){var n=new THREE.ShaderMaterial;return THREE.Material.prototype.clone.call(this,n),n.fragmentShader=this.fragmentShader,n.vertexShader=this.vertexShader,n.uniforms=THREE.UniformsUtils.clone(this.uniforms),n.attributes=this.attributes,n.defines=this.defines,n.shading=this.shading,n.wireframe=this.wireframe,n.wireframeLinewidth=this.wireframeLinewidth,n.fog=this.fog,n.lights=this.lights,n.vertexColors=this.vertexColors,n.skinning=this.skinning,n.morphTargets=this.morphTargets,n.morphNormals=this.morphNormals,n};THREE.SpriteMaterial=function(n){THREE.Material.call(this);this.color=new THREE.Color(16777215);this.map=new THREE.Texture;this.useScreenCoordinates=!0;this.depthTest=!this.useScreenCoordinates;this.sizeAttenuation=!this.useScreenCoordinates;this.scaleByViewport=!this.sizeAttenuation;this.alignment=THREE.SpriteAlignment.center.clone();this.fog=!1;this.uvOffset=new THREE.Vector2(0,0);this.uvScale=new THREE.Vector2(1,1);this.setValues(n);n=n||{};void 0===n.depthTest&&(this.depthTest=!this.useScreenCoordinates);void 0===n.sizeAttenuation&&(this.sizeAttenuation=!this.useScreenCoordinates);void 0===n.scaleByViewport&&(this.scaleByViewport=!this.sizeAttenuation)};THREE.SpriteMaterial.prototype=Object.create(THREE.Material.prototype);THREE.SpriteMaterial.prototype.clone=function(){var n=new THREE.SpriteMaterial;return THREE.Material.prototype.clone.call(this,n),n.color.copy(this.color),n.map=this.map,n.useScreenCoordinates=this.useScreenCoordinates,n.sizeAttenuation=this.sizeAttenuation,n.scaleByViewport=this.scaleByViewport,n.alignment.copy(this.alignment),n.uvOffset.copy(this.uvOffset),n.uvScale.copy(this.uvScale),n.fog=this.fog,n};THREE.SpriteAlignment={};THREE.SpriteAlignment.topLeft=new THREE.Vector2(1,-1);THREE.SpriteAlignment.topCenter=new THREE.Vector2(0,-1);THREE.SpriteAlignment.topRight=new THREE.Vector2(-1,-1);THREE.SpriteAlignment.centerLeft=new THREE.Vector2(1,0);THREE.SpriteAlignment.center=new THREE.Vector2(0,0);THREE.SpriteAlignment.centerRight=new THREE.Vector2(-1,0);THREE.SpriteAlignment.bottomLeft=new THREE.Vector2(1,1);THREE.SpriteAlignment.bottomCenter=new THREE.Vector2(0,1);THREE.SpriteAlignment.bottomRight=new THREE.Vector2(-1,1);THREE.Texture=function(n,t,i,r,u,f,e,o,s){THREE.EventDispatcher.call(this);this.id=THREE.TextureIdCount++;this.name="";this.image=n;this.mipmaps=[];this.mapping=void 0!==t?t:new THREE.UVMapping;this.wrapS=void 0!==i?i:THREE.ClampToEdgeWrapping;this.wrapT=void 0!==r?r:THREE.ClampToEdgeWrapping;this.magFilter=void 0!==u?u:THREE.LinearFilter;this.minFilter=void 0!==f?f:THREE.LinearMipMapLinearFilter;this.anisotropy=void 0!==s?s:1;this.format=void 0!==e?e:THREE.RGBAFormat;this.type=void 0!==o?o:THREE.UnsignedByteType;this.offset=new THREE.Vector2(0,0);this.repeat=new THREE.Vector2(1,1);this.generateMipmaps=!0;this.premultiplyAlpha=!1;this.flipY=!0;this.unpackAlignment=4;this.needsUpdate=!1;this.onUpdate=null};THREE.Texture.prototype={constructor:THREE.Texture,clone:function(n){return void 0===n&&(n=new THREE.Texture),n.image=this.image,n.mipmaps=this.mipmaps.slice(0),n.mapping=this.mapping,n.wrapS=this.wrapS,n.wrapT=this.wrapT,n.magFilter=this.magFilter,n.minFilter=this.minFilter,n.anisotropy=this.anisotropy,n.format=this.format,n.type=this.type,n.offset.copy(this.offset),n.repeat.copy(this.repeat),n.generateMipmaps=this.generateMipmaps,n.premultiplyAlpha=this.premultiplyAlpha,n.flipY=this.flipY,n.unpackAlignment=this.unpackAlignment,n},dispose:function(){this.dispatchEvent({type:"dispose"})}};THREE.TextureIdCount=0;THREE.CompressedTexture=function(n,t,i,r,u,f,e,o,s,h,c){THREE.Texture.call(this,null,f,e,o,s,h,r,u,c);this.image={width:t,height:i};this.mipmaps=n;this.generateMipmaps=!1};THREE.CompressedTexture.prototype=Object.create(THREE.Texture.prototype);THREE.CompressedTexture.prototype.clone=function(){var n=new THREE.CompressedTexture;return THREE.Texture.prototype.clone.call(this,n),n};THREE.DataTexture=function(n,t,i,r,u,f,e,o,s,h,c){THREE.Texture.call(this,null,f,e,o,s,h,r,u,c);this.image={data:n,width:t,height:i}};THREE.DataTexture.prototype=Object.create(THREE.Texture.prototype);THREE.DataTexture.prototype.clone=function(){var n=new THREE.DataTexture;return THREE.Texture.prototype.clone.call(this,n),n};THREE.Particle=function(n){THREE.Object3D.call(this);this.material=n};THREE.Particle.prototype=Object.create(THREE.Object3D.prototype);THREE.Particle.prototype.clone=function(n){return void 0===n&&(n=new THREE.Particle(this.material)),THREE.Object3D.prototype.clone.call(this,n),n};THREE.ParticleSystem=function(n,t){THREE.Object3D.call(this);this.geometry=n;this.material=void 0!==t?t:new THREE.ParticleBasicMaterial({color:16777215*Math.random()});this.sortParticles=!1;this.geometry&&null===this.geometry.boundingSphere&&this.geometry.computeBoundingSphere();this.frustumCulled=!1};THREE.ParticleSystem.prototype=Object.create(THREE.Object3D.prototype);THREE.ParticleSystem.prototype.clone=function(n){return void 0===n&&(n=new THREE.ParticleSystem(this.geometry,this.material)),n.sortParticles=this.sortParticles,THREE.Object3D.prototype.clone.call(this,n),n};THREE.Line=function(n,t,i){THREE.Object3D.call(this);this.geometry=n;this.material=void 0!==t?t:new THREE.LineBasicMaterial({color:16777215*Math.random()});this.type=void 0!==i?i:THREE.LineStrip;this.geometry&&(this.geometry.boundingSphere||this.geometry.computeBoundingSphere())};THREE.LineStrip=0;THREE.LinePieces=1;THREE.Line.prototype=Object.create(THREE.Object3D.prototype);THREE.Line.prototype.clone=function(n){return void 0===n&&(n=new THREE.Line(this.geometry,this.material,this.type)),THREE.Object3D.prototype.clone.call(this,n),n};THREE.Mesh=function(n,t){THREE.Object3D.call(this);this.geometry=n;this.material=void 0!==t?t:new THREE.MeshBasicMaterial({color:16777215*Math.random(),wireframe:!0});void 0!==this.geometry&&(null===this.geometry.boundingSphere&&this.geometry.computeBoundingSphere(),this.updateMorphTargets())};THREE.Mesh.prototype=Object.create(THREE.Object3D.prototype);THREE.Mesh.prototype.updateMorphTargets=function(){if(0<this.geometry.morphTargets.length){this.morphTargetBase=-1;this.morphTargetForcedOrder=[];this.morphTargetInfluences=[];this.morphTargetDictionary={};for(var n=0,t=this.geometry.morphTargets.length;n<t;n++)this.morphTargetInfluences.push(0),this.morphTargetDictionary[this.geometry.morphTargets[n].name]=n}};THREE.Mesh.prototype.getMorphTargetIndexByName=function(n){return void 0!==this.morphTargetDictionary[n]?this.morphTargetDictionary[n]:(console.log("THREE.Mesh.getMorphTargetIndexByName: morph target "+n+" does not exist. Returning 0."),0)};THREE.Mesh.prototype.clone=function(n){return void 0===n&&(n=new THREE.Mesh(this.geometry,this.material)),THREE.Object3D.prototype.clone.call(this,n),n};THREE.Bone=function(n){THREE.Object3D.call(this);this.skin=n;this.skinMatrix=new THREE.Matrix4};THREE.Bone.prototype=Object.create(THREE.Object3D.prototype);THREE.Bone.prototype.update=function(n,t){this.matrixAutoUpdate&&(t|=this.updateMatrix());(t||this.matrixWorldNeedsUpdate)&&(n?this.skinMatrix.multiplyMatrices(n,this.matrix):this.skinMatrix.copy(this.matrix),this.matrixWorldNeedsUpdate=!1,t=!0);for(var r=this.children.length,i=0;i<r;i++)this.children[i].update(this.skinMatrix,t)};THREE.SkinnedMesh=function(n,t,i){THREE.Mesh.call(this,n,t);this.useVertexTexture=void 0!==i?i:!0;this.identityMatrix=new THREE.Matrix4;this.bones=[];this.boneMatrices=[];var f,r,u;if(this.geometry&&void 0!==this.geometry.bones){for(n=0;n<this.geometry.bones.length;n++)i=this.geometry.bones[n],f=i.pos,r=i.rotq,u=i.scl,t=this.addBone(),t.name=i.name,t.position.set(f[0],f[1],f[2]),t.quaternion.set(r[0],r[1],r[2],r[3]),t.useQuaternion=!0,void 0!==u?t.scale.set(u[0],u[1],u[2]):t.scale.set(1,1,1);for(n=0;n<this.bones.length;n++)i=this.geometry.bones[n],t=this.bones[n],-1===i.parent?this.add(t):this.bones[i.parent].add(t);n=this.bones.length;this.useVertexTexture?(this.boneTextureHeight=this.boneTextureWidth=n=256<n?64:64<n?32:16<n?16:8,this.boneMatrices=new Float32Array(4*this.boneTextureWidth*this.boneTextureHeight),this.boneTexture=new THREE.DataTexture(this.boneMatrices,this.boneTextureWidth,this.boneTextureHeight,THREE.RGBAFormat,THREE.FloatType),this.boneTexture.minFilter=THREE.NearestFilter,this.boneTexture.magFilter=THREE.NearestFilter,this.boneTexture.generateMipmaps=!1,this.boneTexture.flipY=!1):this.boneMatrices=new Float32Array(16*n);this.pose()}};THREE.SkinnedMesh.prototype=Object.create(THREE.Mesh.prototype);THREE.SkinnedMesh.prototype.addBone=function(n){return void 0===n&&(n=new THREE.Bone(this)),this.bones.push(n),n};THREE.SkinnedMesh.prototype.updateMatrixWorld=function(n){var n,i,t;for(this.matrixAutoUpdate&&this.updateMatrix(),(this.matrixWorldNeedsUpdate||n)&&(this.parent?this.matrixWorld.multiplyMatrices(this.parent.matrixWorld,this.matrix):this.matrixWorld.copy(this.matrix),this.matrixWorldNeedsUpdate=!1),n=0,i=this.children.length;n<i;n++)t=this.children[n],t instanceof THREE.Bone?t.update(this.identityMatrix,!1):t.updateMatrixWorld(!0);if(void 0==this.boneInverses)for(this.boneInverses=[],n=0,i=this.bones.length;n<i;n++)t=new THREE.Matrix4,t.getInverse(this.bones[n].skinMatrix),this.boneInverses.push(t);for(n=0,i=this.bones.length;n<i;n++)THREE.SkinnedMesh.offsetMatrix.multiplyMatrices(this.bones[n].skinMatrix,this.boneInverses[n]),THREE.SkinnedMesh.offsetMatrix.flattenToArrayOffset(this.boneMatrices,16*n);this.useVertexTexture&&(this.boneTexture.needsUpdate=!0)};THREE.SkinnedMesh.prototype.pose=function(){var n,t,i;for(this.updateMatrixWorld(!0),n=0;n<this.geometry.skinIndices.length;n++)t=this.geometry.skinWeights[n],i=1/t.lengthManhattan(),Infinity!==i?t.multiplyScalar(i):t.set(1)};THREE.SkinnedMesh.prototype.clone=function(n){return void 0===n&&(n=new THREE.SkinnedMesh(this.geometry,this.material,this.useVertexTexture)),THREE.Mesh.prototype.clone.call(this,n),n};THREE.SkinnedMesh.offsetMatrix=new THREE.Matrix4;THREE.MorphAnimMesh=function(n,t){THREE.Mesh.call(this,n,t);this.duration=1e3;this.mirroredLoop=!1;this.currentKeyframe=this.lastKeyframe=this.time=0;this.direction=1;this.directionBackwards=!1;this.setFrameRange(0,this.geometry.morphTargets.length-1)};THREE.MorphAnimMesh.prototype=Object.create(THREE.Mesh.prototype);THREE.MorphAnimMesh.prototype.setFrameRange=function(n,t){this.startKeyframe=n;this.endKeyframe=t;this.length=this.endKeyframe-this.startKeyframe+1};THREE.MorphAnimMesh.prototype.setDirectionForward=function(){this.direction=1;this.directionBackwards=!1};THREE.MorphAnimMesh.prototype.setDirectionBackward=function(){this.direction=-1;this.directionBackwards=!0};THREE.MorphAnimMesh.prototype.parseAnimations=function(){var i=this.geometry,n,r;i.animations||(i.animations={});for(var u,f=i.animations,t=0,e=i.morphTargets.length;t<e;t++)n=i.morphTargets[t].name.match(/([a-z]+)(\d+)/),n&&1<n.length&&(n=n[1],f[n]||(f[n]={start:Infinity,end:-Infinity}),r=f[n],t<r.start&&(r.start=t),t>r.end&&(r.end=t),u||(u=n));i.firstAnimation=u};THREE.MorphAnimMesh.prototype.setAnimationLabel=function(n,t,i){this.geometry.animations||(this.geometry.animations={});this.geometry.animations[n]={start:t,end:i}};THREE.MorphAnimMesh.prototype.playAnimation=function(n,t){var i=this.geometry.animations[n];i?(this.setFrameRange(i.start,i.end),this.duration=1e3*((i.end-i.start)/t),this.time=0):console.warn("animation["+n+"] undefined")};THREE.MorphAnimMesh.prototype.updateAnimation=function(n){var t=this.duration/this.length;this.time+=this.direction*n;this.mirroredLoop?(this.time>this.duration||0>this.time)&&(this.direction*=-1,this.time>this.duration&&(this.time=this.duration,this.directionBackwards=!0),0>this.time&&(this.time=0,this.directionBackwards=!1)):(this.time%=this.duration,0>this.time&&(this.time+=this.duration));n=this.startKeyframe+THREE.Math.clamp(Math.floor(this.time/t),0,this.length-1);n!==this.currentKeyframe&&(this.morphTargetInfluences[this.lastKeyframe]=0,this.morphTargetInfluences[this.currentKeyframe]=1,this.morphTargetInfluences[n]=0,this.lastKeyframe=this.currentKeyframe,this.currentKeyframe=n);t=this.time%t/t;this.directionBackwards&&(t=1-t);this.morphTargetInfluences[this.currentKeyframe]=t;this.morphTargetInfluences[this.lastKeyframe]=1-t};THREE.MorphAnimMesh.prototype.clone=function(n){return void 0===n&&(n=new THREE.MorphAnimMesh(this.geometry,this.material)),n.duration=this.duration,n.mirroredLoop=this.mirroredLoop,n.time=this.time,n.lastKeyframe=this.lastKeyframe,n.currentKeyframe=this.currentKeyframe,n.direction=this.direction,n.directionBackwards=this.directionBackwards,THREE.Mesh.prototype.clone.call(this,n),n};THREE.Ribbon=function(n,t){THREE.Object3D.call(this);this.geometry=n;this.material=t};THREE.Ribbon.prototype=Object.create(THREE.Object3D.prototype);THREE.Ribbon.prototype.clone=function(n){return void 0===n&&(n=new THREE.Ribbon(this.geometry,this.material)),THREE.Object3D.prototype.clone.call(this,n),n};THREE.LOD=function(){THREE.Object3D.call(this);this.LODs=[]};THREE.LOD.prototype=Object.create(THREE.Object3D.prototype);THREE.LOD.prototype.addLevel=function(n,t){void 0===t&&(t=0);for(var t=Math.abs(t),i=0;i<this.LODs.length&&!(t<this.LODs[i].visibleAtDistance);i++);this.LODs.splice(i,0,{visibleAtDistance:t,object3D:n});this.add(n)};THREE.LOD.prototype.update=function(n){if(1<this.LODs.length){n.matrixWorldInverse.getInverse(n.matrixWorld);n=n.matrixWorldInverse;n=-(n.elements[2]*this.matrixWorld.elements[12]+n.elements[6]*this.matrixWorld.elements[13]+n.elements[10]*this.matrixWorld.elements[14]+n.elements[14]);this.LODs[0].object3D.visible=!0;for(var t=1;t<this.LODs.length;t++)if(n>=this.LODs[t].visibleAtDistance)this.LODs[t-1].object3D.visible=!1,this.LODs[t].object3D.visible=!0;else break;for(;t<this.LODs.length;t++)this.LODs[t].object3D.visible=!1}};THREE.LOD.prototype.clone=function(){};THREE.Sprite=function(n){THREE.Object3D.call(this);this.material=void 0!==n?n:new THREE.SpriteMaterial;this.rotation3d=this.rotation;this.rotation=0};THREE.Sprite.prototype=Object.create(THREE.Object3D.prototype);THREE.Sprite.prototype.updateMatrix=function(){this.matrix.setPosition(this.position);this.rotation3d.set(0,0,this.rotation);this.matrix.setRotationFromEuler(this.rotation3d);(1!==this.scale.x||1!==this.scale.y)&&this.matrix.scale(this.scale);this.matrixWorldNeedsUpdate=!0};THREE.Sprite.prototype.clone=function(n){return void 0===n&&(n=new THREE.Sprite(this.material)),THREE.Object3D.prototype.clone.call(this,n),n};THREE.Scene=function(){THREE.Object3D.call(this);this.overrideMaterial=this.fog=null;this.matrixAutoUpdate=!1;this.__objects=[];this.__lights=[];this.__objectsAdded=[];this.__objectsRemoved=[]};THREE.Scene.prototype=Object.create(THREE.Object3D.prototype);THREE.Scene.prototype.__addObject=function(n){if(n instanceof THREE.Light)-1===this.__lights.indexOf(n)&&this.__lights.push(n),n.target&&void 0===n.target.parent&&this.add(n.target);else if(!(n instanceof THREE.Camera||n instanceof THREE.Bone)&&-1===this.__objects.indexOf(n)){this.__objects.push(n);this.__objectsAdded.push(n);var t=this.__objectsRemoved.indexOf(n);-1!==t&&this.__objectsRemoved.splice(t,1)}for(t=0;t<n.children.length;t++)this.__addObject(n.children[t])};THREE.Scene.prototype.__removeObject=function(n){if(n instanceof THREE.Light){var t=this.__lights.indexOf(n);-1!==t&&this.__lights.splice(t,1)}else n instanceof THREE.Camera||(t=this.__objects.indexOf(n),-1!==t&&(this.__objects.splice(t,1),this.__objectsRemoved.push(n),t=this.__objectsAdded.indexOf(n),-1!==t&&this.__objectsAdded.splice(t,1)));for(t=0;t<n.children.length;t++)this.__removeObject(n.children[t])};THREE.Fog=function(n,t,i){this.name="";this.color=new THREE.Color(n);this.near=void 0!==t?t:1;this.far=void 0!==i?i:1e3};THREE.Fog.prototype.clone=function(){return new THREE.Fog(this.color.getHex(),this.near,this.far)};THREE.FogExp2=function(n,t){this.name="";this.color=new THREE.Color(n);this.density=void 0!==t?t:.00025};THREE.FogExp2.prototype.clone=function(){return new THREE.FogExp2(this.color.getHex(),this.density)};THREE.CanvasRenderer=function(n){function ti(n){ki!==n&&(ki=t.globalAlpha=n)}function ii(n){di!==n&&(n===THREE.NormalBlending?t.globalCompositeOperation="source-over":n===THREE.AdditiveBlending?t.globalCompositeOperation="lighter":n===THREE.SubtractiveBlending&&(t.globalCompositeOperation="darker"),di=n)}function li(n){tr!==n&&(tr=t.lineWidth=n)}function ai(n){ir!==n&&(ir=t.lineCap=n)}function vi(n){rr!==n&&(rr=t.lineJoin=n)}function si(n){gi!==n&&(gi=t.strokeStyle=n)}function wt(n){nr!==n&&(nr=t.fillStyle=n)}function cr(n,i){(lr!==n||ar!==i)&&(t.setLineDash([n,i]),lr=n,ar=i)}console.log("THREE.CanvasRenderer",THREE.REVISION);var ct=THREE.Math.smoothstep,n=n||{},bt=this,yi,pi,kt,nu=new THREE.Projector,dt=void 0!==n.canvas?n.canvas:document.createElement("canvas"),wi,bi,u,f,t=dt.getContext("2d"),ri=new THREE.Color(0),ui=0,ki=1,di=0,gi=null,nr=null,tr=null,ir=null,rr=null,lr=null,ar=0,r,e,l,ot,ur=new THREE.RenderableVertex,fr=new THREE.RenderableVertex,a,v,y,p,w,b,ut,ft,lt,at,vt,yt,i=new THREE.Color,h=new THREE.Color,o=new THREE.Color,s=new THREE.Color,d=new THREE.Color,nt=new THREE.Color,it=new THREE.Color,er=new THREE.Color,or={},sr={},st,ht,g,pt,vr,yr,pr,wr,br,kr,gt=new THREE.Box2,c=new THREE.Box2,k=new THREE.Box2,hi=!1,et=new THREE.Color,dr=new THREE.Color,gr=new THREE.Color,rt=new THREE.Vector3,fi,ei,hr,tt,oi,ci,ni=16;fi=document.createElement("canvas");fi.width=fi.height=2;ei=fi.getContext("2d");ei.fillStyle="rgba(0,0,0,1)";ei.fillRect(0,0,2,2);hr=ei.getImageData(0,0,2,2);tt=hr.data;oi=document.createElement("canvas");oi.width=oi.height=ni;ci=oi.getContext("2d");ci.translate(-ni/2,-ni/2);ci.scale(ni,ni);ni--;void 0===t.setLineDash&&(t.setLineDash=void 0!==t.mozDash?function(n){t.mozDash=null!==n[0]?n:null}:function(){});this.domElement=dt;this.devicePixelRatio=void 0!==n.devicePixelRatio?n.devicePixelRatio:void 0!==window.devicePixelRatio?window.devicePixelRatio:1;this.sortElements=this.sortObjects=this.autoClear=!0;this.info={render:{vertices:0,faces:0}};this.supportsVertexTextures=function(){};this.setFaceCulling=function(){};this.setSize=function(n,t){wi=n*this.devicePixelRatio;bi=t*this.devicePixelRatio;u=Math.floor(wi/2);f=Math.floor(bi/2);dt.width=wi;dt.height=bi;dt.style.width=n+"px";dt.style.height=t+"px";gt.set(new THREE.Vector2(-u,-f),new THREE.Vector2(u,f));c.set(new THREE.Vector2(-u,-f),new THREE.Vector2(u,f));ki=1;di=0;rr=ir=tr=nr=gi=null};this.setClearColor=function(n,t){ri.copy(n);ui=void 0!==t?t:1;c.set(new THREE.Vector2(-u,-f),new THREE.Vector2(u,f))};this.setClearColorHex=function(n,t){ri.setHex(n);ui=void 0!==t?t:1;c.set(new THREE.Vector2(-u,-f),new THREE.Vector2(u,f))};this.getMaxAnisotropy=function(){return 0};this.clear=function(){t.setTransform(1,0,0,-1,u,f);!1===c.empty()&&(c.intersect(gt),c.expandByScalar(2),1>ui&&t.clearRect(c.min.x|0,c.min.y|0,c.max.x-c.min.x|0,c.max.y-c.min.y|0),0<ui&&(ii(THREE.NormalBlending),ti(1),wt("rgba("+Math.floor(255*ri.r)+","+Math.floor(255*ri.g)+","+Math.floor(255*ri.b)+","+ui+")"),t.fillRect(c.min.x|0,c.min.y|0,c.max.x-c.min.x|0,c.max.y-c.min.y|0)),c.makeEmpty())};this.render=function(n,dt){function nr(n,t,i){for(var r,f,u,e=0,o=kt.length;e<o;e++)r=kt[e],er.copy(r.color),r instanceof THREE.DirectionalLight?(f=rt.getPositionFromMatrix(r.matrixWorld).normalize(),u=t.dot(f),0>=u||(u*=r.intensity,i.add(er.multiplyScalar(u)))):r instanceof THREE.PointLight&&(f=rt.getPositionFromMatrix(r.matrixWorld),u=t.dot(rt.subVectors(f,n).normalize()),0>=u||(u*=0==r.distance?1:1-Math.min(n.distanceTo(f)/r.distance,1),0!=u&&(u*=r.intensity,i.add(er.multiplyScalar(u)))))}function fu(n,t,r,u,f,e,c,l){bt.info.render.vertices+=3;bt.info.render.faces++;ti(l.opacity);ii(l.blending);a=n.positionScreen.x;v=n.positionScreen.y;y=t.positionScreen.x;p=t.positionScreen.y;w=r.positionScreen.x;b=r.positionScreen.y;tu(a,v,y,p,w,b);(l instanceof THREE.MeshLambertMaterial||l instanceof THREE.MeshPhongMaterial)&&null===l.map?(nt.copy(l.color),it.copy(l.emissive),l.vertexColors===THREE.FaceColors&&nt.multiply(c.color),!0===hi?!1===l.wireframe&&l.shading==THREE.SmoothShading&&3==c.vertexNormalsLength?(h.copy(et),o.copy(et),s.copy(et),nr(c.v1.positionWorld,c.vertexNormalsModel[0],h),nr(c.v2.positionWorld,c.vertexNormalsModel[1],o),nr(c.v3.positionWorld,c.vertexNormalsModel[2],s),h.multiply(nt).add(it),o.multiply(nt).add(it),s.multiply(nt).add(it),d.addColors(o,s).multiplyScalar(.5),g=iu(h,o,s,d),ir(a,v,y,p,w,b,0,0,1,0,0,1,g)):(i.copy(et),nr(c.centroidModel,c.normalModel,i),i.multiply(nt).add(it),!0===l.wireframe?rr(i,l.wireframeLinewidth,l.wireframeLinecap,l.wireframeLinejoin):tr(i)):!0===l.wireframe?rr(l.color,l.wireframeLinewidth,l.wireframeLinecap,l.wireframeLinejoin):tr(l.color)):l instanceof THREE.MeshBasicMaterial||l instanceof THREE.MeshLambertMaterial||l instanceof THREE.MeshPhongMaterial?null!==l.map?l.map.mapping instanceof THREE.UVMapping&&(pt=c.uvs[0],su(a,v,y,p,w,b,pt[u].x,pt[u].y,pt[f].x,pt[f].y,pt[e].x,pt[e].y,l.map)):null!==l.envMap?l.envMap.mapping instanceof THREE.SphericalReflectionMapping&&(rt.copy(c.vertexNormalsModelView[u]),vr=.5*rt.x+.5,yr=.5*rt.y+.5,rt.copy(c.vertexNormalsModelView[f]),pr=.5*rt.x+.5,wr=.5*rt.y+.5,rt.copy(c.vertexNormalsModelView[e]),br=.5*rt.x+.5,kr=.5*rt.y+.5,su(a,v,y,p,w,b,vr,yr,pr,wr,br,kr,l.envMap)):(i.copy(l.color),l.vertexColors===THREE.FaceColors&&i.multiply(c.color),!0===l.wireframe?rr(i,l.wireframeLinewidth,l.wireframeLinecap,l.wireframeLinejoin):tr(i)):l instanceof THREE.MeshDepthMaterial?(st=dt.near,ht=dt.far,h.r=h.g=h.b=1-ct(n.positionScreen.z*n.positionScreen.w,st,ht),o.r=o.g=o.b=1-ct(t.positionScreen.z*t.positionScreen.w,st,ht),s.r=s.g=s.b=1-ct(r.positionScreen.z*r.positionScreen.w,st,ht),d.addColors(o,s).multiplyScalar(.5),g=iu(h,o,s,d),ir(a,v,y,p,w,b,0,0,1,0,0,1,g)):l instanceof THREE.MeshNormalMaterial&&(l.shading==THREE.FlatShading?(n=c.normalModelView,i.setRGB(n.x,n.y,n.z).multiplyScalar(.5).addScalar(.5),!0===l.wireframe?rr(i,l.wireframeLinewidth,l.wireframeLinecap,l.wireframeLinejoin):tr(i)):l.shading==THREE.SmoothShading&&(n=c.vertexNormalsModelView[u],h.setRGB(n.x,n.y,n.z).multiplyScalar(.5).addScalar(.5),n=c.vertexNormalsModelView[f],o.setRGB(n.x,n.y,n.z).multiplyScalar(.5).addScalar(.5),n=c.vertexNormalsModelView[e],s.setRGB(n.x,n.y,n.z).multiplyScalar(.5).addScalar(.5),d.addColors(o,s).multiplyScalar(.5),g=iu(h,o,s,d),ir(a,v,y,p,w,b,0,0,1,0,0,1,g)))}function tu(n,i,r,u,f,e){t.beginPath();t.moveTo(n,i);t.lineTo(r,u);t.lineTo(f,e);t.closePath()}function ru(n,i,r,u,f,e,o,s){t.beginPath();t.moveTo(n,i);t.lineTo(r,u);t.lineTo(f,e);t.lineTo(o,s);t.closePath()}function rr(n,i,r,u){li(i);ai(r);vi(u);si(n.getStyle());t.stroke();k.expandByScalar(2*i)}function tr(n){wt(n.getStyle());t.fill()}function su(n,r,u,f,e,o,s,h,c,l,a,v,y){var p,w;if(!(y instanceof THREE.DataTexture||void 0===y.image||0==y.image.width)){!0===y.needsUpdate&&(p=y.wrapS==THREE.RepeatWrapping,w=y.wrapT==THREE.RepeatWrapping,or[y.id]=t.createPattern(y.image,!0===p&&!0===w?"repeat":!0===p&&!1===w?"repeat-x":!1===p&&!0===w?"repeat-y":"no-repeat"),y.needsUpdate=!1);void 0===or[y.id]?wt("rgba(0,0,0,1)"):wt(or[y.id]);var p=y.offset.x/y.repeat.x,w=y.offset.y/y.repeat.y,b=y.image.width*y.repeat.x,k=y.image.height*y.repeat.y,s=(s+p)*b,h=(1-h+w)*k,u=u-n,f=f-r,e=e-n,o=o-r,c=(c+p)*b-s,l=(1-l+w)*k-h,a=(a+p)*b-s,v=(1-v+w)*k-h,p=c*v-a*l;0===p?(void 0===sr[y.id]&&(r=document.createElement("canvas"),r.width=y.image.width,r.height=y.image.height,r=r.getContext("2d"),r.drawImage(y.image,0,0),sr[y.id]=r.getImageData(0,0,y.image.width,y.image.height).data),r=sr[y.id],s=4*(Math.floor(s)+Math.floor(h)*y.image.width),i.setRGB(r[s]/255,r[s+1]/255,r[s+2]/255),tr(i)):(p=1/p,y=(v*u-l*e)*p,l=(v*f-l*o)*p,u=(c*e-a*u)*p,f=(c*o-a*f)*p,n=n-y*s-u*h,s=r-l*s-f*h,t.save(),t.transform(y,l,u,f,n,s),t.fill(),t.restore())}}function ir(n,i,r,u,f,e,o,s,h,c,l,a,v){var p,y;p=v.width-1;y=v.height-1;o*=p;s*=y;r-=n;u-=i;f-=n;e-=i;h=h*p-o;c=c*y-s;l=l*p-o;a=a*y-s;y=1/(h*a-l*c);p=(a*r-c*f)*y;c=(a*u-c*e)*y;r=(h*f-l*r)*y;u=(h*e-l*u)*y;n=n-p*o-r*s;i=i-c*o-u*s;t.save();t.transform(p,c,r,u,n,i);t.clip();t.drawImage(v,0,0);t.restore()}function iu(n,t,i,r){return tt[0]=255*n.r|0,tt[1]=255*n.g|0,tt[2]=255*n.b|0,tt[4]=255*t.r|0,tt[5]=255*t.g|0,tt[6]=255*t.b|0,tt[8]=255*i.r|0,tt[9]=255*i.g|0,tt[10]=255*i.b|0,tt[12]=255*r.r|0,tt[13]=255*r.g|0,tt[14]=255*r.b|0,ei.putImageData(hr,0,0),ci.drawImage(fi,0,0),oi}function lr(n,t){var i=t.x-n.x,r=t.y-n.y,u=i*i+r*r;0!==u&&(u=1/Math.sqrt(u),i*=u,r*=u,t.x+=i,t.y+=r,n.x-=i,n.y-=r)}var ar,uu,ui,ni,ri,wi;if(!1==dt instanceof THREE.Camera)console.error("THREE.CanvasRenderer.render: camera is not an instance of THREE.Camera.");else{if(!0===this.autoClear&&this.clear(),t.setTransform(1,0,0,-1,u,f),bt.info.render.vertices=0,bt.info.render.faces=0,yi=nu.projectScene(n,dt,this.sortObjects,this.sortElements),pi=yi.elements,kt=yi.lights,hi=0<kt.length,!0===hi)for(et.setRGB(0,0,0),dr.setRGB(0,0,0),gr.setRGB(0,0,0),ar=0,uu=kt.length;ar<uu;ar++)ni=kt[ar],ri=ni.color,ni instanceof THREE.AmbientLight?et.add(ri):ni instanceof THREE.DirectionalLight?dr.add(ri):ni instanceof THREE.PointLight&&gr.add(ri);for(ar=0,uu=pi.length;ar<uu;ar++)if(ui=pi[ar],ni=ui.material,!(void 0===ni||!1===ni.visible)){if(k.makeEmpty(),ui instanceof THREE.RenderableParticle){r=ui;r.x*=u;r.y*=f;ri=r;wi=ui;ti(ni.opacity);ii(ni.blending);var bi=void 0,ki=void 0,di=void 0,gi=void 0,eu=ui=void 0,ou=void 0;ni instanceof THREE.ParticleBasicMaterial?null===ni.map?(di=wi.object.scale.x,gi=wi.object.scale.y,di*=wi.scale.x*u,gi*=wi.scale.y*f,k.min.set(ri.x-di,ri.y-gi),k.max.set(ri.x+di,ri.y+gi),!1!==gt.isIntersectionBox(k)&&(wt(ni.color.getStyle()),t.save(),t.translate(ri.x,ri.y),t.rotate(-wi.rotation),t.scale(di,gi),t.fillRect(-1,-1,2,2),t.restore())):(ui=ni.map.image,eu=ui.width>>1,ou=ui.height>>1,di=wi.scale.x*u,gi=wi.scale.y*f,bi=di*eu,ki=gi*ou,k.min.set(ri.x-bi,ri.y-ki),k.max.set(ri.x+bi,ri.y+ki),!1!==gt.isIntersectionBox(k)&&(t.save(),t.translate(ri.x,ri.y),t.rotate(-wi.rotation),t.scale(di,-gi),t.translate(-eu,-ou),t.drawImage(ui,0,0),t.restore())):ni instanceof THREE.ParticleCanvasMaterial&&(bi=wi.scale.x*u,ki=wi.scale.y*f,k.min.set(ri.x-bi,ri.y-ki),k.max.set(ri.x+bi,ri.y+ki),!1!==gt.isIntersectionBox(k)&&(si(ni.color.getStyle()),wt(ni.color.getStyle()),t.save(),t.translate(ri.x,ri.y),t.rotate(-wi.rotation),t.scale(bi,ki),ni.program(t),t.restore()))}else if(ui instanceof THREE.RenderableLine)r=ui.v1,e=ui.v2,r.positionScreen.x*=u,r.positionScreen.y*=f,e.positionScreen.x*=u,e.positionScreen.y*=f,k.setFromPoints([r.positionScreen,e.positionScreen]),!0===gt.isIntersectionBox(k)&&(ri=r,wi=e,ti(ni.opacity),ii(ni.blending),t.beginPath(),t.moveTo(ri.positionScreen.x,ri.positionScreen.y),t.lineTo(wi.positionScreen.x,wi.positionScreen.y),ni instanceof THREE.LineBasicMaterial?(li(ni.linewidth),ai(ni.linecap),vi(ni.linejoin),si(ni.color.getStyle()),cr(null,null),t.stroke(),k.expandByScalar(2*ni.linewidth)):ni instanceof THREE.LineDashedMaterial&&(li(ni.linewidth),ai(ni.linecap),vi(ni.linejoin),si(ni.color.getStyle()),cr(ni.dashSize,ni.gapSize),t.stroke(),k.expandByScalar(2*ni.linewidth)));else if(ui instanceof THREE.RenderableFace3){if(r=ui.v1,e=ui.v2,l=ui.v3,-1>r.positionScreen.z||1<r.positionScreen.z)continue;if(-1>e.positionScreen.z||1<e.positionScreen.z)continue;if(-1>l.positionScreen.z||1<l.positionScreen.z)continue;r.positionScreen.x*=u;r.positionScreen.y*=f;e.positionScreen.x*=u;e.positionScreen.y*=f;l.positionScreen.x*=u;l.positionScreen.y*=f;!0===ni.overdraw&&(lr(r.positionScreen,e.positionScreen),lr(e.positionScreen,l.positionScreen),lr(l.positionScreen,r.positionScreen));k.setFromPoints([r.positionScreen,e.positionScreen,l.positionScreen]);fu(r,e,l,0,1,2,ui,ni)}else if(ui instanceof THREE.RenderableFace4){if(r=ui.v1,e=ui.v2,l=ui.v3,ot=ui.v4,-1>r.positionScreen.z||1<r.positionScreen.z)continue;if(-1>e.positionScreen.z||1<e.positionScreen.z)continue;if(-1>l.positionScreen.z||1<l.positionScreen.z)continue;if(-1>ot.positionScreen.z||1<ot.positionScreen.z)continue;r.positionScreen.x*=u;r.positionScreen.y*=f;e.positionScreen.x*=u;e.positionScreen.y*=f;l.positionScreen.x*=u;l.positionScreen.y*=f;ot.positionScreen.x*=u;ot.positionScreen.y*=f;ur.positionScreen.copy(e.positionScreen);fr.positionScreen.copy(ot.positionScreen);!0===ni.overdraw&&(lr(r.positionScreen,e.positionScreen),lr(e.positionScreen,ot.positionScreen),lr(ot.positionScreen,r.positionScreen),lr(l.positionScreen,ur.positionScreen),lr(l.positionScreen,fr.positionScreen));k.setFromPoints([r.positionScreen,e.positionScreen,l.positionScreen,ot.positionScreen]);ri=r;wi=e;bi=l;ki=ot;di=ur;gi=fr;bt.info.render.vertices+=4;bt.info.render.faces++;ti(ni.opacity);ii(ni.blending);void 0!==ni.map&&null!==ni.map||void 0!==ni.envMap&&null!==ni.envMap?(fu(ri,wi,ki,0,1,3,ui,ni),fu(di,bi,gi,1,2,3,ui,ni)):(a=ri.positionScreen.x,v=ri.positionScreen.y,y=wi.positionScreen.x,p=wi.positionScreen.y,w=bi.positionScreen.x,b=bi.positionScreen.y,ut=ki.positionScreen.x,ft=ki.positionScreen.y,lt=di.positionScreen.x,at=di.positionScreen.y,vt=gi.positionScreen.x,yt=gi.positionScreen.y,ni instanceof THREE.MeshLambertMaterial||ni instanceof THREE.MeshPhongMaterial?(nt.copy(ni.color),it.copy(ni.emissive),ni.vertexColors===THREE.FaceColors&&nt.multiply(ui.color),!0===hi?!1===ni.wireframe&&ni.shading==THREE.SmoothShading&&4==ui.vertexNormalsLength?(h.copy(et),o.copy(et),s.copy(et),d.copy(et),nr(ui.v1.positionWorld,ui.vertexNormalsModel[0],h),nr(ui.v2.positionWorld,ui.vertexNormalsModel[1],o),nr(ui.v4.positionWorld,ui.vertexNormalsModel[3],s),nr(ui.v3.positionWorld,ui.vertexNormalsModel[2],d),h.multiply(nt).add(it),o.multiply(nt).add(it),s.multiply(nt).add(it),d.multiply(nt).add(it),g=iu(h,o,s,d),tu(a,v,y,p,ut,ft),ir(a,v,y,p,ut,ft,0,0,1,0,0,1,g),tu(lt,at,w,b,vt,yt),ir(lt,at,w,b,vt,yt,1,0,1,1,0,1,g)):(i.copy(et),nr(ui.centroidModel,ui.normalModel,i),i.multiply(nt).add(it),ru(a,v,y,p,w,b,ut,ft),!0===ni.wireframe?rr(i,ni.wireframeLinewidth,ni.wireframeLinecap,ni.wireframeLinejoin):tr(i)):(i.addColors(nt,it),ru(a,v,y,p,w,b,ut,ft),!0===ni.wireframe?rr(i,ni.wireframeLinewidth,ni.wireframeLinecap,ni.wireframeLinejoin):tr(i))):ni instanceof THREE.MeshBasicMaterial?(i.copy(ni.color),ni.vertexColors===THREE.FaceColors&&i.multiply(ui.color),ru(a,v,y,p,w,b,ut,ft),!0===ni.wireframe?rr(i,ni.wireframeLinewidth,ni.wireframeLinecap,ni.wireframeLinejoin):tr(i)):ni instanceof THREE.MeshNormalMaterial?(ri=void 0,ni.shading==THREE.FlatShading?(ri=ui.normalModelView,i.setRGB(ri.x,ri.y,ri.z).multiplyScalar(.5).addScalar(.5),ru(a,v,y,p,w,b,ut,ft),!0===ni.wireframe?rr(i,ni.wireframeLinewidth,ni.wireframeLinecap,ni.wireframeLinejoin):tr(i)):ni.shading==THREE.SmoothShading&&(ri=ui.vertexNormalsModelView[0],h.setRGB(ri.x,ri.y,ri.z).multiplyScalar(.5).addScalar(.5),ri=ui.vertexNormalsModelView[1],o.setRGB(ri.x,ri.y,ri.z).multiplyScalar(.5).addScalar(.5),ri=ui.vertexNormalsModelView[3],s.setRGB(ri.x,ri.y,ri.z).multiplyScalar(.5).addScalar(.5),ri=ui.vertexNormalsModelView[2],d.setRGB(ri.x,ri.y,ri.z).multiplyScalar(.5).addScalar(.5),g=iu(h,o,s,d),tu(a,v,y,p,ut,ft),ir(a,v,y,p,ut,ft,0,0,1,0,0,1,g),tu(lt,at,w,b,vt,yt),ir(lt,at,w,b,vt,yt,1,0,1,1,0,1,g))):ni instanceof THREE.MeshDepthMaterial&&(st=dt.near,ht=dt.far,h.r=h.g=h.b=1-ct(ri.positionScreen.z*ri.positionScreen.w,st,ht),o.r=o.g=o.b=1-ct(wi.positionScreen.z*wi.positionScreen.w,st,ht),s.r=s.g=s.b=1-ct(ki.positionScreen.z*ki.positionScreen.w,st,ht),d.r=d.g=d.b=1-ct(bi.positionScreen.z*bi.positionScreen.w,st,ht),g=iu(h,o,s,d),tu(a,v,y,p,ut,ft),ir(a,v,y,p,ut,ft,0,0,1,0,0,1,g),tu(lt,at,w,b,vt,yt),ir(lt,at,w,b,vt,yt,1,0,1,1,0,1,g)))}c.union(k)}t.setTransform(1,0,0,1,0,0)}}};THREE.ShaderChunk={fog_pars_fragment:"#ifdef USE_FOG\nuniform vec3 fogColor;\n#ifdef FOG_EXP2\nuniform float fogDensity;\n#else\nuniform float fogNear;\nuniform float fogFar;\n#endif\n#endif",fog_fragment:"#ifdef USE_FOG\nfloat depth = gl_FragCoord.z / gl_FragCoord.w;\n#ifdef FOG_EXP2\nconst float LOG2 = 1.442695;\nfloat fogFactor = exp2( - fogDensity * fogDensity * depth * depth * LOG2 );\nfogFactor = 1.0 - clamp( fogFactor, 0.0, 1.0 );\n#else\nfloat fogFactor = smoothstep( fogNear, fogFar, depth );\n#endif\ngl_FragColor = mix( gl_FragColor, vec4( fogColor, gl_FragColor.w ), fogFactor );\n#endif",envmap_pars_fragment:"#ifdef USE_ENVMAP\nuniform float reflectivity;\nuniform samplerCube envMap;\nuniform float flipEnvMap;\nuniform int combine;\n#if defined( USE_BUMPMAP ) || defined( USE_NORMALMAP )\nuniform bool useRefract;\nuniform float refractionRatio;\n#else\nvarying vec3 vReflect;\n#endif\n#endif",envmap_fragment:"#ifdef USE_ENVMAP\nvec3 reflectVec;\n#if defined( USE_BUMPMAP ) || defined( USE_NORMALMAP )\nvec3 cameraToVertex = normalize( vWorldPosition - cameraPosition );\nif ( useRefract ) {\nreflectVec = refract( cameraToVertex, normal, refractionRatio );\n} else { \nreflectVec = reflect( cameraToVertex, normal );\n}\n#else\nreflectVec = vReflect;\n#endif\n#ifdef DOUBLE_SIDED\nfloat flipNormal = ( -1.0 + 2.0 * float( gl_FrontFacing ) );\nvec4 cubeColor = textureCube( envMap, flipNormal * vec3( flipEnvMap * reflectVec.x, reflectVec.yz ) );\n#else\nvec4 cubeColor = textureCube( envMap, vec3( flipEnvMap * reflectVec.x, reflectVec.yz ) );\n#endif\n#ifdef GAMMA_INPUT\ncubeColor.xyz *= cubeColor.xyz;\n#endif\nif ( combine == 1 ) {\ngl_FragColor.xyz = mix( gl_FragColor.xyz, cubeColor.xyz, specularStrength * reflectivity );\n} else if ( combine == 2 ) {\ngl_FragColor.xyz += cubeColor.xyz * specularStrength * reflectivity;\n} else {\ngl_FragColor.xyz = mix( gl_FragColor.xyz, gl_FragColor.xyz * cubeColor.xyz, specularStrength * reflectivity );\n}\n#endif",envmap_pars_vertex:"#if defined( USE_ENVMAP ) && ! defined( USE_BUMPMAP ) && ! defined( USE_NORMALMAP )\nvarying vec3 vReflect;\nuniform float refractionRatio;\nuniform bool useRefract;\n#endif",worldpos_vertex:"#if defined( USE_ENVMAP ) || defined( PHONG ) || defined( LAMBERT ) || defined ( USE_SHADOWMAP )\n#ifdef USE_SKINNING\nvec4 worldPosition = modelMatrix * skinned;\n#endif\n#if defined( USE_MORPHTARGETS ) && ! defined( USE_SKINNING )\nvec4 worldPosition = modelMatrix * vec4( morphed, 1.0 );\n#endif\n#if ! defined( USE_MORPHTARGETS ) && ! defined( USE_SKINNING )\nvec4 worldPosition = modelMatrix * vec4( position, 1.0 );\n#endif\n#endif",envmap_vertex:"#if defined( USE_ENVMAP ) && ! defined( USE_BUMPMAP ) && ! defined( USE_NORMALMAP )\nvec3 worldNormal = mat3( modelMatrix[ 0 ].xyz, modelMatrix[ 1 ].xyz, modelMatrix[ 2 ].xyz ) * objectNormal;\nworldNormal = normalize( worldNormal );\nvec3 cameraToVertex = normalize( worldPosition.xyz - cameraPosition );\nif ( useRefract ) {\nvReflect = refract( cameraToVertex, worldNormal, refractionRatio );\n} else {\nvReflect = reflect( cameraToVertex, worldNormal );\n}\n#endif",map_particle_pars_fragment:"#ifdef USE_MAP\nuniform sampler2D map;\n#endif",map_particle_fragment:"#ifdef USE_MAP\ngl_FragColor = gl_FragColor * texture2D( map, vec2( gl_PointCoord.x, 1.0 - gl_PointCoord.y ) );\n#endif",map_pars_vertex:"#if defined( USE_MAP ) || defined( USE_BUMPMAP ) || defined( USE_NORMALMAP ) || defined( USE_SPECULARMAP )\nvarying vec2 vUv;\nuniform vec4 offsetRepeat;\n#endif",map_pars_fragment:"#if defined( USE_MAP ) || defined( USE_BUMPMAP ) || defined( USE_NORMALMAP ) || defined( USE_SPECULARMAP )\nvarying vec2 vUv;\n#endif\n#ifdef USE_MAP\nuniform sampler2D map;\n#endif",map_vertex:"#if defined( USE_MAP ) || defined( USE_BUMPMAP ) || defined( USE_NORMALMAP ) || defined( USE_SPECULARMAP )\nvUv = uv * offsetRepeat.zw + offsetRepeat.xy;\n#endif",map_fragment:"#ifdef USE_MAP\nvec4 texelColor = texture2D( map, vUv );\n#ifdef GAMMA_INPUT\ntexelColor.xyz *= texelColor.xyz;\n#endif\ngl_FragColor = gl_FragColor * texelColor;\n#endif",lightmap_pars_fragment:"#ifdef USE_LIGHTMAP\nvarying vec2 vUv2;\nuniform sampler2D lightMap;\n#endif",lightmap_pars_vertex:"#ifdef USE_LIGHTMAP\nvarying vec2 vUv2;\n#endif",lightmap_fragment:"#ifdef USE_LIGHTMAP\ngl_FragColor = gl_FragColor * texture2D( lightMap, vUv2 );\n#endif",lightmap_vertex:"#ifdef USE_LIGHTMAP\nvUv2 = uv2;\n#endif",bumpmap_pars_fragment:"#ifdef USE_BUMPMAP\nuniform sampler2D bumpMap;\nuniform float bumpScale;\nvec2 dHdxy_fwd() {\nvec2 dSTdx = dFdx( vUv );\nvec2 dSTdy = dFdy( vUv );\nfloat Hll = bumpScale * texture2D( bumpMap, vUv ).x;\nfloat dBx = bumpScale * texture2D( bumpMap, vUv + dSTdx ).x - Hll;\nfloat dBy = bumpScale * texture2D( bumpMap, vUv + dSTdy ).x - Hll;\nreturn vec2( dBx, dBy );\n}\nvec3 perturbNormalArb( vec3 surf_pos, vec3 surf_norm, vec2 dHdxy ) {\nvec3 vSigmaX = dFdx( surf_pos );\nvec3 vSigmaY = dFdy( surf_pos );\nvec3 vN = surf_norm;\nvec3 R1 = cross( vSigmaY, vN );\nvec3 R2 = cross( vN, vSigmaX );\nfloat fDet = dot( vSigmaX, R1 );\nvec3 vGrad = sign( fDet ) * ( dHdxy.x * R1 + dHdxy.y * R2 );\nreturn normalize( abs( fDet ) * surf_norm - vGrad );\n}\n#endif",normalmap_pars_fragment:"#ifdef USE_NORMALMAP\nuniform sampler2D normalMap;\nuniform vec2 normalScale;\nvec3 perturbNormal2Arb( vec3 eye_pos, vec3 surf_norm ) {\nvec3 q0 = dFdx( eye_pos.xyz );\nvec3 q1 = dFdy( eye_pos.xyz );\nvec2 st0 = dFdx( vUv.st );\nvec2 st1 = dFdy( vUv.st );\nvec3 S = normalize(  q0 * st1.t - q1 * st0.t );\nvec3 T = normalize( -q0 * st1.s + q1 * st0.s );\nvec3 N = normalize( surf_norm );\nvec3 mapN = texture2D( normalMap, vUv ).xyz * 2.0 - 1.0;\nmapN.xy = normalScale * mapN.xy;\nmat3 tsn = mat3( S, T, N );\nreturn normalize( tsn * mapN );\n}\n#endif",specularmap_pars_fragment:"#ifdef USE_SPECULARMAP\nuniform sampler2D specularMap;\n#endif",specularmap_fragment:"float specularStrength;\n#ifdef USE_SPECULARMAP\nvec4 texelSpecular = texture2D( specularMap, vUv );\nspecularStrength = texelSpecular.r;\n#else\nspecularStrength = 1.0;\n#endif",lights_lambert_pars_vertex:"uniform vec3 ambient;\nuniform vec3 diffuse;\nuniform vec3 emissive;\nuniform vec3 ambientLightColor;\n#if MAX_DIR_LIGHTS > 0\nuniform vec3 directionalLightColor[ MAX_DIR_LIGHTS ];\nuniform vec3 directionalLightDirection[ MAX_DIR_LIGHTS ];\n#endif\n#if MAX_HEMI_LIGHTS > 0\nuniform vec3 hemisphereLightSkyColor[ MAX_HEMI_LIGHTS ];\nuniform vec3 hemisphereLightGroundColor[ MAX_HEMI_LIGHTS ];\nuniform vec3 hemisphereLightDirection[ MAX_HEMI_LIGHTS ];\n#endif\n#if MAX_POINT_LIGHTS > 0\nuniform vec3 pointLightColor[ MAX_POINT_LIGHTS ];\nuniform vec3 pointLightPosition[ MAX_POINT_LIGHTS ];\nuniform float pointLightDistance[ MAX_POINT_LIGHTS ];\n#endif\n#if MAX_SPOT_LIGHTS > 0\nuniform vec3 spotLightColor[ MAX_SPOT_LIGHTS ];\nuniform vec3 spotLightPosition[ MAX_SPOT_LIGHTS ];\nuniform vec3 spotLightDirection[ MAX_SPOT_LIGHTS ];\nuniform float spotLightDistance[ MAX_SPOT_LIGHTS ];\nuniform float spotLightAngleCos[ MAX_SPOT_LIGHTS ];\nuniform float spotLightExponent[ MAX_SPOT_LIGHTS ];\n#endif\n#ifdef WRAP_AROUND\nuniform vec3 wrapRGB;\n#endif",lights_lambert_vertex:"vLightFront = vec3( 0.0 );\n#ifdef DOUBLE_SIDED\nvLightBack = vec3( 0.0 );\n#endif\ntransformedNormal = normalize( transformedNormal );\n#if MAX_DIR_LIGHTS > 0\nfor( int i = 0; i < MAX_DIR_LIGHTS; i ++ ) {\nvec4 lDirection = viewMatrix * vec4( directionalLightDirection[ i ], 0.0 );\nvec3 dirVector = normalize( lDirection.xyz );\nfloat dotProduct = dot( transformedNormal, dirVector );\nvec3 directionalLightWeighting = vec3( max( dotProduct, 0.0 ) );\n#ifdef DOUBLE_SIDED\nvec3 directionalLightWeightingBack = vec3( max( -dotProduct, 0.0 ) );\n#ifdef WRAP_AROUND\nvec3 directionalLightWeightingHalfBack = vec3( max( -0.5 * dotProduct + 0.5, 0.0 ) );\n#endif\n#endif\n#ifdef WRAP_AROUND\nvec3 directionalLightWeightingHalf = vec3( max( 0.5 * dotProduct + 0.5, 0.0 ) );\ndirectionalLightWeighting = mix( directionalLightWeighting, directionalLightWeightingHalf, wrapRGB );\n#ifdef DOUBLE_SIDED\ndirectionalLightWeightingBack = mix( directionalLightWeightingBack, directionalLightWeightingHalfBack, wrapRGB );\n#endif\n#endif\nvLightFront += directionalLightColor[ i ] * directionalLightWeighting;\n#ifdef DOUBLE_SIDED\nvLightBack += directionalLightColor[ i ] * directionalLightWeightingBack;\n#endif\n}\n#endif\n#if MAX_POINT_LIGHTS > 0\nfor( int i = 0; i < MAX_POINT_LIGHTS; i ++ ) {\nvec4 lPosition = viewMatrix * vec4( pointLightPosition[ i ], 1.0 );\nvec3 lVector = lPosition.xyz - mvPosition.xyz;\nfloat lDistance = 1.0;\nif ( pointLightDistance[ i ] > 0.0 )\nlDistance = 1.0 - min( ( length( lVector ) / pointLightDistance[ i ] ), 1.0 );\nlVector = normalize( lVector );\nfloat dotProduct = dot( transformedNormal, lVector );\nvec3 pointLightWeighting = vec3( max( dotProduct, 0.0 ) );\n#ifdef DOUBLE_SIDED\nvec3 pointLightWeightingBack = vec3( max( -dotProduct, 0.0 ) );\n#ifdef WRAP_AROUND\nvec3 pointLightWeightingHalfBack = vec3( max( -0.5 * dotProduct + 0.5, 0.0 ) );\n#endif\n#endif\n#ifdef WRAP_AROUND\nvec3 pointLightWeightingHalf = vec3( max( 0.5 * dotProduct + 0.5, 0.0 ) );\npointLightWeighting = mix( pointLightWeighting, pointLightWeightingHalf, wrapRGB );\n#ifdef DOUBLE_SIDED\npointLightWeightingBack = mix( pointLightWeightingBack, pointLightWeightingHalfBack, wrapRGB );\n#endif\n#endif\nvLightFront += pointLightColor[ i ] * pointLightWeighting * lDistance;\n#ifdef DOUBLE_SIDED\nvLightBack += pointLightColor[ i ] * pointLightWeightingBack * lDistance;\n#endif\n}\n#endif\n#if MAX_SPOT_LIGHTS > 0\nfor( int i = 0; i < MAX_SPOT_LIGHTS; i ++ ) {\nvec4 lPosition = viewMatrix * vec4( spotLightPosition[ i ], 1.0 );\nvec3 lVector = lPosition.xyz - mvPosition.xyz;\nfloat spotEffect = dot( spotLightDirection[ i ], normalize( spotLightPosition[ i ] - worldPosition.xyz ) );\nif ( spotEffect > spotLightAngleCos[ i ] ) {\nspotEffect = max( pow( spotEffect, spotLightExponent[ i ] ), 0.0 );\nfloat lDistance = 1.0;\nif ( spotLightDistance[ i ] > 0.0 )\nlDistance = 1.0 - min( ( length( lVector ) / spotLightDistance[ i ] ), 1.0 );\nlVector = normalize( lVector );\nfloat dotProduct = dot( transformedNormal, lVector );\nvec3 spotLightWeighting = vec3( max( dotProduct, 0.0 ) );\n#ifdef DOUBLE_SIDED\nvec3 spotLightWeightingBack = vec3( max( -dotProduct, 0.0 ) );\n#ifdef WRAP_AROUND\nvec3 spotLightWeightingHalfBack = vec3( max( -0.5 * dotProduct + 0.5, 0.0 ) );\n#endif\n#endif\n#ifdef WRAP_AROUND\nvec3 spotLightWeightingHalf = vec3( max( 0.5 * dotProduct + 0.5, 0.0 ) );\nspotLightWeighting = mix( spotLightWeighting, spotLightWeightingHalf, wrapRGB );\n#ifdef DOUBLE_SIDED\nspotLightWeightingBack = mix( spotLightWeightingBack, spotLightWeightingHalfBack, wrapRGB );\n#endif\n#endif\nvLightFront += spotLightColor[ i ] * spotLightWeighting * lDistance * spotEffect;\n#ifdef DOUBLE_SIDED\nvLightBack += spotLightColor[ i ] * spotLightWeightingBack * lDistance * spotEffect;\n#endif\n}\n}\n#endif\n#if MAX_HEMI_LIGHTS > 0\nfor( int i = 0; i < MAX_HEMI_LIGHTS; i ++ ) {\nvec4 lDirection = viewMatrix * vec4( hemisphereLightDirection[ i ], 0.0 );\nvec3 lVector = normalize( lDirection.xyz );\nfloat dotProduct = dot( transformedNormal, lVector );\nfloat hemiDiffuseWeight = 0.5 * dotProduct + 0.5;\nfloat hemiDiffuseWeightBack = -0.5 * dotProduct + 0.5;\nvLightFront += mix( hemisphereLightGroundColor[ i ], hemisphereLightSkyColor[ i ], hemiDiffuseWeight );\n#ifdef DOUBLE_SIDED\nvLightBack += mix( hemisphereLightGroundColor[ i ], hemisphereLightSkyColor[ i ], hemiDiffuseWeightBack );\n#endif\n}\n#endif\nvLightFront = vLightFront * diffuse + ambient * ambientLightColor + emissive;\n#ifdef DOUBLE_SIDED\nvLightBack = vLightBack * diffuse + ambient * ambientLightColor + emissive;\n#endif",lights_phong_pars_vertex:"#ifndef PHONG_PER_PIXEL\n#if MAX_POINT_LIGHTS > 0\nuniform vec3 pointLightPosition[ MAX_POINT_LIGHTS ];\nuniform float pointLightDistance[ MAX_POINT_LIGHTS ];\nvarying vec4 vPointLight[ MAX_POINT_LIGHTS ];\n#endif\n#if MAX_SPOT_LIGHTS > 0\nuniform vec3 spotLightPosition[ MAX_SPOT_LIGHTS ];\nuniform float spotLightDistance[ MAX_SPOT_LIGHTS ];\nvarying vec4 vSpotLight[ MAX_SPOT_LIGHTS ];\n#endif\n#endif\n#if MAX_SPOT_LIGHTS > 0 || defined( USE_BUMPMAP )\nvarying vec3 vWorldPosition;\n#endif",lights_phong_vertex:"#ifndef PHONG_PER_PIXEL\n#if MAX_POINT_LIGHTS > 0\nfor( int i = 0; i < MAX_POINT_LIGHTS; i ++ ) {\nvec4 lPosition = viewMatrix * vec4( pointLightPosition[ i ], 1.0 );\nvec3 lVector = lPosition.xyz - mvPosition.xyz;\nfloat lDistance = 1.0;\nif ( pointLightDistance[ i ] > 0.0 )\nlDistance = 1.0 - min( ( length( lVector ) / pointLightDistance[ i ] ), 1.0 );\nvPointLight[ i ] = vec4( lVector, lDistance );\n}\n#endif\n#if MAX_SPOT_LIGHTS > 0\nfor( int i = 0; i < MAX_SPOT_LIGHTS; i ++ ) {\nvec4 lPosition = viewMatrix * vec4( spotLightPosition[ i ], 1.0 );\nvec3 lVector = lPosition.xyz - mvPosition.xyz;\nfloat lDistance = 1.0;\nif ( spotLightDistance[ i ] > 0.0 )\nlDistance = 1.0 - min( ( length( lVector ) / spotLightDistance[ i ] ), 1.0 );\nvSpotLight[ i ] = vec4( lVector, lDistance );\n}\n#endif\n#endif\n#if MAX_SPOT_LIGHTS > 0 || defined( USE_BUMPMAP )\nvWorldPosition = worldPosition.xyz;\n#endif",lights_phong_pars_fragment:"uniform vec3 ambientLightColor;\n#if MAX_DIR_LIGHTS > 0\nuniform vec3 directionalLightColor[ MAX_DIR_LIGHTS ];\nuniform vec3 directionalLightDirection[ MAX_DIR_LIGHTS ];\n#endif\n#if MAX_HEMI_LIGHTS > 0\nuniform vec3 hemisphereLightSkyColor[ MAX_HEMI_LIGHTS ];\nuniform vec3 hemisphereLightGroundColor[ MAX_HEMI_LIGHTS ];\nuniform vec3 hemisphereLightDirection[ MAX_HEMI_LIGHTS ];\n#endif\n#if MAX_POINT_LIGHTS > 0\nuniform vec3 pointLightColor[ MAX_POINT_LIGHTS ];\n#ifdef PHONG_PER_PIXEL\nuniform vec3 pointLightPosition[ MAX_POINT_LIGHTS ];\nuniform float pointLightDistance[ MAX_POINT_LIGHTS ];\n#else\nvarying vec4 vPointLight[ MAX_POINT_LIGHTS ];\n#endif\n#endif\n#if MAX_SPOT_LIGHTS > 0\nuniform vec3 spotLightColor[ MAX_SPOT_LIGHTS ];\nuniform vec3 spotLightPosition[ MAX_SPOT_LIGHTS ];\nuniform vec3 spotLightDirection[ MAX_SPOT_LIGHTS ];\nuniform float spotLightAngleCos[ MAX_SPOT_LIGHTS ];\nuniform float spotLightExponent[ MAX_SPOT_LIGHTS ];\n#ifdef PHONG_PER_PIXEL\nuniform float spotLightDistance[ MAX_SPOT_LIGHTS ];\n#else\nvarying vec4 vSpotLight[ MAX_SPOT_LIGHTS ];\n#endif\n#endif\n#if MAX_SPOT_LIGHTS > 0 || defined( USE_BUMPMAP )\nvarying vec3 vWorldPosition;\n#endif\n#ifdef WRAP_AROUND\nuniform vec3 wrapRGB;\n#endif\nvarying vec3 vViewPosition;\nvarying vec3 vNormal;",lights_phong_fragment:"vec3 normal = normalize( vNormal );\nvec3 viewPosition = normalize( vViewPosition );\n#ifdef DOUBLE_SIDED\nnormal = normal * ( -1.0 + 2.0 * float( gl_FrontFacing ) );\n#endif\n#ifdef USE_NORMALMAP\nnormal = perturbNormal2Arb( -viewPosition, normal );\n#elif defined( USE_BUMPMAP )\nnormal = perturbNormalArb( -vViewPosition, normal, dHdxy_fwd() );\n#endif\n#if MAX_POINT_LIGHTS > 0\nvec3 pointDiffuse  = vec3( 0.0 );\nvec3 pointSpecular = vec3( 0.0 );\nfor ( int i = 0; i < MAX_POINT_LIGHTS; i ++ ) {\n#ifdef PHONG_PER_PIXEL\nvec4 lPosition = viewMatrix * vec4( pointLightPosition[ i ], 1.0 );\nvec3 lVector = lPosition.xyz + vViewPosition.xyz;\nfloat lDistance = 1.0;\nif ( pointLightDistance[ i ] > 0.0 )\nlDistance = 1.0 - min( ( length( lVector ) / pointLightDistance[ i ] ), 1.0 );\nlVector = normalize( lVector );\n#else\nvec3 lVector = normalize( vPointLight[ i ].xyz );\nfloat lDistance = vPointLight[ i ].w;\n#endif\nfloat dotProduct = dot( normal, lVector );\n#ifdef WRAP_AROUND\nfloat pointDiffuseWeightFull = max( dotProduct, 0.0 );\nfloat pointDiffuseWeightHalf = max( 0.5 * dotProduct + 0.5, 0.0 );\nvec3 pointDiffuseWeight = mix( vec3 ( pointDiffuseWeightFull ), vec3( pointDiffuseWeightHalf ), wrapRGB );\n#else\nfloat pointDiffuseWeight = max( dotProduct, 0.0 );\n#endif\npointDiffuse  += diffuse * pointLightColor[ i ] * pointDiffuseWeight * lDistance;\nvec3 pointHalfVector = normalize( lVector + viewPosition );\nfloat pointDotNormalHalf = max( dot( normal, pointHalfVector ), 0.0 );\nfloat pointSpecularWeight = specularStrength * max( pow( pointDotNormalHalf, shininess ), 0.0 );\n#ifdef PHYSICALLY_BASED_SHADING\nfloat specularNormalization = ( shininess + 2.0001 ) / 8.0;\nvec3 schlick = specular + vec3( 1.0 - specular ) * pow( 1.0 - dot( lVector, pointHalfVector ), 5.0 );\npointSpecular += schlick * pointLightColor[ i ] * pointSpecularWeight * pointDiffuseWeight * lDistance * specularNormalization;\n#else\npointSpecular += specular * pointLightColor[ i ] * pointSpecularWeight * pointDiffuseWeight * lDistance;\n#endif\n}\n#endif\n#if MAX_SPOT_LIGHTS > 0\nvec3 spotDiffuse  = vec3( 0.0 );\nvec3 spotSpecular = vec3( 0.0 );\nfor ( int i = 0; i < MAX_SPOT_LIGHTS; i ++ ) {\n#ifdef PHONG_PER_PIXEL\nvec4 lPosition = viewMatrix * vec4( spotLightPosition[ i ], 1.0 );\nvec3 lVector = lPosition.xyz + vViewPosition.xyz;\nfloat lDistance = 1.0;\nif ( spotLightDistance[ i ] > 0.0 )\nlDistance = 1.0 - min( ( length( lVector ) / spotLightDistance[ i ] ), 1.0 );\nlVector = normalize( lVector );\n#else\nvec3 lVector = normalize( vSpotLight[ i ].xyz );\nfloat lDistance = vSpotLight[ i ].w;\n#endif\nfloat spotEffect = dot( spotLightDirection[ i ], normalize( spotLightPosition[ i ] - vWorldPosition ) );\nif ( spotEffect > spotLightAngleCos[ i ] ) {\nspotEffect = max( pow( spotEffect, spotLightExponent[ i ] ), 0.0 );\nfloat dotProduct = dot( normal, lVector );\n#ifdef WRAP_AROUND\nfloat spotDiffuseWeightFull = max( dotProduct, 0.0 );\nfloat spotDiffuseWeightHalf = max( 0.5 * dotProduct + 0.5, 0.0 );\nvec3 spotDiffuseWeight = mix( vec3 ( spotDiffuseWeightFull ), vec3( spotDiffuseWeightHalf ), wrapRGB );\n#else\nfloat spotDiffuseWeight = max( dotProduct, 0.0 );\n#endif\nspotDiffuse += diffuse * spotLightColor[ i ] * spotDiffuseWeight * lDistance * spotEffect;\nvec3 spotHalfVector = normalize( lVector + viewPosition );\nfloat spotDotNormalHalf = max( dot( normal, spotHalfVector ), 0.0 );\nfloat spotSpecularWeight = specularStrength * max( pow( spotDotNormalHalf, shininess ), 0.0 );\n#ifdef PHYSICALLY_BASED_SHADING\nfloat specularNormalization = ( shininess + 2.0001 ) / 8.0;\nvec3 schlick = specular + vec3( 1.0 - specular ) * pow( 1.0 - dot( lVector, spotHalfVector ), 5.0 );\nspotSpecular += schlick * spotLightColor[ i ] * spotSpecularWeight * spotDiffuseWeight * lDistance * specularNormalization * spotEffect;\n#else\nspotSpecular += specular * spotLightColor[ i ] * spotSpecularWeight * spotDiffuseWeight * lDistance * spotEffect;\n#endif\n}\n}\n#endif\n#if MAX_DIR_LIGHTS > 0\nvec3 dirDiffuse  = vec3( 0.0 );\nvec3 dirSpecular = vec3( 0.0 );\nfor( int i = 0; i < MAX_DIR_LIGHTS; i ++ ) {\nvec4 lDirection = viewMatrix * vec4( directionalLightDirection[ i ], 0.0 );\nvec3 dirVector = normalize( lDirection.xyz );\nfloat dotProduct = dot( normal, dirVector );\n#ifdef WRAP_AROUND\nfloat dirDiffuseWeightFull = max( dotProduct, 0.0 );\nfloat dirDiffuseWeightHalf = max( 0.5 * dotProduct + 0.5, 0.0 );\nvec3 dirDiffuseWeight = mix( vec3( dirDiffuseWeightFull ), vec3( dirDiffuseWeightHalf ), wrapRGB );\n#else\nfloat dirDiffuseWeight = max( dotProduct, 0.0 );\n#endif\ndirDiffuse  += diffuse * directionalLightColor[ i ] * dirDiffuseWeight;\nvec3 dirHalfVector = normalize( dirVector + viewPosition );\nfloat dirDotNormalHalf = max( dot( normal, dirHalfVector ), 0.0 );\nfloat dirSpecularWeight = specularStrength * max( pow( dirDotNormalHalf, shininess ), 0.0 );\n#ifdef PHYSICALLY_BASED_SHADING\nfloat specularNormalization = ( shininess + 2.0001 ) / 8.0;\nvec3 schlick = specular + vec3( 1.0 - specular ) * pow( 1.0 - dot( dirVector, dirHalfVector ), 5.0 );\ndirSpecular += schlick * directionalLightColor[ i ] * dirSpecularWeight * dirDiffuseWeight * specularNormalization;\n#else\ndirSpecular += specular * directionalLightColor[ i ] * dirSpecularWeight * dirDiffuseWeight;\n#endif\n}\n#endif\n#if MAX_HEMI_LIGHTS > 0\nvec3 hemiDiffuse  = vec3( 0.0 );\nvec3 hemiSpecular = vec3( 0.0 );\nfor( int i = 0; i < MAX_HEMI_LIGHTS; i ++ ) {\nvec4 lDirection = viewMatrix * vec4( hemisphereLightDirection[ i ], 0.0 );\nvec3 lVector = normalize( lDirection.xyz );\nfloat dotProduct = dot( normal, lVector );\nfloat hemiDiffuseWeight = 0.5 * dotProduct + 0.5;\nvec3 hemiColor = mix( hemisphereLightGroundColor[ i ], hemisphereLightSkyColor[ i ], hemiDiffuseWeight );\nhemiDiffuse += diffuse * hemiColor;\nvec3 hemiHalfVectorSky = normalize( lVector + viewPosition );\nfloat hemiDotNormalHalfSky = 0.5 * dot( normal, hemiHalfVectorSky ) + 0.5;\nfloat hemiSpecularWeightSky = specularStrength * max( pow( hemiDotNormalHalfSky, shininess ), 0.0 );\nvec3 lVectorGround = -lVector;\nvec3 hemiHalfVectorGround = normalize( lVectorGround + viewPosition );\nfloat hemiDotNormalHalfGround = 0.5 * dot( normal, hemiHalfVectorGround ) + 0.5;\nfloat hemiSpecularWeightGround = specularStrength * max( pow( hemiDotNormalHalfGround, shininess ), 0.0 );\n#ifdef PHYSICALLY_BASED_SHADING\nfloat dotProductGround = dot( normal, lVectorGround );\nfloat specularNormalization = ( shininess + 2.0001 ) / 8.0;\nvec3 schlickSky = specular + vec3( 1.0 - specular ) * pow( 1.0 - dot( lVector, hemiHalfVectorSky ), 5.0 );\nvec3 schlickGround = specular + vec3( 1.0 - specular ) * pow( 1.0 - dot( lVectorGround, hemiHalfVectorGround ), 5.0 );\nhemiSpecular += hemiColor * specularNormalization * ( schlickSky * hemiSpecularWeightSky * max( dotProduct, 0.0 ) + schlickGround * hemiSpecularWeightGround * max( dotProductGround, 0.0 ) );\n#else\nhemiSpecular += specular * hemiColor * ( hemiSpecularWeightSky + hemiSpecularWeightGround ) * hemiDiffuseWeight;\n#endif\n}\n#endif\nvec3 totalDiffuse = vec3( 0.0 );\nvec3 totalSpecular = vec3( 0.0 );\n#if MAX_DIR_LIGHTS > 0\ntotalDiffuse += dirDiffuse;\ntotalSpecular += dirSpecular;\n#endif\n#if MAX_HEMI_LIGHTS > 0\ntotalDiffuse += hemiDiffuse;\ntotalSpecular += hemiSpecular;\n#endif\n#if MAX_POINT_LIGHTS > 0\ntotalDiffuse += pointDiffuse;\ntotalSpecular += pointSpecular;\n#endif\n#if MAX_SPOT_LIGHTS > 0\ntotalDiffuse += spotDiffuse;\ntotalSpecular += spotSpecular;\n#endif\n#ifdef METAL\ngl_FragColor.xyz = gl_FragColor.xyz * ( emissive + totalDiffuse + ambientLightColor * ambient + totalSpecular );\n#else\ngl_FragColor.xyz = gl_FragColor.xyz * ( emissive + totalDiffuse + ambientLightColor * ambient ) + totalSpecular;\n#endif",color_pars_fragment:"#ifdef USE_COLOR\nvarying vec3 vColor;\n#endif",color_fragment:"#ifdef USE_COLOR\ngl_FragColor = gl_FragColor * vec4( vColor, opacity );\n#endif",color_pars_vertex:"#ifdef USE_COLOR\nvarying vec3 vColor;\n#endif",color_vertex:"#ifdef USE_COLOR\n#ifdef GAMMA_INPUT\nvColor = color * color;\n#else\nvColor = color;\n#endif\n#endif",skinning_pars_vertex:"#ifdef USE_SKINNING\n#ifdef BONE_TEXTURE\nuniform sampler2D boneTexture;\nmat4 getBoneMatrix( const in float i ) {\nfloat j = i * 4.0;\nfloat x = mod( j, N_BONE_PIXEL_X );\nfloat y = floor( j / N_BONE_PIXEL_X );\nconst float dx = 1.0 / N_BONE_PIXEL_X;\nconst float dy = 1.0 / N_BONE_PIXEL_Y;\ny = dy * ( y + 0.5 );\nvec4 v1 = texture2D( boneTexture, vec2( dx * ( x + 0.5 ), y ) );\nvec4 v2 = texture2D( boneTexture, vec2( dx * ( x + 1.5 ), y ) );\nvec4 v3 = texture2D( boneTexture, vec2( dx * ( x + 2.5 ), y ) );\nvec4 v4 = texture2D( boneTexture, vec2( dx * ( x + 3.5 ), y ) );\nmat4 bone = mat4( v1, v2, v3, v4 );\nreturn bone;\n}\n#else\nuniform mat4 boneGlobalMatrices[ MAX_BONES ];\nmat4 getBoneMatrix( const in float i ) {\nmat4 bone = boneGlobalMatrices[ int(i) ];\nreturn bone;\n}\n#endif\n#endif",skinbase_vertex:"#ifdef USE_SKINNING\nmat4 boneMatX = getBoneMatrix( skinIndex.x );\nmat4 boneMatY = getBoneMatrix( skinIndex.y );\n#endif",skinning_vertex:"#ifdef USE_SKINNING\n#ifdef USE_MORPHTARGETS\nvec4 skinVertex = vec4( morphed, 1.0 );\n#else\nvec4 skinVertex = vec4( position, 1.0 );\n#endif\nvec4 skinned  = boneMatX * skinVertex * skinWeight.x;\nskinned \t  += boneMatY * skinVertex * skinWeight.y;\n#endif",morphtarget_pars_vertex:"#ifdef USE_MORPHTARGETS\n#ifndef USE_MORPHNORMALS\nuniform float morphTargetInfluences[ 8 ];\n#else\nuniform float morphTargetInfluences[ 4 ];\n#endif\n#endif",morphtarget_vertex:"#ifdef USE_MORPHTARGETS\nvec3 morphed = vec3( 0.0 );\nmorphed += ( morphTarget0 - position ) * morphTargetInfluences[ 0 ];\nmorphed += ( morphTarget1 - position ) * morphTargetInfluences[ 1 ];\nmorphed += ( morphTarget2 - position ) * morphTargetInfluences[ 2 ];\nmorphed += ( morphTarget3 - position ) * morphTargetInfluences[ 3 ];\n#ifndef USE_MORPHNORMALS\nmorphed += ( morphTarget4 - position ) * morphTargetInfluences[ 4 ];\nmorphed += ( morphTarget5 - position ) * morphTargetInfluences[ 5 ];\nmorphed += ( morphTarget6 - position ) * morphTargetInfluences[ 6 ];\nmorphed += ( morphTarget7 - position ) * morphTargetInfluences[ 7 ];\n#endif\nmorphed += position;\n#endif",default_vertex:"vec4 mvPosition;\n#ifdef USE_SKINNING\nmvPosition = modelViewMatrix * skinned;\n#endif\n#if !defined( USE_SKINNING ) && defined( USE_MORPHTARGETS )\nmvPosition = modelViewMatrix * vec4( morphed, 1.0 );\n#endif\n#if !defined( USE_SKINNING ) && ! defined( USE_MORPHTARGETS )\nmvPosition = modelViewMatrix * vec4( position, 1.0 );\n#endif\ngl_Position = projectionMatrix * mvPosition;",morphnormal_vertex:"#ifdef USE_MORPHNORMALS\nvec3 morphedNormal = vec3( 0.0 );\nmorphedNormal +=  ( morphNormal0 - normal ) * morphTargetInfluences[ 0 ];\nmorphedNormal +=  ( morphNormal1 - normal ) * morphTargetInfluences[ 1 ];\nmorphedNormal +=  ( morphNormal2 - normal ) * morphTargetInfluences[ 2 ];\nmorphedNormal +=  ( morphNormal3 - normal ) * morphTargetInfluences[ 3 ];\nmorphedNormal += normal;\n#endif",skinnormal_vertex:"#ifdef USE_SKINNING\nmat4 skinMatrix = skinWeight.x * boneMatX;\nskinMatrix \t+= skinWeight.y * boneMatY;\n#ifdef USE_MORPHNORMALS\nvec4 skinnedNormal = skinMatrix * vec4( morphedNormal, 0.0 );\n#else\nvec4 skinnedNormal = skinMatrix * vec4( normal, 0.0 );\n#endif\n#endif",defaultnormal_vertex:"vec3 objectNormal;\n#ifdef USE_SKINNING\nobjectNormal = skinnedNormal.xyz;\n#endif\n#if !defined( USE_SKINNING ) && defined( USE_MORPHNORMALS )\nobjectNormal = morphedNormal;\n#endif\n#if !defined( USE_SKINNING ) && ! defined( USE_MORPHNORMALS )\nobjectNormal = normal;\n#endif\n#ifdef FLIP_SIDED\nobjectNormal = -objectNormal;\n#endif\nvec3 transformedNormal = normalMatrix * objectNormal;",shadowmap_pars_fragment:"#ifdef USE_SHADOWMAP\nuniform sampler2D shadowMap[ MAX_SHADOWS ];\nuniform vec2 shadowMapSize[ MAX_SHADOWS ];\nuniform float shadowDarkness[ MAX_SHADOWS ];\nuniform float shadowBias[ MAX_SHADOWS ];\nvarying vec4 vShadowCoord[ MAX_SHADOWS ];\nfloat unpackDepth( const in vec4 rgba_depth ) {\nconst vec4 bit_shift = vec4( 1.0 / ( 256.0 * 256.0 * 256.0 ), 1.0 / ( 256.0 * 256.0 ), 1.0 / 256.0, 1.0 );\nfloat depth = dot( rgba_depth, bit_shift );\nreturn depth;\n}\n#endif",shadowmap_fragment:"#ifdef USE_SHADOWMAP\n#ifdef SHADOWMAP_DEBUG\nvec3 frustumColors[3];\nfrustumColors[0] = vec3( 1.0, 0.5, 0.0 );\nfrustumColors[1] = vec3( 0.0, 1.0, 0.8 );\nfrustumColors[2] = vec3( 0.0, 0.5, 1.0 );\n#endif\n#ifdef SHADOWMAP_CASCADE\nint inFrustumCount = 0;\n#endif\nfloat fDepth;\nvec3 shadowColor = vec3( 1.0 );\nfor( int i = 0; i < MAX_SHADOWS; i ++ ) {\nvec3 shadowCoord = vShadowCoord[ i ].xyz / vShadowCoord[ i ].w;\nbvec4 inFrustumVec = bvec4 ( shadowCoord.x >= 0.0, shadowCoord.x <= 1.0, shadowCoord.y >= 0.0, shadowCoord.y <= 1.0 );\nbool inFrustum = all( inFrustumVec );\n#ifdef SHADOWMAP_CASCADE\ninFrustumCount += int( inFrustum );\nbvec3 frustumTestVec = bvec3( inFrustum, inFrustumCount == 1, shadowCoord.z <= 1.0 );\n#else\nbvec2 frustumTestVec = bvec2( inFrustum, shadowCoord.z <= 1.0 );\n#endif\nbool frustumTest = all( frustumTestVec );\nif ( frustumTest ) {\nshadowCoord.z += shadowBias[ i ];\n#if defined( SHADOWMAP_TYPE_PCF )\nfloat shadow = 0.0;\nconst float shadowDelta = 1.0 / 9.0;\nfloat xPixelOffset = 1.0 / shadowMapSize[ i ].x;\nfloat yPixelOffset = 1.0 / shadowMapSize[ i ].y;\nfloat dx0 = -1.25 * xPixelOffset;\nfloat dy0 = -1.25 * yPixelOffset;\nfloat dx1 = 1.25 * xPixelOffset;\nfloat dy1 = 1.25 * yPixelOffset;\nfDepth = unpackDepth( texture2D( shadowMap[ i ], shadowCoord.xy + vec2( dx0, dy0 ) ) );\nif ( fDepth < shadowCoord.z ) shadow += shadowDelta;\nfDepth = unpackDepth( texture2D( shadowMap[ i ], shadowCoord.xy + vec2( 0.0, dy0 ) ) );\nif ( fDepth < shadowCoord.z ) shadow += shadowDelta;\nfDepth = unpackDepth( texture2D( shadowMap[ i ], shadowCoord.xy + vec2( dx1, dy0 ) ) );\nif ( fDepth < shadowCoord.z ) shadow += shadowDelta;\nfDepth = unpackDepth( texture2D( shadowMap[ i ], shadowCoord.xy + vec2( dx0, 0.0 ) ) );\nif ( fDepth < shadowCoord.z ) shadow += shadowDelta;\nfDepth = unpackDepth( texture2D( shadowMap[ i ], shadowCoord.xy ) );\nif ( fDepth < shadowCoord.z ) shadow += shadowDelta;\nfDepth = unpackDepth( texture2D( shadowMap[ i ], shadowCoord.xy + vec2( dx1, 0.0 ) ) );\nif ( fDepth < shadowCoord.z ) shadow += shadowDelta;\nfDepth = unpackDepth( texture2D( shadowMap[ i ], shadowCoord.xy + vec2( dx0, dy1 ) ) );\nif ( fDepth < shadowCoord.z ) shadow += shadowDelta;\nfDepth = unpackDepth( texture2D( shadowMap[ i ], shadowCoord.xy + vec2( 0.0, dy1 ) ) );\nif ( fDepth < shadowCoord.z ) shadow += shadowDelta;\nfDepth = unpackDepth( texture2D( shadowMap[ i ], shadowCoord.xy + vec2( dx1, dy1 ) ) );\nif ( fDepth < shadowCoord.z ) shadow += shadowDelta;\nshadowColor = shadowColor * vec3( ( 1.0 - shadowDarkness[ i ] * shadow ) );\n#elif defined( SHADOWMAP_TYPE_PCF_SOFT )\nfloat shadow = 0.0;\nfloat xPixelOffset = 1.0 / shadowMapSize[ i ].x;\nfloat yPixelOffset = 1.0 / shadowMapSize[ i ].y;\nfloat dx0 = -1.0 * xPixelOffset;\nfloat dy0 = -1.0 * yPixelOffset;\nfloat dx1 = 1.0 * xPixelOffset;\nfloat dy1 = 1.0 * yPixelOffset;\nmat3 shadowKernel;\nmat3 depthKernel;\ndepthKernel[0][0] = unpackDepth( texture2D( shadowMap[ i ], shadowCoord.xy + vec2( dx0, dy0 ) ) );\nif ( depthKernel[0][0] < shadowCoord.z ) shadowKernel[0][0] = 0.25;\nelse shadowKernel[0][0] = 0.0;\ndepthKernel[0][1] = unpackDepth( texture2D( shadowMap[ i ], shadowCoord.xy + vec2( dx0, 0.0 ) ) );\nif ( depthKernel[0][1] < shadowCoord.z ) shadowKernel[0][1] = 0.25;\nelse shadowKernel[0][1] = 0.0;\ndepthKernel[0][2] = unpackDepth( texture2D( shadowMap[ i], shadowCoord.xy + vec2( dx0, dy1 ) ) );\nif ( depthKernel[0][2] < shadowCoord.z ) shadowKernel[0][2] = 0.25;\nelse shadowKernel[0][2] = 0.0;\ndepthKernel[1][0] = unpackDepth( texture2D( shadowMap[ i ], shadowCoord.xy + vec2( 0.0, dy0 ) ) );\nif ( depthKernel[1][0] < shadowCoord.z ) shadowKernel[1][0] = 0.25;\nelse shadowKernel[1][0] = 0.0;\ndepthKernel[1][1] = unpackDepth( texture2D( shadowMap[ i ], shadowCoord.xy ) );\nif ( depthKernel[1][1] < shadowCoord.z ) shadowKernel[1][1] = 0.25;\nelse shadowKernel[1][1] = 0.0;\ndepthKernel[1][2] = unpackDepth( texture2D( shadowMap[ i ], shadowCoord.xy + vec2( 0.0, dy1 ) ) );\nif ( depthKernel[1][2] < shadowCoord.z ) shadowKernel[1][2] = 0.25;\nelse shadowKernel[1][2] = 0.0;\ndepthKernel[2][0] = unpackDepth( texture2D( shadowMap[ i ], shadowCoord.xy + vec2( dx1, dy0 ) ) );\nif ( depthKernel[2][0] < shadowCoord.z ) shadowKernel[2][0] = 0.25;\nelse shadowKernel[2][0] = 0.0;\ndepthKernel[2][1] = unpackDepth( texture2D( shadowMap[ i ], shadowCoord.xy + vec2( dx1, 0.0 ) ) );\nif ( depthKernel[2][1] < shadowCoord.z ) shadowKernel[2][1] = 0.25;\nelse shadowKernel[2][1] = 0.0;\ndepthKernel[2][2] = unpackDepth( texture2D( shadowMap[ i ], shadowCoord.xy + vec2( dx1, dy1 ) ) );\nif ( depthKernel[2][2] < shadowCoord.z ) shadowKernel[2][2] = 0.25;\nelse shadowKernel[2][2] = 0.0;\nvec2 fractionalCoord = 1.0 - fract( shadowCoord.xy * shadowMapSize[i].xy );\nshadowKernel[0] = mix( shadowKernel[1], shadowKernel[0], fractionalCoord.x );\nshadowKernel[1] = mix( shadowKernel[2], shadowKernel[1], fractionalCoord.x );\nvec4 shadowValues;\nshadowValues.x = mix( shadowKernel[0][1], shadowKernel[0][0], fractionalCoord.y );\nshadowValues.y = mix( shadowKernel[0][2], shadowKernel[0][1], fractionalCoord.y );\nshadowValues.z = mix( shadowKernel[1][1], shadowKernel[1][0], fractionalCoord.y );\nshadowValues.w = mix( shadowKernel[1][2], shadowKernel[1][1], fractionalCoord.y );\nshadow = dot( shadowValues, vec4( 1.0 ) );\nshadowColor = shadowColor * vec3( ( 1.0 - shadowDarkness[ i ] * shadow ) );\n#else\nvec4 rgbaDepth = texture2D( shadowMap[ i ], shadowCoord.xy );\nfloat fDepth = unpackDepth( rgbaDepth );\nif ( fDepth < shadowCoord.z )\nshadowColor = shadowColor * vec3( 1.0 - shadowDarkness[ i ] );\n#endif\n}\n#ifdef SHADOWMAP_DEBUG\n#ifdef SHADOWMAP_CASCADE\nif ( inFrustum && inFrustumCount == 1 ) gl_FragColor.xyz *= frustumColors[ i ];\n#else\nif ( inFrustum ) gl_FragColor.xyz *= frustumColors[ i ];\n#endif\n#endif\n}\n#ifdef GAMMA_OUTPUT\nshadowColor *= shadowColor;\n#endif\ngl_FragColor.xyz = gl_FragColor.xyz * shadowColor;\n#endif",shadowmap_pars_vertex:"#ifdef USE_SHADOWMAP\nvarying vec4 vShadowCoord[ MAX_SHADOWS ];\nuniform mat4 shadowMatrix[ MAX_SHADOWS ];\n#endif",shadowmap_vertex:"#ifdef USE_SHADOWMAP\nfor( int i = 0; i < MAX_SHADOWS; i ++ ) {\nvShadowCoord[ i ] = shadowMatrix[ i ] * worldPosition;\n}\n#endif",alphatest_fragment:"#ifdef ALPHATEST\nif ( gl_FragColor.a < ALPHATEST ) discard;\n#endif",linear_to_gamma_fragment:"#ifdef GAMMA_OUTPUT\ngl_FragColor.xyz = sqrt( gl_FragColor.xyz );\n#endif"};THREE.UniformsUtils={merge:function(n){for(var i,r,u={},t=0;t<n.length;t++)for(i in r=this.clone(n[t]),r)u[i]=r[i];return u},clone:function(n){var i,r,t,u={};for(i in n)for(r in u[i]={},n[i])t=n[i][r],u[i][r]=t instanceof THREE.Color||t instanceof THREE.Vector2||t instanceof THREE.Vector3||t instanceof THREE.Vector4||t instanceof THREE.Matrix4||t instanceof THREE.Texture?t.clone():t instanceof Array?t.slice():t;return u}};THREE.UniformsLib={common:{diffuse:{type:"c",value:new THREE.Color(15658734)},opacity:{type:"f",value:1},map:{type:"t",value:null},offsetRepeat:{type:"v4",value:new THREE.Vector4(0,0,1,1)},lightMap:{type:"t",value:null},specularMap:{type:"t",value:null},envMap:{type:"t",value:null},flipEnvMap:{type:"f",value:-1},useRefract:{type:"i",value:0},reflectivity:{type:"f",value:1},refractionRatio:{type:"f",value:.98},combine:{type:"i",value:0},morphTargetInfluences:{type:"f",value:0}},bump:{bumpMap:{type:"t",value:null},bumpScale:{type:"f",value:1}},normalmap:{normalMap:{type:"t",value:null},normalScale:{type:"v2",value:new THREE.Vector2(1,1)}},fog:{fogDensity:{type:"f",value:.00025},fogNear:{type:"f",value:1},fogFar:{type:"f",value:2e3},fogColor:{type:"c",value:new THREE.Color(16777215)}},lights:{ambientLightColor:{type:"fv",value:[]},directionalLightDirection:{type:"fv",value:[]},directionalLightColor:{type:"fv",value:[]},hemisphereLightDirection:{type:"fv",value:[]},hemisphereLightSkyColor:{type:"fv",value:[]},hemisphereLightGroundColor:{type:"fv",value:[]},pointLightColor:{type:"fv",value:[]},pointLightPosition:{type:"fv",value:[]},pointLightDistance:{type:"fv1",value:[]},spotLightColor:{type:"fv",value:[]},spotLightPosition:{type:"fv",value:[]},spotLightDirection:{type:"fv",value:[]},spotLightDistance:{type:"fv1",value:[]},spotLightAngleCos:{type:"fv1",value:[]},spotLightExponent:{type:"fv1",value:[]}},particle:{psColor:{type:"c",value:new THREE.Color(15658734)},opacity:{type:"f",value:1},size:{type:"f",value:1},scale:{type:"f",value:1},map:{type:"t",value:null},fogDensity:{type:"f",value:.00025},fogNear:{type:"f",value:1},fogFar:{type:"f",value:2e3},fogColor:{type:"c",value:new THREE.Color(16777215)}},shadowmap:{shadowMap:{type:"tv",value:[]},shadowMapSize:{type:"v2v",value:[]},shadowBias:{type:"fv1",value:[]},shadowDarkness:{type:"fv1",value:[]},shadowMatrix:{type:"m4v",value:[]}}};THREE.ShaderLib={basic:{uniforms:THREE.UniformsUtils.merge([THREE.UniformsLib.common,THREE.UniformsLib.fog,THREE.UniformsLib.shadowmap]),vertexShader:[THREE.ShaderChunk.map_pars_vertex,THREE.ShaderChunk.lightmap_pars_vertex,THREE.ShaderChunk.envmap_pars_vertex,THREE.ShaderChunk.color_pars_vertex,THREE.ShaderChunk.morphtarget_pars_vertex,THREE.ShaderChunk.skinning_pars_vertex,THREE.ShaderChunk.shadowmap_pars_vertex,"void main() {",THREE.ShaderChunk.map_vertex,THREE.ShaderChunk.lightmap_vertex,THREE.ShaderChunk.color_vertex,THREE.ShaderChunk.skinbase_vertex,"#ifdef USE_ENVMAP",THREE.ShaderChunk.morphnormal_vertex,THREE.ShaderChunk.skinnormal_vertex,THREE.ShaderChunk.defaultnormal_vertex,"#endif",THREE.ShaderChunk.morphtarget_vertex,THREE.ShaderChunk.skinning_vertex,THREE.ShaderChunk.default_vertex,THREE.ShaderChunk.worldpos_vertex,THREE.ShaderChunk.envmap_vertex,THREE.ShaderChunk.shadowmap_vertex,"}"].join("\n"),fragmentShader:["uniform vec3 diffuse;\nuniform float opacity;",THREE.ShaderChunk.color_pars_fragment,THREE.ShaderChunk.map_pars_fragment,THREE.ShaderChunk.lightmap_pars_fragment,THREE.ShaderChunk.envmap_pars_fragment,THREE.ShaderChunk.fog_pars_fragment,THREE.ShaderChunk.shadowmap_pars_fragment,THREE.ShaderChunk.specularmap_pars_fragment,"void main() {\ngl_FragColor = vec4( diffuse, opacity );",THREE.ShaderChunk.map_fragment,THREE.ShaderChunk.alphatest_fragment,THREE.ShaderChunk.specularmap_fragment,THREE.ShaderChunk.lightmap_fragment,THREE.ShaderChunk.color_fragment,THREE.ShaderChunk.envmap_fragment,THREE.ShaderChunk.shadowmap_fragment,THREE.ShaderChunk.linear_to_gamma_fragment,THREE.ShaderChunk.fog_fragment,"}"].join("\n")},lambert:{uniforms:THREE.UniformsUtils.merge([THREE.UniformsLib.common,THREE.UniformsLib.fog,THREE.UniformsLib.lights,THREE.UniformsLib.shadowmap,{ambient:{type:"c",value:new THREE.Color(16777215)},emissive:{type:"c",value:new THREE.Color(0)},wrapRGB:{type:"v3",value:new THREE.Vector3(1,1,1)}}]),vertexShader:["#define LAMBERT\nvarying vec3 vLightFront;\n#ifdef DOUBLE_SIDED\nvarying vec3 vLightBack;\n#endif",THREE.ShaderChunk.map_pars_vertex,THREE.ShaderChunk.lightmap_pars_vertex,THREE.ShaderChunk.envmap_pars_vertex,THREE.ShaderChunk.lights_lambert_pars_vertex,THREE.ShaderChunk.color_pars_vertex,THREE.ShaderChunk.morphtarget_pars_vertex,THREE.ShaderChunk.skinning_pars_vertex,THREE.ShaderChunk.shadowmap_pars_vertex,"void main() {",THREE.ShaderChunk.map_vertex,THREE.ShaderChunk.lightmap_vertex,THREE.ShaderChunk.color_vertex,THREE.ShaderChunk.morphnormal_vertex,THREE.ShaderChunk.skinbase_vertex,THREE.ShaderChunk.skinnormal_vertex,THREE.ShaderChunk.defaultnormal_vertex,THREE.ShaderChunk.morphtarget_vertex,THREE.ShaderChunk.skinning_vertex,THREE.ShaderChunk.default_vertex,THREE.ShaderChunk.worldpos_vertex,THREE.ShaderChunk.envmap_vertex,THREE.ShaderChunk.lights_lambert_vertex,THREE.ShaderChunk.shadowmap_vertex,"}"].join("\n"),fragmentShader:["uniform float opacity;\nvarying vec3 vLightFront;\n#ifdef DOUBLE_SIDED\nvarying vec3 vLightBack;\n#endif",THREE.ShaderChunk.color_pars_fragment,THREE.ShaderChunk.map_pars_fragment,THREE.ShaderChunk.lightmap_pars_fragment,THREE.ShaderChunk.envmap_pars_fragment,THREE.ShaderChunk.fog_pars_fragment,THREE.ShaderChunk.shadowmap_pars_fragment,THREE.ShaderChunk.specularmap_pars_fragment,"void main() {\ngl_FragColor = vec4( vec3 ( 1.0 ), opacity );",THREE.ShaderChunk.map_fragment,THREE.ShaderChunk.alphatest_fragment,THREE.ShaderChunk.specularmap_fragment,"#ifdef DOUBLE_SIDED\nif ( gl_FrontFacing )\ngl_FragColor.xyz *= vLightFront;\nelse\ngl_FragColor.xyz *= vLightBack;\n#else\ngl_FragColor.xyz *= vLightFront;\n#endif",THREE.ShaderChunk.lightmap_fragment,THREE.ShaderChunk.color_fragment,THREE.ShaderChunk.envmap_fragment,THREE.ShaderChunk.shadowmap_fragment,THREE.ShaderChunk.linear_to_gamma_fragment,THREE.ShaderChunk.fog_fragment,"}"].join("\n")},phong:{uniforms:THREE.UniformsUtils.merge([THREE.UniformsLib.common,THREE.UniformsLib.bump,THREE.UniformsLib.normalmap,THREE.UniformsLib.fog,THREE.UniformsLib.lights,THREE.UniformsLib.shadowmap,{ambient:{type:"c",value:new THREE.Color(16777215)},emissive:{type:"c",value:new THREE.Color(0)},specular:{type:"c",value:new THREE.Color(1118481)},shininess:{type:"f",value:30},wrapRGB:{type:"v3",value:new THREE.Vector3(1,1,1)}}]),vertexShader:["#define PHONG\nvarying vec3 vViewPosition;\nvarying vec3 vNormal;",THREE.ShaderChunk.map_pars_vertex,THREE.ShaderChunk.lightmap_pars_vertex,THREE.ShaderChunk.envmap_pars_vertex,THREE.ShaderChunk.lights_phong_pars_vertex,THREE.ShaderChunk.color_pars_vertex,THREE.ShaderChunk.morphtarget_pars_vertex,THREE.ShaderChunk.skinning_pars_vertex,THREE.ShaderChunk.shadowmap_pars_vertex,"void main() {",THREE.ShaderChunk.map_vertex,THREE.ShaderChunk.lightmap_vertex,THREE.ShaderChunk.color_vertex,THREE.ShaderChunk.morphnormal_vertex,THREE.ShaderChunk.skinbase_vertex,THREE.ShaderChunk.skinnormal_vertex,THREE.ShaderChunk.defaultnormal_vertex,"vNormal = normalize( transformedNormal );",THREE.ShaderChunk.morphtarget_vertex,THREE.ShaderChunk.skinning_vertex,THREE.ShaderChunk.default_vertex,"vViewPosition = -mvPosition.xyz;",THREE.ShaderChunk.worldpos_vertex,THREE.ShaderChunk.envmap_vertex,THREE.ShaderChunk.lights_phong_vertex,THREE.ShaderChunk.shadowmap_vertex,"}"].join("\n"),fragmentShader:["uniform vec3 diffuse;\nuniform float opacity;\nuniform vec3 ambient;\nuniform vec3 emissive;\nuniform vec3 specular;\nuniform float shininess;",THREE.ShaderChunk.color_pars_fragment,THREE.ShaderChunk.map_pars_fragment,THREE.ShaderChunk.lightmap_pars_fragment,THREE.ShaderChunk.envmap_pars_fragment,THREE.ShaderChunk.fog_pars_fragment,THREE.ShaderChunk.lights_phong_pars_fragment,THREE.ShaderChunk.shadowmap_pars_fragment,THREE.ShaderChunk.bumpmap_pars_fragment,THREE.ShaderChunk.normalmap_pars_fragment,THREE.ShaderChunk.specularmap_pars_fragment,"void main() {\ngl_FragColor = vec4( vec3 ( 1.0 ), opacity );",THREE.ShaderChunk.map_fragment,THREE.ShaderChunk.alphatest_fragment,THREE.ShaderChunk.specularmap_fragment,THREE.ShaderChunk.lights_phong_fragment,THREE.ShaderChunk.lightmap_fragment,THREE.ShaderChunk.color_fragment,THREE.ShaderChunk.envmap_fragment,THREE.ShaderChunk.shadowmap_fragment,THREE.ShaderChunk.linear_to_gamma_fragment,THREE.ShaderChunk.fog_fragment,"}"].join("\n")},particle_basic:{uniforms:THREE.UniformsUtils.merge([THREE.UniformsLib.particle,THREE.UniformsLib.shadowmap]),vertexShader:["uniform float size;\nuniform float scale;",THREE.ShaderChunk.color_pars_vertex,THREE.ShaderChunk.shadowmap_pars_vertex,"void main() {",THREE.ShaderChunk.color_vertex,"vec4 mvPosition = modelViewMatrix * vec4( position, 1.0 );\n#ifdef USE_SIZEATTENUATION\ngl_PointSize = size * ( scale / length( mvPosition.xyz ) );\n#else\ngl_PointSize = size;\n#endif\ngl_Position = projectionMatrix * mvPosition;",THREE.ShaderChunk.worldpos_vertex,THREE.ShaderChunk.shadowmap_vertex,"}"].join("\n"),fragmentShader:["uniform vec3 psColor;\nuniform float opacity;",THREE.ShaderChunk.color_pars_fragment,THREE.ShaderChunk.map_particle_pars_fragment,THREE.ShaderChunk.fog_pars_fragment,THREE.ShaderChunk.shadowmap_pars_fragment,"void main() {\ngl_FragColor = vec4( psColor, opacity );",THREE.ShaderChunk.map_particle_fragment,THREE.ShaderChunk.alphatest_fragment,THREE.ShaderChunk.color_fragment,THREE.ShaderChunk.shadowmap_fragment,THREE.ShaderChunk.fog_fragment,"}"].join("\n")},dashed:{uniforms:THREE.UniformsUtils.merge([THREE.UniformsLib.common,THREE.UniformsLib.fog,{scale:{type:"f",value:1},dashSize:{type:"f",value:1},totalSize:{type:"f",value:2}}]),vertexShader:["uniform float scale;\nattribute float lineDistance;\nvarying float vLineDistance;",THREE.ShaderChunk.color_pars_vertex,"void main() {",THREE.ShaderChunk.color_vertex,"vLineDistance = scale * lineDistance;\nvec4 mvPosition = modelViewMatrix * vec4( position, 1.0 );\ngl_Position = projectionMatrix * mvPosition;\n}"].join("\n"),fragmentShader:["uniform vec3 diffuse;\nuniform float opacity;\nuniform float dashSize;\nuniform float totalSize;\nvarying float vLineDistance;",THREE.ShaderChunk.color_pars_fragment,THREE.ShaderChunk.fog_pars_fragment,"void main() {\nif ( mod( vLineDistance, totalSize ) > dashSize ) {\ndiscard;\n}\ngl_FragColor = vec4( diffuse, opacity );",THREE.ShaderChunk.color_fragment,THREE.ShaderChunk.fog_fragment,"}"].join("\n")},depth:{uniforms:{mNear:{type:"f",value:1},mFar:{type:"f",value:2e3},opacity:{type:"f",value:1}},vertexShader:"void main() {\ngl_Position = projectionMatrix * modelViewMatrix * vec4( position, 1.0 );\n}",fragmentShader:"uniform float mNear;\nuniform float mFar;\nuniform float opacity;\nvoid main() {\nfloat depth = gl_FragCoord.z / gl_FragCoord.w;\nfloat color = 1.0 - smoothstep( mNear, mFar, depth );\ngl_FragColor = vec4( vec3( color ), opacity );\n}"},normal:{uniforms:{opacity:{type:"f",value:1}},vertexShader:"varying vec3 vNormal;\nvoid main() {\nvec4 mvPosition = modelViewMatrix * vec4( position, 1.0 );\nvNormal = normalize( normalMatrix * normal );\ngl_Position = projectionMatrix * mvPosition;\n}",fragmentShader:"uniform float opacity;\nvarying vec3 vNormal;\nvoid main() {\ngl_FragColor = vec4( 0.5 * normalize( vNormal ) + 0.5, opacity );\n}"},normalmap:{uniforms:THREE.UniformsUtils.merge([THREE.UniformsLib.fog,THREE.UniformsLib.lights,THREE.UniformsLib.shadowmap,{enableAO:{type:"i",value:0},enableDiffuse:{type:"i",value:0},enableSpecular:{type:"i",value:0},enableReflection:{type:"i",value:0},enableDisplacement:{type:"i",value:0},tDisplacement:{type:"t",value:null},tDiffuse:{type:"t",value:null},tCube:{type:"t",value:null},tNormal:{type:"t",value:null},tSpecular:{type:"t",value:null},tAO:{type:"t",value:null},uNormalScale:{type:"v2",value:new THREE.Vector2(1,1)},uDisplacementBias:{type:"f",value:0},uDisplacementScale:{type:"f",value:1},uDiffuseColor:{type:"c",value:new THREE.Color(16777215)},uSpecularColor:{type:"c",value:new THREE.Color(1118481)},uAmbientColor:{type:"c",value:new THREE.Color(16777215)},uShininess:{type:"f",value:30},uOpacity:{type:"f",value:1},useRefract:{type:"i",value:0},uRefractionRatio:{type:"f",value:.98},uReflectivity:{type:"f",value:.5},uOffset:{type:"v2",value:new THREE.Vector2(0,0)},uRepeat:{type:"v2",value:new THREE.Vector2(1,1)},wrapRGB:{type:"v3",value:new THREE.Vector3(1,1,1)}}]),fragmentShader:["uniform vec3 uAmbientColor;\nuniform vec3 uDiffuseColor;\nuniform vec3 uSpecularColor;\nuniform float uShininess;\nuniform float uOpacity;\nuniform bool enableDiffuse;\nuniform bool enableSpecular;\nuniform bool enableAO;\nuniform bool enableReflection;\nuniform sampler2D tDiffuse;\nuniform sampler2D tNormal;\nuniform sampler2D tSpecular;\nuniform sampler2D tAO;\nuniform samplerCube tCube;\nuniform vec2 uNormalScale;\nuniform bool useRefract;\nuniform float uRefractionRatio;\nuniform float uReflectivity;\nvarying vec3 vTangent;\nvarying vec3 vBinormal;\nvarying vec3 vNormal;\nvarying vec2 vUv;\nuniform vec3 ambientLightColor;\n#if MAX_DIR_LIGHTS > 0\nuniform vec3 directionalLightColor[ MAX_DIR_LIGHTS ];\nuniform vec3 directionalLightDirection[ MAX_DIR_LIGHTS ];\n#endif\n#if MAX_HEMI_LIGHTS > 0\nuniform vec3 hemisphereLightSkyColor[ MAX_HEMI_LIGHTS ];\nuniform vec3 hemisphereLightGroundColor[ MAX_HEMI_LIGHTS ];\nuniform vec3 hemisphereLightDirection[ MAX_HEMI_LIGHTS ];\n#endif\n#if MAX_POINT_LIGHTS > 0\nuniform vec3 pointLightColor[ MAX_POINT_LIGHTS ];\nuniform vec3 pointLightPosition[ MAX_POINT_LIGHTS ];\nuniform float pointLightDistance[ MAX_POINT_LIGHTS ];\n#endif\n#if MAX_SPOT_LIGHTS > 0\nuniform vec3 spotLightColor[ MAX_SPOT_LIGHTS ];\nuniform vec3 spotLightPosition[ MAX_SPOT_LIGHTS ];\nuniform vec3 spotLightDirection[ MAX_SPOT_LIGHTS ];\nuniform float spotLightAngleCos[ MAX_SPOT_LIGHTS ];\nuniform float spotLightExponent[ MAX_SPOT_LIGHTS ];\nuniform float spotLightDistance[ MAX_SPOT_LIGHTS ];\n#endif\n#ifdef WRAP_AROUND\nuniform vec3 wrapRGB;\n#endif\nvarying vec3 vWorldPosition;\nvarying vec3 vViewPosition;",THREE.ShaderChunk.shadowmap_pars_fragment,THREE.ShaderChunk.fog_pars_fragment,"void main() {\ngl_FragColor = vec4( vec3( 1.0 ), uOpacity );\nvec3 specularTex = vec3( 1.0 );\nvec3 normalTex = texture2D( tNormal, vUv ).xyz * 2.0 - 1.0;\nnormalTex.xy *= uNormalScale;\nnormalTex = normalize( normalTex );\nif( enableDiffuse ) {\n#ifdef GAMMA_INPUT\nvec4 texelColor = texture2D( tDiffuse, vUv );\ntexelColor.xyz *= texelColor.xyz;\ngl_FragColor = gl_FragColor * texelColor;\n#else\ngl_FragColor = gl_FragColor * texture2D( tDiffuse, vUv );\n#endif\n}\nif( enableAO ) {\n#ifdef GAMMA_INPUT\nvec4 aoColor = texture2D( tAO, vUv );\naoColor.xyz *= aoColor.xyz;\ngl_FragColor.xyz = gl_FragColor.xyz * aoColor.xyz;\n#else\ngl_FragColor.xyz = gl_FragColor.xyz * texture2D( tAO, vUv ).xyz;\n#endif\n}\nif( enableSpecular )\nspecularTex = texture2D( tSpecular, vUv ).xyz;\nmat3 tsb = mat3( normalize( vTangent ), normalize( vBinormal ), normalize( vNormal ) );\nvec3 finalNormal = tsb * normalTex;\n#ifdef FLIP_SIDED\nfinalNormal = -finalNormal;\n#endif\nvec3 normal = normalize( finalNormal );\nvec3 viewPosition = normalize( vViewPosition );\n#if MAX_POINT_LIGHTS > 0\nvec3 pointDiffuse = vec3( 0.0 );\nvec3 pointSpecular = vec3( 0.0 );\nfor ( int i = 0; i < MAX_POINT_LIGHTS; i ++ ) {\nvec4 lPosition = viewMatrix * vec4( pointLightPosition[ i ], 1.0 );\nvec3 pointVector = lPosition.xyz + vViewPosition.xyz;\nfloat pointDistance = 1.0;\nif ( pointLightDistance[ i ] > 0.0 )\npointDistance = 1.0 - min( ( length( pointVector ) / pointLightDistance[ i ] ), 1.0 );\npointVector = normalize( pointVector );\n#ifdef WRAP_AROUND\nfloat pointDiffuseWeightFull = max( dot( normal, pointVector ), 0.0 );\nfloat pointDiffuseWeightHalf = max( 0.5 * dot( normal, pointVector ) + 0.5, 0.0 );\nvec3 pointDiffuseWeight = mix( vec3 ( pointDiffuseWeightFull ), vec3( pointDiffuseWeightHalf ), wrapRGB );\n#else\nfloat pointDiffuseWeight = max( dot( normal, pointVector ), 0.0 );\n#endif\npointDiffuse += pointDistance * pointLightColor[ i ] * uDiffuseColor * pointDiffuseWeight;\nvec3 pointHalfVector = normalize( pointVector + viewPosition );\nfloat pointDotNormalHalf = max( dot( normal, pointHalfVector ), 0.0 );\nfloat pointSpecularWeight = specularTex.r * max( pow( pointDotNormalHalf, uShininess ), 0.0 );\n#ifdef PHYSICALLY_BASED_SHADING\nfloat specularNormalization = ( uShininess + 2.0001 ) / 8.0;\nvec3 schlick = uSpecularColor + vec3( 1.0 - uSpecularColor ) * pow( 1.0 - dot( pointVector, pointHalfVector ), 5.0 );\npointSpecular += schlick * pointLightColor[ i ] * pointSpecularWeight * pointDiffuseWeight * pointDistance * specularNormalization;\n#else\npointSpecular += pointDistance * pointLightColor[ i ] * uSpecularColor * pointSpecularWeight * pointDiffuseWeight;\n#endif\n}\n#endif\n#if MAX_SPOT_LIGHTS > 0\nvec3 spotDiffuse = vec3( 0.0 );\nvec3 spotSpecular = vec3( 0.0 );\nfor ( int i = 0; i < MAX_SPOT_LIGHTS; i ++ ) {\nvec4 lPosition = viewMatrix * vec4( spotLightPosition[ i ], 1.0 );\nvec3 spotVector = lPosition.xyz + vViewPosition.xyz;\nfloat spotDistance = 1.0;\nif ( spotLightDistance[ i ] > 0.0 )\nspotDistance = 1.0 - min( ( length( spotVector ) / spotLightDistance[ i ] ), 1.0 );\nspotVector = normalize( spotVector );\nfloat spotEffect = dot( spotLightDirection[ i ], normalize( spotLightPosition[ i ] - vWorldPosition ) );\nif ( spotEffect > spotLightAngleCos[ i ] ) {\nspotEffect = max( pow( spotEffect, spotLightExponent[ i ] ), 0.0 );\n#ifdef WRAP_AROUND\nfloat spotDiffuseWeightFull = max( dot( normal, spotVector ), 0.0 );\nfloat spotDiffuseWeightHalf = max( 0.5 * dot( normal, spotVector ) + 0.5, 0.0 );\nvec3 spotDiffuseWeight = mix( vec3 ( spotDiffuseWeightFull ), vec3( spotDiffuseWeightHalf ), wrapRGB );\n#else\nfloat spotDiffuseWeight = max( dot( normal, spotVector ), 0.0 );\n#endif\nspotDiffuse += spotDistance * spotLightColor[ i ] * uDiffuseColor * spotDiffuseWeight * spotEffect;\nvec3 spotHalfVector = normalize( spotVector + viewPosition );\nfloat spotDotNormalHalf = max( dot( normal, spotHalfVector ), 0.0 );\nfloat spotSpecularWeight = specularTex.r * max( pow( spotDotNormalHalf, uShininess ), 0.0 );\n#ifdef PHYSICALLY_BASED_SHADING\nfloat specularNormalization = ( uShininess + 2.0001 ) / 8.0;\nvec3 schlick = uSpecularColor + vec3( 1.0 - uSpecularColor ) * pow( 1.0 - dot( spotVector, spotHalfVector ), 5.0 );\nspotSpecular += schlick * spotLightColor[ i ] * spotSpecularWeight * spotDiffuseWeight * spotDistance * specularNormalization * spotEffect;\n#else\nspotSpecular += spotDistance * spotLightColor[ i ] * uSpecularColor * spotSpecularWeight * spotDiffuseWeight * spotEffect;\n#endif\n}\n}\n#endif\n#if MAX_DIR_LIGHTS > 0\nvec3 dirDiffuse = vec3( 0.0 );\nvec3 dirSpecular = vec3( 0.0 );\nfor( int i = 0; i < MAX_DIR_LIGHTS; i++ ) {\nvec4 lDirection = viewMatrix * vec4( directionalLightDirection[ i ], 0.0 );\nvec3 dirVector = normalize( lDirection.xyz );\n#ifdef WRAP_AROUND\nfloat directionalLightWeightingFull = max( dot( normal, dirVector ), 0.0 );\nfloat directionalLightWeightingHalf = max( 0.5 * dot( normal, dirVector ) + 0.5, 0.0 );\nvec3 dirDiffuseWeight = mix( vec3( directionalLightWeightingFull ), vec3( directionalLightWeightingHalf ), wrapRGB );\n#else\nfloat dirDiffuseWeight = max( dot( normal, dirVector ), 0.0 );\n#endif\ndirDiffuse += directionalLightColor[ i ] * uDiffuseColor * dirDiffuseWeight;\nvec3 dirHalfVector = normalize( dirVector + viewPosition );\nfloat dirDotNormalHalf = max( dot( normal, dirHalfVector ), 0.0 );\nfloat dirSpecularWeight = specularTex.r * max( pow( dirDotNormalHalf, uShininess ), 0.0 );\n#ifdef PHYSICALLY_BASED_SHADING\nfloat specularNormalization = ( uShininess + 2.0001 ) / 8.0;\nvec3 schlick = uSpecularColor + vec3( 1.0 - uSpecularColor ) * pow( 1.0 - dot( dirVector, dirHalfVector ), 5.0 );\ndirSpecular += schlick * directionalLightColor[ i ] * dirSpecularWeight * dirDiffuseWeight * specularNormalization;\n#else\ndirSpecular += directionalLightColor[ i ] * uSpecularColor * dirSpecularWeight * dirDiffuseWeight;\n#endif\n}\n#endif\n#if MAX_HEMI_LIGHTS > 0\nvec3 hemiDiffuse  = vec3( 0.0 );\nvec3 hemiSpecular = vec3( 0.0 );\nfor( int i = 0; i < MAX_HEMI_LIGHTS; i ++ ) {\nvec4 lDirection = viewMatrix * vec4( hemisphereLightDirection[ i ], 0.0 );\nvec3 lVector = normalize( lDirection.xyz );\nfloat dotProduct = dot( normal, lVector );\nfloat hemiDiffuseWeight = 0.5 * dotProduct + 0.5;\nvec3 hemiColor = mix( hemisphereLightGroundColor[ i ], hemisphereLightSkyColor[ i ], hemiDiffuseWeight );\nhemiDiffuse += uDiffuseColor * hemiColor;\nvec3 hemiHalfVectorSky = normalize( lVector + viewPosition );\nfloat hemiDotNormalHalfSky = 0.5 * dot( normal, hemiHalfVectorSky ) + 0.5;\nfloat hemiSpecularWeightSky = specularTex.r * max( pow( hemiDotNormalHalfSky, uShininess ), 0.0 );\nvec3 lVectorGround = -lVector;\nvec3 hemiHalfVectorGround = normalize( lVectorGround + viewPosition );\nfloat hemiDotNormalHalfGround = 0.5 * dot( normal, hemiHalfVectorGround ) + 0.5;\nfloat hemiSpecularWeightGround = specularTex.r * max( pow( hemiDotNormalHalfGround, uShininess ), 0.0 );\n#ifdef PHYSICALLY_BASED_SHADING\nfloat dotProductGround = dot( normal, lVectorGround );\nfloat specularNormalization = ( uShininess + 2.0001 ) / 8.0;\nvec3 schlickSky = uSpecularColor + vec3( 1.0 - uSpecularColor ) * pow( 1.0 - dot( lVector, hemiHalfVectorSky ), 5.0 );\nvec3 schlickGround = uSpecularColor + vec3( 1.0 - uSpecularColor ) * pow( 1.0 - dot( lVectorGround, hemiHalfVectorGround ), 5.0 );\nhemiSpecular += hemiColor * specularNormalization * ( schlickSky * hemiSpecularWeightSky * max( dotProduct, 0.0 ) + schlickGround * hemiSpecularWeightGround * max( dotProductGround, 0.0 ) );\n#else\nhemiSpecular += uSpecularColor * hemiColor * ( hemiSpecularWeightSky + hemiSpecularWeightGround ) * hemiDiffuseWeight;\n#endif\n}\n#endif\nvec3 totalDiffuse = vec3( 0.0 );\nvec3 totalSpecular = vec3( 0.0 );\n#if MAX_DIR_LIGHTS > 0\ntotalDiffuse += dirDiffuse;\ntotalSpecular += dirSpecular;\n#endif\n#if MAX_HEMI_LIGHTS > 0\ntotalDiffuse += hemiDiffuse;\ntotalSpecular += hemiSpecular;\n#endif\n#if MAX_POINT_LIGHTS > 0\ntotalDiffuse += pointDiffuse;\ntotalSpecular += pointSpecular;\n#endif\n#if MAX_SPOT_LIGHTS > 0\ntotalDiffuse += spotDiffuse;\ntotalSpecular += spotSpecular;\n#endif\n#ifdef METAL\ngl_FragColor.xyz = gl_FragColor.xyz * ( totalDiffuse + ambientLightColor * uAmbientColor + totalSpecular );\n#else\ngl_FragColor.xyz = gl_FragColor.xyz * ( totalDiffuse + ambientLightColor * uAmbientColor ) + totalSpecular;\n#endif\nif ( enableReflection ) {\nvec3 vReflect;\nvec3 cameraToVertex = normalize( vWorldPosition - cameraPosition );\nif ( useRefract ) {\nvReflect = refract( cameraToVertex, normal, uRefractionRatio );\n} else {\nvReflect = reflect( cameraToVertex, normal );\n}\nvec4 cubeColor = textureCube( tCube, vec3( -vReflect.x, vReflect.yz ) );\n#ifdef GAMMA_INPUT\ncubeColor.xyz *= cubeColor.xyz;\n#endif\ngl_FragColor.xyz = mix( gl_FragColor.xyz, cubeColor.xyz, specularTex.r * uReflectivity );\n}",THREE.ShaderChunk.shadowmap_fragment,THREE.ShaderChunk.linear_to_gamma_fragment,THREE.ShaderChunk.fog_fragment,"}"].join("\n"),vertexShader:["attribute vec4 tangent;\nuniform vec2 uOffset;\nuniform vec2 uRepeat;\nuniform bool enableDisplacement;\n#ifdef VERTEX_TEXTURES\nuniform sampler2D tDisplacement;\nuniform float uDisplacementScale;\nuniform float uDisplacementBias;\n#endif\nvarying vec3 vTangent;\nvarying vec3 vBinormal;\nvarying vec3 vNormal;\nvarying vec2 vUv;\nvarying vec3 vWorldPosition;\nvarying vec3 vViewPosition;",THREE.ShaderChunk.skinning_pars_vertex,THREE.ShaderChunk.shadowmap_pars_vertex,"void main() {",THREE.ShaderChunk.skinbase_vertex,THREE.ShaderChunk.skinnormal_vertex,"#ifdef USE_SKINNING\nvNormal = normalize( normalMatrix * skinnedNormal.xyz );\nvec4 skinnedTangent = skinMatrix * vec4( tangent.xyz, 0.0 );\nvTangent = normalize( normalMatrix * skinnedTangent.xyz );\n#else\nvNormal = normalize( normalMatrix * normal );\nvTangent = normalize( normalMatrix * tangent.xyz );\n#endif\nvBinormal = normalize( cross( vNormal, vTangent ) * tangent.w );\nvUv = uv * uRepeat + uOffset;\nvec3 displacedPosition;\n#ifdef VERTEX_TEXTURES\nif ( enableDisplacement ) {\nvec3 dv = texture2D( tDisplacement, uv ).xyz;\nfloat df = uDisplacementScale * dv.x + uDisplacementBias;\ndisplacedPosition = position + normalize( normal ) * df;\n} else {\n#ifdef USE_SKINNING\nvec4 skinVertex = vec4( position, 1.0 );\nvec4 skinned  = boneMatX * skinVertex * skinWeight.x;\nskinned \t  += boneMatY * skinVertex * skinWeight.y;\ndisplacedPosition  = skinned.xyz;\n#else\ndisplacedPosition = position;\n#endif\n}\n#else\n#ifdef USE_SKINNING\nvec4 skinVertex = vec4( position, 1.0 );\nvec4 skinned  = boneMatX * skinVertex * skinWeight.x;\nskinned \t  += boneMatY * skinVertex * skinWeight.y;\ndisplacedPosition  = skinned.xyz;\n#else\ndisplacedPosition = position;\n#endif\n#endif\nvec4 mvPosition = modelViewMatrix * vec4( displacedPosition, 1.0 );\nvec4 worldPosition = modelMatrix * vec4( displacedPosition, 1.0 );\ngl_Position = projectionMatrix * mvPosition;\nvWorldPosition = worldPosition.xyz;\nvViewPosition = -mvPosition.xyz;\n#ifdef USE_SHADOWMAP\nfor( int i = 0; i < MAX_SHADOWS; i ++ ) {\nvShadowCoord[ i ] = shadowMatrix[ i ] * worldPosition;\n}\n#endif\n}"].join("\n")},cube:{uniforms:{tCube:{type:"t",value:null},tFlip:{type:"f",value:-1}},vertexShader:"varying vec3 vWorldPosition;\nvoid main() {\nvec4 worldPosition = modelMatrix * vec4( position, 1.0 );\nvWorldPosition = worldPosition.xyz;\ngl_Position = projectionMatrix * modelViewMatrix * vec4( position, 1.0 );\n}",fragmentShader:"uniform samplerCube tCube;\nuniform float tFlip;\nvarying vec3 vWorldPosition;\nvoid main() {\ngl_FragColor = textureCube( tCube, vec3( tFlip * vWorldPosition.x, vWorldPosition.yz ) );\n}"},depthRGBA:{uniforms:{},vertexShader:[THREE.ShaderChunk.morphtarget_pars_vertex,THREE.ShaderChunk.skinning_pars_vertex,"void main() {",THREE.ShaderChunk.skinbase_vertex,THREE.ShaderChunk.morphtarget_vertex,THREE.ShaderChunk.skinning_vertex,THREE.ShaderChunk.default_vertex,"}"].join("\n"),fragmentShader:"vec4 pack_depth( const in float depth ) {\nconst vec4 bit_shift = vec4( 256.0 * 256.0 * 256.0, 256.0 * 256.0, 256.0, 1.0 );\nconst vec4 bit_mask  = vec4( 0.0, 1.0 / 256.0, 1.0 / 256.0, 1.0 / 256.0 );\nvec4 res = fract( depth * bit_shift );\nres -= res.xxyz * bit_mask;\nreturn res;\n}\nvoid main() {\ngl_FragData[ 0 ] = pack_depth( gl_FragCoord.z );\n}"}};THREE.WebGLRenderer=function(n){function bi(n){if(n.__webglCustomAttributesList)for(var i in n.__webglCustomAttributesList)t.deleteBuffer(n.__webglCustomAttributesList[i].buffer)}function at(n,i){var o=n.vertices.length,f=i.material,e,r,u;if(f.attributes){void 0===n.__webglCustomAttributesList&&(n.__webglCustomAttributesList=[]);for(e in f.attributes)r=f.attributes[e],(!r.__webglInitialized||r.createUniqueBuffers)&&(r.__webglInitialized=!0,u=1,"v2"===r.type?u=2:"v3"===r.type?u=3:"v4"===r.type?u=4:"c"===r.type&&(u=3),r.size=u,r.array=new Float32Array(o*u),r.buffer=t.createBuffer(),r.buffer.belongsToAttribute=e,r.needsUpdate=!0),n.__webglCustomAttributesList.push(r)}}function ki(n,i){var r=i.geometry,e=n.faces3,u=n.faces4,f=3*e.length+4*u.length,o=1*e.length+2*u.length,u=3*e.length+4*u.length,e=b(i,n),s=gi(e),l=di(e),a=e.vertexColors?e.vertexColors:!1,h,c;if(n.__vertexArray=new Float32Array(3*f),l&&(n.__normalArray=new Float32Array(3*f)),r.hasTangents&&(n.__tangentArray=new Float32Array(4*f)),a&&(n.__colorArray=new Float32Array(3*f)),s&&((0<r.faceUvs.length||0<r.faceVertexUvs.length)&&(n.__uvArray=new Float32Array(2*f)),(1<r.faceUvs.length||1<r.faceVertexUvs.length)&&(n.__uv2Array=new Float32Array(2*f))),i.geometry.skinWeights.length&&i.geometry.skinIndices.length&&(n.__skinIndexArray=new Float32Array(4*f),n.__skinWeightArray=new Float32Array(4*f)),n.__faceArray=new Uint16Array(3*o),n.__lineArray=new Uint16Array(2*u),n.numMorphTargets)for(n.__morphTargetsArrays=[],r=0,s=n.numMorphTargets;r<s;r++)n.__morphTargetsArrays.push(new Float32Array(3*f));if(n.numMorphNormals)for(n.__morphNormalsArrays=[],r=0,s=n.numMorphNormals;r<s;r++)n.__morphNormalsArrays.push(new Float32Array(3*f));if(n.__webglFaceCount=3*o,n.__webglLineCount=2*u,e.attributes){void 0===n.__webglCustomAttributesList&&(n.__webglCustomAttributesList=[]);for(h in e.attributes){o=e.attributes[h];r={};for(c in o)r[c]=o[c];(!r.__webglInitialized||r.createUniqueBuffers)&&(r.__webglInitialized=!0,u=1,"v2"===r.type?u=2:"v3"===r.type?u=3:"v4"===r.type?u=4:"c"===r.type&&(u=3),r.size=u,r.array=new Float32Array(f*u),r.buffer=t.createBuffer(),r.buffer.belongsToAttribute=h,o.needsUpdate=!0,r.__original=o);n.__webglCustomAttributesList.push(r)}}n.__inittedArrays=!0}function b(n,t){return n.material instanceof THREE.MeshFaceMaterial?n.material.materials[t.materialIndex]:n.material}function di(n){return n instanceof THREE.MeshBasicMaterial&&!n.envMap||n instanceof THREE.MeshDepthMaterial?!1:n&&void 0!==n.shading&&n.shading===THREE.SmoothShading?THREE.SmoothShading:THREE.FlatShading}function gi(n){return n.map||n.lightMap||n.bumpMap||n.normalMap||n.specularMap||n instanceof THREE.ShaderMaterial?!0:!1}function vt(n){var r,i,u;for(r in n.attributes)u="index"===r?t.ELEMENT_ARRAY_BUFFER:t.ARRAY_BUFFER,i=n.attributes[r],i.buffer=t.createBuffer(),t.bindBuffer(u,i.buffer),t.bufferData(u,i.array,t.STATIC_DRAW)}function yt(n,i,r){var u=n.attributes,f=u.index,e=u.position,o=u.normal,s=u.uv,h=u.color,u=u.tangent,c;if(n.elementsNeedUpdate&&void 0!==f&&(t.bindBuffer(t.ELEMENT_ARRAY_BUFFER,f.buffer),t.bufferData(t.ELEMENT_ARRAY_BUFFER,f.array,i)),n.verticesNeedUpdate&&void 0!==e&&(t.bindBuffer(t.ARRAY_BUFFER,e.buffer),t.bufferData(t.ARRAY_BUFFER,e.array,i)),n.normalsNeedUpdate&&void 0!==o&&(t.bindBuffer(t.ARRAY_BUFFER,o.buffer),t.bufferData(t.ARRAY_BUFFER,o.array,i)),n.uvsNeedUpdate&&void 0!==s&&(t.bindBuffer(t.ARRAY_BUFFER,s.buffer),t.bufferData(t.ARRAY_BUFFER,s.array,i)),n.colorsNeedUpdate&&void 0!==h&&(t.bindBuffer(t.ARRAY_BUFFER,h.buffer),t.bufferData(t.ARRAY_BUFFER,h.array,i)),n.tangentsNeedUpdate&&void 0!==u&&(t.bindBuffer(t.ARRAY_BUFFER,u.buffer),t.bufferData(t.ARRAY_BUFFER,u.array,i)),r)for(c in n.attributes)delete n.attributes[c].array}function r(n){et[n]||(t.enableVertexAttribArray(n),et[n]=!0)}function nr(){for(var n in et)et[n]&&(t.disableVertexAttribArray(n),et[n]=!1)}function ou(n,t){return n.z!==t.z?t.z-n.z:t.id-n.id}function pt(n,t){return t[0]-n[0]}function tr(n,t,i){if(n.length)for(var r=0,u=n.length;r<u;r++)nt=g=null,y=a=it=tt=ft=ut=rt=-1,p=!0,n[r].render(t,i,vr,yr),nt=g=null,y=a=it=tt=ft=ut=rt=-1,p=!0}function wt(n,t,r,u,f,e,o,s){var h,c,l,v,a;for(t?(c=n.length-1,v=t=-1):(c=0,t=n.length,v=1),a=c;a!==t;a+=v)if(h=n[a],h.render){if(c=h.object,l=h.buffer,s)h=s;else{if(h=h[r],!h)continue;o&&i.setBlending(h.blending,h.blendEquation,h.blendSrc,h.blendDst);i.setDepthTest(h.depthTest);i.setDepthWrite(h.depthWrite);ti(h.polygonOffset,h.polygonOffsetFactor,h.polygonOffsetUnits)}i.setMaterialFaces(h);l instanceof THREE.BufferGeometry?i.renderBufferDirect(u,f,e,h,l,c):i.renderBuffer(u,f,e,h,l,c)}}function bt(n,t,r,u,f,e,o){for(var s,h,c=0,l=n.length;c<l;c++)if(s=n[c],h=s.object,h.visible){if(o)s=o;else{if(s=s[t],!s)continue;e&&i.setBlending(s.blending,s.blendEquation,s.blendSrc,s.blendDst);i.setDepthTest(s.depthTest);i.setDepthWrite(s.depthWrite);ti(s.polygonOffset,s.polygonOffsetFactor,s.polygonOffsetUnits)}i.renderImmediateObject(r,u,f,s,h)}}function kt(n,t,i){n.push({buffer:t,object:i,opaque:null,transparent:null})}function ot(n){for(var t in n.attributes)if(n.attributes[t].needsUpdate)return!0;return!1}function st(n){for(var t in n.attributes)n.attributes[t].needsUpdate=!1}function ir(n,t){for(var i=n.length-1;0<=i;i--)n[i].object===t&&n.splice(i,1)}function rr(n,t){for(var i=n.length-1;0<=i;i--)n[i]===t&&n.splice(i,1)}function dt(n,r,o,h,c){var v,ut;ii=0;h.needsUpdate&&(h.program&&eu(h),i.initMaterial(h,r,o,c),h.needsUpdate=!1);h.morphTargets&&!c.__webglMorphTargetInfluences&&(c.__webglMorphTargetInfluences=new Float32Array(i.maxMorphTargets));var b=!1,pt=h.program,lt=pt.uniforms,l=h.uniforms;if(pt!==g&&(t.useProgram(pt),g=pt,b=!0),h.id!==y&&(y=h.id,b=!0),(b||n!==nt)&&(t.uniformMatrix4fv(lt.projectionMatrix,!1,n.projectionMatrix.elements),n!==nt&&(nt=n)),h.skinning&&(pi&&c.useVertexTexture?null!==lt.boneTexture&&(v=gt(),t.uniform1i(lt.boneTexture,v),i.setTexture(c.boneTexture,v)):null!==lt.boneGlobalMatrices&&t.uniformMatrix4fv(lt.boneGlobalMatrices,!1,c.boneMatrices)),b){if(o&&h.fog&&(l.fogColor.value=o.color,o instanceof THREE.Fog?(l.fogNear.value=o.near,l.fogFar.value=o.far):o instanceof THREE.FogExp2&&(l.fogDensity.value=o.density)),h instanceof THREE.MeshPhongMaterial||h instanceof THREE.MeshLambertMaterial||h.lights){if(p){for(var tt,a=v=0,it=0,rt,et,at,w=pr,ft=w.directional.colors,vt=w.directional.positions,ct=w.point.colors,ot=w.point.positions,ri=w.point.distances,kt=w.spot.colors,ui=w.spot.positions,li=w.spot.distances,fi=w.spot.directions,ai=w.spot.anglesCos,vi=w.spot.exponents,dt=w.hemi.skyColors,ni=w.hemi.groundColors,ei=w.hemi.positions,ti=0,wt=0,yt=0,bt=0,si=0,hi=0,ci=0,oi=0,st=tt=0,o=at=st=0,b=r.length;o<b;o++)tt=r[o],tt.onlyShadow||(rt=tt.color,et=tt.intensity,at=tt.distance,tt instanceof THREE.AmbientLight?tt.visible&&(i.gammaInput?(v+=rt.r*rt.r,a+=rt.g*rt.g,it+=rt.b*rt.b):(v+=rt.r,a+=rt.g,it+=rt.b)):tt instanceof THREE.DirectionalLight?(si+=1,tt.visible&&(f.getPositionFromMatrix(tt.matrixWorld),u.getPositionFromMatrix(tt.target.matrixWorld),f.sub(u),f.normalize(),0===f.x&&0===f.y&&0===f.z||(tt=3*ti,vt[tt]=f.x,vt[tt+1]=f.y,vt[tt+2]=f.z,i.gammaInput?k(ft,tt,rt,et*et):d(ft,tt,rt,et),ti+=1))):tt instanceof THREE.PointLight?(hi+=1,tt.visible&&(st=3*wt,i.gammaInput?k(ct,st,rt,et*et):d(ct,st,rt,et),u.getPositionFromMatrix(tt.matrixWorld),ot[st]=u.x,ot[st+1]=u.y,ot[st+2]=u.z,ri[wt]=at,wt+=1)):tt instanceof THREE.SpotLight?(ci+=1,tt.visible&&(st=3*yt,i.gammaInput?k(kt,st,rt,et*et):d(kt,st,rt,et),u.getPositionFromMatrix(tt.matrixWorld),ui[st]=u.x,ui[st+1]=u.y,ui[st+2]=u.z,li[yt]=at,f.copy(u),u.getPositionFromMatrix(tt.target.matrixWorld),f.sub(u),f.normalize(),fi[st]=f.x,fi[st+1]=f.y,fi[st+2]=f.z,ai[yt]=Math.cos(tt.angle),vi[yt]=tt.exponent,yt+=1)):tt instanceof THREE.HemisphereLight&&(oi+=1,tt.visible&&(f.getPositionFromMatrix(tt.matrixWorld),f.normalize(),0===f.x&&0===f.y&&0===f.z||(at=3*bt,ei[at]=f.x,ei[at+1]=f.y,ei[at+2]=f.z,rt=tt.color,tt=tt.groundColor,i.gammaInput?(et*=et,k(dt,at,rt,et),k(ni,at,tt,et)):(d(dt,at,rt,et),d(ni,at,tt,et)),bt+=1))));for(o=3*ti,b=Math.max(ft.length,3*si);o<b;o++)ft[o]=0;for(o=3*wt,b=Math.max(ct.length,3*hi);o<b;o++)ct[o]=0;for(o=3*yt,b=Math.max(kt.length,3*ci);o<b;o++)kt[o]=0;for(o=3*bt,b=Math.max(dt.length,3*oi);o<b;o++)dt[o]=0;for(o=3*bt,b=Math.max(ni.length,3*oi);o<b;o++)ni[o]=0;w.directional.length=ti;w.point.length=wt;w.spot.length=yt;w.hemi.length=bt;w.ambient[0]=v;w.ambient[1]=a;w.ambient[2]=it;p=!1}o=pr;l.ambientLightColor.value=o.ambient;l.directionalLightColor.value=o.directional.colors;l.directionalLightDirection.value=o.directional.positions;l.pointLightColor.value=o.point.colors;l.pointLightPosition.value=o.point.positions;l.pointLightDistance.value=o.point.distances;l.spotLightColor.value=o.spot.colors;l.spotLightPosition.value=o.spot.positions;l.spotLightDistance.value=o.spot.distances;l.spotLightDirection.value=o.spot.directions;l.spotLightAngleCos.value=o.spot.anglesCos;l.spotLightExponent.value=o.spot.exponents;l.hemisphereLightSkyColor.value=o.hemi.skyColors;l.hemisphereLightGroundColor.value=o.hemi.groundColors;l.hemisphereLightDirection.value=o.hemi.positions}if((h instanceof THREE.MeshBasicMaterial||h instanceof THREE.MeshLambertMaterial||h instanceof THREE.MeshPhongMaterial)&&(l.opacity.value=h.opacity,i.gammaInput?l.diffuse.value.copyGammaToLinear(h.color):l.diffuse.value=h.color,l.map.value=h.map,l.lightMap.value=h.lightMap,l.specularMap.value=h.specularMap,h.bumpMap&&(l.bumpMap.value=h.bumpMap,l.bumpScale.value=h.bumpScale),h.normalMap&&(l.normalMap.value=h.normalMap,l.normalScale.value.copy(h.normalScale)),h.map?ut=h.map:h.specularMap?ut=h.specularMap:h.normalMap?ut=h.normalMap:h.bumpMap&&(ut=h.bumpMap),void 0!==ut&&(o=ut.offset,ut=ut.repeat,l.offsetRepeat.value.set(o.x,o.y,ut.x,ut.y)),l.envMap.value=h.envMap,l.flipEnvMap.value=h.envMap instanceof THREE.WebGLRenderTargetCube?1:-1,l.reflectivity.value=h.reflectivity,l.refractionRatio.value=h.refractionRatio,l.combine.value=h.combine,l.useRefract.value=h.envMap&&h.envMap.mapping instanceof THREE.CubeRefractionMapping),h instanceof THREE.LineBasicMaterial?(l.diffuse.value=h.color,l.opacity.value=h.opacity):h instanceof THREE.LineDashedMaterial?(l.diffuse.value=h.color,l.opacity.value=h.opacity,l.dashSize.value=h.dashSize,l.totalSize.value=h.dashSize+h.gapSize,l.scale.value=h.scale):h instanceof THREE.ParticleBasicMaterial?(l.psColor.value=h.color,l.opacity.value=h.opacity,l.size.value=h.size,l.scale.value=s.height/2,l.map.value=h.map):h instanceof THREE.MeshPhongMaterial?(l.shininess.value=h.shininess,i.gammaInput?(l.ambient.value.copyGammaToLinear(h.ambient),l.emissive.value.copyGammaToLinear(h.emissive),l.specular.value.copyGammaToLinear(h.specular)):(l.ambient.value=h.ambient,l.emissive.value=h.emissive,l.specular.value=h.specular),h.wrapAround&&l.wrapRGB.value.copy(h.wrapRGB)):h instanceof THREE.MeshLambertMaterial?(i.gammaInput?(l.ambient.value.copyGammaToLinear(h.ambient),l.emissive.value.copyGammaToLinear(h.emissive)):(l.ambient.value=h.ambient,l.emissive.value=h.emissive),h.wrapAround&&l.wrapRGB.value.copy(h.wrapRGB)):h instanceof THREE.MeshDepthMaterial?(l.mNear.value=n.near,l.mFar.value=n.far,l.opacity.value=h.opacity):h instanceof THREE.MeshNormalMaterial&&(l.opacity.value=h.opacity),c.receiveShadow&&!h._shadowPass&&l.shadowMatrix)for(o=ut=0,b=r.length;o<b;o++)(v=r[o],v.castShadow&&(v instanceof THREE.SpotLight||v instanceof THREE.DirectionalLight&&!v.shadowCascade))&&(l.shadowMap.value[ut]=v.shadowMap,l.shadowMapSize.value[ut]=v.shadowMapSize,l.shadowMatrix.value[ut]=v.shadowMatrix,l.shadowDarkness.value[ut]=v.shadowDarkness,l.shadowBias.value[ut]=v.shadowBias,ut++);for(r=h.uniformsList,l=0,ut=r.length;l<ut;l++)if(b=pt.uniforms[r[l][1]])if(o=r[l][0],a=o.type,v=o.value,"i"===a)t.uniform1i(b,v);else if("f"===a)t.uniform1f(b,v);else if("v2"===a)t.uniform2f(b,v.x,v.y);else if("v3"===a)t.uniform3f(b,v.x,v.y,v.z);else if("v4"===a)t.uniform4f(b,v.x,v.y,v.z,v.w);else if("c"===a)t.uniform3f(b,v.r,v.g,v.b);else if("iv1"===a)t.uniform1iv(b,v);else if("iv"===a)t.uniform3iv(b,v);else if("fv1"===a)t.uniform1fv(b,v);else if("fv"===a)t.uniform3fv(b,v);else if("v2v"===a){for(void 0===o._array&&(o._array=new Float32Array(2*v.length)),a=0,it=v.length;a<it;a++)w=2*a,o._array[w]=v[a].x,o._array[w+1]=v[a].y;t.uniform2fv(b,o._array)}else if("v3v"===a){for(void 0===o._array&&(o._array=new Float32Array(3*v.length)),a=0,it=v.length;a<it;a++)w=3*a,o._array[w]=v[a].x,o._array[w+1]=v[a].y,o._array[w+2]=v[a].z;t.uniform3fv(b,o._array)}else if("v4v"===a){for(void 0===o._array&&(o._array=new Float32Array(4*v.length)),a=0,it=v.length;a<it;a++)w=4*a,o._array[w]=v[a].x,o._array[w+1]=v[a].y,o._array[w+2]=v[a].z,o._array[w+3]=v[a].w;t.uniform4fv(b,o._array)}else if("m4"===a)void 0===o._array&&(o._array=new Float32Array(16)),v.flattenToArray(o._array),t.uniformMatrix4fv(b,!1,o._array);else if("m4v"===a){for(void 0===o._array&&(o._array=new Float32Array(16*v.length)),a=0,it=v.length;a<it;a++)v[a].flattenToArrayOffset(o._array,16*a);t.uniformMatrix4fv(b,!1,o._array)}else if("t"===a){if(w=v,v=gt(),t.uniform1i(b,v),w)if(w.image instanceof Array&&6===w.image.length){if(o=w,b=v,6===o.image.length)if(o.needsUpdate){for(o.image.__webglTextureCube||(o.image.__webglTextureCube=t.createTexture(),i.info.memory.textures++),t.activeTexture(t.TEXTURE0+b),t.bindTexture(t.TEXTURE_CUBE_MAP,o.image.__webglTextureCube),t.pixelStorei(t.UNPACK_FLIP_Y_WEBGL,o.flipY),b=o instanceof THREE.CompressedTexture,v=[],a=0;6>a;a++)i.autoScaleCubemaps&&!b?(it=v,w=a,ft=o.image[a],ct=bu,ft.width<=ct&&ft.height<=ct||(ot=Math.max(ft.width,ft.height),vt=Math.floor(ft.width*ct/ot),ct=Math.floor(ft.height*ct/ot),ot=document.createElement("canvas"),ot.width=vt,ot.height=ct,ot.getContext("2d").drawImage(ft,0,0,ft.width,ft.height,0,0,vt,ct),ft=ot),it[w]=ft):v[a]=o.image[a];for(a=v[0],it=0==(a.width&a.width-1)&&0==(a.height&a.height-1),w=e(o.format),ft=e(o.type),ht(t.TEXTURE_CUBE_MAP,o,it),a=0;6>a;a++)if(b)for(ct=v[a].mipmaps,ot=0,ri=ct.length;ot<ri;ot++)vt=ct[ot],t.compressedTexImage2D(t.TEXTURE_CUBE_MAP_POSITIVE_X+a,ot,w,vt.width,vt.height,0,vt.data);else t.texImage2D(t.TEXTURE_CUBE_MAP_POSITIVE_X+a,0,w,w,ft,v[a]);o.generateMipmaps&&it&&t.generateMipmap(t.TEXTURE_CUBE_MAP);o.needsUpdate=!1;o.onUpdate&&o.onUpdate()}else t.activeTexture(t.TEXTURE0+b),t.bindTexture(t.TEXTURE_CUBE_MAP,o.image.__webglTextureCube)}else w instanceof THREE.WebGLRenderTargetCube?(o=w,t.activeTexture(t.TEXTURE0+v),t.bindTexture(t.TEXTURE_CUBE_MAP,o.__webglTexture)):i.setTexture(w,v)}else if("tv"===a){for(void 0===o._array&&(o._array=[]),a=0,it=o.value.length;a<it;a++)o._array[a]=gt();for(t.uniform1iv(b,o._array),a=0,it=o.value.length;a<it;a++)w=o.value[a],v=o._array[a],w&&i.setTexture(w,v)}(h instanceof THREE.ShaderMaterial||h instanceof THREE.MeshPhongMaterial||h.envMap)&&null!==lt.cameraPosition&&(u.getPositionFromMatrix(n.matrixWorld),t.uniform3f(lt.cameraPosition,u.x,u.y,u.z));(h instanceof THREE.MeshPhongMaterial||h instanceof THREE.MeshLambertMaterial||h instanceof THREE.ShaderMaterial||h.skinning)&&null!==lt.viewMatrix&&t.uniformMatrix4fv(lt.viewMatrix,!1,n.matrixWorldInverse.elements)}return t.uniformMatrix4fv(lt.modelViewMatrix,!1,c._modelViewMatrix.elements),lt.normalMatrix&&t.uniformMatrix3fv(lt.normalMatrix,!1,c._normalMatrix.elements),null!==lt.modelMatrix&&t.uniformMatrix4fv(lt.modelMatrix,!1,c.matrixWorld.elements),pt}function gt(){var n=ii;return n>=vi&&console.warn("WebGLRenderer: trying to use "+n+" texture units while this GPU supports only "+vi),ii+=1,n}function ur(n,t){n._modelViewMatrix.multiplyMatrices(t.matrixWorldInverse,n.matrixWorld);n._normalMatrix.getInverse(n._modelViewMatrix);n._normalMatrix.transpose()}function k(n,t,i,r){n[t]=i.r*i.r*r;n[t+1]=i.g*i.g*r;n[t+2]=i.b*i.b*r}function d(n,t,i,r){n[t]=i.r*r;n[t+1]=i.g*r;n[t+2]=i.b*r}function ni(n){n!==ar&&(t.lineWidth(n),ar=n)}function ti(n,i,r){hr!==n&&(n?t.enable(t.POLYGON_OFFSET_FILL):t.disable(t.POLYGON_OFFSET_FILL),hr=n);n&&(cr!==i||lr!==r)&&(t.polygonOffset(i,r),cr=i,lr=r)}function su(n){for(var n=n.split("\n"),t=0,i=n.length;t<i;t++)n[t]=t+1+": "+n[t];return n.join("\n")}function fr(n,i){var r;return"fragment"===n?r=t.createShader(t.FRAGMENT_SHADER):"vertex"===n&&(r=t.createShader(t.VERTEX_SHADER)),t.shaderSource(r,i),t.compileShader(r),t.getShaderParameter(r,t.COMPILE_STATUS)?r:(console.error(t.getShaderInfoLog(r)),console.error(su(i)),null)}function ht(n,i,r){r?(t.texParameteri(n,t.TEXTURE_WRAP_S,e(i.wrapS)),t.texParameteri(n,t.TEXTURE_WRAP_T,e(i.wrapT)),t.texParameteri(n,t.TEXTURE_MAG_FILTER,e(i.magFilter)),t.texParameteri(n,t.TEXTURE_MIN_FILTER,e(i.minFilter))):(t.texParameteri(n,t.TEXTURE_WRAP_S,t.CLAMP_TO_EDGE),t.texParameteri(n,t.TEXTURE_WRAP_T,t.CLAMP_TO_EDGE),t.texParameteri(n,t.TEXTURE_MAG_FILTER,or(i.magFilter)),t.texParameteri(n,t.TEXTURE_MIN_FILTER,or(i.minFilter)));w&&i.type!==THREE.FloatType&&(1<i.anisotropy||i.__oldAnisotropy)&&(t.texParameterf(n,w.TEXTURE_MAX_ANISOTROPY_EXT,Math.min(i.anisotropy,br)),i.__oldAnisotropy=i.anisotropy)}function er(n,i){t.bindRenderbuffer(t.RENDERBUFFER,n);i.depthBuffer&&!i.stencilBuffer?(t.renderbufferStorage(t.RENDERBUFFER,t.DEPTH_COMPONENT16,i.width,i.height),t.framebufferRenderbuffer(t.FRAMEBUFFER,t.DEPTH_ATTACHMENT,t.RENDERBUFFER,n)):i.depthBuffer&&i.stencilBuffer?(t.renderbufferStorage(t.RENDERBUFFER,t.DEPTH_STENCIL,i.width,i.height),t.framebufferRenderbuffer(t.FRAMEBUFFER,t.DEPTH_STENCIL_ATTACHMENT,t.RENDERBUFFER,n)):t.renderbufferStorage(t.RENDERBUFFER,t.RGBA4,i.width,i.height)}function or(n){return n===THREE.NearestFilter||n===THREE.NearestMipMapNearestFilter||n===THREE.NearestMipMapLinearFilter?t.NEAREST:t.LINEAR}function e(n){if(n===THREE.RepeatWrapping)return t.REPEAT;if(n===THREE.ClampToEdgeWrapping)return t.CLAMP_TO_EDGE;if(n===THREE.MirroredRepeatWrapping)return t.MIRRORED_REPEAT;if(n===THREE.NearestFilter)return t.NEAREST;if(n===THREE.NearestMipMapNearestFilter)return t.NEAREST_MIPMAP_NEAREST;if(n===THREE.NearestMipMapLinearFilter)return t.NEAREST_MIPMAP_LINEAR;if(n===THREE.LinearFilter)return t.LINEAR;if(n===THREE.LinearMipMapNearestFilter)return t.LINEAR_MIPMAP_NEAREST;if(n===THREE.LinearMipMapLinearFilter)return t.LINEAR_MIPMAP_LINEAR;if(n===THREE.UnsignedByteType)return t.UNSIGNED_BYTE;if(n===THREE.UnsignedShort4444Type)return t.UNSIGNED_SHORT_4_4_4_4;if(n===THREE.UnsignedShort5551Type)return t.UNSIGNED_SHORT_5_5_5_1;if(n===THREE.UnsignedShort565Type)return t.UNSIGNED_SHORT_5_6_5;if(n===THREE.ByteType)return t.BYTE;if(n===THREE.ShortType)return t.SHORT;if(n===THREE.UnsignedShortType)return t.UNSIGNED_SHORT;if(n===THREE.IntType)return t.INT;if(n===THREE.UnsignedIntType)return t.UNSIGNED_INT;if(n===THREE.FloatType)return t.FLOAT;if(n===THREE.AlphaFormat)return t.ALPHA;if(n===THREE.RGBFormat)return t.RGB;if(n===THREE.RGBAFormat)return t.RGBA;if(n===THREE.LuminanceFormat)return t.LUMINANCE;if(n===THREE.LuminanceAlphaFormat)return t.LUMINANCE_ALPHA;if(n===THREE.AddEquation)return t.FUNC_ADD;if(n===THREE.SubtractEquation)return t.FUNC_SUBTRACT;if(n===THREE.ReverseSubtractEquation)return t.FUNC_REVERSE_SUBTRACT;if(n===THREE.ZeroFactor)return t.ZERO;if(n===THREE.OneFactor)return t.ONE;if(n===THREE.SrcColorFactor)return t.SRC_COLOR;if(n===THREE.OneMinusSrcColorFactor)return t.ONE_MINUS_SRC_COLOR;if(n===THREE.SrcAlphaFactor)return t.SRC_ALPHA;if(n===THREE.OneMinusSrcAlphaFactor)return t.ONE_MINUS_SRC_ALPHA;if(n===THREE.DstAlphaFactor)return t.DST_ALPHA;if(n===THREE.OneMinusDstAlphaFactor)return t.ONE_MINUS_DST_ALPHA;if(n===THREE.DstColorFactor)return t.DST_COLOR;if(n===THREE.OneMinusDstColorFactor)return t.ONE_MINUS_DST_COLOR;if(n===THREE.SrcAlphaSaturateFactor)return t.SRC_ALPHA_SATURATE;if(void 0!==c){if(n===THREE.RGB_S3TC_DXT1_Format)return c.COMPRESSED_RGB_S3TC_DXT1_EXT;if(n===THREE.RGBA_S3TC_DXT1_Format)return c.COMPRESSED_RGBA_S3TC_DXT1_EXT;if(n===THREE.RGBA_S3TC_DXT3_Format)return c.COMPRESSED_RGBA_S3TC_DXT3_EXT;if(n===THREE.RGBA_S3TC_DXT5_Format)return c.COMPRESSED_RGBA_S3TC_DXT5_EXT}return 0}var vi,wr,kr,dr,gr,nu,tu,wi;console.log("THREE.WebGLRenderer",THREE.REVISION);var n=n||{},s=void 0!==n.canvas?n.canvas:document.createElement("canvas"),l=void 0!==n.precision?n.precision:"highp",hu=void 0!==n.alpha?n.alpha:!0,cu=void 0!==n.premultipliedAlpha?n.premultipliedAlpha:!0,lu=void 0!==n.antialias?n.antialias:!1,au=void 0!==n.stencil?n.stencil:!0,vu=void 0!==n.preserveDrawingBuffer?n.preserveDrawingBuffer:!1,o=void 0!==n.clearColor?new THREE.Color(n.clearColor):new THREE.Color(0),v=void 0!==n.clearAlpha?n.clearAlpha:0;this.domElement=s;this.context=null;this.devicePixelRatio=void 0!==n.devicePixelRatio?n.devicePixelRatio:void 0!==window.devicePixelRatio?window.devicePixelRatio:1;this.autoUpdateScene=this.autoUpdateObjects=this.sortObjects=this.autoClearStencil=this.autoClearDepth=this.autoClearColor=this.autoClear=!0;this.shadowMapEnabled=this.physicallyBasedShading=this.gammaOutput=this.gammaInput=!1;this.shadowMapAutoUpdate=!0;this.shadowMapType=THREE.PCFShadowMap;this.shadowMapCullFace=THREE.CullFaceFront;this.shadowMapCascade=this.shadowMapDebug=!1;this.maxMorphTargets=8;this.maxMorphNormals=4;this.autoScaleCubemaps=!0;this.renderPluginsPre=[];this.renderPluginsPost=[];this.info={memory:{programs:0,geometries:0,textures:0},render:{calls:0,vertices:0,faces:0,points:0}};var i=this,h=[],yu=0,g=null,sr=null,y=-1,a=null,nt=null,pu=0,ii=0,tt=-1,it=-1,rt=-1,ri=-1,ui=-1,fi=-1,ut=-1,ft=-1,hr=null,cr=null,lr=null,ar=null,ei=0,oi=0,si=0,hi=0,vr=0,yr=0,et={},ci=new THREE.Frustum,ct=new THREE.Matrix4,li=new THREE.Matrix4,u=new THREE.Vector3,f=new THREE.Vector3,p=!0,pr={ambient:[0,0,0],directional:{length:0,colors:[],positions:[]},point:{length:0,colors:[],positions:[],distances:[]},spot:{length:0,colors:[],positions:[],distances:[],directions:[],anglesCos:[],exponents:[]},hemi:{length:0,skyColors:[],groundColors:[],positions:[]}},t,lt,ai,w,c;try{if(!(t=s.getContext("experimental-webgl",{alpha:hu,premultipliedAlpha:cu,antialias:lu,stencil:au,preserveDrawingBuffer:vu})))throw"Error creating WebGL context.";}catch(wu){console.error(wu)}lt=t.getExtension("OES_texture_float");ai=t.getExtension("OES_standard_derivatives");w=t.getExtension("EXT_texture_filter_anisotropic")||t.getExtension("MOZ_EXT_texture_filter_anisotropic")||t.getExtension("WEBKIT_EXT_texture_filter_anisotropic");c=t.getExtension("WEBGL_compressed_texture_s3tc")||t.getExtension("MOZ_WEBGL_compressed_texture_s3tc")||t.getExtension("WEBKIT_WEBGL_compressed_texture_s3tc");lt||console.log("THREE.WebGLRenderer: Float textures not supported.");ai||console.log("THREE.WebGLRenderer: Standard derivatives not supported.");w||console.log("THREE.WebGLRenderer: Anisotropic texture filtering not supported.");c||console.log("THREE.WebGLRenderer: S3TC compressed textures not supported.");void 0===t.getShaderPrecisionFormat&&(t.getShaderPrecisionFormat=function(){return{rangeMin:1,rangeMax:1,precision:1}});t.clearColor(0,0,0,1);t.clearDepth(1);t.clearStencil(0);t.enable(t.DEPTH_TEST);t.depthFunc(t.LEQUAL);t.frontFace(t.CCW);t.cullFace(t.BACK);t.enable(t.CULL_FACE);t.enable(t.BLEND);t.blendEquation(t.FUNC_ADD);t.blendFunc(t.SRC_ALPHA,t.ONE_MINUS_SRC_ALPHA);t.clearColor(o.r,o.g,o.b,v);this.context=t;vi=t.getParameter(t.MAX_TEXTURE_IMAGE_UNITS);wr=t.getParameter(t.MAX_VERTEX_TEXTURE_IMAGE_UNITS);t.getParameter(t.MAX_TEXTURE_SIZE);var bu=t.getParameter(t.MAX_CUBE_MAP_TEXTURE_SIZE),br=w?t.getParameter(w.MAX_TEXTURE_MAX_ANISOTROPY_EXT):0,yi=0<wr,pi=yi&&lt;c&&t.getParameter(t.COMPRESSED_TEXTURE_FORMATS);kr=t.getShaderPrecisionFormat(t.VERTEX_SHADER,t.HIGH_FLOAT);dr=t.getShaderPrecisionFormat(t.VERTEX_SHADER,t.MEDIUM_FLOAT);t.getShaderPrecisionFormat(t.VERTEX_SHADER,t.LOW_FLOAT);gr=t.getShaderPrecisionFormat(t.FRAGMENT_SHADER,t.HIGH_FLOAT);nu=t.getShaderPrecisionFormat(t.FRAGMENT_SHADER,t.MEDIUM_FLOAT);t.getShaderPrecisionFormat(t.FRAGMENT_SHADER,t.LOW_FLOAT);t.getShaderPrecisionFormat(t.VERTEX_SHADER,t.HIGH_INT);t.getShaderPrecisionFormat(t.VERTEX_SHADER,t.MEDIUM_INT);t.getShaderPrecisionFormat(t.VERTEX_SHADER,t.LOW_INT);t.getShaderPrecisionFormat(t.FRAGMENT_SHADER,t.HIGH_INT);t.getShaderPrecisionFormat(t.FRAGMENT_SHADER,t.MEDIUM_INT);t.getShaderPrecisionFormat(t.FRAGMENT_SHADER,t.LOW_INT);tu=0<kr.precision&&0<gr.precision;wi=0<dr.precision&&0<nu.precision;"highp"!==l||tu||(wi?(l="mediump",console.warn("WebGLRenderer: highp not supported, using mediump")):(l="lowp",console.warn("WebGLRenderer: highp and mediump not supported, using lowp")));"mediump"!==l||wi||(l="lowp",console.warn("WebGLRenderer: mediump not supported, using lowp"));this.getContext=function(){return t};this.supportsVertexTextures=function(){return yi};this.supportsFloatTextures=function(){return lt};this.supportsStandardDerivatives=function(){return ai};this.supportsCompressedTextureS3TC=function(){return c};this.getMaxAnisotropy=function(){return br};this.getPrecision=function(){return l};this.setSize=function(n,t){s.width=n*this.devicePixelRatio;s.height=t*this.devicePixelRatio;s.style.width=n+"px";s.style.height=t+"px";this.setViewport(0,0,s.width,s.height)};this.setViewport=function(n,i,r,u){ei=void 0!==n?n:0;oi=void 0!==i?i:0;si=void 0!==r?r:s.width;hi=void 0!==u?u:s.height;t.viewport(ei,oi,si,hi)};this.setScissor=function(n,i,r,u){t.scissor(n,i,r,u)};this.enableScissorTest=function(n){n?t.enable(t.SCISSOR_TEST):t.disable(t.SCISSOR_TEST)};this.setClearColorHex=function(n,i){o.setHex(n);v=i;t.clearColor(o.r,o.g,o.b,v)};this.setClearColor=function(n,i){o.copy(n);v=i;t.clearColor(o.r,o.g,o.b,v)};this.getClearColor=function(){return o};this.getClearAlpha=function(){return v};this.clear=function(n,i,r){var u=0;(void 0===n||n)&&(u|=t.COLOR_BUFFER_BIT);(void 0===i||i)&&(u|=t.DEPTH_BUFFER_BIT);(void 0===r||r)&&(u|=t.STENCIL_BUFFER_BIT);t.clear(u)};this.clearTarget=function(n,t,i,r){this.setRenderTarget(n);this.clear(t,i,r)};this.addPostPlugin=function(n){n.init(this);this.renderPluginsPost.push(n)};this.addPrePlugin=function(n){n.init(this);this.renderPluginsPre.push(n)};this.updateShadowMap=function(n,t){g=null;y=a=ft=ut=rt=-1;p=!0;it=tt=-1;this.shadowMapPlugin.update(n,t)};var iu=function(n){var e,r,u,f;if(n=n.target,n.removeEventListener("dispose",iu),n.__webglInit=void 0,void 0!==n.__webglVertexBuffer&&t.deleteBuffer(n.__webglVertexBuffer),void 0!==n.__webglNormalBuffer&&t.deleteBuffer(n.__webglNormalBuffer),void 0!==n.__webglTangentBuffer&&t.deleteBuffer(n.__webglTangentBuffer),void 0!==n.__webglColorBuffer&&t.deleteBuffer(n.__webglColorBuffer),void 0!==n.__webglUVBuffer&&t.deleteBuffer(n.__webglUVBuffer),void 0!==n.__webglUV2Buffer&&t.deleteBuffer(n.__webglUV2Buffer),void 0!==n.__webglSkinIndicesBuffer&&t.deleteBuffer(n.__webglSkinIndicesBuffer),void 0!==n.__webglSkinWeightsBuffer&&t.deleteBuffer(n.__webglSkinWeightsBuffer),void 0!==n.__webglFaceBuffer&&t.deleteBuffer(n.__webglFaceBuffer),void 0!==n.__webglLineBuffer&&t.deleteBuffer(n.__webglLineBuffer),void 0!==n.__webglLineDistanceBuffer&&t.deleteBuffer(n.__webglLineDistanceBuffer),void 0!==n.geometryGroups)for(e in n.geometryGroups){if(r=n.geometryGroups[e],void 0!==r.numMorphTargets)for(u=0,f=r.numMorphTargets;u<f;u++)t.deleteBuffer(r.__webglMorphTargetsBuffers[u]);if(void 0!==r.numMorphNormals)for(u=0,f=r.numMorphNormals;u<f;u++)t.deleteBuffer(r.__webglMorphNormalsBuffers[u]);bi(r)}bi(n);i.info.memory.geometries--},ru=function(n){n=n.target;n.removeEventListener("dispose",ru);n.image&&n.image.__webglTextureCube?t.deleteTexture(n.image.__webglTextureCube):n.__webglInit&&(n.__webglInit=!1,t.deleteTexture(n.__webglTexture));i.info.memory.textures--},uu=function(n){if(n=n.target,n.removeEventListener("dispose",uu),n&&n.__webglTexture)if(t.deleteTexture(n.__webglTexture),n instanceof THREE.WebGLRenderTargetCube)for(var r=0;6>r;r++)t.deleteFramebuffer(n.__webglFramebuffer[r]),t.deleteRenderbuffer(n.__webglRenderbuffer[r]);else t.deleteFramebuffer(n.__webglFramebuffer),t.deleteRenderbuffer(n.__webglRenderbuffer);i.info.memory.textures--},fu=function(n){n=n.target;n.removeEventListener("dispose",fu);eu(n)},eu=function(n){var f=n.program,e,r,u,n;if(void 0!==f){for(n.program=void 0,u=!1,n=0,e=h.length;n<e;n++)if(r=h[n],r.program===f){r.usedTimes--;0===r.usedTimes&&(u=!0);break}if(!0===u){for(u=[],n=0,e=h.length;n<e;n++)r=h[n],r.program!==f&&u.push(r);h=u;t.deleteProgram(f);i.info.memory.programs--}}};this.renderBufferImmediate=function(n,i,r){if(n.hasPositions&&!n.__webglVertexBuffer&&(n.__webglVertexBuffer=t.createBuffer()),n.hasNormals&&!n.__webglNormalBuffer&&(n.__webglNormalBuffer=t.createBuffer()),n.hasUvs&&!n.__webglUvBuffer&&(n.__webglUvBuffer=t.createBuffer()),n.hasColors&&!n.__webglColorBuffer&&(n.__webglColorBuffer=t.createBuffer()),n.hasPositions&&(t.bindBuffer(t.ARRAY_BUFFER,n.__webglVertexBuffer),t.bufferData(t.ARRAY_BUFFER,n.positionArray,t.DYNAMIC_DRAW),t.enableVertexAttribArray(i.attributes.position),t.vertexAttribPointer(i.attributes.position,3,t.FLOAT,!1,0,0)),n.hasNormals){if(t.bindBuffer(t.ARRAY_BUFFER,n.__webglNormalBuffer),r.shading===THREE.FlatShading)for(var e,o,s,h,c,l,a,v,y,f,p=3*n.count,u=0;u<p;u+=9)f=n.normalArray,e=f[u],o=f[u+1],s=f[u+2],h=f[u+3],l=f[u+4],v=f[u+5],c=f[u+6],a=f[u+7],y=f[u+8],e=(e+h+c)/3,o=(o+l+a)/3,s=(s+v+y)/3,f[u]=e,f[u+1]=o,f[u+2]=s,f[u+3]=e,f[u+4]=o,f[u+5]=s,f[u+6]=e,f[u+7]=o,f[u+8]=s;t.bufferData(t.ARRAY_BUFFER,n.normalArray,t.DYNAMIC_DRAW);t.enableVertexAttribArray(i.attributes.normal);t.vertexAttribPointer(i.attributes.normal,3,t.FLOAT,!1,0,0)}n.hasUvs&&r.map&&(t.bindBuffer(t.ARRAY_BUFFER,n.__webglUvBuffer),t.bufferData(t.ARRAY_BUFFER,n.uvArray,t.DYNAMIC_DRAW),t.enableVertexAttribArray(i.attributes.uv),t.vertexAttribPointer(i.attributes.uv,2,t.FLOAT,!1,0,0));n.hasColors&&r.vertexColors!==THREE.NoColors&&(t.bindBuffer(t.ARRAY_BUFFER,n.__webglColorBuffer),t.bufferData(t.ARRAY_BUFFER,n.colorArray,t.DYNAMIC_DRAW),t.enableVertexAttribArray(i.attributes.color),t.vertexAttribPointer(i.attributes.color,3,t.FLOAT,!1,0,0));t.drawArrays(t.TRIANGLES,0,n.count);n.count=0};this.renderBufferDirect=function(n,u,f,e,o,s){var f,y,v,l,h,c;if(!1!==e.visible)if(f=dt(n,u,f,e,s),n=f.attributes,u=!1,f=16777215*o.id+2*f.id+(e.wireframe?1:0),f!==a&&(a=f,u=!0),u&&nr(),s instanceof THREE.Mesh)if(e=o.attributes.index)for(s=o.offsets,1<s.length&&(u=!0),f=0,y=s.length;f<y;f++)v=s[f].index,u&&(l=o.attributes.position,h=l.itemSize,t.bindBuffer(t.ARRAY_BUFFER,l.buffer),r(n.position),t.vertexAttribPointer(n.position,h,t.FLOAT,!1,0,4*v*h),h=o.attributes.normal,0<=n.normal&&h&&(c=h.itemSize,t.bindBuffer(t.ARRAY_BUFFER,h.buffer),r(n.normal),t.vertexAttribPointer(n.normal,c,t.FLOAT,!1,0,4*v*c)),h=o.attributes.uv,0<=n.uv&&h&&(c=h.itemSize,t.bindBuffer(t.ARRAY_BUFFER,h.buffer),r(n.uv),t.vertexAttribPointer(n.uv,c,t.FLOAT,!1,0,4*v*c)),h=o.attributes.color,0<=n.color&&h&&(c=h.itemSize,t.bindBuffer(t.ARRAY_BUFFER,h.buffer),r(n.color),t.vertexAttribPointer(n.color,c,t.FLOAT,!1,0,4*v*c)),h=o.attributes.tangent,0<=n.tangent&&h&&(c=h.itemSize,t.bindBuffer(t.ARRAY_BUFFER,h.buffer),r(n.tangent),t.vertexAttribPointer(n.tangent,c,t.FLOAT,!1,0,4*v*c)),t.bindBuffer(t.ELEMENT_ARRAY_BUFFER,e.buffer)),t.drawElements(t.TRIANGLES,s[f].count,t.UNSIGNED_SHORT,2*s[f].start),i.info.render.calls++,i.info.render.vertices+=s[f].count,i.info.render.faces+=s[f].count/3;else u&&(l=o.attributes.position,h=l.itemSize,t.bindBuffer(t.ARRAY_BUFFER,l.buffer),r(n.position),t.vertexAttribPointer(n.position,h,t.FLOAT,!1,0,0),h=o.attributes.normal,0<=n.normal&&h&&(c=h.itemSize,t.bindBuffer(t.ARRAY_BUFFER,h.buffer),r(n.normal),t.vertexAttribPointer(n.normal,c,t.FLOAT,!1,0,0)),h=o.attributes.uv,0<=n.uv&&h&&(c=h.itemSize,t.bindBuffer(t.ARRAY_BUFFER,h.buffer),r(n.uv),t.vertexAttribPointer(n.uv,c,t.FLOAT,!1,0,0)),h=o.attributes.color,0<=n.color&&h&&(c=h.itemSize,t.bindBuffer(t.ARRAY_BUFFER,h.buffer),r(n.color),t.vertexAttribPointer(n.color,c,t.FLOAT,!1,0,0)),h=o.attributes.tangent,0<=n.tangent&&h&&(c=h.itemSize,t.bindBuffer(t.ARRAY_BUFFER,h.buffer),r(n.tangent),t.vertexAttribPointer(n.tangent,c,t.FLOAT,!1,0,0))),t.drawArrays(t.TRIANGLES,0,l.numItems/3),i.info.render.calls++,i.info.render.vertices+=l.numItems/3,i.info.render.faces+=l.numItems/9;else s instanceof THREE.ParticleSystem?u&&(l=o.attributes.position,h=l.itemSize,t.bindBuffer(t.ARRAY_BUFFER,l.buffer),r(n.position),t.vertexAttribPointer(n.position,h,t.FLOAT,!1,0,0),h=o.attributes.color,0<=n.color&&h&&(c=h.itemSize,t.bindBuffer(t.ARRAY_BUFFER,h.buffer),r(n.color),t.vertexAttribPointer(n.color,c,t.FLOAT,!1,0,0)),t.drawArrays(t.POINTS,0,l.numItems/3),i.info.render.calls++,i.info.render.points+=l.numItems/3):s instanceof THREE.Line&&u&&(l=o.attributes.position,h=l.itemSize,t.bindBuffer(t.ARRAY_BUFFER,l.buffer),r(n.position),t.vertexAttribPointer(n.position,h,t.FLOAT,!1,0,0),h=o.attributes.color,0<=n.color&&h&&(c=h.itemSize,t.bindBuffer(t.ARRAY_BUFFER,h.buffer),r(n.color),t.vertexAttribPointer(n.color,c,t.FLOAT,!1,0,0)),ni(e.linewidth),t.drawArrays(t.LINE_STRIP,0,l.numItems/3),i.info.render.calls++,i.info.render.points+=l.numItems)};this.renderBuffer=function(n,u,f,e,o,s){var h,v,y;if(!1!==e.visible){var l,c,f=dt(n,u,f,e,s),n=f.attributes,u=!1,f=16777215*o.id+2*f.id+(e.wireframe?1:0);if(f!==a&&(a=f,u=!0),u&&nr(),!e.morphTargets&&0<=n.position)u&&(t.bindBuffer(t.ARRAY_BUFFER,o.__webglVertexBuffer),r(n.position),t.vertexAttribPointer(n.position,3,t.FLOAT,!1,0,0));else if(s.morphTargetBase){if(f=e.program.attributes,-1!==s.morphTargetBase&&0<=f.position?(t.bindBuffer(t.ARRAY_BUFFER,o.__webglMorphTargetsBuffers[s.morphTargetBase]),r(f.position),t.vertexAttribPointer(f.position,3,t.FLOAT,!1,0,0)):0<=f.position&&(t.bindBuffer(t.ARRAY_BUFFER,o.__webglVertexBuffer),r(f.position),t.vertexAttribPointer(f.position,3,t.FLOAT,!1,0,0)),s.morphTargetForcedOrder.length)for(h=0,c=s.morphTargetForcedOrder,l=s.morphTargetInfluences;h<e.numSupportedMorphTargets&&h<c.length;)0<=f["morphTarget"+h]&&(t.bindBuffer(t.ARRAY_BUFFER,o.__webglMorphTargetsBuffers[c[h]]),r(f["morphTarget"+h]),t.vertexAttribPointer(f["morphTarget"+h],3,t.FLOAT,!1,0,0)),0<=f["morphNormal"+h]&&e.morphNormals&&(t.bindBuffer(t.ARRAY_BUFFER,o.__webglMorphNormalsBuffers[c[h]]),r(f["morphNormal"+h]),t.vertexAttribPointer(f["morphNormal"+h],3,t.FLOAT,!1,0,0)),s.__webglMorphTargetInfluences[h]=l[c[h]],h++;else{for(c=[],l=s.morphTargetInfluences,y=l.length,v=0;v<y;v++)h=l[v],0<h&&c.push([h,v]);for(c.length>e.numSupportedMorphTargets?(c.sort(pt),c.length=e.numSupportedMorphTargets):c.length>e.numSupportedMorphNormals?c.sort(pt):0===c.length&&c.push([0,0]),h=0;h<e.numSupportedMorphTargets;)c[h]?(v=c[h][1],0<=f["morphTarget"+h]&&(t.bindBuffer(t.ARRAY_BUFFER,o.__webglMorphTargetsBuffers[v]),r(f["morphTarget"+h]),t.vertexAttribPointer(f["morphTarget"+h],3,t.FLOAT,!1,0,0)),0<=f["morphNormal"+h]&&e.morphNormals&&(t.bindBuffer(t.ARRAY_BUFFER,o.__webglMorphNormalsBuffers[v]),r(f["morphNormal"+h]),t.vertexAttribPointer(f["morphNormal"+h],3,t.FLOAT,!1,0,0)),s.__webglMorphTargetInfluences[h]=l[v]):s.__webglMorphTargetInfluences[h]=0,h++}null!==e.program.uniforms.morphTargetInfluences&&t.uniform1fv(e.program.uniforms.morphTargetInfluences,s.__webglMorphTargetInfluences)}if(u){if(o.__webglCustomAttributesList)for(l=0,c=o.__webglCustomAttributesList.length;l<c;l++)f=o.__webglCustomAttributesList[l],0<=n[f.buffer.belongsToAttribute]&&(t.bindBuffer(t.ARRAY_BUFFER,f.buffer),r(n[f.buffer.belongsToAttribute]),t.vertexAttribPointer(n[f.buffer.belongsToAttribute],f.size,t.FLOAT,!1,0,0));0<=n.color&&(t.bindBuffer(t.ARRAY_BUFFER,o.__webglColorBuffer),r(n.color),t.vertexAttribPointer(n.color,3,t.FLOAT,!1,0,0));0<=n.normal&&(t.bindBuffer(t.ARRAY_BUFFER,o.__webglNormalBuffer),r(n.normal),t.vertexAttribPointer(n.normal,3,t.FLOAT,!1,0,0));0<=n.tangent&&(t.bindBuffer(t.ARRAY_BUFFER,o.__webglTangentBuffer),r(n.tangent),t.vertexAttribPointer(n.tangent,4,t.FLOAT,!1,0,0));0<=n.uv&&(t.bindBuffer(t.ARRAY_BUFFER,o.__webglUVBuffer),r(n.uv),t.vertexAttribPointer(n.uv,2,t.FLOAT,!1,0,0));0<=n.uv2&&(t.bindBuffer(t.ARRAY_BUFFER,o.__webglUV2Buffer),r(n.uv2),t.vertexAttribPointer(n.uv2,2,t.FLOAT,!1,0,0));e.skinning&&0<=n.skinIndex&&0<=n.skinWeight&&(t.bindBuffer(t.ARRAY_BUFFER,o.__webglSkinIndicesBuffer),r(n.skinIndex),t.vertexAttribPointer(n.skinIndex,4,t.FLOAT,!1,0,0),t.bindBuffer(t.ARRAY_BUFFER,o.__webglSkinWeightsBuffer),r(n.skinWeight),t.vertexAttribPointer(n.skinWeight,4,t.FLOAT,!1,0,0));0<=n.lineDistance&&(t.bindBuffer(t.ARRAY_BUFFER,o.__webglLineDistanceBuffer),r(n.lineDistance),t.vertexAttribPointer(n.lineDistance,1,t.FLOAT,!1,0,0))}s instanceof THREE.Mesh?(e.wireframe?(ni(e.wireframeLinewidth),u&&t.bindBuffer(t.ELEMENT_ARRAY_BUFFER,o.__webglLineBuffer),t.drawElements(t.LINES,o.__webglLineCount,t.UNSIGNED_SHORT,0)):(u&&t.bindBuffer(t.ELEMENT_ARRAY_BUFFER,o.__webglFaceBuffer),t.drawElements(t.TRIANGLES,o.__webglFaceCount,t.UNSIGNED_SHORT,0)),i.info.render.calls++,i.info.render.vertices+=o.__webglFaceCount,i.info.render.faces+=o.__webglFaceCount/3):s instanceof THREE.Line?(s=s.type===THREE.LineStrip?t.LINE_STRIP:t.LINES,ni(e.linewidth),t.drawArrays(s,0,o.__webglLineCount),i.info.render.calls++):s instanceof THREE.ParticleSystem?(t.drawArrays(t.POINTS,0,o.__webglParticleCount),i.info.render.calls++,i.info.render.points+=o.__webglParticleCount):s instanceof THREE.Ribbon&&(t.drawArrays(t.TRIANGLE_STRIP,0,o.__webglVertexCount),i.info.render.calls++)}};this.render=function(n,r,f,e){var w,s,o,l,a,v;if(!1==r instanceof THREE.Camera)console.error("THREE.WebGLRenderer.render: camera is not an instance of THREE.Camera.");else{for(a=n.__lights,v=n.fog,y=-1,p=!0,this.autoUpdateScene&&n.updateMatrixWorld(),void 0===r.parent&&r.updateMatrixWorld(),r.matrixWorldInverse.getInverse(r.matrixWorld),ct.multiplyMatrices(r.projectionMatrix,r.matrixWorldInverse),ci.setFromMatrix(ct),this.autoUpdateObjects&&this.initWebGLObjects(n),tr(this.renderPluginsPre,n,r),i.info.render.calls=0,i.info.render.vertices=0,i.info.render.faces=0,i.info.render.points=0,this.setRenderTarget(f),(this.autoClear||e)&&this.clear(this.autoClearColor,this.autoClearDepth,this.autoClearStencil),l=n.__webglObjects,e=0,w=l.length;e<w;e++)if(s=l[e],o=s.object,s.render=!1,o.visible&&(!(o instanceof THREE.Mesh||o instanceof THREE.ParticleSystem)||!o.frustumCulled||ci.intersectsObject(o))){ur(o,r);var c=s,k=c.buffer,h=void 0,b=h=void 0,b=c.object.material;b instanceof THREE.MeshFaceMaterial?(h=k.materialIndex,h=b.materials[h],h.transparent?(c.transparent=h,c.opaque=null):(c.opaque=h,c.transparent=null)):(h=b)&&(h.transparent?(c.transparent=h,c.opaque=null):(c.opaque=h,c.transparent=null));s.render=!0;!0===this.sortObjects&&(null!==o.renderDepth?s.z=o.renderDepth:(u.getPositionFromMatrix(o.matrixWorld),u.applyProjection(ct),s.z=u.z),s.id=o.id)}for(this.sortObjects&&l.sort(ou),l=n.__webglObjectsImmediate,e=0,w=l.length;e<w;e++)s=l[e],o=s.object,o.visible&&(ur(o,r),o=s.object.material,o.transparent?(s.transparent=o,s.opaque=null):(s.opaque=o,s.transparent=null));n.overrideMaterial?(e=n.overrideMaterial,this.setBlending(e.blending,e.blendEquation,e.blendSrc,e.blendDst),this.setDepthTest(e.depthTest),this.setDepthWrite(e.depthWrite),ti(e.polygonOffset,e.polygonOffsetFactor,e.polygonOffsetUnits),wt(n.__webglObjects,!1,"",r,a,v,!0,e),bt(n.__webglObjectsImmediate,"",r,a,v,!1,e)):(e=null,this.setBlending(THREE.NoBlending),wt(n.__webglObjects,!0,"opaque",r,a,v,!1,e),bt(n.__webglObjectsImmediate,"opaque",r,a,v,!1,e),wt(n.__webglObjects,!1,"transparent",r,a,v,!0,e),bt(n.__webglObjectsImmediate,"transparent",r,a,v,!0,e));tr(this.renderPluginsPost,n,r);f&&f.generateMipmaps&&f.minFilter!==THREE.NearestFilter&&f.minFilter!==THREE.LinearFilter&&(f instanceof THREE.WebGLRenderTargetCube?(t.bindTexture(t.TEXTURE_CUBE_MAP,f.__webglTexture),t.generateMipmap(t.TEXTURE_CUBE_MAP),t.bindTexture(t.TEXTURE_CUBE_MAP,null)):(t.bindTexture(t.TEXTURE_2D,f.__webglTexture),t.generateMipmap(t.TEXTURE_2D),t.bindTexture(t.TEXTURE_2D,null)));this.setDepthTest(!0);this.setDepthWrite(!0)}};this.renderImmediateObject=function(n,r,u,f,e){var o=dt(n,r,u,f,e);a=-1;i.setMaterialFaces(f);e.immediateRenderCallback?e.immediateRenderCallback(o,t,ci):e.render(function(n){i.renderBufferImmediate(n,o,f)})};this.initWebGLObjects=function(n){var vo,wi,pf,we,yo,wo,gs,hr,be,ko,wh,ns,bh,v;for(n.__webglObjects||(n.__webglObjects=[],n.__webglObjectsImmediate=[],n.__webglSprites=[],n.__webglFlares=[]);n.__objectsAdded.length;){var tt=n.__objectsAdded[0],ie=n,lo=void 0,k=void 0,ah=void 0,re=void 0;if(!tt.__webglInit)if(tt.__webglInit=!0,tt._modelViewMatrix=new THREE.Matrix4,tt._normalMatrix=new THREE.Matrix3,void 0!==tt.geometry&&void 0===tt.geometry.__webglInit&&(tt.geometry.__webglInit=!0,tt.geometry.addEventListener("dispose",iu)),tt instanceof THREE.Mesh)if(k=tt.geometry,ah=tt.material,k instanceof THREE.Geometry){if(void 0===k.geometryGroups){var bi=k,ue=void 0,vh=void 0,ao=void 0,br=void 0,ds=void 0,gr=void 0,yf={},yh=bi.morphTargets.length,ph=bi.morphNormals.length,yc=ah instanceof THREE.MeshFaceMaterial;for(bi.geometryGroups={},ue=0,vh=bi.faces.length;ue<vh;ue++)ao=bi.faces[ue],br=yc?ao.materialIndex:0,void 0===yf[br]&&(yf[br]={hash:br,counter:0}),gr=yf[br].hash+"_"+yf[br].counter,void 0===bi.geometryGroups[gr]&&(bi.geometryGroups[gr]={faces3:[],faces4:[],materialIndex:br,vertices:0,numMorphTargets:yh,numMorphNormals:ph}),ds=ao instanceof THREE.Face3?3:4,65535<bi.geometryGroups[gr].vertices+ds&&(yf[br].counter+=1,gr=yf[br].hash+"_"+yf[br].counter,void 0===bi.geometryGroups[gr]&&(bi.geometryGroups[gr]={faces3:[],faces4:[],materialIndex:br,vertices:0,numMorphTargets:yh,numMorphNormals:ph})),ao instanceof THREE.Face3?bi.geometryGroups[gr].faces3.push(ue):bi.geometryGroups[gr].faces4.push(ue),bi.geometryGroups[gr].vertices+=ds;bi.geometryGroupsList=[];vo=void 0;for(vo in bi.geometryGroups)bi.geometryGroups[vo].id=pu++,bi.geometryGroupsList.push(bi.geometryGroups[vo])}for(lo in k.geometryGroups)if(re=k.geometryGroups[lo],!re.__webglVertexBuffer){if(wi=re,wi.__webglVertexBuffer=t.createBuffer(),wi.__webglNormalBuffer=t.createBuffer(),wi.__webglTangentBuffer=t.createBuffer(),wi.__webglColorBuffer=t.createBuffer(),wi.__webglUVBuffer=t.createBuffer(),wi.__webglUV2Buffer=t.createBuffer(),wi.__webglSkinIndicesBuffer=t.createBuffer(),wi.__webglSkinWeightsBuffer=t.createBuffer(),wi.__webglFaceBuffer=t.createBuffer(),wi.__webglLineBuffer=t.createBuffer(),pf=void 0,we=void 0,wi.numMorphTargets)for(wi.__webglMorphTargetsBuffers=[],pf=0,we=wi.numMorphTargets;pf<we;pf++)wi.__webglMorphTargetsBuffers.push(t.createBuffer());if(wi.numMorphNormals)for(wi.__webglMorphNormalsBuffers=[],pf=0,we=wi.numMorphNormals;pf<we;pf++)wi.__webglMorphNormalsBuffers.push(t.createBuffer());i.info.memory.geometries++;ki(re,tt);k.verticesNeedUpdate=!0;k.morphTargetsNeedUpdate=!0;k.elementsNeedUpdate=!0;k.uvsNeedUpdate=!0;k.normalsNeedUpdate=!0;k.tangentsNeedUpdate=!0;k.colorsNeedUpdate=!0}}else k instanceof THREE.BufferGeometry&&vt(k);else if(tt instanceof THREE.Ribbon){if(k=tt.geometry,!k.__webglVertexBuffer){yo=k;yo.__webglVertexBuffer=t.createBuffer();yo.__webglColorBuffer=t.createBuffer();yo.__webglNormalBuffer=t.createBuffer();i.info.memory.geometries++;var fe=k,pc=tt,po=fe.vertices.length;fe.__vertexArray=new Float32Array(3*po);fe.__colorArray=new Float32Array(3*po);fe.__normalArray=new Float32Array(3*po);fe.__webglVertexCount=po;at(fe,pc);k.verticesNeedUpdate=!0;k.colorsNeedUpdate=!0;k.normalsNeedUpdate=!0}}else if(tt instanceof THREE.Line){if(k=tt.geometry,!k.__webglVertexBuffer)if(k instanceof THREE.Geometry){wo=k;wo.__webglVertexBuffer=t.createBuffer();wo.__webglColorBuffer=t.createBuffer();wo.__webglLineDistanceBuffer=t.createBuffer();i.info.memory.geometries++;var ee=k,wc=tt,bo=ee.vertices.length;ee.__vertexArray=new Float32Array(3*bo);ee.__colorArray=new Float32Array(3*bo);ee.__lineDistanceArray=new Float32Array(1*bo);ee.__webglLineCount=bo;at(ee,wc);k.verticesNeedUpdate=!0;k.colorsNeedUpdate=!0;k.lineDistancesNeedUpdate=!0}else k instanceof THREE.BufferGeometry&&vt(k)}else if(tt instanceof THREE.ParticleSystem&&(k=tt.geometry,!k.__webglVertexBuffer))if(k instanceof THREE.Geometry){gs=k;gs.__webglVertexBuffer=t.createBuffer();gs.__webglColorBuffer=t.createBuffer();i.info.memory.geometries++;var oe=k,bc=tt,nh=oe.vertices.length;oe.__vertexArray=new Float32Array(3*nh);oe.__colorArray=new Float32Array(3*nh);oe.__sortArray=[];oe.__webglParticleCount=nh;at(oe,bc);k.verticesNeedUpdate=!0;k.colorsNeedUpdate=!0}else k instanceof THREE.BufferGeometry&&vt(k);if(!tt.__webglActive){if(tt instanceof THREE.Mesh){if(k=tt.geometry,k instanceof THREE.BufferGeometry)kt(ie.__webglObjects,k,tt);else if(k instanceof THREE.Geometry)for(lo in k.geometryGroups)re=k.geometryGroups[lo],kt(ie.__webglObjects,re,tt)}else tt instanceof THREE.Ribbon||tt instanceof THREE.Line||tt instanceof THREE.ParticleSystem?(k=tt.geometry,kt(ie.__webglObjects,k,tt)):tt instanceof THREE.ImmediateRenderObject||tt.immediateRenderCallback?ie.__webglObjectsImmediate.push({object:tt,opaque:null,transparent:null}):tt instanceof THREE.Sprite?ie.__webglSprites.push(tt):tt instanceof THREE.LensFlare&&ie.__webglFlares.push(tt);tt.__webglActive=!0}n.__objectsAdded.splice(0,1)}for(;n.__objectsRemoved.length;)hr=n.__objectsRemoved[0],be=n,hr instanceof THREE.Mesh||hr instanceof THREE.ParticleSystem||hr instanceof THREE.Ribbon||hr instanceof THREE.Line?ir(be.__webglObjects,hr):hr instanceof THREE.Sprite?rr(be.__webglSprites,hr):hr instanceof THREE.LensFlare?rr(be.__webglFlares,hr):(hr instanceof THREE.ImmediateRenderObject||hr.immediateRenderCallback)&&ir(be.__webglObjectsImmediate,hr),hr.__webglActive=!1,n.__objectsRemoved.splice(0,1);for(ko=0,wh=n.__webglObjects.length;ko<wh;ko++){var cr=n.__webglObjects[ko].object,a=cr.geometry,go=void 0,ef=void 0,ai=void 0;if(cr instanceof THREE.Mesh)if(a instanceof THREE.BufferGeometry)(a.verticesNeedUpdate||a.elementsNeedUpdate||a.uvsNeedUpdate||a.normalsNeedUpdate||a.colorsNeedUpdate||a.tangentsNeedUpdate)&&yt(a,t.DYNAMIC_DRAW,!a.dynamic),a.verticesNeedUpdate=!1,a.elementsNeedUpdate=!1,a.uvsNeedUpdate=!1,a.normalsNeedUpdate=!1,a.colorsNeedUpdate=!1,a.tangentsNeedUpdate=!1;else{for(ns=0,bh=a.geometryGroupsList.length;ns<bh;ns++)if(go=a.geometryGroupsList[ns],ai=b(cr,go),a.buffersNeedUpdate&&ki(go,cr),ef=ai.attributes&&ot(ai),a.verticesNeedUpdate||a.morphTargetsNeedUpdate||a.elementsNeedUpdate||a.uvsNeedUpdate||a.normalsNeedUpdate||a.colorsNeedUpdate||a.tangentsNeedUpdate||ef){var it=go,kc=cr,lr=t.DYNAMIC_DRAW,dc=!a.dynamic,wf=ai;if(it.__inittedArrays){var kh=di(wf),th=wf.vertexColors?wf.vertexColors:!1,dh=gi(wf),ts=kh===THREE.SmoothShading,e=void 0,l=void 0,of=void 0,o=void 0,se=void 0,sf=void 0,kr=void 0,is=void 0,tf=void 0,he=void 0,ce=void 0,s=void 0,h=void 0,c=void 0,nt=void 0,nu=void 0,tu=void 0,ru=void 0,ke=void 0,uu=void 0,fu=void 0,eu=void 0,de=void 0,ou=void 0,su=void 0,hu=void 0,ge=void 0,cu=void 0,lu=void 0,au=void 0,no=void 0,vu=void 0,yu=void 0,wu=void 0,to=void 0,ft=void 0,gh=void 0,hf=void 0,le=void 0,ae=void 0,si=void 0,nc=void 0,fi=void 0,ei=void 0,cf=void 0,rf=void 0,ii=0,oi=0,bf=0,kf=0,dr=0,nr=0,lt=0,tr=0,ui=0,y=0,d=0,f=0,et=void 0,hi=it.__vertexArray,io=it.__uvArray,ro=it.__uv2Array,ar=it.__normalArray,dt=it.__tangentArray,ci=it.__colorArray,gt=it.__skinIndexArray,ni=it.__skinWeightArray,ih=it.__morphTargetsArrays,rh=it.__morphNormalsArrays,uh=it.__webglCustomAttributesList,r=void 0,bu=it.__faceArray,sr=it.__lineArray,ur=kc.geometry,gc=ur.elementsNeedUpdate,tc=ur.uvsNeedUpdate,nl=ur.normalsNeedUpdate,tl=ur.tangentsNeedUpdate,il=ur.colorsNeedUpdate,rl=ur.morphTargetsNeedUpdate,df=ur.vertices,rt=it.faces3,ut=it.faces4,pi=ur.faces,fh=ur.faceVertexUvs[0],eh=ur.faceVertexUvs[1],gf=ur.skinIndices,lf=ur.skinWeights,af=ur.morphTargets,rs=ur.morphNormals;if(ur.verticesNeedUpdate){for(e=0,l=rt.length;e<l;e++)o=pi[rt[e]],s=df[o.a],h=df[o.b],c=df[o.c],hi[oi]=s.x,hi[oi+1]=s.y,hi[oi+2]=s.z,hi[oi+3]=h.x,hi[oi+4]=h.y,hi[oi+5]=h.z,hi[oi+6]=c.x,hi[oi+7]=c.y,hi[oi+8]=c.z,oi+=9;for(e=0,l=ut.length;e<l;e++)o=pi[ut[e]],s=df[o.a],h=df[o.b],c=df[o.c],nt=df[o.d],hi[oi]=s.x,hi[oi+1]=s.y,hi[oi+2]=s.z,hi[oi+3]=h.x,hi[oi+4]=h.y,hi[oi+5]=h.z,hi[oi+6]=c.x,hi[oi+7]=c.y,hi[oi+8]=c.z,hi[oi+9]=nt.x,hi[oi+10]=nt.y,hi[oi+11]=nt.z,oi+=12;t.bindBuffer(t.ARRAY_BUFFER,it.__webglVertexBuffer);t.bufferData(t.ARRAY_BUFFER,hi,lr)}if(rl)for(si=0,nc=af.length;si<nc;si++){for(e=d=0,l=rt.length;e<l;e++)cf=rt[e],o=pi[cf],s=af[si].vertices[o.a],h=af[si].vertices[o.b],c=af[si].vertices[o.c],fi=ih[si],fi[d]=s.x,fi[d+1]=s.y,fi[d+2]=s.z,fi[d+3]=h.x,fi[d+4]=h.y,fi[d+5]=h.z,fi[d+6]=c.x,fi[d+7]=c.y,fi[d+8]=c.z,wf.morphNormals&&(ts?(rf=rs[si].vertexNormals[cf],uu=rf.a,fu=rf.b,eu=rf.c):eu=fu=uu=rs[si].faceNormals[cf],ei=rh[si],ei[d]=uu.x,ei[d+1]=uu.y,ei[d+2]=uu.z,ei[d+3]=fu.x,ei[d+4]=fu.y,ei[d+5]=fu.z,ei[d+6]=eu.x,ei[d+7]=eu.y,ei[d+8]=eu.z),d+=9;for(e=0,l=ut.length;e<l;e++)cf=ut[e],o=pi[cf],s=af[si].vertices[o.a],h=af[si].vertices[o.b],c=af[si].vertices[o.c],nt=af[si].vertices[o.d],fi=ih[si],fi[d]=s.x,fi[d+1]=s.y,fi[d+2]=s.z,fi[d+3]=h.x,fi[d+4]=h.y,fi[d+5]=h.z,fi[d+6]=c.x,fi[d+7]=c.y,fi[d+8]=c.z,fi[d+9]=nt.x,fi[d+10]=nt.y,fi[d+11]=nt.z,wf.morphNormals&&(ts?(rf=rs[si].vertexNormals[cf],uu=rf.a,fu=rf.b,eu=rf.c,de=rf.d):de=eu=fu=uu=rs[si].faceNormals[cf],ei=rh[si],ei[d]=uu.x,ei[d+1]=uu.y,ei[d+2]=uu.z,ei[d+3]=fu.x,ei[d+4]=fu.y,ei[d+5]=fu.z,ei[d+6]=eu.x,ei[d+7]=eu.y,ei[d+8]=eu.z,ei[d+9]=de.x,ei[d+10]=de.y,ei[d+11]=de.z),d+=12;t.bindBuffer(t.ARRAY_BUFFER,it.__webglMorphTargetsBuffers[si]);t.bufferData(t.ARRAY_BUFFER,ih[si],lr);wf.morphNormals&&(t.bindBuffer(t.ARRAY_BUFFER,it.__webglMorphNormalsBuffers[si]),t.bufferData(t.ARRAY_BUFFER,rh[si],lr))}if(lf.length){for(e=0,l=rt.length;e<l;e++)o=pi[rt[e]],cu=lf[o.a],lu=lf[o.b],au=lf[o.c],ni[y]=cu.x,ni[y+1]=cu.y,ni[y+2]=cu.z,ni[y+3]=cu.w,ni[y+4]=lu.x,ni[y+5]=lu.y,ni[y+6]=lu.z,ni[y+7]=lu.w,ni[y+8]=au.x,ni[y+9]=au.y,ni[y+10]=au.z,ni[y+11]=au.w,vu=gf[o.a],yu=gf[o.b],wu=gf[o.c],gt[y]=vu.x,gt[y+1]=vu.y,gt[y+2]=vu.z,gt[y+3]=vu.w,gt[y+4]=yu.x,gt[y+5]=yu.y,gt[y+6]=yu.z,gt[y+7]=yu.w,gt[y+8]=wu.x,gt[y+9]=wu.y,gt[y+10]=wu.z,gt[y+11]=wu.w,y+=12;for(e=0,l=ut.length;e<l;e++)o=pi[ut[e]],cu=lf[o.a],lu=lf[o.b],au=lf[o.c],no=lf[o.d],ni[y]=cu.x,ni[y+1]=cu.y,ni[y+2]=cu.z,ni[y+3]=cu.w,ni[y+4]=lu.x,ni[y+5]=lu.y,ni[y+6]=lu.z,ni[y+7]=lu.w,ni[y+8]=au.x,ni[y+9]=au.y,ni[y+10]=au.z,ni[y+11]=au.w,ni[y+12]=no.x,ni[y+13]=no.y,ni[y+14]=no.z,ni[y+15]=no.w,vu=gf[o.a],yu=gf[o.b],wu=gf[o.c],to=gf[o.d],gt[y]=vu.x,gt[y+1]=vu.y,gt[y+2]=vu.z,gt[y+3]=vu.w,gt[y+4]=yu.x,gt[y+5]=yu.y,gt[y+6]=yu.z,gt[y+7]=yu.w,gt[y+8]=wu.x,gt[y+9]=wu.y,gt[y+10]=wu.z,gt[y+11]=wu.w,gt[y+12]=to.x,gt[y+13]=to.y,gt[y+14]=to.z,gt[y+15]=to.w,y+=16;0<y&&(t.bindBuffer(t.ARRAY_BUFFER,it.__webglSkinIndicesBuffer),t.bufferData(t.ARRAY_BUFFER,gt,lr),t.bindBuffer(t.ARRAY_BUFFER,it.__webglSkinWeightsBuffer),t.bufferData(t.ARRAY_BUFFER,ni,lr))}if(il&&th){for(e=0,l=rt.length;e<l;e++)o=pi[rt[e]],kr=o.vertexColors,is=o.color,3===kr.length&&th===THREE.VertexColors?(ou=kr[0],su=kr[1],hu=kr[2]):hu=su=ou=is,ci[ui]=ou.r,ci[ui+1]=ou.g,ci[ui+2]=ou.b,ci[ui+3]=su.r,ci[ui+4]=su.g,ci[ui+5]=su.b,ci[ui+6]=hu.r,ci[ui+7]=hu.g,ci[ui+8]=hu.b,ui+=9;for(e=0,l=ut.length;e<l;e++)o=pi[ut[e]],kr=o.vertexColors,is=o.color,4===kr.length&&th===THREE.VertexColors?(ou=kr[0],su=kr[1],hu=kr[2],ge=kr[3]):ge=hu=su=ou=is,ci[ui]=ou.r,ci[ui+1]=ou.g,ci[ui+2]=ou.b,ci[ui+3]=su.r,ci[ui+4]=su.g,ci[ui+5]=su.b,ci[ui+6]=hu.r,ci[ui+7]=hu.g,ci[ui+8]=hu.b,ci[ui+9]=ge.r,ci[ui+10]=ge.g,ci[ui+11]=ge.b,ui+=12;0<ui&&(t.bindBuffer(t.ARRAY_BUFFER,it.__webglColorBuffer),t.bufferData(t.ARRAY_BUFFER,ci,lr))}if(tl&&ur.hasTangents){for(e=0,l=rt.length;e<l;e++)o=pi[rt[e]],tf=o.vertexTangents,nu=tf[0],tu=tf[1],ru=tf[2],dt[lt]=nu.x,dt[lt+1]=nu.y,dt[lt+2]=nu.z,dt[lt+3]=nu.w,dt[lt+4]=tu.x,dt[lt+5]=tu.y,dt[lt+6]=tu.z,dt[lt+7]=tu.w,dt[lt+8]=ru.x,dt[lt+9]=ru.y,dt[lt+10]=ru.z,dt[lt+11]=ru.w,lt+=12;for(e=0,l=ut.length;e<l;e++)o=pi[ut[e]],tf=o.vertexTangents,nu=tf[0],tu=tf[1],ru=tf[2],ke=tf[3],dt[lt]=nu.x,dt[lt+1]=nu.y,dt[lt+2]=nu.z,dt[lt+3]=nu.w,dt[lt+4]=tu.x,dt[lt+5]=tu.y,dt[lt+6]=tu.z,dt[lt+7]=tu.w,dt[lt+8]=ru.x,dt[lt+9]=ru.y,dt[lt+10]=ru.z,dt[lt+11]=ru.w,dt[lt+12]=ke.x,dt[lt+13]=ke.y,dt[lt+14]=ke.z,dt[lt+15]=ke.w,lt+=16;t.bindBuffer(t.ARRAY_BUFFER,it.__webglTangentBuffer);t.bufferData(t.ARRAY_BUFFER,dt,lr)}if(nl&&kh){for(e=0,l=rt.length;e<l;e++)if(o=pi[rt[e]],se=o.vertexNormals,sf=o.normal,3===se.length&&ts)for(ft=0;3>ft;ft++)hf=se[ft],ar[nr]=hf.x,ar[nr+1]=hf.y,ar[nr+2]=hf.z,nr+=3;else for(ft=0;3>ft;ft++)ar[nr]=sf.x,ar[nr+1]=sf.y,ar[nr+2]=sf.z,nr+=3;for(e=0,l=ut.length;e<l;e++)if(o=pi[ut[e]],se=o.vertexNormals,sf=o.normal,4===se.length&&ts)for(ft=0;4>ft;ft++)hf=se[ft],ar[nr]=hf.x,ar[nr+1]=hf.y,ar[nr+2]=hf.z,nr+=3;else for(ft=0;4>ft;ft++)ar[nr]=sf.x,ar[nr+1]=sf.y,ar[nr+2]=sf.z,nr+=3;t.bindBuffer(t.ARRAY_BUFFER,it.__webglNormalBuffer);t.bufferData(t.ARRAY_BUFFER,ar,lr)}if(tc&&fh&&dh){for(e=0,l=rt.length;e<l;e++)if(of=rt[e],he=fh[of],void 0!==he)for(ft=0;3>ft;ft++)le=he[ft],io[bf]=le.x,io[bf+1]=le.y,bf+=2;for(e=0,l=ut.length;e<l;e++)if(of=ut[e],he=fh[of],void 0!==he)for(ft=0;4>ft;ft++)le=he[ft],io[bf]=le.x,io[bf+1]=le.y,bf+=2;0<bf&&(t.bindBuffer(t.ARRAY_BUFFER,it.__webglUVBuffer),t.bufferData(t.ARRAY_BUFFER,io,lr))}if(tc&&eh&&dh){for(e=0,l=rt.length;e<l;e++)if(of=rt[e],ce=eh[of],void 0!==ce)for(ft=0;3>ft;ft++)ae=ce[ft],ro[kf]=ae.x,ro[kf+1]=ae.y,kf+=2;for(e=0,l=ut.length;e<l;e++)if(of=ut[e],ce=eh[of],void 0!==ce)for(ft=0;4>ft;ft++)ae=ce[ft],ro[kf]=ae.x,ro[kf+1]=ae.y,kf+=2;0<kf&&(t.bindBuffer(t.ARRAY_BUFFER,it.__webglUV2Buffer),t.bufferData(t.ARRAY_BUFFER,ro,lr))}if(gc){for(e=0,l=rt.length;e<l;e++)bu[dr]=ii,bu[dr+1]=ii+1,bu[dr+2]=ii+2,dr+=3,sr[tr]=ii,sr[tr+1]=ii+1,sr[tr+2]=ii,sr[tr+3]=ii+2,sr[tr+4]=ii+1,sr[tr+5]=ii+2,tr+=6,ii+=3;for(e=0,l=ut.length;e<l;e++)bu[dr]=ii,bu[dr+1]=ii+1,bu[dr+2]=ii+3,bu[dr+3]=ii+1,bu[dr+4]=ii+2,bu[dr+5]=ii+3,dr+=6,sr[tr]=ii,sr[tr+1]=ii+1,sr[tr+2]=ii,sr[tr+3]=ii+3,sr[tr+4]=ii+1,sr[tr+5]=ii+2,sr[tr+6]=ii+2,sr[tr+7]=ii+3,tr+=8,ii+=4;t.bindBuffer(t.ELEMENT_ARRAY_BUFFER,it.__webglFaceBuffer);t.bufferData(t.ELEMENT_ARRAY_BUFFER,bu,lr);t.bindBuffer(t.ELEMENT_ARRAY_BUFFER,it.__webglLineBuffer);t.bufferData(t.ELEMENT_ARRAY_BUFFER,sr,lr)}if(uh)for(ft=0,gh=uh.length;ft<gh;ft++)if(r=uh[ft],r.__original.needsUpdate){if(f=0,1===r.size){if(void 0===r.boundTo||"vertices"===r.boundTo){for(e=0,l=rt.length;e<l;e++)o=pi[rt[e]],r.array[f]=r.value[o.a],r.array[f+1]=r.value[o.b],r.array[f+2]=r.value[o.c],f+=3;for(e=0,l=ut.length;e<l;e++)o=pi[ut[e]],r.array[f]=r.value[o.a],r.array[f+1]=r.value[o.b],r.array[f+2]=r.value[o.c],r.array[f+3]=r.value[o.d],f+=4}else if("faces"===r.boundTo){for(e=0,l=rt.length;e<l;e++)et=r.value[rt[e]],r.array[f]=et,r.array[f+1]=et,r.array[f+2]=et,f+=3;for(e=0,l=ut.length;e<l;e++)et=r.value[ut[e]],r.array[f]=et,r.array[f+1]=et,r.array[f+2]=et,r.array[f+3]=et,f+=4}}else if(2===r.size){if(void 0===r.boundTo||"vertices"===r.boundTo){for(e=0,l=rt.length;e<l;e++)o=pi[rt[e]],s=r.value[o.a],h=r.value[o.b],c=r.value[o.c],r.array[f]=s.x,r.array[f+1]=s.y,r.array[f+2]=h.x,r.array[f+3]=h.y,r.array[f+4]=c.x,r.array[f+5]=c.y,f+=6;for(e=0,l=ut.length;e<l;e++)o=pi[ut[e]],s=r.value[o.a],h=r.value[o.b],c=r.value[o.c],nt=r.value[o.d],r.array[f]=s.x,r.array[f+1]=s.y,r.array[f+2]=h.x,r.array[f+3]=h.y,r.array[f+4]=c.x,r.array[f+5]=c.y,r.array[f+6]=nt.x,r.array[f+7]=nt.y,f+=8}else if("faces"===r.boundTo){for(e=0,l=rt.length;e<l;e++)c=h=s=et=r.value[rt[e]],r.array[f]=s.x,r.array[f+1]=s.y,r.array[f+2]=h.x,r.array[f+3]=h.y,r.array[f+4]=c.x,r.array[f+5]=c.y,f+=6;for(e=0,l=ut.length;e<l;e++)nt=c=h=s=et=r.value[ut[e]],r.array[f]=s.x,r.array[f+1]=s.y,r.array[f+2]=h.x,r.array[f+3]=h.y,r.array[f+4]=c.x,r.array[f+5]=c.y,r.array[f+6]=nt.x,r.array[f+7]=nt.y,f+=8}}else if(3===r.size){if(v="c"===r.type?["r","g","b"]:["x","y","z"],void 0===r.boundTo||"vertices"===r.boundTo){for(e=0,l=rt.length;e<l;e++)o=pi[rt[e]],s=r.value[o.a],h=r.value[o.b],c=r.value[o.c],r.array[f]=s[v[0]],r.array[f+1]=s[v[1]],r.array[f+2]=s[v[2]],r.array[f+3]=h[v[0]],r.array[f+4]=h[v[1]],r.array[f+5]=h[v[2]],r.array[f+6]=c[v[0]],r.array[f+7]=c[v[1]],r.array[f+8]=c[v[2]],f+=9;for(e=0,l=ut.length;e<l;e++)o=pi[ut[e]],s=r.value[o.a],h=r.value[o.b],c=r.value[o.c],nt=r.value[o.d],r.array[f]=s[v[0]],r.array[f+1]=s[v[1]],r.array[f+2]=s[v[2]],r.array[f+3]=h[v[0]],r.array[f+4]=h[v[1]],r.array[f+5]=h[v[2]],r.array[f+6]=c[v[0]],r.array[f+7]=c[v[1]],r.array[f+8]=c[v[2]],r.array[f+9]=nt[v[0]],r.array[f+10]=nt[v[1]],r.array[f+11]=nt[v[2]],f+=12}else if("faces"===r.boundTo){for(e=0,l=rt.length;e<l;e++)c=h=s=et=r.value[rt[e]],r.array[f]=s[v[0]],r.array[f+1]=s[v[1]],r.array[f+2]=s[v[2]],r.array[f+3]=h[v[0]],r.array[f+4]=h[v[1]],r.array[f+5]=h[v[2]],r.array[f+6]=c[v[0]],r.array[f+7]=c[v[1]],r.array[f+8]=c[v[2]],f+=9;for(e=0,l=ut.length;e<l;e++)nt=c=h=s=et=r.value[ut[e]],r.array[f]=s[v[0]],r.array[f+1]=s[v[1]],r.array[f+2]=s[v[2]],r.array[f+3]=h[v[0]],r.array[f+4]=h[v[1]],r.array[f+5]=h[v[2]],r.array[f+6]=c[v[0]],r.array[f+7]=c[v[1]],r.array[f+8]=c[v[2]],r.array[f+9]=nt[v[0]],r.array[f+10]=nt[v[1]],r.array[f+11]=nt[v[2]],f+=12}else if("faceVertices"===r.boundTo){for(e=0,l=rt.length;e<l;e++)et=r.value[rt[e]],s=et[0],h=et[1],c=et[2],r.array[f]=s[v[0]],r.array[f+1]=s[v[1]],r.array[f+2]=s[v[2]],r.array[f+3]=h[v[0]],r.array[f+4]=h[v[1]],r.array[f+5]=h[v[2]],r.array[f+6]=c[v[0]],r.array[f+7]=c[v[1]],r.array[f+8]=c[v[2]],f+=9;for(e=0,l=ut.length;e<l;e++)et=r.value[ut[e]],s=et[0],h=et[1],c=et[2],nt=et[3],r.array[f]=s[v[0]],r.array[f+1]=s[v[1]],r.array[f+2]=s[v[2]],r.array[f+3]=h[v[0]],r.array[f+4]=h[v[1]],r.array[f+5]=h[v[2]],r.array[f+6]=c[v[0]],r.array[f+7]=c[v[1]],r.array[f+8]=c[v[2]],r.array[f+9]=nt[v[0]],r.array[f+10]=nt[v[1]],r.array[f+11]=nt[v[2]],f+=12}}else if(4===r.size)if(void 0===r.boundTo||"vertices"===r.boundTo){for(e=0,l=rt.length;e<l;e++)o=pi[rt[e]],s=r.value[o.a],h=r.value[o.b],c=r.value[o.c],r.array[f]=s.x,r.array[f+1]=s.y,r.array[f+2]=s.z,r.array[f+3]=s.w,r.array[f+4]=h.x,r.array[f+5]=h.y,r.array[f+6]=h.z,r.array[f+7]=h.w,r.array[f+8]=c.x,r.array[f+9]=c.y,r.array[f+10]=c.z,r.array[f+11]=c.w,f+=12;for(e=0,l=ut.length;e<l;e++)o=pi[ut[e]],s=r.value[o.a],h=r.value[o.b],c=r.value[o.c],nt=r.value[o.d],r.array[f]=s.x,r.array[f+1]=s.y,r.array[f+2]=s.z,r.array[f+3]=s.w,r.array[f+4]=h.x,r.array[f+5]=h.y,r.array[f+6]=h.z,r.array[f+7]=h.w,r.array[f+8]=c.x,r.array[f+9]=c.y,r.array[f+10]=c.z,r.array[f+11]=c.w,r.array[f+12]=nt.x,r.array[f+13]=nt.y,r.array[f+14]=nt.z,r.array[f+15]=nt.w,f+=16}else if("faces"===r.boundTo){for(e=0,l=rt.length;e<l;e++)c=h=s=et=r.value[rt[e]],r.array[f]=s.x,r.array[f+1]=s.y,r.array[f+2]=s.z,r.array[f+3]=s.w,r.array[f+4]=h.x,r.array[f+5]=h.y,r.array[f+6]=h.z,r.array[f+7]=h.w,r.array[f+8]=c.x,r.array[f+9]=c.y,r.array[f+10]=c.z,r.array[f+11]=c.w,f+=12;for(e=0,l=ut.length;e<l;e++)nt=c=h=s=et=r.value[ut[e]],r.array[f]=s.x,r.array[f+1]=s.y,r.array[f+2]=s.z,r.array[f+3]=s.w,r.array[f+4]=h.x,r.array[f+5]=h.y,r.array[f+6]=h.z,r.array[f+7]=h.w,r.array[f+8]=c.x,r.array[f+9]=c.y,r.array[f+10]=c.z,r.array[f+11]=c.w,r.array[f+12]=nt.x,r.array[f+13]=nt.y,r.array[f+14]=nt.z,r.array[f+15]=nt.w,f+=16}else if("faceVertices"===r.boundTo){for(e=0,l=rt.length;e<l;e++)et=r.value[rt[e]],s=et[0],h=et[1],c=et[2],r.array[f]=s.x,r.array[f+1]=s.y,r.array[f+2]=s.z,r.array[f+3]=s.w,r.array[f+4]=h.x,r.array[f+5]=h.y,r.array[f+6]=h.z,r.array[f+7]=h.w,r.array[f+8]=c.x,r.array[f+9]=c.y,r.array[f+10]=c.z,r.array[f+11]=c.w,f+=12;for(e=0,l=ut.length;e<l;e++)et=r.value[ut[e]],s=et[0],h=et[1],c=et[2],nt=et[3],r.array[f]=s.x,r.array[f+1]=s.y,r.array[f+2]=s.z,r.array[f+3]=s.w,r.array[f+4]=h.x,r.array[f+5]=h.y,r.array[f+6]=h.z,r.array[f+7]=h.w,r.array[f+8]=c.x,r.array[f+9]=c.y,r.array[f+10]=c.z,r.array[f+11]=c.w,r.array[f+12]=nt.x,r.array[f+13]=nt.y,r.array[f+14]=nt.z,r.array[f+15]=nt.w,f+=16}t.bindBuffer(t.ARRAY_BUFFER,r.buffer);t.bufferData(t.ARRAY_BUFFER,r.array,lr)}dc&&(delete it.__inittedArrays,delete it.__colorArray,delete it.__normalArray,delete it.__tangentArray,delete it.__uvArray,delete it.__uv2Array,delete it.__faceArray,delete it.__vertexArray,delete it.__lineArray,delete it.__skinIndexArray,delete it.__skinWeightArray)}}a.verticesNeedUpdate=!1;a.morphTargetsNeedUpdate=!1;a.elementsNeedUpdate=!1;a.uvsNeedUpdate=!1;a.normalsNeedUpdate=!1;a.colorsNeedUpdate=!1;a.tangentsNeedUpdate=!1;a.buffersNeedUpdate=!1;ai.attributes&&st(ai)}else if(cr instanceof THREE.Ribbon){if(ai=b(cr,a),ef=ai.attributes&&ot(ai),a.verticesNeedUpdate||a.colorsNeedUpdate||a.normalsNeedUpdate||ef){var vr=a,us=t.DYNAMIC_DRAW,uo=void 0,fo=void 0,eo=void 0,fs=void 0,ti=void 0,es=void 0,os=void 0,ss=void 0,ic=void 0,vi=void 0,ve=void 0,wt=void 0,fr=void 0,rc=vr.vertices,uc=vr.colors,fc=vr.normals,ul=rc.length,fl=uc.length,el=fc.length,hs=vr.__vertexArray,cs=vr.__colorArray,ls=vr.__normalArray,ol=vr.colorsNeedUpdate,sl=vr.normalsNeedUpdate,oh=vr.__webglCustomAttributesList;if(vr.verticesNeedUpdate){for(uo=0;uo<ul;uo++)fs=rc[uo],ti=3*uo,hs[ti]=fs.x,hs[ti+1]=fs.y,hs[ti+2]=fs.z;t.bindBuffer(t.ARRAY_BUFFER,vr.__webglVertexBuffer);t.bufferData(t.ARRAY_BUFFER,hs,us)}if(ol){for(fo=0;fo<fl;fo++)es=uc[fo],ti=3*fo,cs[ti]=es.r,cs[ti+1]=es.g,cs[ti+2]=es.b;t.bindBuffer(t.ARRAY_BUFFER,vr.__webglColorBuffer);t.bufferData(t.ARRAY_BUFFER,cs,us)}if(sl){for(eo=0;eo<el;eo++)os=fc[eo],ti=3*eo,ls[ti]=os.x,ls[ti+1]=os.y,ls[ti+2]=os.z;t.bindBuffer(t.ARRAY_BUFFER,vr.__webglNormalBuffer);t.bufferData(t.ARRAY_BUFFER,ls,us)}if(oh)for(ss=0,ic=oh.length;ss<ic;ss++)if(wt=oh[ss],wt.needsUpdate&&(void 0===wt.boundTo||"vertices"===wt.boundTo)){if(ti=0,ve=wt.value.length,1===wt.size)for(vi=0;vi<ve;vi++)wt.array[vi]=wt.value[vi];else if(2===wt.size)for(vi=0;vi<ve;vi++)fr=wt.value[vi],wt.array[ti]=fr.x,wt.array[ti+1]=fr.y,ti+=2;else if(3===wt.size)if("c"===wt.type)for(vi=0;vi<ve;vi++)fr=wt.value[vi],wt.array[ti]=fr.r,wt.array[ti+1]=fr.g,wt.array[ti+2]=fr.b,ti+=3;else for(vi=0;vi<ve;vi++)fr=wt.value[vi],wt.array[ti]=fr.x,wt.array[ti+1]=fr.y,wt.array[ti+2]=fr.z,ti+=3;else if(4===wt.size)for(vi=0;vi<ve;vi++)fr=wt.value[vi],wt.array[ti]=fr.x,wt.array[ti+1]=fr.y,wt.array[ti+2]=fr.z,wt.array[ti+3]=fr.w,ti+=4;t.bindBuffer(t.ARRAY_BUFFER,wt.buffer);t.bufferData(t.ARRAY_BUFFER,wt.array,us)}}a.verticesNeedUpdate=!1;a.colorsNeedUpdate=!1;a.normalsNeedUpdate=!1;ai.attributes&&st(ai)}else if(cr instanceof THREE.Line)if(a instanceof THREE.BufferGeometry)(a.verticesNeedUpdate||a.colorsNeedUpdate)&&yt(a,t.DYNAMIC_DRAW,!a.dynamic),a.verticesNeedUpdate=!1,a.colorsNeedUpdate=!1;else{if(ai=b(cr,a),ef=ai.attributes&&ot(ai),a.verticesNeedUpdate||a.colorsNeedUpdate||a.lineDistancesNeedUpdate||ef){var yr=a,as=t.DYNAMIC_DRAW,oo=void 0,so=void 0,ho=void 0,vs=void 0,ri=void 0,ys=void 0,ec=yr.vertices,oc=yr.colors,sc=yr.lineDistances,hl=ec.length,cl=oc.length,ll=sc.length,ps=yr.__vertexArray,ws=yr.__colorArray,hc=yr.__lineDistanceArray,al=yr.colorsNeedUpdate,vl=yr.lineDistancesNeedUpdate,sh=yr.__webglCustomAttributesList,bs=void 0,cc=void 0,yi=void 0,ye=void 0,er=void 0,bt=void 0;if(yr.verticesNeedUpdate){for(oo=0;oo<hl;oo++)vs=ec[oo],ri=3*oo,ps[ri]=vs.x,ps[ri+1]=vs.y,ps[ri+2]=vs.z;t.bindBuffer(t.ARRAY_BUFFER,yr.__webglVertexBuffer);t.bufferData(t.ARRAY_BUFFER,ps,as)}if(al){for(so=0;so<cl;so++)ys=oc[so],ri=3*so,ws[ri]=ys.r,ws[ri+1]=ys.g,ws[ri+2]=ys.b;t.bindBuffer(t.ARRAY_BUFFER,yr.__webglColorBuffer);t.bufferData(t.ARRAY_BUFFER,ws,as)}if(vl){for(ho=0;ho<ll;ho++)hc[ho]=sc[ho];t.bindBuffer(t.ARRAY_BUFFER,yr.__webglLineDistanceBuffer);t.bufferData(t.ARRAY_BUFFER,hc,as)}if(sh)for(bs=0,cc=sh.length;bs<cc;bs++)if(bt=sh[bs],bt.needsUpdate&&(void 0===bt.boundTo||"vertices"===bt.boundTo)){if(ri=0,ye=bt.value.length,1===bt.size)for(yi=0;yi<ye;yi++)bt.array[yi]=bt.value[yi];else if(2===bt.size)for(yi=0;yi<ye;yi++)er=bt.value[yi],bt.array[ri]=er.x,bt.array[ri+1]=er.y,ri+=2;else if(3===bt.size)if("c"===bt.type)for(yi=0;yi<ye;yi++)er=bt.value[yi],bt.array[ri]=er.r,bt.array[ri+1]=er.g,bt.array[ri+2]=er.b,ri+=3;else for(yi=0;yi<ye;yi++)er=bt.value[yi],bt.array[ri]=er.x,bt.array[ri+1]=er.y,bt.array[ri+2]=er.z,ri+=3;else if(4===bt.size)for(yi=0;yi<ye;yi++)er=bt.value[yi],bt.array[ri]=er.x,bt.array[ri+1]=er.y,bt.array[ri+2]=er.z,bt.array[ri+3]=er.w,ri+=4;t.bindBuffer(t.ARRAY_BUFFER,bt.buffer);t.bufferData(t.ARRAY_BUFFER,bt.array,as)}}a.verticesNeedUpdate=!1;a.colorsNeedUpdate=!1;a.lineDistancesNeedUpdate=!1;ai.attributes&&st(ai)}else if(cr instanceof THREE.ParticleSystem)if(a instanceof THREE.BufferGeometry)(a.verticesNeedUpdate||a.colorsNeedUpdate)&&yt(a,t.DYNAMIC_DRAW,!a.dynamic),a.verticesNeedUpdate=!1,a.colorsNeedUpdate=!1;else{if(ai=b(cr,a),ef=ai.attributes&&ot(ai),a.verticesNeedUpdate||a.colorsNeedUpdate||cr.sortParticles||ef){var ku=a,hh=t.DYNAMIC_DRAW,co=cr,or=void 0,du=void 0,gu=void 0,w=void 0,nf=void 0,vf=void 0,ks=ku.vertices,ch=ks.length,lh=ku.colors,lc=lh.length,ne=ku.__vertexArray,te=ku.__colorArray,uf=ku.__sortArray,ac=ku.verticesNeedUpdate,vc=ku.colorsNeedUpdate,ff=ku.__webglCustomAttributesList,pr=void 0,pe=void 0,g=void 0,wr=void 0,ht=void 0,p=void 0;if(co.sortParticles){for(li.copy(ct),li.multiply(co.matrixWorld),or=0;or<ch;or++)gu=ks[or],u.copy(gu),u.applyProjection(li),uf[or]=[u.z,or];for(uf.sort(pt),or=0;or<ch;or++)gu=ks[uf[or][1]],w=3*or,ne[w]=gu.x,ne[w+1]=gu.y,ne[w+2]=gu.z;for(du=0;du<lc;du++)w=3*du,vf=lh[uf[du][1]],te[w]=vf.r,te[w+1]=vf.g,te[w+2]=vf.b;if(ff)for(pr=0,pe=ff.length;pr<pe;pr++)if(p=ff[pr],void 0===p.boundTo||"vertices"===p.boundTo)if(w=0,wr=p.value.length,1===p.size)for(g=0;g<wr;g++)nf=uf[g][1],p.array[g]=p.value[nf];else if(2===p.size)for(g=0;g<wr;g++)nf=uf[g][1],ht=p.value[nf],p.array[w]=ht.x,p.array[w+1]=ht.y,w+=2;else if(3===p.size)if("c"===p.type)for(g=0;g<wr;g++)nf=uf[g][1],ht=p.value[nf],p.array[w]=ht.r,p.array[w+1]=ht.g,p.array[w+2]=ht.b,w+=3;else for(g=0;g<wr;g++)nf=uf[g][1],ht=p.value[nf],p.array[w]=ht.x,p.array[w+1]=ht.y,p.array[w+2]=ht.z,w+=3;else if(4===p.size)for(g=0;g<wr;g++)nf=uf[g][1],ht=p.value[nf],p.array[w]=ht.x,p.array[w+1]=ht.y,p.array[w+2]=ht.z,p.array[w+3]=ht.w,w+=4}else{if(ac)for(or=0;or<ch;or++)gu=ks[or],w=3*or,ne[w]=gu.x,ne[w+1]=gu.y,ne[w+2]=gu.z;if(vc)for(du=0;du<lc;du++)vf=lh[du],w=3*du,te[w]=vf.r,te[w+1]=vf.g,te[w+2]=vf.b;if(ff)for(pr=0,pe=ff.length;pr<pe;pr++)if(p=ff[pr],p.needsUpdate&&(void 0===p.boundTo||"vertices"===p.boundTo))if(wr=p.value.length,w=0,1===p.size)for(g=0;g<wr;g++)p.array[g]=p.value[g];else if(2===p.size)for(g=0;g<wr;g++)ht=p.value[g],p.array[w]=ht.x,p.array[w+1]=ht.y,w+=2;else if(3===p.size)if("c"===p.type)for(g=0;g<wr;g++)ht=p.value[g],p.array[w]=ht.r,p.array[w+1]=ht.g,p.array[w+2]=ht.b,w+=3;else for(g=0;g<wr;g++)ht=p.value[g],p.array[w]=ht.x,p.array[w+1]=ht.y,p.array[w+2]=ht.z,w+=3;else if(4===p.size)for(g=0;g<wr;g++)ht=p.value[g],p.array[w]=ht.x,p.array[w+1]=ht.y,p.array[w+2]=ht.z,p.array[w+3]=ht.w,w+=4}if((ac||co.sortParticles)&&(t.bindBuffer(t.ARRAY_BUFFER,ku.__webglVertexBuffer),t.bufferData(t.ARRAY_BUFFER,ne,hh)),(vc||co.sortParticles)&&(t.bindBuffer(t.ARRAY_BUFFER,ku.__webglColorBuffer),t.bufferData(t.ARRAY_BUFFER,te,hh)),ff)for(pr=0,pe=ff.length;pr<pe;pr++)(p=ff[pr],p.needsUpdate||co.sortParticles)&&(t.bindBuffer(t.ARRAY_BUFFER,p.buffer),t.bufferData(t.ARRAY_BUFFER,p.array,hh))}a.verticesNeedUpdate=!1;a.colorsNeedUpdate=!1;ai.attributes&&st(ai)}}};this.initMaterial=function(n,r,u,f){var k,g,d,it,nt,c,tt,rt,v,a,y,w,b,u,e,p,o,f,s;for(n.addEventListener("dispose",fu),n instanceof THREE.MeshDepthMaterial?v="depth":n instanceof THREE.MeshNormalMaterial?v="normal":n instanceof THREE.MeshBasicMaterial?v="basic":n instanceof THREE.MeshLambertMaterial?v="lambert":n instanceof THREE.MeshPhongMaterial?v="phong":n instanceof THREE.LineBasicMaterial?v="basic":n instanceof THREE.LineDashedMaterial?v="dashed":n instanceof THREE.ParticleBasicMaterial&&(v="particle_basic"),v&&(a=THREE.ShaderLib[v],n.uniforms=THREE.UniformsUtils.clone(a.uniforms),n.vertexShader=a.vertexShader,n.fragmentShader=a.fragmentShader),k=d=w=b=a=0,g=r.length;k<g;k++)y=r[k],y.onlyShadow||(y instanceof THREE.DirectionalLight&&d++,y instanceof THREE.PointLight&&w++,y instanceof THREE.SpotLight&&b++,y instanceof THREE.HemisphereLight&&a++);for(k=d,g=w,d=b,it=a,a=y=0,b=r.length;a<b;a++)w=r[a],w.castShadow&&(w instanceof THREE.SpotLight&&y++,w instanceof THREE.DirectionalLight&&!w.shadowCascade&&y++);rt=y;pi&&f&&f.useVertexTexture?tt=1024:(r=t.getParameter(t.MAX_VERTEX_UNIFORM_VECTORS),r=Math.floor((r-20)/4),void 0!==f&&f instanceof THREE.SkinnedMesh&&(r=Math.min(f.bones.length,r),r<f.bones.length&&console.warn("WebGLRenderer: too many bones - "+f.bones.length+", this GPU supports just "+r+" (try OpenGL instead of ANGLE)")),tt=r);n:{w=n.fragmentShader;b=n.vertexShader;a=n.uniforms;r=n.attributes;y=n.defines;u={map:!!n.map,envMap:!!n.envMap,lightMap:!!n.lightMap,bumpMap:!!n.bumpMap,normalMap:!!n.normalMap,specularMap:!!n.specularMap,vertexColors:n.vertexColors,fog:u,useFog:n.fog,fogExp:u instanceof THREE.FogExp2,sizeAttenuation:n.sizeAttenuation,skinning:n.skinning,maxBones:tt,useVertexTexture:pi&&f&&f.useVertexTexture,boneTextureWidth:f&&f.boneTextureWidth,boneTextureHeight:f&&f.boneTextureHeight,morphTargets:n.morphTargets,morphNormals:n.morphNormals,maxMorphTargets:this.maxMorphTargets,maxMorphNormals:this.maxMorphNormals,maxDirLights:k,maxPointLights:g,maxSpotLights:d,maxHemiLights:it,maxShadows:rt,shadowMapEnabled:this.shadowMapEnabled&&f.receiveShadow,shadowMapType:this.shadowMapType,shadowMapDebug:this.shadowMapDebug,shadowMapCascade:this.shadowMapCascade,alphaTest:n.alphaTest,metal:n.metal,perPixel:n.perPixel,wrapAround:n.wrapAround,doubleSided:n.side===THREE.DoubleSide,flipSided:n.side===THREE.BackSide};f=[];v?f.push(v):(f.push(w),f.push(b));for(p in y)f.push(p),f.push(y[p]);for(e in u)f.push(e),f.push(u[e]);for(v=f.join(),e=0,p=h.length;e<p;e++)if(f=h[e],f.code===v){f.usedTimes++;c=f.program;break n}e="SHADOWMAP_TYPE_BASIC";u.shadowMapType===THREE.PCFShadowMap?e="SHADOWMAP_TYPE_PCF":u.shadowMapType===THREE.PCFSoftShadowMap&&(e="SHADOWMAP_TYPE_PCF_SOFT");p=[];for(o in y)f=y[o],!1!==f&&(f="#define "+o+" "+f,p.push(f));f=p.join("\n");o=t.createProgram();p=["precision "+l+" float;",f,yi?"#define VERTEX_TEXTURES":"",i.gammaInput?"#define GAMMA_INPUT":"",i.gammaOutput?"#define GAMMA_OUTPUT":"",i.physicallyBasedShading?"#define PHYSICALLY_BASED_SHADING":"","#define MAX_DIR_LIGHTS "+u.maxDirLights,"#define MAX_POINT_LIGHTS "+u.maxPointLights,"#define MAX_SPOT_LIGHTS "+u.maxSpotLights,"#define MAX_HEMI_LIGHTS "+u.maxHemiLights,"#define MAX_SHADOWS "+u.maxShadows,"#define MAX_BONES "+u.maxBones,u.map?"#define USE_MAP":"",u.envMap?"#define USE_ENVMAP":"",u.lightMap?"#define USE_LIGHTMAP":"",u.bumpMap?"#define USE_BUMPMAP":"",u.normalMap?"#define USE_NORMALMAP":"",u.specularMap?"#define USE_SPECULARMAP":"",u.vertexColors?"#define USE_COLOR":"",u.skinning?"#define USE_SKINNING":"",u.useVertexTexture?"#define BONE_TEXTURE":"",u.boneTextureWidth?"#define N_BONE_PIXEL_X "+u.boneTextureWidth.toFixed(1):"",u.boneTextureHeight?"#define N_BONE_PIXEL_Y "+u.boneTextureHeight.toFixed(1):"",u.morphTargets?"#define USE_MORPHTARGETS":"",u.morphNormals?"#define USE_MORPHNORMALS":"",u.perPixel?"#define PHONG_PER_PIXEL":"",u.wrapAround?"#define WRAP_AROUND":"",u.doubleSided?"#define DOUBLE_SIDED":"",u.flipSided?"#define FLIP_SIDED":"",u.shadowMapEnabled?"#define USE_SHADOWMAP":"",u.shadowMapEnabled?"#define "+e:"",u.shadowMapDebug?"#define SHADOWMAP_DEBUG":"",u.shadowMapCascade?"#define SHADOWMAP_CASCADE":"",u.sizeAttenuation?"#define USE_SIZEATTENUATION":"","uniform mat4 modelMatrix;\nuniform mat4 modelViewMatrix;\nuniform mat4 projectionMatrix;\nuniform mat4 viewMatrix;\nuniform mat3 normalMatrix;\nuniform vec3 cameraPosition;\nattribute vec3 position;\nattribute vec3 normal;\nattribute vec2 uv;\nattribute vec2 uv2;\n#ifdef USE_COLOR\nattribute vec3 color;\n#endif\n#ifdef USE_MORPHTARGETS\nattribute vec3 morphTarget0;\nattribute vec3 morphTarget1;\nattribute vec3 morphTarget2;\nattribute vec3 morphTarget3;\n#ifdef USE_MORPHNORMALS\nattribute vec3 morphNormal0;\nattribute vec3 morphNormal1;\nattribute vec3 morphNormal2;\nattribute vec3 morphNormal3;\n#else\nattribute vec3 morphTarget4;\nattribute vec3 morphTarget5;\nattribute vec3 morphTarget6;\nattribute vec3 morphTarget7;\n#endif\n#endif\n#ifdef USE_SKINNING\nattribute vec4 skinIndex;\nattribute vec4 skinWeight;\n#endif\n"].join("\n");e=["precision "+l+" float;",u.bumpMap||u.normalMap?"#extension GL_OES_standard_derivatives : enable":"",f,"#define MAX_DIR_LIGHTS "+u.maxDirLights,"#define MAX_POINT_LIGHTS "+u.maxPointLights,"#define MAX_SPOT_LIGHTS "+u.maxSpotLights,"#define MAX_HEMI_LIGHTS "+u.maxHemiLights,"#define MAX_SHADOWS "+u.maxShadows,u.alphaTest?"#define ALPHATEST "+u.alphaTest:"",i.gammaInput?"#define GAMMA_INPUT":"",i.gammaOutput?"#define GAMMA_OUTPUT":"",i.physicallyBasedShading?"#define PHYSICALLY_BASED_SHADING":"",u.useFog&&u.fog?"#define USE_FOG":"",u.useFog&&u.fogExp?"#define FOG_EXP2":"",u.map?"#define USE_MAP":"",u.envMap?"#define USE_ENVMAP":"",u.lightMap?"#define USE_LIGHTMAP":"",u.bumpMap?"#define USE_BUMPMAP":"",u.normalMap?"#define USE_NORMALMAP":"",u.specularMap?"#define USE_SPECULARMAP":"",u.vertexColors?"#define USE_COLOR":"",u.metal?"#define METAL":"",u.perPixel?"#define PHONG_PER_PIXEL":"",u.wrapAround?"#define WRAP_AROUND":"",u.doubleSided?"#define DOUBLE_SIDED":"",u.flipSided?"#define FLIP_SIDED":"",u.shadowMapEnabled?"#define USE_SHADOWMAP":"",u.shadowMapEnabled?"#define "+e:"",u.shadowMapDebug?"#define SHADOWMAP_DEBUG":"",u.shadowMapCascade?"#define SHADOWMAP_CASCADE":"","uniform mat4 viewMatrix;\nuniform vec3 cameraPosition;\n"].join("\n");e=fr("fragment",e+w);p=fr("vertex",p+b);t.attachShader(o,p);t.attachShader(o,e);t.linkProgram(o);t.getProgramParameter(o,t.LINK_STATUS)||console.error("Could not initialise shader\nVALIDATE_STATUS: "+t.getProgramParameter(o,t.VALIDATE_STATUS)+", gl error ["+t.getError()+"]");t.deleteShader(e);t.deleteShader(p);o.uniforms={};o.attributes={};e="viewMatrix modelViewMatrix projectionMatrix normalMatrix modelMatrix cameraPosition morphTargetInfluences".split(" ");u.useVertexTexture?e.push("boneTexture"):e.push("boneGlobalMatrices");for(s in a)e.push(s);for(s=e,e=0,p=s.length;e<p;e++)a=s[e],o.uniforms[a]=t.getUniformLocation(o,a);for(e="position normal uv uv2 tangent color skinIndex skinWeight lineDistance".split(" "),s=0;s<u.maxMorphTargets;s++)e.push("morphTarget"+s);for(s=0;s<u.maxMorphNormals;s++)e.push("morphNormal"+s);for(c in r)e.push(c);for(c=e,s=0,r=c.length;s<r;s++)e=c[s],o.attributes[e]=t.getAttribLocation(o,e);o.id=yu++;h.push({program:o,code:v,usedTimes:1});i.info.memory.programs=h.length;c=o}if(n.program=c,s=n.program.attributes,n.morphTargets)for(n.numSupportedMorphTargets=0,r="morphTarget",c=0;c<this.maxMorphTargets;c++)o=r+c,0<=s[o]&&n.numSupportedMorphTargets++;if(n.morphNormals)for(n.numSupportedMorphNormals=0,r="morphNormal",c=0;c<this.maxMorphNormals;c++)o=r+c,0<=s[o]&&n.numSupportedMorphNormals++;n.uniformsList=[];for(nt in n.uniforms)n.uniformsList.push([n.uniforms[nt],nt])};this.setFaceCulling=function(n,i){n===THREE.CullFaceNone?t.disable(t.CULL_FACE):(i===THREE.FrontFaceDirectionCW?t.frontFace(t.CW):t.frontFace(t.CCW),n===THREE.CullFaceBack?t.cullFace(t.BACK):n===THREE.CullFaceFront?t.cullFace(t.FRONT):t.cullFace(t.FRONT_AND_BACK),t.enable(t.CULL_FACE))};this.setMaterialFaces=function(n){var i=n.side===THREE.DoubleSide,n=n.side===THREE.BackSide;tt!==i&&(i?t.disable(t.CULL_FACE):t.enable(t.CULL_FACE),tt=i);it!==n&&(n?t.frontFace(t.CW):t.frontFace(t.CCW),it=n)};this.setDepthTest=function(n){ut!==n&&(n?t.enable(t.DEPTH_TEST):t.disable(t.DEPTH_TEST),ut=n)};this.setDepthWrite=function(n){ft!==n&&(t.depthMask(n),ft=n)};this.setBlending=function(n,i,r,u){n!==rt&&(n===THREE.NoBlending?t.disable(t.BLEND):n===THREE.AdditiveBlending?(t.enable(t.BLEND),t.blendEquation(t.FUNC_ADD),t.blendFunc(t.SRC_ALPHA,t.ONE)):n===THREE.SubtractiveBlending?(t.enable(t.BLEND),t.blendEquation(t.FUNC_ADD),t.blendFunc(t.ZERO,t.ONE_MINUS_SRC_COLOR)):n===THREE.MultiplyBlending?(t.enable(t.BLEND),t.blendEquation(t.FUNC_ADD),t.blendFunc(t.ZERO,t.SRC_COLOR)):n===THREE.CustomBlending?t.enable(t.BLEND):(t.enable(t.BLEND),t.blendEquationSeparate(t.FUNC_ADD,t.FUNC_ADD),t.blendFuncSeparate(t.SRC_ALPHA,t.ONE_MINUS_SRC_ALPHA,t.ONE,t.ONE_MINUS_SRC_ALPHA)),rt=n);n===THREE.CustomBlending?(i!==ri&&(t.blendEquation(e(i)),ri=i),r!==ui||u!==fi)&&(t.blendFunc(e(r),e(u)),ui=r,fi=u):fi=ui=ri=null};this.setTexture=function(n,r){var s,f,h;if(n.needsUpdate){n.__webglInit||(n.__webglInit=!0,n.addEventListener("dispose",ru),n.__webglTexture=t.createTexture(),i.info.memory.textures++);t.activeTexture(t.TEXTURE0+r);t.bindTexture(t.TEXTURE_2D,n.__webglTexture);t.pixelStorei(t.UNPACK_FLIP_Y_WEBGL,n.flipY);t.pixelStorei(t.UNPACK_PREMULTIPLY_ALPHA_WEBGL,n.premultiplyAlpha);t.pixelStorei(t.UNPACK_ALIGNMENT,n.unpackAlignment);var u=n.image,c=0==(u.width&u.width-1)&&0==(u.height&u.height-1),o=e(n.format),l=e(n.type);if(ht(t.TEXTURE_2D,n,c),s=n.mipmaps,n instanceof THREE.DataTexture)if(0<s.length&&c){for(f=0,h=s.length;f<h;f++)u=s[f],t.texImage2D(t.TEXTURE_2D,f,o,u.width,u.height,0,o,l,u.data);n.generateMipmaps=!1}else t.texImage2D(t.TEXTURE_2D,0,o,u.width,u.height,0,o,l,u.data);else if(n instanceof THREE.CompressedTexture)for(f=0,h=s.length;f<h;f++)u=s[f],t.compressedTexImage2D(t.TEXTURE_2D,f,o,u.width,u.height,0,u.data);else if(0<s.length&&c){for(f=0,h=s.length;f<h;f++)u=s[f],t.texImage2D(t.TEXTURE_2D,f,o,o,l,u);n.generateMipmaps=!1}else t.texImage2D(t.TEXTURE_2D,0,o,o,l,n.image);n.generateMipmaps&&c&&t.generateMipmap(t.TEXTURE_2D);n.needsUpdate=!1;n.onUpdate&&n.onUpdate()}else t.activeTexture(t.TEXTURE0+r),t.bindTexture(t.TEXTURE_2D,n.__webglTexture)};this.setRenderTarget=function(n){var f=n instanceof THREE.WebGLRenderTargetCube,u,h,c;if(n&&!n.__webglFramebuffer){void 0===n.depthBuffer&&(n.depthBuffer=!0);void 0===n.stencilBuffer&&(n.stencilBuffer=!0);n.addEventListener("dispose",uu);n.__webglTexture=t.createTexture();i.info.memory.textures++;var o=0==(n.width&n.width-1)&&0==(n.height&n.height-1),r=e(n.format),s=e(n.type);if(f){for(n.__webglFramebuffer=[],n.__webglRenderbuffer=[],t.bindTexture(t.TEXTURE_CUBE_MAP,n.__webglTexture),ht(t.TEXTURE_CUBE_MAP,n,o),u=0;6>u;u++)n.__webglFramebuffer[u]=t.createFramebuffer(),n.__webglRenderbuffer[u]=t.createRenderbuffer(),t.texImage2D(t.TEXTURE_CUBE_MAP_POSITIVE_X+u,0,r,n.width,n.height,0,r,s,null),h=n,c=t.TEXTURE_CUBE_MAP_POSITIVE_X+u,t.bindFramebuffer(t.FRAMEBUFFER,n.__webglFramebuffer[u]),t.framebufferTexture2D(t.FRAMEBUFFER,t.COLOR_ATTACHMENT0,c,h.__webglTexture,0),er(n.__webglRenderbuffer[u],n);o&&t.generateMipmap(t.TEXTURE_CUBE_MAP)}else n.__webglFramebuffer=t.createFramebuffer(),n.__webglRenderbuffer=n.shareDepthFrom?n.shareDepthFrom.__webglRenderbuffer:t.createRenderbuffer(),t.bindTexture(t.TEXTURE_2D,n.__webglTexture),ht(t.TEXTURE_2D,n,o),t.texImage2D(t.TEXTURE_2D,0,r,n.width,n.height,0,r,s,null),r=t.TEXTURE_2D,t.bindFramebuffer(t.FRAMEBUFFER,n.__webglFramebuffer),t.framebufferTexture2D(t.FRAMEBUFFER,t.COLOR_ATTACHMENT0,r,n.__webglTexture,0),n.shareDepthFrom?n.depthBuffer&&!n.stencilBuffer?t.framebufferRenderbuffer(t.FRAMEBUFFER,t.DEPTH_ATTACHMENT,t.RENDERBUFFER,n.__webglRenderbuffer):n.depthBuffer&&n.stencilBuffer&&t.framebufferRenderbuffer(t.FRAMEBUFFER,t.DEPTH_STENCIL_ATTACHMENT,t.RENDERBUFFER,n.__webglRenderbuffer):er(n.__webglRenderbuffer,n),o&&t.generateMipmap(t.TEXTURE_2D);f?t.bindTexture(t.TEXTURE_CUBE_MAP,null):t.bindTexture(t.TEXTURE_2D,null);t.bindRenderbuffer(t.RENDERBUFFER,null);t.bindFramebuffer(t.FRAMEBUFFER,null)}n?(f=f?n.__webglFramebuffer[n.activeCubeFace]:n.__webglFramebuffer,o=n.width,n=n.height,s=r=0):(f=null,o=si,n=hi,r=ei,s=oi);f!==sr&&(t.bindFramebuffer(t.FRAMEBUFFER,f),t.viewport(r,s,o,n),sr=f);vr=o;yr=n};this.shadowMapPlugin=new THREE.ShadowMapPlugin;this.addPrePlugin(this.shadowMapPlugin);this.addPostPlugin(new THREE.SpritePlugin);this.addPostPlugin(new THREE.LensFlarePlugin)};THREE.WebGLRenderTarget=function(n,t,i){THREE.EventDispatcher.call(this);this.width=n;this.height=t;i=i||{};this.wrapS=void 0!==i.wrapS?i.wrapS:THREE.ClampToEdgeWrapping;this.wrapT=void 0!==i.wrapT?i.wrapT:THREE.ClampToEdgeWrapping;this.magFilter=void 0!==i.magFilter?i.magFilter:THREE.LinearFilter;this.minFilter=void 0!==i.minFilter?i.minFilter:THREE.LinearMipMapLinearFilter;this.anisotropy=void 0!==i.anisotropy?i.anisotropy:1;this.offset=new THREE.Vector2(0,0);this.repeat=new THREE.Vector2(1,1);this.format=void 0!==i.format?i.format:THREE.RGBAFormat;this.type=void 0!==i.type?i.type:THREE.UnsignedByteType;this.depthBuffer=void 0!==i.depthBuffer?i.depthBuffer:!0;this.stencilBuffer=void 0!==i.stencilBuffer?i.stencilBuffer:!0;this.generateMipmaps=!0;this.shareDepthFrom=null};THREE.WebGLRenderTarget.prototype.clone=function(){var n=new THREE.WebGLRenderTarget(this.width,this.height);return n.wrapS=this.wrapS,n.wrapT=this.wrapT,n.magFilter=this.magFilter,n.minFilter=this.minFilter,n.anisotropy=this.anisotropy,n.offset.copy(this.offset),n.repeat.copy(this.repeat),n.format=this.format,n.type=this.type,n.depthBuffer=this.depthBuffer,n.stencilBuffer=this.stencilBuffer,n.generateMipmaps=this.generateMipmaps,n.shareDepthFrom=this.shareDepthFrom,n};THREE.WebGLRenderTarget.prototype.dispose=function(){this.dispatchEvent({type:"dispose"})};THREE.WebGLRenderTargetCube=function(n,t,i){THREE.WebGLRenderTarget.call(this,n,t,i);this.activeCubeFace=0};THREE.WebGLRenderTargetCube.prototype=Object.create(THREE.WebGLRenderTarget.prototype);THREE.RenderableVertex=function(){this.positionWorld=new THREE.Vector3;this.positionScreen=new THREE.Vector4;this.visible=!0};THREE.RenderableVertex.prototype.copy=function(n){this.positionWorld.copy(n.positionWorld);this.positionScreen.copy(n.positionScreen)};THREE.RenderableFace3=function(){this.v1=new THREE.RenderableVertex;this.v2=new THREE.RenderableVertex;this.v3=new THREE.RenderableVertex;this.centroidModel=new THREE.Vector3;this.normalModel=new THREE.Vector3;this.normalModelView=new THREE.Vector3;this.vertexNormalsLength=0;this.vertexNormalsModel=[new THREE.Vector3,new THREE.Vector3,new THREE.Vector3];this.vertexNormalsModelView=[new THREE.Vector3,new THREE.Vector3,new THREE.Vector3];this.material=this.color=null;this.uvs=[[]];this.z=null};THREE.RenderableFace4=function(){this.v1=new THREE.RenderableVertex;this.v2=new THREE.RenderableVertex;this.v3=new THREE.RenderableVertex;this.v4=new THREE.RenderableVertex;this.centroidModel=new THREE.Vector3;this.normalModel=new THREE.Vector3;this.normalModelView=new THREE.Vector3;this.vertexNormalsLength=0;this.vertexNormalsModel=[new THREE.Vector3,new THREE.Vector3,new THREE.Vector3,new THREE.Vector3];this.vertexNormalsModelView=[new THREE.Vector3,new THREE.Vector3,new THREE.Vector3,new THREE.Vector3];this.material=this.color=null;this.uvs=[[]];this.z=null};THREE.RenderableObject=function(){this.z=this.object=null};THREE.RenderableParticle=function(){this.rotation=this.z=this.y=this.x=this.object=null;this.scale=new THREE.Vector2;this.material=null};THREE.RenderableLine=function(){this.z=null;this.v1=new THREE.RenderableVertex;this.v2=new THREE.RenderableVertex;this.material=null};THREE.GeometryUtils={merge:function(n,t){var e,o,h=n.vertices.length,l=t instanceof THREE.Mesh?t.geometry:t,r=n.vertices,s=l.vertices,w=n.faces,v=l.faces,b=n.faceVertexUvs[0],l=l.faceVertexUvs[0],u,c,i;for(t instanceof THREE.Mesh&&(t.matrixAutoUpdate&&t.updateMatrix(),e=t.matrix,o=new THREE.Matrix3,o.getInverse(e),o.transpose()),u=0,c=s.length;u<c;u++)i=s[u].clone(),e&&i.applyMatrix4(e),r.push(i);for(u=0,c=v.length;u<c;u++){var i=v[u],f,a,y=i.vertexNormals,p=i.vertexColors;for(i instanceof THREE.Face3?f=new THREE.Face3(i.a+h,i.b+h,i.c+h):i instanceof THREE.Face4&&(f=new THREE.Face4(i.a+h,i.b+h,i.c+h,i.d+h)),f.normal.copy(i.normal),o&&f.normal.applyMatrix3(o).normalize(),r=0,s=y.length;r<s;r++)a=y[r].clone(),o&&a.applyMatrix3(o).normalize(),f.vertexNormals.push(a);for(f.color.copy(i.color),r=0,s=p.length;r<s;r++)a=p[r],f.vertexColors.push(a.clone());f.materialIndex=i.materialIndex;f.centroid.copy(i.centroid);e&&f.centroid.applyMatrix4(e);w.push(f)}for(u=0,c=l.length;u<c;u++){for(e=l[u],o=[],r=0,s=e.length;r<s;r++)o.push(new THREE.Vector2(e[r].x,e[r].y));b.push(o)}},removeMaterials:function(n,t){for(var u={},i=0,f=t.length;i<f;i++)u[t[i]]=!0;for(var r,e=[],i=0,f=n.faces.length;i<f;i++)r=n.faces[i],r.materialIndex in u||e.push(r);n.faces=e},randomPointInTriangle:function(n,t,i){var r,u,o,e=new THREE.Vector3,f=THREE.GeometryUtils.__v1;return r=THREE.GeometryUtils.random(),u=THREE.GeometryUtils.random(),1<r+u&&(r=1-r,u=1-u),o=1-r-u,e.copy(n),e.multiplyScalar(r),f.copy(t),f.multiplyScalar(u),e.add(f),f.copy(i),f.multiplyScalar(o),e.add(f),e},randomPointInFace:function(n,t,i){var u,r,f,t,e;return n instanceof THREE.Face3?(u=t.vertices[n.a],r=t.vertices[n.b],f=t.vertices[n.c],THREE.GeometryUtils.randomPointInTriangle(u,r,f)):n instanceof THREE.Face4?(u=t.vertices[n.a],r=t.vertices[n.b],f=t.vertices[n.c],t=t.vertices[n.d],i?n._area1&&n._area2?(i=n._area1,e=n._area2):(i=THREE.GeometryUtils.triangleArea(u,r,t),e=THREE.GeometryUtils.triangleArea(r,f,t),n._area1=i,n._area2=e):(i=THREE.GeometryUtils.triangleArea(u,r,t),e=THREE.GeometryUtils.triangleArea(r,f,t)),THREE.GeometryUtils.random()*(i+e)<i?THREE.GeometryUtils.randomPointInTriangle(u,r,t):THREE.GeometryUtils.randomPointInTriangle(r,f,t)):void 0},randomPointsInGeometry:function(n,t){function a(n){function t(i,r){if(r<i)return i;var u=i+Math.floor((r-i)/2);return e[u]>n?t(i,u-1):e[u]<n?t(u+1,r):u}return t(0,e.length-1)}for(var i,h=n.faces,r=n.vertices,v=h.length,c=0,e=[],o,f,s,l,u=0;u<v;u++)i=h[u],i instanceof THREE.Face3?(o=r[i.a],f=r[i.b],s=r[i.c],i._area=THREE.GeometryUtils.triangleArea(o,f,s)):i instanceof THREE.Face4&&(o=r[i.a],f=r[i.b],s=r[i.c],l=r[i.d],i._area1=THREE.GeometryUtils.triangleArea(o,f,l),i._area2=THREE.GeometryUtils.triangleArea(f,s,l),i._area=i._area1+i._area2),c+=i._area,e[u]=c;for(i=[],u=0;u<t;u++)r=THREE.GeometryUtils.random()*c,r=a(r),i[u]=THREE.GeometryUtils.randomPointInFace(h[r],n,!0);return i},triangleArea:function(n,t,i){var r=THREE.GeometryUtils.__v1,u=THREE.GeometryUtils.__v2;return r.subVectors(t,n),u.subVectors(i,n),r.cross(u),.5*r.length()},center:function(n){n.computeBoundingBox();var i=n.boundingBox,t=new THREE.Vector3;return t.addVectors(i.min,i.max),t.multiplyScalar(-.5),n.applyMatrix((new THREE.Matrix4).makeTranslation(t.x,t.y,t.z)),n.computeBoundingBox(),t},normalizeUVs:function(n){for(var n=n.faceVertexUvs[0],r=0,u=n.length;r<u;r++)for(var i=n[r],t=0,f=i.length;t<f;t++)1!==i[t].x&&(i[t].x-=Math.floor(i[t].x)),1!==i[t].y&&(i[t].y-=Math.floor(i[t].y))},triangulateQuads:function(n){for(var t,u,l=[],h=[],c=[],i=0,o=n.faceUvs.length;i<o;i++)h[i]=[];for(i=0,o=n.faceVertexUvs.length;i<o;i++)c[i]=[];for(i=0,o=n.faces.length;i<o;i++)if(t=n.faces[i],t instanceof THREE.Face4){u=t.a;var f=t.b,a=t.c,s=t.d,r=new THREE.Face3,e=new THREE.Face3;for(r.color.copy(t.color),e.color.copy(t.color),r.materialIndex=t.materialIndex,e.materialIndex=t.materialIndex,r.a=u,r.b=f,r.c=s,e.a=f,e.b=a,e.c=s,4===t.vertexColors.length&&(r.vertexColors[0]=t.vertexColors[0].clone(),r.vertexColors[1]=t.vertexColors[1].clone(),r.vertexColors[2]=t.vertexColors[3].clone(),e.vertexColors[0]=t.vertexColors[1].clone(),e.vertexColors[1]=t.vertexColors[2].clone(),e.vertexColors[2]=t.vertexColors[3].clone()),l.push(r,e),t=0,u=n.faceVertexUvs.length;t<u;t++)n.faceVertexUvs[t].length&&(r=n.faceVertexUvs[t][i],f=r[1],a=r[2],s=r[3],r=[r[0].clone(),f.clone(),s.clone()],f=[f.clone(),a.clone(),s.clone()],c[t].push(r,f));for(t=0,u=n.faceUvs.length;t<u;t++)n.faceUvs[t].length&&(f=n.faceUvs[t][i],h[t].push(f,f))}else{for(l.push(t),t=0,u=n.faceUvs.length;t<u;t++)h[t].push(n.faceUvs[t][i]);for(t=0,u=n.faceVertexUvs.length;t<u;t++)c[t].push(n.faceVertexUvs[t][i])}n.faces=l;n.faceUvs=h;n.faceVertexUvs=c;n.computeCentroids();n.computeFaceNormals();n.computeVertexNormals();n.hasTangents&&n.computeTangents()},setMaterialIndex:function(n,t,i,r){for(n=n.faces,r=r||n.length-1,i=i||0;i<=r;i++)n[i].materialIndex=t}};THREE.GeometryUtils.random=THREE.Math.random16;THREE.GeometryUtils.__v1=new THREE.Vector3;THREE.GeometryUtils.__v2=new THREE.Vector3;THREE.ImageUtils={crossOrigin:"anonymous",loadTexture:function(n,t,i,r){var f=new Image,u=new THREE.Texture(f,t),t=new THREE.ImageLoader;return t.addEventListener("load",function(n){u.image=n.content;u.needsUpdate=!0;i&&i(u)}),t.addEventListener("error",function(n){r&&r(n.message)}),t.crossOrigin=this.crossOrigin,t.load(n,f),u.sourceFile=n,u},loadCompressedTexture:function(n,t,i,r){var u=new THREE.CompressedTexture,f;return u.mapping=t,f=new XMLHttpRequest,f.onload=function(){var n=THREE.ImageUtils.parseDDS(f.response,!0);u.format=n.format;u.mipmaps=n.mipmaps;u.image.width=n.width;u.image.height=n.height;u.generateMipmaps=!1;u.needsUpdate=!0;i&&i(u)},f.onerror=r,f.open("GET",n,!0),f.responseType="arraybuffer",f.send(null),u},loadTextureCube:function(n,t,i,r){var e=[],u,t,o,f;for(e.loadCount=0,u=new THREE.Texture,u.image=e,void 0!==t&&(u.mapping=t),u.flipY=!1,t=0,o=n.length;t<o;++t)f=new Image,e[t]=f,f.onload=function(){e.loadCount+=1;6===e.loadCount&&(u.needsUpdate=!0,i&&i(u))},f.onerror=r,f.crossOrigin=this.crossOrigin,f.src=n[t];return u},loadCompressedTextureCube:function(n,t,i,r){var e=[],f,o,h,s,u;if(e.loadCount=0,f=new THREE.CompressedTexture,f.image=e,void 0!==t&&(f.mapping=t),f.flipY=!1,f.generateMipmaps=!1,t=function(n,t){return function(){var r=THREE.ImageUtils.parseDDS(n.response,!0);t.format=r.format;t.mipmaps=r.mipmaps;t.width=r.width;t.height=r.height;e.loadCount+=1;6===e.loadCount&&(f.format=r.format,f.needsUpdate=!0,i&&i(f))}},n instanceof Array)for(o=0,h=n.length;o<h;++o)s={},e[o]=s,u=new XMLHttpRequest,u.onload=t(u,s),u.onerror=r,s=n[o],u.open("GET",s,!0),u.responseType="arraybuffer",u.send(null);else u=new XMLHttpRequest,u.onload=function(){var n=THREE.ImageUtils.parseDDS(u.response,!0),o,t,r;if(n.isCubemap){for(o=n.mipmaps.length/n.mipmapCount,t=0;t<o;t++)for(e[t]={mipmaps:[]},r=0;r<n.mipmapCount;r++)e[t].mipmaps.push(n.mipmaps[t*n.mipmapCount+r]),e[t].format=n.format,e[t].width=n.width,e[t].height=n.height;f.format=n.format;f.needsUpdate=!0;i&&i(f)}},u.onerror=r,u.open("GET",n,!0),u.responseType="arraybuffer",u.send(null);return f},parseDDS:function(n,t){function s(n){return n.charCodeAt(0)+(n.charCodeAt(1)<<8)+(n.charCodeAt(2)<<16)+(n.charCodeAt(3)<<24)}var i={mipmaps:[],width:0,height:0,format:null,mipmapCount:1},o=s("DXT1"),f=s("DXT3"),e=s("DXT5"),r=new Int32Array(n,0,31),u,h,c,a;if(542327876!==r[0])return console.error("ImageUtils.parseDDS(): Invalid magic number in DDS header"),i;if(!r[20]&4)return console.error("ImageUtils.parseDDS(): Unsupported format, must contain a FourCC code"),i;u=r[21];switch(u){case o:o=8;i.format=THREE.RGB_S3TC_DXT1_Format;break;case f:o=16;i.format=THREE.RGBA_S3TC_DXT3_Format;break;case e:o=16;i.format=THREE.RGBA_S3TC_DXT5_Format;break;default:return console.error("ImageUtils.parseDDS(): Unsupported FourCC code: ",String.fromCharCode(u&255,u>>8&255,u>>16&255,u>>24&255)),i}i.mipmapCount=1;r[2]&131072&&!1!==t&&(i.mipmapCount=Math.max(1,r[7]));i.isCubemap=r[28]&512?!0:!1;i.width=r[4];i.height=r[3];for(var r=r[1]+4,f=i.width,e=i.height,u=i.isCubemap?6:1,l=0;l<u;l++){for(h=0;h<i.mipmapCount;h++)c=Math.max(4,f)/4*Math.max(4,e)/4*o,a={data:new Uint8Array(n,r,c),width:f,height:e},i.mipmaps.push(a),r+=c,f=Math.max(.5*f,1),e=Math.max(.5*e,1);f=i.width;e=i.height}return i},getNormalMap:function(n,t){var k=function(n){var t=Math.sqrt(n[0]*n[0]+n[1]*n[1]+n[2]*n[2]);return[n[0]/t,n[1]/t,n[2]/t]},t=t|1,r=n.width,a=n.height,p=document.createElement("canvas"),y,o;p.width=r;p.height=a;y=p.getContext("2d");y.drawImage(n,0,0);for(var c=y.getImageData(0,0,r,a).data,b=y.createImageData(r,a),w=b.data,h=0;h<r;h++)for(o=0;o<a;o++){var f=0>o-1?0:o-1,u=o+1>a-1?a-1:o+1,v=0>h-1?0:h-1,e=h+1>r-1?r-1:h+1,i=[],s=[0,0,c[4*(o*r+h)]/255*t];for(i.push([-1,0,c[4*(o*r+v)]/255*t]),i.push([-1,-1,c[4*(f*r+v)]/255*t]),i.push([0,-1,c[4*(f*r+h)]/255*t]),i.push([1,-1,c[4*(f*r+e)]/255*t]),i.push([1,0,c[4*(o*r+e)]/255*t]),i.push([1,1,c[4*(u*r+e)]/255*t]),i.push([0,1,c[4*(u*r+h)]/255*t]),i.push([-1,1,c[4*(u*r+v)]/255*t]),f=[],v=i.length,u=0;u<v;u++){var e=i[u],l=i[(u+1)%v],e=[e[0]-s[0],e[1]-s[1],e[2]-s[2]],l=[l[0]-s[0],l[1]-s[1],l[2]-s[2]];f.push(k([e[1]*l[2]-e[2]*l[1],e[2]*l[0]-e[0]*l[2],e[0]*l[1]-e[1]*l[0]]))}for(i=[0,0,0],u=0;u<f.length;u++)i[0]+=f[u][0],i[1]+=f[u][1],i[2]+=f[u][2];i[0]/=f.length;i[1]/=f.length;i[2]/=f.length;s=4*(o*r+h);w[s]=255*((i[0]+1)/2)|0;w[s+1]=255*((i[1]+1)/2)|0;w[s+2]=255*i[2]|0;w[s+3]=255}return y.putImageData(b,0,0),p},generateDataTexture:function(n,t,i){for(var f=n*t,u=new Uint8Array(3*f),e=Math.floor(255*i.r),o=Math.floor(255*i.g),i=Math.floor(255*i.b),r=0;r<f;r++)u[3*r]=e,u[3*r+1]=o,u[3*r+2]=i;return n=new THREE.DataTexture(u,n,t,THREE.RGBFormat),n.needsUpdate=!0,n}};THREE.SceneUtils={createMultiMaterialObject:function(n,t){for(var r=new THREE.Object3D,i=0,u=t.length;i<u;i++)r.add(new THREE.Mesh(n,t[i]));return r},detach:function(n,t,i){n.applyMatrix(t.matrixWorld);t.remove(n);i.add(n)},attach:function(n,t,i){var r=new THREE.Matrix4;r.getInverse(i.matrixWorld);n.applyMatrix(r);t.remove(n);i.add(n)}};THREE.FontUtils={faces:{},face:"helvetiker",weight:"normal",style:"normal",size:150,divisions:10,getFace:function(){return this.faces[this.face][this.weight][this.style]},loadFace:function(n){var t=n.familyName.toLowerCase();return this.faces[t]=this.faces[t]||{},this.faces[t][n.cssFontWeight]=this.faces[t][n.cssFontWeight]||{},this.faces[t][n.cssFontWeight][n.cssFontStyle]=n,this.faces[t][n.cssFontWeight][n.cssFontStyle]=n},drawText:function(n){for(var r=this.getFace(),e=this.size/r.resolution,t=0,u=String(n).split(""),o=u.length,f=[],n=0;n<o;n++){var i=new THREE.Path,i=this.extractGlyphPoints(u[n],r,e,t,i),t=t+i.offset;f.push(i.path)}return{paths:f,offset:t/2}},extractGlyphPoints:function(n,t,i,r,u){var v=[],f,s,e,k,o,y,p,h,c,w,b,l=t.glyphs[n]||t.glyphs["?"],a;if(l){if(l.o)for(t=l._cachedOutline||(l._cachedOutline=l.o.split(" ")),k=t.length,n=0;n<k;)switch(e=t[n++],e){case"m":e=t[n++]*i+r;o=t[n++]*i;u.moveTo(e,o);break;case"l":e=t[n++]*i+r;o=t[n++]*i;u.lineTo(e,o);break;case"q":if(e=t[n++]*i+r,o=t[n++]*i,h=t[n++]*i+r,c=t[n++]*i,u.quadraticCurveTo(h,c,e,o),f=v[v.length-1])for(y=f.x,p=f.y,f=1,s=this.divisions;f<=s;f++)a=f/s,THREE.Shape.Utils.b2(a,y,h,e),THREE.Shape.Utils.b2(a,p,c,o);break;case"b":if(e=t[n++]*i+r,o=t[n++]*i,h=t[n++]*i+r,c=t[n++]*-i,w=t[n++]*i+r,b=t[n++]*-i,u.bezierCurveTo(e,o,h,c,w,b),f=v[v.length-1])for(y=f.x,p=f.y,f=1,s=this.divisions;f<=s;f++)a=f/s,THREE.Shape.Utils.b3(a,y,h,w,e),THREE.Shape.Utils.b3(a,p,c,b,o)}return{offset:l.ha*i,path:u}}}};THREE.FontUtils.generateShapes=function(n,t){var t=t||{},r=void 0!==t.curveSegments?t.curveSegments:4,u=void 0!==t.font?t.font:"helvetiker",i=void 0!==t.weight?t.weight:"normal",f=void 0!==t.style?t.style:"normal";for(THREE.FontUtils.size=void 0!==t.size?t.size:100,THREE.FontUtils.divisions=r,THREE.FontUtils.face=u,THREE.FontUtils.weight=i,THREE.FontUtils.style=f,r=THREE.FontUtils.drawText(n).paths,u=[],i=0,f=r.length;i<f;i++)Array.prototype.push.apply(u,r[i].toShapes());return u},function(n){var t=function(n){for(var r=n.length,u=0,i=r-1,t=0;t<r;i=t++)u+=n[i].x*n[t].y-n[t].x*n[i].y;return.5*u};return n.Triangulate=function(n,i){var f=n.length,g,e;if(3>f)return null;var nt=[],u=[],tt=[],o,r,s;if(0<t(n))for(r=0;r<f;r++)u[r]=r;else for(r=0;r<f;r++)u[r]=f-1-r;for(g=2*f,r=f-1;2<f;){if(0>=g--){console.log("Warning, unable to triangulate polygon!");break}o=r;f<=o&&(o=0);r=o+1;f<=r&&(r=0);s=r+1;f<=s&&(s=0);n:{var l=e=void 0,a=void 0,p=void 0,w=void 0,b=void 0,k=void 0,h=void 0,c=void 0,l=n[u[o]].x,a=n[u[o]].y,p=n[u[r]].x,w=n[u[r]].y,b=n[u[s]].x,k=n[u[s]].y;if(1e-10>(p-l)*(k-a)-(w-a)*(b-l))e=!1;else{var rt=void 0,ut=void 0,ft=void 0,et=void 0,ot=void 0,st=void 0,v=void 0,y=void 0,d=void 0,it=void 0,d=y=v=c=h=void 0,rt=b-p,ut=k-w,ft=l-b,et=a-k,ot=p-l,st=w-a;for(e=0;e<f;e++)if(!(e===o||e===r||e===s)&&(h=n[u[e]].x,c=n[u[e]].y,v=h-l,y=c-a,d=h-p,it=c-w,h-=b,c-=k,d=rt*it-ut*d,v=ot*y-st*v,y=ft*c-et*h,0<=d&&0<=y&&0<=v)){e=!1;break n}e=!0}}if(e){for(nt.push([n[u[o]],n[u[r]],n[u[s]]]),tt.push([u[o],u[r],u[s]]),o=r,s=r+1;s<f;o++,s++)u[o]=u[s];f--;g=2*f}}return i?tt:nt},n.Triangulate.area=t,n}(THREE.FontUtils);self._typeface_js={faces:THREE.FontUtils.faces,loadFace:THREE.FontUtils.loadFace};THREE.Curve=function(){};THREE.Curve.prototype.getPoint=function(){return console.log("Warning, getPoint() not implemented!"),null};THREE.Curve.prototype.getPointAt=function(n){return n=this.getUtoTmapping(n),this.getPoint(n)};THREE.Curve.prototype.getPoints=function(n){n||(n=5);for(var i=[],t=0;t<=n;t++)i.push(this.getPoint(t/n));return i};THREE.Curve.prototype.getSpacedPoints=function(n){n||(n=5);for(var i=[],t=0;t<=n;t++)i.push(this.getPointAt(t/n));return i};THREE.Curve.prototype.getLength=function(){var n=this.getLengths();return n[n.length-1]};THREE.Curve.prototype.getLengths=function(n){if(n||(n=this.__arcLengthDivisions?this.__arcLengthDivisions:200),this.cacheArcLengths&&this.cacheArcLengths.length==n+1&&!this.needsUpdate)return this.cacheArcLengths;this.needsUpdate=!1;var i=[],r,u=this.getPoint(0),t,f=0;for(i.push(0),t=1;t<=n;t++)r=this.getPoint(t/n),f+=r.distanceTo(u),i.push(f),u=r;return this.cacheArcLengths=i};THREE.Curve.prototype.updateArcLengths=function(){this.needsUpdate=!0;this.getLengths()};THREE.Curve.prototype.getUtoTmapping=function(n,t){var u=this.getLengths(),i=0,e=u.length,o,r,f,s;for(o=t?t:n*u[e-1],r=0,f=e-1;r<=f;)if(i=Math.floor(r+(f-r)/2),s=u[i]-o,0>s)r=i+1;else if(0<s)f=i-1;else{f=i;break}return(i=f,u[i]==o)?i/(e-1):(r=u[i],(i+(o-r)/(u[i+1]-r))/(e-1))};THREE.Curve.prototype.getTangent=function(n){var t=n-.0001,n=n+.0001;return 0>t&&(t=0),1<n&&(n=1),t=this.getPoint(t),this.getPoint(n).clone().sub(t).normalize()};THREE.Curve.prototype.getTangentAt=function(n){return n=this.getUtoTmapping(n),this.getTangent(n)};THREE.LineCurve=function(n,t){this.v1=n;this.v2=t};THREE.LineCurve.prototype=Object.create(THREE.Curve.prototype);THREE.LineCurve.prototype.getPoint=function(n){var t=this.v2.clone().sub(this.v1);return t.multiplyScalar(n).add(this.v1),t};THREE.LineCurve.prototype.getPointAt=function(n){return this.getPoint(n)};THREE.LineCurve.prototype.getTangent=function(){return this.v2.clone().sub(this.v1).normalize()};THREE.QuadraticBezierCurve=function(n,t,i){this.v0=n;this.v1=t;this.v2=i};THREE.QuadraticBezierCurve.prototype=Object.create(THREE.Curve.prototype);THREE.QuadraticBezierCurve.prototype.getPoint=function(n){var t;return t=THREE.Shape.Utils.b2(n,this.v0.x,this.v1.x,this.v2.x),n=THREE.Shape.Utils.b2(n,this.v0.y,this.v1.y,this.v2.y),new THREE.Vector2(t,n)};THREE.QuadraticBezierCurve.prototype.getTangent=function(n){var t;return t=THREE.Curve.Utils.tangentQuadraticBezier(n,this.v0.x,this.v1.x,this.v2.x),n=THREE.Curve.Utils.tangentQuadraticBezier(n,this.v0.y,this.v1.y,this.v2.y),t=new THREE.Vector2(t,n),t.normalize(),t};THREE.CubicBezierCurve=function(n,t,i,r){this.v0=n;this.v1=t;this.v2=i;this.v3=r};THREE.CubicBezierCurve.prototype=Object.create(THREE.Curve.prototype);THREE.CubicBezierCurve.prototype.getPoint=function(n){var t;return t=THREE.Shape.Utils.b3(n,this.v0.x,this.v1.x,this.v2.x,this.v3.x),n=THREE.Shape.Utils.b3(n,this.v0.y,this.v1.y,this.v2.y,this.v3.y),new THREE.Vector2(t,n)};THREE.CubicBezierCurve.prototype.getTangent=function(n){var t;return t=THREE.Curve.Utils.tangentCubicBezier(n,this.v0.x,this.v1.x,this.v2.x,this.v3.x),n=THREE.Curve.Utils.tangentCubicBezier(n,this.v0.y,this.v1.y,this.v2.y,this.v3.y),t=new THREE.Vector2(t,n),t.normalize(),t};THREE.SplineCurve=function(n){this.points=void 0==n?[]:n};THREE.SplineCurve.prototype=Object.create(THREE.Curve.prototype);THREE.SplineCurve.prototype.getPoint=function(n){var u=new THREE.Vector2,i=[],t=this.points,r;return r=(t.length-1)*n,n=Math.floor(r),r-=n,i[0]=0==n?n:n-1,i[1]=n,i[2]=n>t.length-2?t.length-1:n+1,i[3]=n>t.length-3?t.length-1:n+2,u.x=THREE.Curve.Utils.interpolate(t[i[0]].x,t[i[1]].x,t[i[2]].x,t[i[3]].x,r),u.y=THREE.Curve.Utils.interpolate(t[i[0]].y,t[i[1]].y,t[i[2]].y,t[i[3]].y,r),u};THREE.EllipseCurve=function(n,t,i,r,u,f,e){this.aX=n;this.aY=t;this.xRadius=i;this.yRadius=r;this.aStartAngle=u;this.aEndAngle=f;this.aClockwise=e};THREE.EllipseCurve.prototype=Object.create(THREE.Curve.prototype);THREE.EllipseCurve.prototype.getPoint=function(n){var t=this.aEndAngle-this.aStartAngle;return this.aClockwise||(n=1-n),t=this.aStartAngle+n*t,n=this.aX+this.xRadius*Math.cos(t),t=this.aY+this.yRadius*Math.sin(t),new THREE.Vector2(n,t)};THREE.ArcCurve=function(n,t,i,r,u,f){THREE.EllipseCurve.call(this,n,t,i,i,r,u,f)};THREE.ArcCurve.prototype=Object.create(THREE.EllipseCurve.prototype);THREE.Curve.Utils={tangentQuadraticBezier:function(n,t,i,r){return 2*(1-n)*(i-t)+2*n*(r-i)},tangentCubicBezier:function(n,t,i,r,u){return-3*t*(1-n)*(1-n)+3*i*(1-n)*(1-n)-6*n*i*(1-n)+6*n*r*(1-n)-3*n*n*r+3*n*n*u},tangentSpline:function(n){return 6*n*n-6*n+(3*n*n-4*n+1)+(-6*n*n+6*n)+(3*n*n-2*n)},interpolate:function(n,t,i,r,u){var n=.5*(i-n),r=.5*(r-t),f=u*u;return(2*t-2*i+n+r)*u*f+(-3*t+3*i-2*n-r)*f+n*u+t}};THREE.Curve.create=function(n,t){return n.prototype=Object.create(THREE.Curve.prototype),n.prototype.getPoint=t,n};THREE.LineCurve3=THREE.Curve.create(function(n,t){this.v1=n;this.v2=t},function(n){var t=new THREE.Vector3;return t.subVectors(this.v2,this.v1),t.multiplyScalar(n),t.add(this.v1),t});THREE.QuadraticBezierCurve3=THREE.Curve.create(function(n,t,i){this.v0=n;this.v1=t;this.v2=i},function(n){var t,i;return t=THREE.Shape.Utils.b2(n,this.v0.x,this.v1.x,this.v2.x),i=THREE.Shape.Utils.b2(n,this.v0.y,this.v1.y,this.v2.y),n=THREE.Shape.Utils.b2(n,this.v0.z,this.v1.z,this.v2.z),new THREE.Vector3(t,i,n)});THREE.CubicBezierCurve3=THREE.Curve.create(function(n,t,i,r){this.v0=n;this.v1=t;this.v2=i;this.v3=r},function(n){var t,i;return t=THREE.Shape.Utils.b3(n,this.v0.x,this.v1.x,this.v2.x,this.v3.x),i=THREE.Shape.Utils.b3(n,this.v0.y,this.v1.y,this.v2.y,this.v3.y),n=THREE.Shape.Utils.b3(n,this.v0.z,this.v1.z,this.v2.z,this.v3.z),new THREE.Vector3(t,i,n)});THREE.SplineCurve3=THREE.Curve.create(function(n){this.points=void 0==n?[]:n},function(n){var u=new THREE.Vector3,i=[],r=this.points,t,n=(r.length-1)*n;t=Math.floor(n);n-=t;i[0]=0==t?t:t-1;i[1]=t;i[2]=t>r.length-2?r.length-1:t+1;i[3]=t>r.length-3?r.length-1:t+2;t=r[i[0]];var f=r[i[1]],e=r[i[2]],i=r[i[3]];return u.x=THREE.Curve.Utils.interpolate(t.x,f.x,e.x,i.x,n),u.y=THREE.Curve.Utils.interpolate(t.y,f.y,e.y,i.y,n),u.z=THREE.Curve.Utils.interpolate(t.z,f.z,e.z,i.z,n),u});THREE.ClosedSplineCurve3=THREE.Curve.create(function(n){this.points=void 0==n?[]:n},function(n){var u=new THREE.Vector3,i=[],t=this.points,r;return r=(t.length-0)*n,n=Math.floor(r),r-=n,n+=0<n?0:(Math.floor(Math.abs(n)/t.length)+1)*t.length,i[0]=(n-1)%t.length,i[1]=n%t.length,i[2]=(n+1)%t.length,i[3]=(n+2)%t.length,u.x=THREE.Curve.Utils.interpolate(t[i[0]].x,t[i[1]].x,t[i[2]].x,t[i[3]].x,r),u.y=THREE.Curve.Utils.interpolate(t[i[0]].y,t[i[1]].y,t[i[2]].y,t[i[3]].y,r),u.z=THREE.Curve.Utils.interpolate(t[i[0]].z,t[i[1]].z,t[i[2]].z,t[i[3]].z,r),u});THREE.CurvePath=function(){this.curves=[];this.bends=[];this.autoClose=!1};THREE.CurvePath.prototype=Object.create(THREE.Curve.prototype);THREE.CurvePath.prototype.add=function(n){this.curves.push(n)};THREE.CurvePath.prototype.checkConnection=function(){};THREE.CurvePath.prototype.closePath=function(){var n=this.curves[0].getPoint(0),t=this.curves[this.curves.length-1].getPoint(1);n.equals(t)||this.curves.push(new THREE.LineCurve(t,n))};THREE.CurvePath.prototype.getPoint=function(n){for(var t=n*this.getLength(),i=this.getCurveLengths(),n=0;n<i.length;){if(i[n]>=t)return t=i[n]-t,n=this.curves[n],t=1-t/n.getLength(),n.getPointAt(t);n++}return null};THREE.CurvePath.prototype.getLength=function(){var n=this.getCurveLengths();return n[n.length-1]};THREE.CurvePath.prototype.getCurveLengths=function(){if(this.cacheLengths&&this.cacheLengths.length==this.curves.length)return this.cacheLengths;for(var t=[],i=0,r=this.curves.length,n=0;n<r;n++)i+=this.curves[n].getLength(),t.push(i);return this.cacheLengths=t};THREE.CurvePath.prototype.getBoundingBox=function(){var t=this.getPoints(),i,r,s,u,f,h,n,e,c,l,o;for(i=r=Number.NEGATIVE_INFINITY,u=f=Number.POSITIVE_INFINITY,o=t[0]instanceof THREE.Vector3,l=o?new THREE.Vector3:new THREE.Vector2,e=0,c=t.length;e<c;e++)n=t[e],n.x>i?i=n.x:n.x<u&&(u=n.x),n.y>r?r=n.y:n.y<f&&(f=n.y),o&&(n.z>s?s=n.z:n.z<h&&(h=n.z)),l.add(n);return t={minX:u,minY:f,maxX:i,maxY:r,centroid:l.divideScalar(c)},o&&(t.maxZ=s,t.minZ=h),t};THREE.CurvePath.prototype.createPointsGeometry=function(n){return n=this.getPoints(n,!0),this.createGeometry(n)};THREE.CurvePath.prototype.createSpacedPointsGeometry=function(n){return n=this.getSpacedPoints(n,!0),this.createGeometry(n)};THREE.CurvePath.prototype.createGeometry=function(n){for(var i=new THREE.Geometry,t=0;t<n.length;t++)i.vertices.push(new THREE.Vector3(n[t].x,n[t].y,n[t].z||0));return i};THREE.CurvePath.prototype.addWrapPath=function(n){this.bends.push(n)};THREE.CurvePath.prototype.getTransformedPoints=function(n,t){var r=this.getPoints(n),i,u;for(t||(t=this.bends),i=0,u=t.length;i<u;i++)r=this.getWrapPoints(r,t[i]);return r};THREE.CurvePath.prototype.getTransformedSpacedPoints=function(n,t){var r=this.getSpacedPoints(n),i,u;for(t||(t=this.bends),i=0,u=t.length;i<u;i++)r=this.getWrapPoints(r,t[i]);return r};THREE.CurvePath.prototype.getWrapPoints=function(n,t){for(var s=this.getBoundingBox(),r,i,u,f,e=0,o=n.length;e<o;e++)r=n[e],i=r.x,u=r.y,f=i/s.maxX,f=t.getUtoTmapping(f,i),i=t.getPoint(f),u=t.getNormalVector(f).multiplyScalar(u),r.x=i.x+u.x,r.y=i.y+u.y;return n};THREE.Gyroscope=function(){THREE.Object3D.call(this)};THREE.Gyroscope.prototype=Object.create(THREE.Object3D.prototype);THREE.Gyroscope.prototype.updateMatrixWorld=function(n){this.matrixAutoUpdate&&this.updateMatrix();(this.matrixWorldNeedsUpdate||n)&&(this.parent?(this.matrixWorld.multiplyMatrices(this.parent.matrixWorld,this.matrix),this.matrixWorld.decompose(this.translationWorld,this.rotationWorld,this.scaleWorld),this.matrix.decompose(this.translationObject,this.rotationObject,this.scaleObject),this.matrixWorld.compose(this.translationWorld,this.rotationObject,this.scaleWorld)):this.matrixWorld.copy(this.matrix),this.matrixWorldNeedsUpdate=!1,n=!0);for(var t=0,i=this.children.length;t<i;t++)this.children[t].updateMatrixWorld(n)};THREE.Gyroscope.prototype.translationWorld=new THREE.Vector3;THREE.Gyroscope.prototype.translationObject=new THREE.Vector3;THREE.Gyroscope.prototype.rotationWorld=new THREE.Quaternion;THREE.Gyroscope.prototype.rotationObject=new THREE.Quaternion;THREE.Gyroscope.prototype.scaleWorld=new THREE.Vector3;THREE.Gyroscope.prototype.scaleObject=new THREE.Vector3;THREE.Path=function(n){THREE.CurvePath.call(this);this.actions=[];n&&this.fromPoints(n)};THREE.Path.prototype=Object.create(THREE.CurvePath.prototype);THREE.PathActions={MOVE_TO:"moveTo",LINE_TO:"lineTo",QUADRATIC_CURVE_TO:"quadraticCurveTo",BEZIER_CURVE_TO:"bezierCurveTo",CSPLINE_THRU:"splineThru",ARC:"arc",ELLIPSE:"ellipse"};THREE.Path.prototype.fromPoints=function(n){this.moveTo(n[0].x,n[0].y);for(var t=1,i=n.length;t<i;t++)this.lineTo(n[t].x,n[t].y)};THREE.Path.prototype.moveTo=function(){var n=Array.prototype.slice.call(arguments);this.actions.push({action:THREE.PathActions.MOVE_TO,args:n})};THREE.Path.prototype.lineTo=function(n,t){var r=Array.prototype.slice.call(arguments),i=this.actions[this.actions.length-1].args,i=new THREE.LineCurve(new THREE.Vector2(i[i.length-2],i[i.length-1]),new THREE.Vector2(n,t));this.curves.push(i);this.actions.push({action:THREE.PathActions.LINE_TO,args:r})};THREE.Path.prototype.quadraticCurveTo=function(n,t,i,r){var f=Array.prototype.slice.call(arguments),u=this.actions[this.actions.length-1].args,u=new THREE.QuadraticBezierCurve(new THREE.Vector2(u[u.length-2],u[u.length-1]),new THREE.Vector2(n,t),new THREE.Vector2(i,r));this.curves.push(u);this.actions.push({action:THREE.PathActions.QUADRATIC_CURVE_TO,args:f})};THREE.Path.prototype.bezierCurveTo=function(n,t,i,r,u,f){var o=Array.prototype.slice.call(arguments),e=this.actions[this.actions.length-1].args,e=new THREE.CubicBezierCurve(new THREE.Vector2(e[e.length-2],e[e.length-1]),new THREE.Vector2(n,t),new THREE.Vector2(i,r),new THREE.Vector2(u,f));this.curves.push(e);this.actions.push({action:THREE.PathActions.BEZIER_CURVE_TO,args:o})};THREE.Path.prototype.splineThru=function(n){var i=Array.prototype.slice.call(arguments),t=this.actions[this.actions.length-1].args,t=[new THREE.Vector2(t[t.length-2],t[t.length-1])];Array.prototype.push.apply(t,n);t=new THREE.SplineCurve(t);this.curves.push(t);this.actions.push({action:THREE.PathActions.CSPLINE_THRU,args:i})};THREE.Path.prototype.arc=function(n,t,i,r,u,f){var e=this.actions[this.actions.length-1].args;this.absarc(n+e[e.length-2],t+e[e.length-1],i,r,u,f)};THREE.Path.prototype.absarc=function(n,t,i,r,u,f){this.absellipse(n,t,i,i,r,u,f)};THREE.Path.prototype.ellipse=function(n,t,i,r,u,f,e){var o=this.actions[this.actions.length-1].args;this.absellipse(n+o[o.length-2],t+o[o.length-1],i,r,u,f,e)};THREE.Path.prototype.absellipse=function(n,t,i,r,u,f,e){var s=Array.prototype.slice.call(arguments),o=new THREE.EllipseCurve(n,t,i,r,u,f,e);this.curves.push(o);o=o.getPoint(e?1:0);s.push(o.x);s.push(o.y);this.actions.push({action:THREE.PathActions.ELLIPSE,args:s})};THREE.Path.prototype.getSpacedPoints=function(n){n||(n=40);for(var i=[],t=0;t<n;t++)i.push(this.getPoint(t/n));return i};THREE.Path.prototype.getPoints=function(n,t){if(this.useSpacedPoints)return console.log("tata"),this.getSpacedPoints(n,t);for(var n=n||12,f=[],i,r,c,l,a,y,v,h,s,e,u,o=0,p=this.actions.length;o<p;o++)switch(i=this.actions[o],r=i.action,i=i.args,r){case THREE.PathActions.MOVE_TO:f.push(new THREE.Vector2(i[0],i[1]));break;case THREE.PathActions.LINE_TO:f.push(new THREE.Vector2(i[0],i[1]));break;case THREE.PathActions.QUADRATIC_CURVE_TO:for(c=i[2],l=i[3],v=i[0],h=i[1],0<f.length?(r=f[f.length-1],s=r.x,e=r.y):(r=this.actions[o-1].args,s=r[r.length-2],e=r[r.length-1]),i=1;i<=n;i++)u=i/n,r=THREE.Shape.Utils.b2(u,s,v,c),u=THREE.Shape.Utils.b2(u,e,h,l),f.push(new THREE.Vector2(r,u));break;case THREE.PathActions.BEZIER_CURVE_TO:for(c=i[4],l=i[5],v=i[0],h=i[1],a=i[2],y=i[3],0<f.length?(r=f[f.length-1],s=r.x,e=r.y):(r=this.actions[o-1].args,s=r[r.length-2],e=r[r.length-1]),i=1;i<=n;i++)u=i/n,r=THREE.Shape.Utils.b3(u,s,v,a,c),u=THREE.Shape.Utils.b3(u,e,h,y,l),f.push(new THREE.Vector2(r,u));break;case THREE.PathActions.CSPLINE_THRU:for(r=this.actions[o-1].args,u=[new THREE.Vector2(r[r.length-2],r[r.length-1])],r=n*i[0].length,u=u.concat(i[0]),u=new THREE.SplineCurve(u),i=1;i<=r;i++)f.push(u.getPointAt(i/r));break;case THREE.PathActions.ARC:for(c=i[0],l=i[1],h=i[2],a=i[3],r=i[4],v=!!i[5],s=r-a,e=2*n,i=1;i<=e;i++)u=i/e,v||(u=1-u),u=a+u*s,r=c+h*Math.cos(u),u=l+h*Math.sin(u),f.push(new THREE.Vector2(r,u));break;case THREE.PathActions.ELLIPSE:for(c=i[0],l=i[1],h=i[2],y=i[3],a=i[4],r=i[5],v=!!i[6],s=r-a,e=2*n,i=1;i<=e;i++)u=i/e,v||(u=1-u),u=a+u*s,r=c+h*Math.cos(u),u=l+y*Math.sin(u),f.push(new THREE.Vector2(r,u))}return o=f[f.length-1],1e-10>Math.abs(o.x-f[0].x)&&1e-10>Math.abs(o.y-f[0].y)&&f.splice(f.length-1,1),t&&f.push(f[0]),f};THREE.Path.prototype.toShapes=function(){for(var e,u,r=[],n=new THREE.Path,t,i=0,f=this.actions.length;i<f;i++)e=this.actions[i],u=e.args,e=e.action,e==THREE.PathActions.MOVE_TO&&0!=n.actions.length&&(r.push(n),n=new THREE.Path),n[e].apply(n,u);if(0!=n.actions.length&&r.push(n),0==r.length)return[];if(u=[],i=!THREE.Shape.Utils.isClockWise(r[0].getPoints()),1==r.length)return n=r[0],t=new THREE.Shape,t.actions=n.actions,t.curves=n.curves,u.push(t),u;if(i)for(t=new THREE.Shape,i=0,f=r.length;i<f;i++)n=r[i],THREE.Shape.Utils.isClockWise(n.getPoints())?(t.actions=n.actions,t.curves=n.curves,u.push(t),t=new THREE.Shape):t.holes.push(n);else{for(i=0,f=r.length;i<f;i++)n=r[i],THREE.Shape.Utils.isClockWise(n.getPoints())?(t&&u.push(t),t=new THREE.Shape,t.actions=n.actions,t.curves=n.curves):t.holes.push(n);u.push(t)}return u};THREE.Shape=function(){THREE.Path.apply(this,arguments);this.holes=[]};THREE.Shape.prototype=Object.create(THREE.Path.prototype);THREE.Shape.prototype.extrude=function(n){return new THREE.ExtrudeGeometry(this,n)};THREE.Shape.prototype.makeGeometry=function(n){return new THREE.ShapeGeometry(this,n)};THREE.Shape.prototype.getPointsHoles=function(n){for(var r=this.holes.length,i=[],t=0;t<r;t++)i[t]=this.holes[t].getTransformedPoints(n,this.bends);return i};THREE.Shape.prototype.getSpacedPointsHoles=function(n){for(var r=this.holes.length,i=[],t=0;t<r;t++)i[t]=this.holes[t].getTransformedSpacedPoints(n,this.bends);return i};THREE.Shape.prototype.extractAllPoints=function(n){return{shape:this.getTransformedPoints(n),holes:this.getPointsHoles(n)}};THREE.Shape.prototype.extractPoints=function(n){return this.useSpacedPoints?this.extractAllSpacedPoints(n):this.extractAllPoints(n)};THREE.Shape.prototype.extractAllSpacedPoints=function(n){return{shape:this.getTransformedSpacedPoints(n),holes:this.getSpacedPointsHoles(n)}};THREE.Shape.Utils={removeHoles:function(n,t){for(var u=n.concat(),w=u.concat(),e,o,i,r,f,s,h,c,l,p=[],a,v,y=0;y<t.length;y++){for(f=t[y],Array.prototype.push.apply(w,f),o=Number.POSITIVE_INFINITY,e=0;e<f.length;e++)for(c=f[e],l=[],h=0;h<u.length;h++)s=u[h],s=c.distanceToSquared(s),l.push(s),s<o&&(o=s,i=e,r=h);e=0<=r-1?r-1:u.length-1;o=0<=i-1?i-1:f.length-1;a=[f[i],u[r],u[e]];h=THREE.FontUtils.Triangulate.area(a);v=[f[i],f[o],u[r]];c=THREE.FontUtils.Triangulate.area(v);l=r;s=i;r+=1;i+=-1;0>r&&(r+=u.length);r%=u.length;0>i&&(i+=f.length);i%=f.length;e=0<=r-1?r-1:u.length-1;o=0<=i-1?i-1:f.length-1;a=[f[i],u[r],u[e]];a=THREE.FontUtils.Triangulate.area(a);v=[f[i],f[o],u[r]];v=THREE.FontUtils.Triangulate.area(v);h+c>a+v&&(r=l,i=s,0>r&&(r+=u.length),r%=u.length,0>i&&(i+=f.length),i%=f.length,e=0<=r-1?r-1:u.length-1,o=0<=i-1?i-1:f.length-1);h=u.slice(0,r);c=u.slice(r);l=f.slice(i);s=f.slice(0,i);o=[f[i],f[o],u[r]];p.push([f[i],u[r],u[e]]);p.push(o);u=h.concat(l).concat(s).concat(c)}return{shape:u,isolatedPts:p,allpoints:w}},triangulateShape:function(n,t){for(var e=THREE.Shape.Utils.removeHoles(n,t),i=e.allpoints,h=e.isolatedPts,e=THREE.FontUtils.Triangulate(e.shape,!1),f,u,s={},r=0,o=i.length;r<o;r++)u=i[r].x+":"+i[r].y,void 0!==s[u]&&console.log("Duplicate point",u),s[u]=r;for(r=0,o=e.length;r<o;r++)for(f=e[r],i=0;3>i;i++)u=f[i].x+":"+f[i].y,u=s[u],void 0!==u&&(f[i]=u);for(r=0,o=h.length;r<o;r++)for(f=h[r],i=0;3>i;i++)u=f[i].x+":"+f[i].y,u=s[u],void 0!==u&&(f[i]=u);return e.concat(h)},isClockWise:function(n){return 0>THREE.FontUtils.Triangulate.area(n)},b2p0:function(n,t){var i=1-n;return i*i*t},b2p1:function(n,t){return 2*(1-n)*n*t},b2p2:function(n,t){return n*n*t},b2:function(n,t,i,r){return this.b2p0(n,t)+this.b2p1(n,i)+this.b2p2(n,r)},b3p0:function(n,t){var i=1-n;return i*i*i*t},b3p1:function(n,t){var i=1-n;return 3*i*i*n*t},b3p2:function(n,t){return 3*(1-n)*n*n*t},b3p3:function(n,t){return n*n*n*t},b3:function(n,t,i,r,u){return this.b3p0(n,t)+this.b3p1(n,i)+this.b3p2(n,r)+this.b3p3(n,u)}};THREE.AnimationHandler=function(){var n=[],t={},i={update:function(t){for(var i=0;i<n.length;i++)n[i].update(t)},addToUpdate:function(t){-1===n.indexOf(t)&&n.push(t)},removeFromUpdate:function(t){t=n.indexOf(t);-1!==t&&n.splice(t,1)},add:function(n){var r,i,f,u,e,o;if(void 0!==t[n.name]&&console.log("THREE.AnimationHandler.add: Warning! "+n.name+" already exists in library. Overwriting."),t[n.name]=n,!0!==n.initialized){for(r=0;r<n.hierarchy.length;r++){for(i=0;i<n.hierarchy[r].keys.length;i++)(0>n.hierarchy[r].keys[i].time&&(n.hierarchy[r].keys[i].time=0),void 0===n.hierarchy[r].keys[i].rot||n.hierarchy[r].keys[i].rot instanceof THREE.Quaternion)||(f=n.hierarchy[r].keys[i].rot,n.hierarchy[r].keys[i].rot=new THREE.Quaternion(f[0],f[1],f[2],f[3]));if(n.hierarchy[r].keys.length&&void 0!==n.hierarchy[r].keys[0].morphTargets){for(f={},i=0;i<n.hierarchy[r].keys.length;i++)for(u=0;u<n.hierarchy[r].keys[i].morphTargets.length;u++)e=n.hierarchy[r].keys[i].morphTargets[u],f[e]=-1;for(n.hierarchy[r].usedMorphTargets=f,i=0;i<n.hierarchy[r].keys.length;i++){o={};for(e in f){for(u=0;u<n.hierarchy[r].keys[i].morphTargets.length;u++)if(n.hierarchy[r].keys[i].morphTargets[u]===e){o[e]=n.hierarchy[r].keys[i].morphTargetsInfluences[u];break}u===n.hierarchy[r].keys[i].morphTargets.length&&(o[e]=0)}n.hierarchy[r].keys[i].morphTargetsInfluences=o}}for(i=1;i<n.hierarchy[r].keys.length;i++)n.hierarchy[r].keys[i].time===n.hierarchy[r].keys[i-1].time&&(n.hierarchy[r].keys.splice(i,1),i--);for(i=0;i<n.hierarchy[r].keys.length;i++)n.hierarchy[r].keys[i].index=i}for(i=parseInt(n.length*n.fps,10),n.JIT={},n.JIT.hierarchy=[],r=0;r<n.hierarchy.length;r++)n.JIT.hierarchy.push(Array(i));n.initialized=!0}},get:function(n){if("string"==typeof n)return t[n]?t[n]:(console.log("THREE.AnimationHandler.get: Couldn't find animation "+n),null)},parse:function(n){var i=[],t;if(n instanceof THREE.SkinnedMesh)for(t=0;t<n.bones.length;t++)i.push(n.bones[t]);else r(n,i);return i}},r=function(n,t){t.push(n);for(var i=0;i<n.children.length;i++)r(n.children[i],t)};return i.LINEAR=0,i.CATMULLROM=1,i.CATMULLROM_FORWARD=2,i}();THREE.Animation=function(n,t,i){this.root=n;this.data=THREE.AnimationHandler.get(t);this.hierarchy=THREE.AnimationHandler.parse(n);this.currentTime=0;this.timeScale=1;this.isPlaying=!1;this.loop=this.isPaused=!0;this.interpolationType=void 0!==i?i:THREE.AnimationHandler.LINEAR;this.points=[];this.target=new THREE.Vector3};THREE.Animation.prototype.play=function(n,t){var r,f,i,u;if(!1===this.isPlaying){for(this.isPlaying=!0,this.loop=void 0!==n?n:!0,this.currentTime=void 0!==t?t:0,f=this.hierarchy.length,r=0;r<f;r++)i=this.hierarchy[r],this.interpolationType!==THREE.AnimationHandler.CATMULLROM_FORWARD&&(i.useQuaternion=!0),i.matrixAutoUpdate=!0,void 0===i.animationCache&&(i.animationCache={},i.animationCache.prevKey={pos:0,rot:0,scl:0},i.animationCache.nextKey={pos:0,rot:0,scl:0},i.animationCache.originalMatrix=i instanceof THREE.Bone?i.skinMatrix:i.matrix),u=i.animationCache.prevKey,i=i.animationCache.nextKey,u.pos=this.data.hierarchy[r].keys[0],u.rot=this.data.hierarchy[r].keys[0],u.scl=this.data.hierarchy[r].keys[0],i.pos=this.getNextKeyWith("pos",r,1),i.rot=this.getNextKeyWith("rot",r,1),i.scl=this.getNextKeyWith("scl",r,1);this.update(0)}this.isPaused=!1;THREE.AnimationHandler.addToUpdate(this)};THREE.Animation.prototype.pause=function(){!0===this.isPaused?THREE.AnimationHandler.addToUpdate(this):THREE.AnimationHandler.removeFromUpdate(this);this.isPaused=!this.isPaused};THREE.Animation.prototype.stop=function(){this.isPaused=this.isPlaying=!1;THREE.AnimationHandler.removeFromUpdate(this)};THREE.Animation.prototype.update=function(n){var a,t,i,r,e,o,u,h,s,l,f,v,c;if(!1!==this.isPlaying)for(a=["pos","rot","scl"],l=this.currentTime+=n*this.timeScale,s=this.currentTime%=this.data.length,parseInt(Math.min(s*this.data.fps,this.data.length*this.data.fps),10),f=0,v=this.hierarchy.length;f<v;f++)for(n=this.hierarchy[f],h=n.animationCache,c=0;3>c;c++){if(t=a[c],o=h.prevKey[t],u=h.nextKey[t],u.time<=l){if(s<l)if(this.loop)for(o=this.data.hierarchy[f].keys[0],u=this.getNextKeyWith(t,f,1);u.time<s;)o=u,u=this.getNextKeyWith(t,f,u.index+1);else{this.stop();return}else do o=u,u=this.getNextKeyWith(t,f,u.index+1);while(u.time<s);h.prevKey[t]=o;h.nextKey[t]=u}n.matrixAutoUpdate=!0;n.matrixWorldNeedsUpdate=!0;i=(s-o.time)/(u.time-o.time);r=o[t];e=u[t];(0>i||1<i)&&(console.log("THREE.Animation.update: Warning! Scale out of bounds:"+i+" on bone "+f),i=0>i?0:1);"pos"===t?(t=n.position,this.interpolationType===THREE.AnimationHandler.LINEAR)?(t.x=r[0]+(e[0]-r[0])*i,t.y=r[1]+(e[1]-r[1])*i,t.z=r[2]+(e[2]-r[2])*i):(this.interpolationType===THREE.AnimationHandler.CATMULLROM||this.interpolationType===THREE.AnimationHandler.CATMULLROM_FORWARD)&&(this.points[0]=this.getPrevKeyWith("pos",f,o.index-1).pos,this.points[1]=r,this.points[2]=e,this.points[3]=this.getNextKeyWith("pos",f,u.index+1).pos,i=.33*i+.33,r=this.interpolateCatmullRom(this.points,i),t.x=r[0],t.y=r[1],t.z=r[2],this.interpolationType===THREE.AnimationHandler.CATMULLROM_FORWARD&&(i=this.interpolateCatmullRom(this.points,1.01*i),this.target.set(i[0],i[1],i[2]),this.target.sub(t),this.target.y=0,this.target.normalize(),i=Math.atan2(this.target.x,this.target.z),n.rotation.set(0,i,0))):"rot"===t?THREE.Quaternion.slerp(r,e,n.quaternion,i):"scl"===t&&(t=n.scale,t.x=r[0]+(e[0]-r[0])*i,t.y=r[1]+(e[1]-r[1])*i,t.z=r[2]+(e[2]-r[2])*i)}};THREE.Animation.prototype.interpolateCatmullRom=function(n,t){var r=[],f=[],u,i,e,o,s,h;return u=(n.length-1)*t,i=Math.floor(u),u-=i,r[0]=0===i?i:i-1,r[1]=i,r[2]=i>n.length-2?i:i+1,r[3]=i>n.length-3?i:i+2,i=n[r[0]],o=n[r[1]],s=n[r[2]],h=n[r[3]],r=u*u,e=u*r,f[0]=this.interpolate(i[0],o[0],s[0],h[0],u,r,e),f[1]=this.interpolate(i[1],o[1],s[1],h[1],u,r,e),f[2]=this.interpolate(i[2],o[2],s[2],h[2],u,r,e),f};THREE.Animation.prototype.interpolate=function(n,t,i,r,u,f,e){return n=.5*(i-n),r=.5*(r-t),(2*(t-i)+n+r)*e+(-3*(t-i)-2*n-r)*f+n*u+t};THREE.Animation.prototype.getNextKeyWith=function(n,t,i){for(var r=this.data.hierarchy[t].keys,i=this.interpolationType===THREE.AnimationHandler.CATMULLROM||this.interpolationType===THREE.AnimationHandler.CATMULLROM_FORWARD?i<r.length-1?i:r.length-1:i%r.length;i<r.length;i++)if(void 0!==r[i][n])return r[i];return this.data.hierarchy[t].keys[0]};THREE.Animation.prototype.getPrevKeyWith=function(n,t,i){for(var r=this.data.hierarchy[t].keys,i=this.interpolationType===THREE.AnimationHandler.CATMULLROM||this.interpolationType===THREE.AnimationHandler.CATMULLROM_FORWARD?0<i?i:0:0<=i?i:i+r.length;0<=i;i--)if(void 0!==r[i][n])return r[i];return this.data.hierarchy[t].keys[r.length-1]};THREE.KeyFrameAnimation=function(n,t,i){var i,u,r,f,e;for(this.root=n,this.data=THREE.AnimationHandler.get(t),this.hierarchy=THREE.AnimationHandler.parse(n),this.currentTime=0,this.timeScale=.001,this.isPlaying=!1,this.loop=this.isPaused=!0,this.JITCompile=void 0!==i?i:!0,n=0,t=this.hierarchy.length;n<t;n++)if(i=this.data.hierarchy[n].sids,u=this.hierarchy[n],this.data.hierarchy[n].keys.length&&i){for(r=0;r<i.length;r++)f=i[r],e=this.getNextKeyWith(f,n,0),e&&e.apply(f);u.matrixAutoUpdate=!1;this.data.hierarchy[n].node.updateMatrix();u.matrixWorldNeedsUpdate=!0}};THREE.KeyFrameAnimation.prototype.play=function(n,t){if(!this.isPlaying){this.isPlaying=!0;this.loop=void 0!==n?n:!0;this.currentTime=void 0!==t?t:0;this.startTimeMs=t;this.startTime=1e7;this.endTime=-this.startTime;for(var f=this.hierarchy.length,i,r,u=0;u<f;u++)i=this.hierarchy[u],r=this.data.hierarchy[u],i.useQuaternion=!0,void 0===r.animationCache&&(r.animationCache={},r.animationCache.prevKey=null,r.animationCache.nextKey=null,r.animationCache.originalMatrix=i instanceof THREE.Bone?i.skinMatrix:i.matrix),i=this.data.hierarchy[u].keys,i.length&&(r.animationCache.prevKey=i[0],r.animationCache.nextKey=i[1],this.startTime=Math.min(i[0].time,this.startTime),this.endTime=Math.max(i[i.length-1].time,this.endTime));this.update(0)}this.isPaused=!1;THREE.AnimationHandler.addToUpdate(this)};THREE.KeyFrameAnimation.prototype.pause=function(){this.isPaused?THREE.AnimationHandler.addToUpdate(this):THREE.AnimationHandler.removeFromUpdate(this);this.isPaused=!this.isPaused};THREE.KeyFrameAnimation.prototype.stop=function(){var t,n,r,i;for(this.isPaused=this.isPlaying=!1,THREE.AnimationHandler.removeFromUpdate(this),t=0;t<this.data.hierarchy.length;t++)n=this.hierarchy[t],r=this.data.hierarchy[t],void 0!==r.animationCache&&(i=r.animationCache.originalMatrix,n instanceof THREE.Bone?(i.copy(n.skinMatrix),n.skinMatrix=i):(i.copy(n.matrix),n.matrix=i),delete r.animationCache)};THREE.KeyFrameAnimation.prototype.update=function(n){var u,t,f,o,i,h,l,n,c,e,r,s,a;if(this.isPlaying)if(e=this.data.JIT.hierarchy,h=this.currentTime+=n*this.timeScale,i=this.currentTime%=this.data.length,i<this.startTimeMs&&(i=this.currentTime=this.startTimeMs+i),o=parseInt(Math.min(i*this.data.fps,this.data.length*this.data.fps),10),(l=i<h)&&!this.loop){for(n=0,c=this.hierarchy.length;n<c;n++)if(r=this.data.hierarchy[n].keys,e=this.data.hierarchy[n].sids,f=r.length-1,o=this.hierarchy[n],r.length){for(r=0;r<e.length;r++)i=e[r],(h=this.getPrevKeyWith(i,n,f))&&h.apply(i);this.data.hierarchy[n].node.updateMatrix();o.matrixWorldNeedsUpdate=!0}this.stop()}else if(!(i<this.startTime)){for(n=0,c=this.hierarchy.length;n<c;n++)if(f=this.hierarchy[n],u=this.data.hierarchy[n],r=u.keys,s=u.animationCache,this.JITCompile&&void 0!==e[n][o])f instanceof THREE.Bone?(f.skinMatrix=e[n][o],f.matrixWorldNeedsUpdate=!1):(f.matrix=e[n][o],f.matrixWorldNeedsUpdate=!0);else if(r.length){if(this.JITCompile&&s&&(f instanceof THREE.Bone?f.skinMatrix=s.originalMatrix:f.matrix=s.originalMatrix),u=s.prevKey,t=s.nextKey,u&&t){if(t.time<=h){if(l&&this.loop)for(u=r[0],t=r[1];t.time<i;)u=t,t=r[u.index+1];else if(!l)for(a=r.length-1;t.time<i&&t.index!==a;)u=t,t=r[u.index+1];s.prevKey=u;s.nextKey=t}t.time>=i?u.interpolate(t,i):u.interpolate(t,t.time)}this.data.hierarchy[n].node.updateMatrix();f.matrixWorldNeedsUpdate=!0}if(this.JITCompile&&void 0===e[0][o])for(this.hierarchy[0].updateMatrixWorld(!0),n=0;n<this.hierarchy.length;n++)e[n][o]=this.hierarchy[n]instanceof THREE.Bone?this.hierarchy[n].skinMatrix.clone():this.hierarchy[n].matrix.clone()}};THREE.KeyFrameAnimation.prototype.getNextKeyWith=function(n,t,i){for(t=this.data.hierarchy[t].keys,i%=t.length;i<t.length;i++)if(t[i].hasTarget(n))return t[i];return t[0]};THREE.KeyFrameAnimation.prototype.getPrevKeyWith=function(n,t,i){for(t=this.data.hierarchy[t].keys,i=0<=i?i:i+t.length;0<=i;i--)if(t[i].hasTarget(n))return t[i];return t[t.length-1]};THREE.CubeCamera=function(n,t,i){var r,u,f,e,o,s;THREE.Object3D.call(this);r=new THREE.PerspectiveCamera(90,1,n,t);r.up.set(0,-1,0);r.lookAt(new THREE.Vector3(1,0,0));this.add(r);u=new THREE.PerspectiveCamera(90,1,n,t);u.up.set(0,-1,0);u.lookAt(new THREE.Vector3(-1,0,0));this.add(u);f=new THREE.PerspectiveCamera(90,1,n,t);f.up.set(0,0,1);f.lookAt(new THREE.Vector3(0,1,0));this.add(f);e=new THREE.PerspectiveCamera(90,1,n,t);e.up.set(0,0,-1);e.lookAt(new THREE.Vector3(0,-1,0));this.add(e);o=new THREE.PerspectiveCamera(90,1,n,t);o.up.set(0,-1,0);o.lookAt(new THREE.Vector3(0,0,1));this.add(o);s=new THREE.PerspectiveCamera(90,1,n,t);s.up.set(0,-1,0);s.lookAt(new THREE.Vector3(0,0,-1));this.add(s);this.renderTarget=new THREE.WebGLRenderTargetCube(i,i,{format:THREE.RGBFormat,magFilter:THREE.LinearFilter,minFilter:THREE.LinearFilter});this.updateCubeMap=function(n,t){var i=this.renderTarget,h=i.generateMipmaps;i.generateMipmaps=!1;i.activeCubeFace=0;n.render(t,r,i);i.activeCubeFace=1;n.render(t,u,i);i.activeCubeFace=2;n.render(t,f,i);i.activeCubeFace=3;n.render(t,e,i);i.activeCubeFace=4;n.render(t,o,i);i.generateMipmaps=h;i.activeCubeFace=5;n.render(t,s,i)}};THREE.CubeCamera.prototype=Object.create(THREE.Object3D.prototype);THREE.CombinedCamera=function(n,t,i,r,u,f,e){THREE.Camera.call(this);this.fov=i;this.left=-n/2;this.right=n/2;this.top=t/2;this.bottom=-t/2;this.cameraO=new THREE.OrthographicCamera(n/-2,n/2,t/2,t/-2,f,e);this.cameraP=new THREE.PerspectiveCamera(i,n/t,r,u);this.zoom=1;this.toPerspective()};THREE.CombinedCamera.prototype=Object.create(THREE.Camera.prototype);THREE.CombinedCamera.prototype.toPerspective=function(){this.near=this.cameraP.near;this.far=this.cameraP.far;this.cameraP.fov=this.fov/this.zoom;this.cameraP.updateProjectionMatrix();this.projectionMatrix=this.cameraP.projectionMatrix;this.inPerspectiveMode=!0;this.inOrthographicMode=!1};THREE.CombinedCamera.prototype.toOrthographic=function(){var t=this.cameraP.aspect,n=(this.cameraP.near+this.cameraP.far)/2,n=Math.tan(this.fov/2)*n,t=2*n*t/2,n=n/this.zoom,t=t/this.zoom;this.cameraO.left=-t;this.cameraO.right=t;this.cameraO.top=n;this.cameraO.bottom=-n;this.cameraO.updateProjectionMatrix();this.near=this.cameraO.near;this.far=this.cameraO.far;this.projectionMatrix=this.cameraO.projectionMatrix;this.inPerspectiveMode=!1;this.inOrthographicMode=!0};THREE.CombinedCamera.prototype.setSize=function(n,t){this.cameraP.aspect=n/t;this.left=-n/2;this.right=n/2;this.top=t/2;this.bottom=-t/2};THREE.CombinedCamera.prototype.setFov=function(n){this.fov=n;this.inPerspectiveMode?this.toPerspective():this.toOrthographic()};THREE.CombinedCamera.prototype.updateProjectionMatrix=function(){this.inPerspectiveMode?this.toPerspective():(this.toPerspective(),this.toOrthographic())};THREE.CombinedCamera.prototype.setLens=function(n,t){void 0===t&&(t=24);var i=2*THREE.Math.radToDeg(Math.atan(t/(2*n)));return this.setFov(i),i};THREE.CombinedCamera.prototype.setZoom=function(n){this.zoom=n;this.inPerspectiveMode?this.toPerspective():this.toOrthographic()};THREE.CombinedCamera.prototype.toFrontView=function(){this.rotation.x=0;this.rotation.y=0;this.rotation.z=0;this.rotationAutoUpdate=!1};THREE.CombinedCamera.prototype.toBackView=function(){this.rotation.x=0;this.rotation.y=Math.PI;this.rotation.z=0;this.rotationAutoUpdate=!1};THREE.CombinedCamera.prototype.toLeftView=function(){this.rotation.x=0;this.rotation.y=-Math.PI/2;this.rotation.z=0;this.rotationAutoUpdate=!1};THREE.CombinedCamera.prototype.toRightView=function(){this.rotation.x=0;this.rotation.y=Math.PI/2;this.rotation.z=0;this.rotationAutoUpdate=!1};THREE.CombinedCamera.prototype.toTopView=function(){this.rotation.x=-Math.PI/2;this.rotation.y=0;this.rotation.z=0;this.rotationAutoUpdate=!1};THREE.CombinedCamera.prototype.toBottomView=function(){this.rotation.x=Math.PI/2;this.rotation.y=0;this.rotation.z=0;this.rotationAutoUpdate=!1};THREE.AsteriskGeometry=function(n,t){THREE.Geometry.call(this);for(var r=.707*n,i=.707*t,r=[[n,0,0],[t,0,0],[-n,0,0],[-t,0,0],[0,n,0],[0,t,0],[0,-n,0],[0,-t,0],[0,0,n],[0,0,t],[0,0,-n],[0,0,-t],[r,r,0],[i,i,0],[-r,-r,0],[-i,-i,0],[r,-r,0],[i,-i,0],[-r,r,0],[-i,i,0],[r,0,r],[i,0,i],[-r,0,-r],[-i,0,-i],[r,0,-r],[i,0,-i],[-r,0,r],[-i,0,i],[0,r,r],[0,i,i],[0,-r,-r],[0,-i,-i],[0,r,-r],[0,i,-i],[0,-r,r],[0,-i,i]],i=0,u=r.length;i<u;i++)this.vertices.push(new THREE.Vector3(r[i][0],r[i][1],r[i][2]))};THREE.AsteriskGeometry.prototype=Object.create(THREE.Geometry.prototype);THREE.CircleGeometry=function(n,t,i,r){var o,f;THREE.Geometry.call(this);var n=n||50,i=void 0!==i?i:0,r=void 0!==r?r:2*Math.PI,t=void 0!==t?Math.max(3,t):8,u,e=[];for(u=new THREE.Vector3,o=new THREE.Vector2(.5,.5),this.vertices.push(u),e.push(o),u=0;u<=t;u++)f=new THREE.Vector3,f.x=n*Math.cos(i+u/t*r),f.y=n*Math.sin(i+u/t*r),this.vertices.push(f),e.push(new THREE.Vector2((f.x/n+1)/2,-(f.y/n+1)/2+1));for(i=new THREE.Vector3(0,0,-1),u=1;u<=t;u++)this.faces.push(new THREE.Face3(u,u+1,0,[i,i,i])),this.faceVertexUvs[0].push([e[u],e[u+1],o]);this.computeCentroids();this.computeFaceNormals();this.boundingSphere=new THREE.Sphere(new THREE.Vector3,n)};THREE.CircleGeometry.prototype=Object.create(THREE.Geometry.prototype);THREE.CubeGeometry=function(n,t,i,r,u,f){function o(n,t,i,r,u,f,o,s){var a,h=e.widthSegments,c=e.heightSegments,w=u/2,b=f/2,p=e.vertices.length,y;"x"===n&&"y"===t||"y"===n&&"x"===t?a="z":"x"===n&&"z"===t||"z"===n&&"x"===t?(a="y",c=e.depthSegments):("z"===n&&"y"===t||"y"===n&&"z"===t)&&(a="x",h=e.depthSegments);var v=h+1,k=c+1,d=u/h,g=f/c,l=new THREE.Vector3;for(l[a]=0<o?1:-1,u=0;u<k;u++)for(f=0;f<v;f++)y=new THREE.Vector3,y[n]=(f*d-w)*i,y[t]=(u*g-b)*r,y[a]=o,e.vertices.push(y);for(u=0;u<c;u++)for(f=0;f<h;f++)n=new THREE.Face4(f+v*u+p,f+v*(u+1)+p,f+1+v*(u+1)+p,f+1+v*u+p),n.normal.copy(l),n.vertexNormals.push(l.clone(),l.clone(),l.clone(),l.clone()),n.materialIndex=s,e.faces.push(n),e.faceVertexUvs[0].push([new THREE.Vector2(f/h,1-u/c),new THREE.Vector2(f/h,1-(u+1)/c),new THREE.Vector2((f+1)/h,1-(u+1)/c),new THREE.Vector2((f+1)/h,1-u/c)])}THREE.Geometry.call(this);var e=this;this.width=n;this.height=t;this.depth=i;this.widthSegments=r||1;this.heightSegments=u||1;this.depthSegments=f||1;n=this.width/2;t=this.height/2;i=this.depth/2;o("z","y",-1,-1,this.depth,this.height,n,0);o("z","y",1,-1,this.depth,this.height,-n,1);o("x","z",1,1,this.width,this.depth,t,2);o("x","z",1,-1,this.width,this.depth,-t,3);o("x","y",1,-1,this.width,this.height,i,4);o("x","y",-1,-1,this.width,this.height,-i,5);this.computeCentroids();this.mergeVertices()};THREE.CubeGeometry.prototype=Object.create(THREE.Geometry.prototype);THREE.CylinderGeometry=function(n,t,i,r,u,f){var a,w;THREE.Geometry.call(this);for(var n=void 0!==n?n:20,t=void 0!==t?t:20,i=void 0!==i?i:100,tt=i/2,r=r||8,u=u||1,e,s=[],l=[],o=0;o<=u;o++){var h=[],c=[],v=o/u,y=v*(t-n)+n;for(e=0;e<=r;e++)a=e/r,w=new THREE.Vector3,w.x=y*Math.sin(2*a*Math.PI),w.y=-v*i+tt,w.z=y*Math.cos(2*a*Math.PI),this.vertices.push(w),h.push(this.vertices.length-1),c.push(new THREE.Vector2(a,1-v));s.push(h);l.push(c)}for(i=(t-n)/i,e=0;e<r;e++)for(0!==n?(h=this.vertices[s[0][e]].clone(),c=this.vertices[s[0][e+1]].clone()):(h=this.vertices[s[1][e]].clone(),c=this.vertices[s[1][e+1]].clone()),h.setY(Math.sqrt(h.x*h.x+h.z*h.z)*i).normalize(),c.setY(Math.sqrt(c.x*c.x+c.z*c.z)*i).normalize(),o=0;o<u;o++){var v=s[o][e],y=s[o+1][e],a=s[o+1][e+1],w=s[o][e+1],b=h.clone(),k=h.clone(),d=c.clone(),it=c.clone(),g=l[o][e].clone(),p=l[o+1][e].clone(),nt=l[o+1][e+1].clone(),rt=l[o][e+1].clone();this.faces.push(new THREE.Face4(v,y,a,w,[b,k,d,it]));this.faceVertexUvs[0].push([g,p,nt,rt])}if(!f&&0<n)for(this.vertices.push(new THREE.Vector3(0,tt,0)),e=0;e<r;e++)v=s[0][e],y=s[0][e+1],a=this.vertices.length-1,b=new THREE.Vector3(0,1,0),k=new THREE.Vector3(0,1,0),d=new THREE.Vector3(0,1,0),g=l[0][e].clone(),p=l[0][e+1].clone(),nt=new THREE.Vector2(p.u,0),this.faces.push(new THREE.Face3(v,y,a,[b,k,d])),this.faceVertexUvs[0].push([g,p,nt]);if(!f&&0<t)for(this.vertices.push(new THREE.Vector3(0,-tt,0)),e=0;e<r;e++)v=s[o][e+1],y=s[o][e],a=this.vertices.length-1,b=new THREE.Vector3(0,-1,0),k=new THREE.Vector3(0,-1,0),d=new THREE.Vector3(0,-1,0),g=l[o][e+1].clone(),p=l[o][e].clone(),nt=new THREE.Vector2(p.u,1),this.faces.push(new THREE.Face3(v,y,a,[b,k,d])),this.faceVertexUvs[0].push([g,p,nt]);this.computeCentroids();this.computeFaceNormals()};THREE.CylinderGeometry.prototype=Object.create(THREE.Geometry.prototype);THREE.ExtrudeGeometry=function(n,t){"undefined"!=typeof n&&(THREE.Geometry.call(this),n=n instanceof Array?n:[n],this.shapebb=n[n.length-1].getBoundingBox(),this.addShapeList(n,t),this.computeCentroids(),this.computeFaceNormals())};THREE.ExtrudeGeometry.prototype=Object.create(THREE.Geometry.prototype);THREE.ExtrudeGeometry.prototype.addShapeList=function(n,t){for(var r=n.length,i=0;i<r;i++)this.addShape(n[i],t)};THREE.ExtrudeGeometry.prototype.addShape=function(n,t){function rt(n,t,i){return t||console.log("die"),t.clone().multiplyScalar(i).add(n)}function kt(n,t,i){var u=THREE.ExtrudeGeometry.__v1,e=THREE.ExtrudeGeometry.__v2,f=THREE.ExtrudeGeometry.__v3,r=THREE.ExtrudeGeometry.__v4,o=THREE.ExtrudeGeometry.__v5,s=THREE.ExtrudeGeometry.__v6;return(u.set(n.x-t.x,n.y-t.y),e.set(n.x-i.x,n.y-i.y),u=u.normalize(),e=e.normalize(),f.set(-u.y,u.x),r.set(e.y,-e.x),o.copy(n).add(f),s.copy(n).add(r),o.equals(s))?r.clone():(o.copy(t).add(f),s.copy(i).add(r),f=u.dot(r),r=s.sub(o).dot(r),0===f&&(console.log("Either infinite or no solutions!"),0===r?console.log("Its finite solutions."):console.log("Too bad, no solutions.")),r/=f,0>r?(t=Math.atan2(t.y-n.y,t.x-n.x),n=Math.atan2(i.y-n.y,i.x-n.x),t>n&&(n+=2*Math.PI),i=(t+n)/2,n=-Math.cos(i),i=-Math.sin(i),new THREE.Vector2(n,i)):u.multiplyScalar(r).add(o).sub(n).clone())}function dt(r,u){var c,f;for(i=r.length;0<=--i;){c=i;f=i-1;0>f&&(f=r.length-1);for(var e=0,a=p+2*nt,e=0;e<a;e++){var o=k*e,s=k*(e+1),h=u+c+o,o=u+f+o,l=u+f+s,s=u+c+s,v=r,y=e,w=a,b=c,g=f,h=h+tt,o=o+tt,l=l+tt,s=s+tt;d.faces.push(new THREE.Face4(h,o,l,s,null,null,ni));h=bt.generateSideWallUV(d,n,v,t,h,o,l,s,y,w,b,g);d.faceVertexUvs[0].push(h)}}}function w(n,t,i){d.vertices.push(new THREE.Vector3(n,t,i))}function lt(i,r,u,f){i+=tt;r+=tt;u+=tt;d.faces.push(new THREE.Face3(i,r,u,null,null,gt));i=f?bt.generateBottomUV(d,n,t,i,r,u):bt.generateTopUV(d,n,t,i,r,u);d.faceVertexUvs[0].push(i)}var f=void 0!==t.amount?t.amount:100,a=void 0!==t.bevelThickness?t.bevelThickness:6,at=void 0!==t.bevelSize?t.bevelSize:a-2,nt=void 0!==t.bevelSegments?t.bevelSegments:3,ft=void 0!==t.bevelEnabled?t.bevelEnabled:!0,h=void 0!==t.curveSegments?t.curveSegments:12,p=void 0!==t.steps?t.steps:1,l=t.extrudePath,et,vt=!1,gt=t.material,ni=t.extrudeMaterial,bt=void 0!==t.UVGenerator?t.UVGenerator:THREE.ExtrudeGeometry.WorldUVGenerator,ot,st,ht,b,ut,wt,g,ct;l&&(et=l.getSpacedPoints(p),vt=!0,ft=!1,ot=void 0!==t.frames?t.frames:new THREE.TubeGeometry.FrenetFrames(l,p,!1),st=new THREE.Vector3,ht=new THREE.Vector3,b=new THREE.Vector3);ft||(at=a=nt=0);var r,u,v,d=this,tt=this.vertices.length,h=n.extractPoints(h),y=h.shape,h=h.holes;if(l=!THREE.Shape.Utils.isClockWise(y)){for(y=y.reverse(),u=0,v=h.length;u<v;u++)r=h[u],THREE.Shape.Utils.isClockWise(r)&&(h[u]=r.reverse());l=!1}for(ut=THREE.Shape.Utils.triangulateShape(y,h),l=y,u=0,v=h.length;u<v;u++)r=h[u],y=y.concat(r);for(var it,e,k=y.length,yt=ut.length,pt=[],i=0,c=l.length,s=c-1,o=i+1;i<c;i++,s++,o++)s===c&&(s=0),o===c&&(o=0),pt[i]=kt(l[i],l[s],l[o]);for(wt=[],ct=pt.concat(),u=0,v=h.length;u<v;u++){for(r=h[u],g=[],i=0,c=r.length,s=c-1,o=i+1;i<c;i++,s++,o++)s===c&&(s=0),o===c&&(o=0),g[i]=kt(r[i],r[s],r[o]);wt.push(g);ct=ct.concat(g)}for(s=0;s<nt;s++){for(r=s/nt,it=a*(1-r),o=at*Math.sin(r*Math.PI/2),i=0,c=l.length;i<c;i++)e=rt(l[i],pt[i],o),w(e.x,e.y,-it);for(u=0,v=h.length;u<v;u++)for(r=h[u],g=wt[u],i=0,c=r.length;i<c;i++)e=rt(r[i],g[i],o),w(e.x,e.y,-it)}for(o=at,i=0;i<k;i++)e=ft?rt(y[i],ct[i],o):y[i],vt?(ht.copy(ot.normals[0]).multiplyScalar(e.x),st.copy(ot.binormals[0]).multiplyScalar(e.y),b.copy(et[0]).add(ht).add(st),w(b.x,b.y,b.z)):w(e.x,e.y,0);for(r=1;r<=p;r++)for(i=0;i<k;i++)e=ft?rt(y[i],ct[i],o):y[i],vt?(ht.copy(ot.normals[r]).multiplyScalar(e.x),st.copy(ot.binormals[r]).multiplyScalar(e.y),b.copy(et[r]).add(ht).add(st),w(b.x,b.y,b.z)):w(e.x,e.y,f/p*r);for(s=nt-1;0<=s;s--){for(r=s/nt,it=a*(1-r),o=at*Math.sin(r*Math.PI/2),i=0,c=l.length;i<c;i++)e=rt(l[i],pt[i],o),w(e.x,e.y,f+it);for(u=0,v=h.length;u<v;u++)for(r=h[u],g=wt[u],i=0,c=r.length;i<c;i++)e=rt(r[i],g[i],o),vt?w(e.x,e.y+et[p-1].y,et[p-1].x+it):w(e.x,e.y,f+it)}if(ft){for(a=0*k,i=0;i<yt;i++)f=ut[i],lt(f[2]+a,f[1]+a,f[0]+a,!0);for(a=k*(p+2*nt),i=0;i<yt;i++)f=ut[i],lt(f[0]+a,f[1]+a,f[2]+a,!1)}else{for(i=0;i<yt;i++)f=ut[i],lt(f[2],f[1],f[0],!0);for(i=0;i<yt;i++)f=ut[i],lt(f[0]+k*p,f[1]+k*p,f[2]+k*p,!1)}for(f=0,dt(l,f),f+=l.length,u=0,v=h.length;u<v;u++)r=h[u],dt(r,f),f+=r.length};THREE.ExtrudeGeometry.WorldUVGenerator={generateTopUV:function(n,t,i,r,u,f){return t=n.vertices[u].x,u=n.vertices[u].y,i=n.vertices[f].x,f=n.vertices[f].y,[new THREE.Vector2(n.vertices[r].x,n.vertices[r].y),new THREE.Vector2(t,u),new THREE.Vector2(i,f)]},generateBottomUV:function(n,t,i,r,u,f){return this.generateTopUV(n,t,i,r,u,f)},generateSideWallUV:function(n,t,i,r,u,f,e,o){var t=n.vertices[u].x,i=n.vertices[u].y,u=n.vertices[u].z,r=n.vertices[f].x,s=n.vertices[f].y,f=n.vertices[f].z,h=n.vertices[e].x,c=n.vertices[e].y,e=n.vertices[e].z,l=n.vertices[o].x,a=n.vertices[o].y,n=n.vertices[o].z;return.01>Math.abs(i-s)?[new THREE.Vector2(t,1-u),new THREE.Vector2(r,1-f),new THREE.Vector2(h,1-e),new THREE.Vector2(l,1-n)]:[new THREE.Vector2(i,1-u),new THREE.Vector2(s,1-f),new THREE.Vector2(c,1-e),new THREE.Vector2(a,1-n)]}};THREE.ExtrudeGeometry.__v1=new THREE.Vector2;THREE.ExtrudeGeometry.__v2=new THREE.Vector2;THREE.ExtrudeGeometry.__v3=new THREE.Vector2;THREE.ExtrudeGeometry.__v4=new THREE.Vector2;THREE.ExtrudeGeometry.__v5=new THREE.Vector2;THREE.ExtrudeGeometry.__v6=new THREE.Vector2;THREE.ShapeGeometry=function(n,t){THREE.Geometry.call(this);!1==n instanceof Array&&(n=[n]);this.shapebb=n[n.length-1].getBoundingBox();this.addShapeList(n,t);this.computeCentroids();this.computeFaceNormals()};THREE.ShapeGeometry.prototype=Object.create(THREE.Geometry.prototype);THREE.ShapeGeometry.prototype.addShapeList=function(n,t){for(var i=0,r=n.length;i<r;i++)this.addShape(n[i],t);return this};THREE.ShapeGeometry.prototype.addShape=function(n,t){var u,r,s;void 0===t&&(t={});var h=t.material,c=void 0===t.UVGenerator?THREE.ExtrudeGeometry.WorldUVGenerator:t.UVGenerator,i,e,f,o=this.vertices.length;if(i=n.extractPoints(void 0!==t.curveSegments?t.curveSegments:12),u=i.shape,r=i.holes,!THREE.Shape.Utils.isClockWise(u))for(u=u.reverse(),i=0,e=r.length;i<e;i++)f=r[i],THREE.Shape.Utils.isClockWise(f)&&(r[i]=f.reverse());for(s=THREE.Shape.Utils.triangulateShape(u,r),i=0,e=r.length;i<e;i++)f=r[i],u=u.concat(f);for(r=u.length,e=s.length,i=0;i<r;i++)f=u[i],this.vertices.push(new THREE.Vector3(f.x,f.y,0));for(i=0;i<e;i++)r=s[i],u=r[0]+o,f=r[1]+o,r=r[2]+o,this.faces.push(new THREE.Face3(u,f,r,null,null,h)),this.faceVertexUvs[0].push(c.generateBottomUV(this,n,t,u,f,r))};THREE.LatheGeometry=function(n,t,i,r){var s,h;THREE.Geometry.call(this);for(var t=t||12,i=i||0,r=r||2*Math.PI,v=1/(n.length-1),c=1/t,f=0,l=t;f<=l;f++)for(var u=i+f*c*r,e=Math.cos(u),o=Math.sin(u),u=0,a=n.length;u<a;u++)s=n[u],h=new THREE.Vector3,h.x=e*s.x-o*s.y,h.y=o*s.x+e*s.y,h.z=s.z,this.vertices.push(h);for(i=n.length,f=0,l=t;f<l;f++)for(u=0,a=n.length-1;u<a;u++)r=t=u+i*f,o=t+i,e=t+1+i,this.faces.push(new THREE.Face4(r,o,e,t+1)),e=f*c,t=u*v,r=e+c,o=t+v,this.faceVertexUvs[0].push([new THREE.Vector2(e,t),new THREE.Vector2(r,t),new THREE.Vector2(r,o),new THREE.Vector2(e,o)]);this.mergeVertices();this.computeCentroids();this.computeFaceNormals();this.computeVertexNormals()};THREE.LatheGeometry.prototype=Object.create(THREE.Geometry.prototype);THREE.PlaneGeometry=function(n,t,i,r){THREE.Geometry.call(this);this.width=n;this.height=t;this.widthSegments=i||1;this.heightSegments=r||1;for(var i=n/2,o=t/2,r=this.widthSegments,u=this.heightSegments,f=r+1,s=u+1,h=this.width/r,c=this.height/u,e=new THREE.Vector3(0,0,1),n=0;n<s;n++)for(t=0;t<f;t++)this.vertices.push(new THREE.Vector3(t*h-i,-(n*c-o),0));for(n=0;n<u;n++)for(t=0;t<r;t++)i=new THREE.Face4(t+f*n,t+f*(n+1),t+1+f*(n+1),t+1+f*n),i.normal.copy(e),i.vertexNormals.push(e.clone(),e.clone(),e.clone(),e.clone()),this.faces.push(i),this.faceVertexUvs[0].push([new THREE.Vector2(t/r,1-n/u),new THREE.Vector2(t/r,1-(n+1)/u),new THREE.Vector2((t+1)/r,1-(n+1)/u),new THREE.Vector2((t+1)/r,1-n/u)]);this.computeCentroids()};THREE.PlaneGeometry.prototype=Object.create(THREE.Geometry.prototype);THREE.SphereGeometry=function(n,t,i,r,u,f,e){THREE.Geometry.call(this);this.radius=n||50;this.widthSegments=Math.max(3,Math.floor(t)||8);this.heightSegments=Math.max(2,Math.floor(i)||6);for(var r=void 0!==r?r:0,u=void 0!==u?u:2*Math.PI,f=void 0!==f?f:0,e=void 0!==e?e:Math.PI,c=[],l=[],i=0;i<=this.heightSegments;i++){for(var a=[],v=[],t=0;t<=this.widthSegments;t++){var s=t/this.widthSegments,h=i/this.heightSegments,o=new THREE.Vector3;o.x=-this.radius*Math.cos(r+s*u)*Math.sin(f+h*e);o.y=this.radius*Math.cos(f+h*e);o.z=this.radius*Math.sin(r+s*u)*Math.sin(f+h*e);this.vertices.push(o);a.push(this.vertices.length-1);v.push(new THREE.Vector2(s,1-h))}c.push(a);l.push(v)}for(i=0;i<this.heightSegments;i++)for(t=0;t<this.widthSegments;t++){var r=c[i][t+1],u=c[i][t],f=c[i+1][t],e=c[i+1][t+1],a=this.vertices[r].clone().normalize(),v=this.vertices[u].clone().normalize(),s=this.vertices[f].clone().normalize(),h=this.vertices[e].clone().normalize(),o=l[i][t+1].clone(),p=l[i][t].clone(),y=l[i+1][t].clone(),w=l[i+1][t+1].clone();Math.abs(this.vertices[r].y)===this.radius?(this.faces.push(new THREE.Face3(r,f,e,[a,s,h])),this.faceVertexUvs[0].push([o,y,w])):Math.abs(this.vertices[f].y)===this.radius?(this.faces.push(new THREE.Face3(r,u,f,[a,v,s])),this.faceVertexUvs[0].push([o,p,y])):(this.faces.push(new THREE.Face4(r,u,f,e,[a,v,s,h])),this.faceVertexUvs[0].push([o,p,y,w]))}this.computeCentroids();this.computeFaceNormals();this.boundingSphere=new THREE.Sphere(new THREE.Vector3,n)};THREE.SphereGeometry.prototype=Object.create(THREE.Geometry.prototype);THREE.TextGeometry=function(n,t){var i=THREE.FontUtils.generateShapes(n,t);t.amount=void 0!==t.height?t.height:50;void 0===t.bevelThickness&&(t.bevelThickness=10);void 0===t.bevelSize&&(t.bevelSize=8);void 0===t.bevelEnabled&&(t.bevelEnabled=!1);THREE.ExtrudeGeometry.call(this,i,t)};THREE.TextGeometry.prototype=Object.create(THREE.ExtrudeGeometry.prototype);THREE.TorusGeometry=function(n,t,i,r,u){var e,o,f;for(THREE.Geometry.call(this),this.radius=n||100,this.tube=t||40,this.radialSegments=i||8,this.tubularSegments=r||6,this.arc=u||2*Math.PI,u=new THREE.Vector3,n=[],t=[],i=0;i<=this.radialSegments;i++)for(r=0;r<=this.tubularSegments;r++)e=r/this.tubularSegments*this.arc,o=2*i/this.radialSegments*Math.PI,u.x=this.radius*Math.cos(e),u.y=this.radius*Math.sin(e),f=new THREE.Vector3,f.x=(this.radius+this.tube*Math.cos(o))*Math.cos(e),f.y=(this.radius+this.tube*Math.cos(o))*Math.sin(e),f.z=this.tube*Math.sin(o),this.vertices.push(f),n.push(new THREE.Vector2(r/this.tubularSegments,i/this.radialSegments)),t.push(f.clone().sub(u).normalize());for(i=1;i<=this.radialSegments;i++)for(r=1;r<=this.tubularSegments;r++){var u=(this.tubularSegments+1)*i+r-1,e=(this.tubularSegments+1)*(i-1)+r-1,o=(this.tubularSegments+1)*(i-1)+r,f=(this.tubularSegments+1)*i+r,s=new THREE.Face4(u,e,o,f,[t[u],t[e],t[o],t[f]]);s.normal.add(t[u]);s.normal.add(t[e]);s.normal.add(t[o]);s.normal.add(t[f]);s.normal.normalize();this.faces.push(s);this.faceVertexUvs[0].push([n[u].clone(),n[e].clone(),n[o].clone(),n[f].clone()])}this.computeCentroids()};THREE.TorusGeometry.prototype=Object.create(THREE.Geometry.prototype);THREE.TorusKnotGeometry=function(n,t,i,r,u,f,e){function s(n,t,i,r,u,f){var e=Math.cos(n);return Math.cos(t),t=Math.sin(n),n*=i/r,i=Math.cos(n),e*=.5*u*(2+i),t=.5*u*(2+i)*t,u=.5*f*u*Math.sin(n),new THREE.Vector3(e,t,u)}for(THREE.Geometry.call(this),this.radius=n||100,this.tube=t||40,this.radialSegments=i||64,this.tubularSegments=r||8,this.p=u||2,this.q=f||3,this.heightScale=e||1,this.grid=Array(this.radialSegments),i=new THREE.Vector3,r=new THREE.Vector3,u=new THREE.Vector3,n=0;n<this.radialSegments;++n)for(this.grid[n]=Array(this.tubularSegments),t=0;t<this.tubularSegments;++t){var o=2*(n/this.radialSegments)*this.p*Math.PI,e=2*(t/this.tubularSegments)*Math.PI,f=s(o,e,this.q,this.p,this.radius,this.heightScale),o=s(o+.01,e,this.q,this.p,this.radius,this.heightScale);i.subVectors(o,f);r.addVectors(o,f);u.crossVectors(i,r);r.crossVectors(u,i);u.normalize();r.normalize();o=-this.tube*Math.cos(e);e=this.tube*Math.sin(e);f.x+=o*r.x+e*u.x;f.y+=o*r.y+e*u.y;f.z+=o*r.z+e*u.z;this.grid[n][t]=this.vertices.push(new THREE.Vector3(f.x,f.y,f.z))-1}for(n=0;n<this.radialSegments;++n)for(t=0;t<this.tubularSegments;++t){var u=(n+1)%this.radialSegments,f=(t+1)%this.tubularSegments,i=this.grid[n][t],r=this.grid[u][t],u=this.grid[u][f],f=this.grid[n][f],e=new THREE.Vector2(n/this.radialSegments,t/this.tubularSegments),o=new THREE.Vector2((n+1)/this.radialSegments,t/this.tubularSegments),h=new THREE.Vector2((n+1)/this.radialSegments,(t+1)/this.tubularSegments),c=new THREE.Vector2(n/this.radialSegments,(t+1)/this.tubularSegments);this.faces.push(new THREE.Face4(i,r,u,f));this.faceVertexUvs[0].push([e,o,h,c])}this.computeCentroids();this.computeFaceNormals();this.computeVertexNormals()};THREE.TorusKnotGeometry.prototype=Object.create(THREE.Geometry.prototype);THREE.TubeGeometry=function(n,t,i,r,u,f){THREE.Geometry.call(this);this.path=n;this.segments=t||64;this.radius=i||1;this.radiusSegments=r||8;this.closed=u||!1;f&&(this.debug=new THREE.Object3D);this.grid=[];var e,s,u=this.segments+1,o,v,h,f=new THREE.Vector3,c,l,a,t=new THREE.TubeGeometry.FrenetFrames(this.path,this.segments,this.closed);for(c=t.tangents,l=t.normals,a=t.binormals,this.tangents=c,this.normals=l,this.binormals=a,t=0;t<u;t++)for(this.grid[t]=[],r=t/(u-1),h=n.getPointAt(r),r=c[t],e=l[t],s=a[t],this.debug&&(this.debug.add(new THREE.ArrowHelper(r,h,i,255)),this.debug.add(new THREE.ArrowHelper(e,h,i,16711680)),this.debug.add(new THREE.ArrowHelper(s,h,i,65280))),r=0;r<this.radiusSegments;r++)o=2*(r/this.radiusSegments)*Math.PI,v=-this.radius*Math.cos(o),o=this.radius*Math.sin(o),f.copy(h),f.x+=v*e.x+o*s.x,f.y+=v*e.y+o*s.y,f.z+=v*e.z+o*s.z,this.grid[t][r]=this.vertices.push(new THREE.Vector3(f.x,f.y,f.z))-1;for(t=0;t<this.segments;t++)for(r=0;r<this.radiusSegments;r++)u=this.closed?(t+1)%this.segments:t+1,f=(r+1)%this.radiusSegments,n=this.grid[t][r],i=this.grid[u][r],u=this.grid[u][f],f=this.grid[t][f],c=new THREE.Vector2(t/this.segments,r/this.radiusSegments),l=new THREE.Vector2((t+1)/this.segments,r/this.radiusSegments),a=new THREE.Vector2((t+1)/this.segments,(r+1)/this.radiusSegments),e=new THREE.Vector2(t/this.segments,(r+1)/this.radiusSegments),this.faces.push(new THREE.Face4(n,i,u,f)),this.faceVertexUvs[0].push([c,l,a,e]);this.computeCentroids();this.computeFaceNormals();this.computeVertexNormals()};THREE.TubeGeometry.prototype=Object.create(THREE.Geometry.prototype);THREE.TubeGeometry.FrenetFrames=function(n,t,i){var e;new THREE.Vector3;e=new THREE.Vector3;new THREE.Vector3;var u=[],f=[],o=[],s=new THREE.Vector3,c=new THREE.Matrix4,t=t+1,r,h,l;for(this.tangents=u,this.normals=f,this.binormals=o,r=0;r<t;r++)h=r/(t-1),u[r]=n.getTangentAt(h),u[r].normalize();for(f[0]=new THREE.Vector3,o[0]=new THREE.Vector3,n=Number.MAX_VALUE,r=Math.abs(u[0].x),h=Math.abs(u[0].y),l=Math.abs(u[0].z),r<=n&&(n=r,e.set(1,0,0)),h<=n&&(n=h,e.set(0,1,0)),l<=n&&e.set(0,0,1),s.crossVectors(u[0],e).normalize(),f[0].crossVectors(u[0],s),o[0].crossVectors(u[0],f[0]),r=1;r<t;r++)f[r]=f[r-1].clone(),o[r]=o[r-1].clone(),s.crossVectors(u[r-1],u[r]),.0001<s.length()&&(s.normalize(),e=Math.acos(u[r-1].dot(u[r])),f[r].applyMatrix4(c.makeRotationAxis(s,e))),o[r].crossVectors(u[r],f[r]);if(i)for(e=Math.acos(f[0].dot(f[t-1])),e/=t-1,0<u[0].dot(s.crossVectors(f[0],f[t-1]))&&(e=-e),r=1;r<t;r++)f[r].applyMatrix4(c.makeRotationAxis(u[r],e*r)),o[r].crossVectors(u[r],f[r])};THREE.PolyhedronGeometry=function(n,t,i,r){function l(n){var t=n.normalize().clone(),i,n;return t.index=c.vertices.push(t)-1,i=Math.atan2(n.z,-n.x)/2/Math.PI+.5,n=Math.atan2(-n.y,Math.sqrt(n.x*n.x+n.z*n.z))/Math.PI+.5,t.uv=new THREE.Vector2(i,1-n),t}function o(n,t,i,r){1>r?(r=new THREE.Face3(n.index,t.index,i.index,[n.clone(),t.clone(),i.clone()]),r.centroid.add(n).add(t).add(i).divideScalar(3),r.normal=r.centroid.clone().normalize(),c.faces.push(r),r=Math.atan2(r.centroid.z,-r.centroid.x),c.faceVertexUvs[0].push([h(n.uv,n,r),h(t.uv,t,r),h(i.uv,i,r)])):(r-=1,o(n,f(n,t),f(n,i),r),o(f(n,t),t,f(t,i),r),o(f(n,i),f(t,i),i,r),o(f(n,t),f(t,i),f(n,i),r))}function f(n,t){e[n.index]||(e[n.index]=[]);e[t.index]||(e[t.index]=[]);var i=e[n.index][t.index];return void 0===i&&(e[n.index][t.index]=e[t.index][n.index]=i=l((new THREE.Vector3).addVectors(n,t).divideScalar(2))),i}function h(n,t,i){return 0>i&&1===n.x&&(n=new THREE.Vector2(n.x-1,n.y)),0===t.x&&0===t.z&&(n=new THREE.Vector2(i/2/Math.PI+.5,n.y)),n}THREE.Geometry.call(this);for(var i=i||1,r=r||0,c=this,u=0,s=n.length;u<s;u++)l(new THREE.Vector3(n[u][0],n[u][1],n[u][2]));for(var e=[],n=this.vertices,u=0,s=t.length;u<s;u++)o(n[t[u][0]],n[t[u][1]],n[t[u][2]],r);for(this.mergeVertices(),u=0,s=this.vertices.length;u<s;u++)this.vertices[u].multiplyScalar(i);this.computeCentroids();this.boundingSphere=new THREE.Sphere(new THREE.Vector3,i)};THREE.PolyhedronGeometry.prototype=Object.create(THREE.Geometry.prototype);THREE.IcosahedronGeometry=function(n,t){var i=(1+Math.sqrt(5))/2;THREE.PolyhedronGeometry.call(this,[[-1,i,0],[1,i,0],[-1,-i,0],[1,-i,0],[0,-1,i],[0,1,i],[0,-1,-i],[0,1,-i],[i,0,-1],[i,0,1],[-i,0,-1],[-i,0,1]],[[0,11,5],[0,5,1],[0,1,7],[0,7,10],[0,10,11],[1,5,9],[5,11,4],[11,10,2],[10,7,6],[7,1,8],[3,9,4],[3,4,2],[3,2,6],[3,6,8],[3,8,9],[4,9,5],[2,4,11],[6,2,10],[8,6,7],[9,8,1]],n,t)};THREE.IcosahedronGeometry.prototype=Object.create(THREE.Geometry.prototype);THREE.OctahedronGeometry=function(n,t){THREE.PolyhedronGeometry.call(this,[[1,0,0],[-1,0,0],[0,1,0],[0,-1,0],[0,0,1],[0,0,-1]],[[0,2,4],[0,4,3],[0,3,5],[0,5,2],[1,2,5],[1,5,3],[1,3,4],[1,4,2]],n,t)};THREE.OctahedronGeometry.prototype=Object.create(THREE.Geometry.prototype);THREE.TetrahedronGeometry=function(n,t){THREE.PolyhedronGeometry.call(this,[[1,1,1],[-1,-1,1],[-1,1,-1],[1,-1,-1]],[[2,1,0],[0,3,2],[1,3,0],[2,3,1]],n,t)};THREE.TetrahedronGeometry.prototype=Object.create(THREE.Geometry.prototype);THREE.ParametricGeometry=function(n,t,i,r){var y,c,l,p;THREE.Geometry.call(this);for(var s=this.vertices,a=this.faces,v=this.faceVertexUvs[0],r=void 0===r?!1:r,f,e,o,h=t+1,u=0;u<=i;u++)for(o=u/i,f=0;f<=t;f++)e=f/t,e=n(e,o),s.push(e);for(u=0;u<i;u++)for(f=0;f<t;f++)n=u*h+f,s=u*h+f+1,o=(u+1)*h+f,e=(u+1)*h+f+1,y=new THREE.Vector2(f/t,u/i),c=new THREE.Vector2((f+1)/t,u/i),l=new THREE.Vector2(f/t,(u+1)/i),p=new THREE.Vector2((f+1)/t,(u+1)/i),r?(a.push(new THREE.Face3(n,s,o)),a.push(new THREE.Face3(s,e,o)),v.push([y,c,l]),v.push([c,p,l])):(a.push(new THREE.Face4(n,s,e,o)),v.push([y,c,p,l]));this.computeCentroids();this.computeFaceNormals();this.computeVertexNormals()};THREE.ParametricGeometry.prototype=Object.create(THREE.Geometry.prototype);THREE.ConvexGeometry=function(n){function v(n){var t=n.length();return new THREE.Vector2(n.x/t,n.y/t)}var r,i,t,u;for(THREE.Geometry.call(this),r=[[0,1,2],[0,2,1]],i=3;i<n.length;i++){var l=i,e=n[l].clone(),t=e.length();for(e.x+=t*2e-6*(Math.random()-.5),e.y+=t*2e-6*(Math.random()-.5),e.z+=t*2e-6*(Math.random()-.5),t=[],u=0;u<r.length;){var c=r[u],h=e,o=n[c[0]],s=o;var f=n[c[1]],p=n[c[2]],a=new THREE.Vector3,y=new THREE.Vector3;if(a.subVectors(p,f),y.subVectors(s,f),a.cross(y),a.normalize(),s=a,o=s.dot(o),s.dot(h)>=o){for(h=0;3>h;h++){for(o=[c[h],c[(h+1)%3]],s=!0,f=0;f<t.length;f++)if(t[f][0]===o[1]&&t[f][1]===o[0]){t[f]=t[t.length-1];t.pop();s=!1;break}s&&t.push(o)}r[u]=r[r.length-1];r.pop()}else u++}for(f=0;f<t.length;f++)r.push([t[f][0],t[f][1],l])}for(l=0,e=Array(n.length),i=0;i<r.length;i++)for(t=r[i],u=0;3>u;u++)void 0===e[t[u]]&&(e[t[u]]=l++,this.vertices.push(n[t[u]])),t[u]=e[t[u]];for(i=0;i<r.length;i++)this.faces.push(new THREE.Face3(r[i][0],r[i][1],r[i][2]));for(i=0;i<this.faces.length;i++)t=this.faces[i],this.faceVertexUvs[0].push([v(this.vertices[t.a]),v(this.vertices[t.b]),v(this.vertices[t.c])]);this.computeCentroids();this.computeFaceNormals();this.computeVertexNormals()};THREE.ConvexGeometry.prototype=Object.create(THREE.Geometry.prototype);THREE.AxisHelper=function(n){var t=new THREE.Geometry;t.vertices.push(new THREE.Vector3,new THREE.Vector3(n||1,0,0),new THREE.Vector3,new THREE.Vector3(0,n||1,0),new THREE.Vector3,new THREE.Vector3(0,0,n||1));t.colors.push(new THREE.Color(16711680),new THREE.Color(16755200),new THREE.Color(65280),new THREE.Color(11206400),new THREE.Color(255),new THREE.Color(43775));n=new THREE.LineBasicMaterial({vertexColors:THREE.VertexColors});THREE.Line.call(this,t,n,THREE.LinePieces)};THREE.AxisHelper.prototype=Object.create(THREE.Line.prototype);THREE.ArrowHelper=function(n,t,i,r){THREE.Object3D.call(this);void 0===i&&(i=20);void 0===r&&(r=16776960);var u=new THREE.Geometry;u.vertices.push(new THREE.Vector3(0,0,0));u.vertices.push(new THREE.Vector3(0,1,0));this.line=new THREE.Line(u,new THREE.LineBasicMaterial({color:r}));this.add(this.line);u=new THREE.CylinderGeometry(0,.05,.25,5,1);this.cone=new THREE.Mesh(u,new THREE.MeshBasicMaterial({color:r}));this.cone.position.set(0,1,0);this.add(this.cone);t instanceof THREE.Vector3&&(this.position=t);this.setDirection(n);this.setLength(i)};THREE.ArrowHelper.prototype=Object.create(THREE.Object3D.prototype);THREE.ArrowHelper.prototype.setDirection=function(n){var t=THREE.ArrowHelper.__v1.copy(n).normalize();.999<t.y?this.rotation.set(0,0,0):-.999>t.y?this.rotation.set(Math.PI,0,0):(n=THREE.ArrowHelper.__v2.set(t.z,0,-t.x).normalize(),t=Math.acos(t.y),n=THREE.ArrowHelper.__q1.setFromAxisAngle(n,t),this.rotation.setEulerFromQuaternion(n,this.eulerOrder))};THREE.ArrowHelper.prototype.setLength=function(n){this.scale.set(n,n,n)};THREE.ArrowHelper.prototype.setColor=function(n){this.line.material.color.setHex(n);this.cone.material.color.setHex(n)};THREE.ArrowHelper.__v1=new THREE.Vector3;THREE.ArrowHelper.__v2=new THREE.Vector3;THREE.ArrowHelper.__q1=new THREE.Quaternion;THREE.CameraHelper=function(n){function t(n,t,i){r(n,i);r(t,i)}function r(n,t){i.geometry.vertices.push(new THREE.Vector3);i.geometry.colors.push(new THREE.Color(t));void 0===i.pointMap[n]&&(i.pointMap[n]=[]);i.pointMap[n].push(i.geometry.vertices.length-1)}THREE.Line.call(this);var i=this;this.geometry=new THREE.Geometry;this.material=new THREE.LineBasicMaterial({color:16777215,vertexColors:THREE.FaceColors});this.type=THREE.LinePieces;this.matrixWorld=n.matrixWorld;this.matrixAutoUpdate=!1;this.pointMap={};t("n1","n2",16755200);t("n2","n4",16755200);t("n4","n3",16755200);t("n3","n1",16755200);t("f1","f2",16755200);t("f2","f4",16755200);t("f4","f3",16755200);t("f3","f1",16755200);t("n1","f1",16755200);t("n2","f2",16755200);t("n3","f3",16755200);t("n4","f4",16755200);t("p","n1",16711680);t("p","n2",16711680);t("p","n3",16711680);t("p","n4",16711680);t("u1","u2",43775);t("u2","u3",43775);t("u3","u1",43775);t("c","t",16777215);t("p","c",3355443);t("cn1","cn2",3355443);t("cn3","cn4",3355443);t("cf1","cf2",3355443);t("cf3","cf4",3355443);this.camera=n;this.update(n)};THREE.CameraHelper.prototype=Object.create(THREE.Line.prototype);THREE.CameraHelper.prototype.update=function(){function n(n,i,r,u){if(THREE.CameraHelper.__v.set(i,r,u),THREE.CameraHelper.__projector.unprojectVector(THREE.CameraHelper.__v,THREE.CameraHelper.__c),n=t.pointMap[n],void 0!==n)for(i=0,r=n.length;i<r;i++)t.geometry.vertices[n[i]].copy(THREE.CameraHelper.__v)}var t=this;THREE.CameraHelper.__c.projectionMatrix.copy(this.camera.projectionMatrix);n("c",0,0,-1);n("t",0,0,1);n("n1",-1,-1,-1);n("n2",1,-1,-1);n("n3",-1,1,-1);n("n4",1,1,-1);n("f1",-1,-1,1);n("f2",1,-1,1);n("f3",-1,1,1);n("f4",1,1,1);n("u1",.7,1.1,-1);n("u2",-.7,1.1,-1);n("u3",0,2,-1);n("cf1",-1,0,1);n("cf2",1,0,1);n("cf3",0,-1,1);n("cf4",0,1,1);n("cn1",-1,0,-1);n("cn2",1,0,-1);n("cn3",0,-1,-1);n("cn4",0,1,-1);this.geometry.verticesNeedUpdate=!0};THREE.CameraHelper.__projector=new THREE.Projector;THREE.CameraHelper.__v=new THREE.Vector3;THREE.CameraHelper.__c=new THREE.Camera;THREE.DirectionalLightHelper=function(n,t){var i;THREE.Object3D.call(this);this.light=n;this.position=n.position;this.direction=new THREE.Vector3;this.direction.subVectors(n.target.position,n.position);i=THREE.Math.clamp(n.intensity,0,1);this.color=n.color.clone();this.color.multiplyScalar(i);var i=this.color.getHex(),r=new THREE.SphereGeometry(t,16,8),u=new THREE.AsteriskGeometry(1.25*t,2.25*t),f=new THREE.MeshBasicMaterial({color:i,fog:!1}),e=new THREE.LineBasicMaterial({color:i,fog:!1});this.lightSphere=new THREE.Mesh(r,f);this.lightRays=new THREE.Line(u,e,THREE.LinePieces);this.add(this.lightSphere);this.add(this.lightRays);this.lightSphere.properties.isGizmo=!0;this.lightSphere.properties.gizmoSubject=n;this.lightSphere.properties.gizmoRoot=this;this.targetSphere=null;void 0!==n.target.properties.targetInverse&&(r=new THREE.SphereGeometry(t,8,4),u=new THREE.MeshBasicMaterial({color:i,wireframe:!0,fog:!1}),this.targetSphere=new THREE.Mesh(r,u),this.targetSphere.position=n.target.position,this.targetSphere.properties.isGizmo=!0,this.targetSphere.properties.gizmoSubject=n.target,this.targetSphere.properties.gizmoRoot=this.targetSphere,i=new THREE.LineDashedMaterial({color:i,dashSize:4,gapSize:4,opacity:.75,transparent:!0,fog:!1}),r=new THREE.Geometry,r.vertices.push(this.position.clone()),r.vertices.push(this.targetSphere.position.clone()),r.computeLineDistances(),this.targetLine=new THREE.Line(r,i),this.targetLine.properties.isGizmo=!0);this.properties.isGizmo=!0};THREE.DirectionalLightHelper.prototype=Object.create(THREE.Object3D.prototype);THREE.DirectionalLightHelper.prototype.update=function(){this.direction.subVectors(this.light.target.position,this.light.position);var n=THREE.Math.clamp(this.light.intensity,0,1);this.color.copy(this.light.color);this.color.multiplyScalar(n);this.lightSphere.material.color.copy(this.color);this.lightRays.material.color.copy(this.color);null!==this.targetSphere&&(this.targetSphere.material.color.copy(this.color),this.targetLine.material.color.copy(this.color),this.targetLine.geometry.vertices[0].copy(this.light.position),this.targetLine.geometry.vertices[1].copy(this.light.target.position),this.targetLine.geometry.computeLineDistances(),this.targetLine.geometry.verticesNeedUpdate=!0)};THREE.HemisphereLightHelper=function(n,t,i){var u,e;THREE.Object3D.call(this);this.light=n;this.position=n.position;u=THREE.Math.clamp(n.intensity,0,1);this.color=n.color.clone();this.color.multiplyScalar(u);e=this.color.getHex();this.groundColor=n.groundColor.clone();this.groundColor.multiplyScalar(u);for(var u=this.groundColor.getHex(),f=new THREE.SphereGeometry(t,16,8,0,2*Math.PI,0,.5*Math.PI),o=new THREE.SphereGeometry(t,16,8,0,2*Math.PI,.5*Math.PI,Math.PI),h=new THREE.MeshBasicMaterial({color:e,fog:!1}),c=new THREE.MeshBasicMaterial({color:u,fog:!1}),r=0,s=f.faces.length;r<s;r++)f.faces[r].materialIndex=0;for(r=0,s=o.faces.length;r<s;r++)o.faces[r].materialIndex=1;THREE.GeometryUtils.merge(f,o);this.lightSphere=new THREE.Mesh(f,new THREE.MeshFaceMaterial([h,c]));this.lightArrow=new THREE.ArrowHelper(new THREE.Vector3(0,1,0),new THREE.Vector3(0,1.1*(t+i),0),i,e);this.lightArrow.rotation.x=Math.PI;this.lightArrowGround=new THREE.ArrowHelper(new THREE.Vector3(0,1,0),new THREE.Vector3(0,-1.1*(t+i),0),i,u);t=new THREE.Object3D;t.rotation.x=.5*-Math.PI;t.add(this.lightSphere);t.add(this.lightArrow);t.add(this.lightArrowGround);this.add(t);this.lightSphere.properties.isGizmo=!0;this.lightSphere.properties.gizmoSubject=n;this.lightSphere.properties.gizmoRoot=this;this.properties.isGizmo=!0;this.target=new THREE.Vector3;this.lookAt(this.target)};THREE.HemisphereLightHelper.prototype=Object.create(THREE.Object3D.prototype);THREE.HemisphereLightHelper.prototype.update=function(){var n=THREE.Math.clamp(this.light.intensity,0,1);this.color.copy(this.light.color);this.color.multiplyScalar(n);this.groundColor.copy(this.light.groundColor);this.groundColor.multiplyScalar(n);this.lightSphere.material.materials[0].color.copy(this.color);this.lightSphere.material.materials[1].color.copy(this.groundColor);this.lightArrow.setColor(this.color.getHex());this.lightArrowGround.setColor(this.groundColor.getHex());this.lookAt(this.target)};THREE.PointLightHelper=function(n,t){var i;THREE.Object3D.call(this);this.light=n;this.position=n.position;i=THREE.Math.clamp(n.intensity,0,1);this.color=n.color.clone();this.color.multiplyScalar(i);var r=this.color.getHex(),i=new THREE.SphereGeometry(t,16,8),u=new THREE.AsteriskGeometry(1.25*t,2.25*t),f=new THREE.IcosahedronGeometry(1,2),e=new THREE.MeshBasicMaterial({color:r,fog:!1}),o=new THREE.LineBasicMaterial({color:r,fog:!1}),r=new THREE.MeshBasicMaterial({color:r,fog:!1,wireframe:!0,opacity:.1,transparent:!0});this.lightSphere=new THREE.Mesh(i,e);this.lightRays=new THREE.Line(u,o,THREE.LinePieces);this.lightDistance=new THREE.Mesh(f,r);i=n.distance;0===i?this.lightDistance.visible=!1:this.lightDistance.scale.set(i,i,i);this.add(this.lightSphere);this.add(this.lightRays);this.add(this.lightDistance);this.lightSphere.properties.isGizmo=!0;this.lightSphere.properties.gizmoSubject=n;this.lightSphere.properties.gizmoRoot=this;this.properties.isGizmo=!0};THREE.PointLightHelper.prototype=Object.create(THREE.Object3D.prototype);THREE.PointLightHelper.prototype.update=function(){var n=THREE.Math.clamp(this.light.intensity,0,1);this.color.copy(this.light.color);this.color.multiplyScalar(n);this.lightSphere.material.color.copy(this.color);this.lightRays.material.color.copy(this.color);this.lightDistance.material.color.copy(this.color);n=this.light.distance;0===n?this.lightDistance.visible=!1:(this.lightDistance.visible=!0,this.lightDistance.scale.set(n,n,n))};THREE.SpotLightHelper=function(n,t){var i;THREE.Object3D.call(this);this.light=n;this.position=n.position;this.direction=new THREE.Vector3;this.direction.subVectors(n.target.position,n.position);i=THREE.Math.clamp(n.intensity,0,1);this.color=n.color.clone();this.color.multiplyScalar(i);var i=this.color.getHex(),e=new THREE.SphereGeometry(t,16,8),r=new THREE.AsteriskGeometry(1.25*t,2.25*t),f=new THREE.CylinderGeometry(.0001,1,1,8,1,!0),u=new THREE.Matrix4;u.rotateX(-Math.PI/2);u.translate(new THREE.Vector3(0,-.5,0));f.applyMatrix(u);var o=new THREE.MeshBasicMaterial({color:i,fog:!1}),u=new THREE.LineBasicMaterial({color:i,fog:!1}),s=new THREE.MeshBasicMaterial({color:i,fog:!1,wireframe:!0,opacity:.3,transparent:!0});this.lightSphere=new THREE.Mesh(e,o);this.lightCone=new THREE.Mesh(f,s);e=n.distance?n.distance:1e4;f=2*e*Math.tan(.5*n.angle);this.lightCone.scale.set(f,f,e);this.lightRays=new THREE.Line(r,u,THREE.LinePieces);this.gyroscope=new THREE.Gyroscope;this.gyroscope.add(this.lightSphere);this.gyroscope.add(this.lightRays);this.add(this.gyroscope);this.add(this.lightCone);this.lookAt(n.target.position);this.lightSphere.properties.isGizmo=!0;this.lightSphere.properties.gizmoSubject=n;this.lightSphere.properties.gizmoRoot=this;this.targetSphere=null;void 0!==n.target.properties.targetInverse&&(r=new THREE.SphereGeometry(t,8,4),u=new THREE.MeshBasicMaterial({color:i,wireframe:!0,fog:!1}),this.targetSphere=new THREE.Mesh(r,u),this.targetSphere.position=n.target.position,this.targetSphere.properties.isGizmo=!0,this.targetSphere.properties.gizmoSubject=n.target,this.targetSphere.properties.gizmoRoot=this.targetSphere,i=new THREE.LineDashedMaterial({color:i,dashSize:4,gapSize:4,opacity:.75,transparent:!0,fog:!1}),r=new THREE.Geometry,r.vertices.push(this.position.clone()),r.vertices.push(this.targetSphere.position.clone()),r.computeLineDistances(),this.targetLine=new THREE.Line(r,i),this.targetLine.properties.isGizmo=!0);this.properties.isGizmo=!0};THREE.SpotLightHelper.prototype=Object.create(THREE.Object3D.prototype);THREE.SpotLightHelper.prototype.update=function(){this.direction.subVectors(this.light.target.position,this.light.position);this.lookAt(this.light.target.position);var n=this.light.distance?this.light.distance:1e4,t=2*n*Math.tan(.5*this.light.angle);this.lightCone.scale.set(t,t,n);n=THREE.Math.clamp(this.light.intensity,0,1);this.color.copy(this.light.color);this.color.multiplyScalar(n);this.lightSphere.material.color.copy(this.color);this.lightRays.material.color.copy(this.color);this.lightCone.material.color.copy(this.color);null!==this.targetSphere&&(this.targetSphere.material.color.copy(this.color),this.targetLine.material.color.copy(this.color),this.targetLine.geometry.vertices[0].copy(this.light.position),this.targetLine.geometry.vertices[1].copy(this.light.target.position),this.targetLine.geometry.computeLineDistances(),this.targetLine.geometry.verticesNeedUpdate=!0)};THREE.ImmediateRenderObject=function(){THREE.Object3D.call(this);this.render=function(){}};THREE.ImmediateRenderObject.prototype=Object.create(THREE.Object3D.prototype);THREE.LensFlare=function(n,t,i,r,u){THREE.Object3D.call(this);this.lensFlares=[];this.positionScreen=new THREE.Vector3;this.customUpdateCallback=void 0;void 0!==n&&this.add(n,t,i,r,u)};THREE.LensFlare.prototype=Object.create(THREE.Object3D.prototype);THREE.LensFlare.prototype.add=function(n,t,i,r,u,f){void 0===t&&(t=-1);void 0===i&&(i=0);void 0===f&&(f=1);void 0===u&&(u=new THREE.Color(16777215));void 0===r&&(r=THREE.NormalBlending);i=Math.min(i,Math.max(0,i));this.lensFlares.push({texture:n,size:t,distance:i,x:0,y:0,z:0,scale:1,rotation:1,opacity:f,color:u,blending:r})};THREE.LensFlare.prototype.updateLensFlares=function(){for(var i=this.lensFlares.length,n,r=2*-this.positionScreen.x,u=2*-this.positionScreen.y,t=0;t<i;t++)n=this.lensFlares[t],n.x=this.positionScreen.x+r*n.distance,n.y=this.positionScreen.y+u*n.distance,n.wantedRotation=.25*n.x*Math.PI,n.rotation+=.25*(n.wantedRotation-n.rotation)};THREE.MorphBlendMesh=function(n,t){THREE.Mesh.call(this,n,t);this.animationsMap={};this.animationsList=[];var i=this.geometry.morphTargets.length;this.createAnimation("__default",0,i-1,i/1);this.setAnimationWeight("__default",1)};THREE.MorphBlendMesh.prototype=Object.create(THREE.Mesh.prototype);THREE.MorphBlendMesh.prototype.createAnimation=function(n,t,i,r){t={startFrame:t,endFrame:i,length:i-t+1,fps:r,duration:(i-t)/r,lastFrame:0,currentFrame:0,active:!1,time:0,direction:1,weight:1,directionBackwards:!1,mirroredLoop:!1};this.animationsMap[n]=t;this.animationsList.push(t)};THREE.MorphBlendMesh.prototype.autoCreateAnimations=function(n){for(var t,i,f,u={},e=this.geometry,r=0,o=e.morphTargets.length;r<o;r++)t=e.morphTargets[r].name.match(/([a-z]+)(\d+)/),t&&1<t.length&&(i=t[1],u[i]||(u[i]={start:Infinity,end:-Infinity}),t=u[i],r<t.start&&(t.start=r),r>t.end&&(t.end=r),f||(f=i));for(i in u)t=u[i],this.createAnimation(i,t.start,t.end,n);this.firstAnimation=f};THREE.MorphBlendMesh.prototype.setAnimationDirectionForward=function(n){(n=this.animationsMap[n])&&(n.direction=1,n.directionBackwards=!1)};THREE.MorphBlendMesh.prototype.setAnimationDirectionBackward=function(n){(n=this.animationsMap[n])&&(n.direction=-1,n.directionBackwards=!0)};THREE.MorphBlendMesh.prototype.setAnimationFPS=function(n,t){var i=this.animationsMap[n];i&&(i.fps=t,i.duration=(i.end-i.start)/i.fps)};THREE.MorphBlendMesh.prototype.setAnimationDuration=function(n,t){var i=this.animationsMap[n];i&&(i.duration=t,i.fps=(i.end-i.start)/i.duration)};THREE.MorphBlendMesh.prototype.setAnimationWeight=function(n,t){var i=this.animationsMap[n];i&&(i.weight=t)};THREE.MorphBlendMesh.prototype.setAnimationTime=function(n,t){var i=this.animationsMap[n];i&&(i.time=t)};THREE.MorphBlendMesh.prototype.getAnimationTime=function(n){var t=0;return(n=this.animationsMap[n])&&(t=n.time),t};THREE.MorphBlendMesh.prototype.getAnimationDuration=function(n){var t=-1;return(n=this.animationsMap[n])&&(t=n.duration),t};THREE.MorphBlendMesh.prototype.playAnimation=function(n){var t=this.animationsMap[n];t?(t.time=0,t.active=!0):console.warn("animation["+n+"] undefined")};THREE.MorphBlendMesh.prototype.stopAnimation=function(n){(n=this.animationsMap[n])&&(n.active=!1)};THREE.MorphBlendMesh.prototype.update=function(n){for(var t,i,u,f,r=0,e=this.animationsList.length;r<e;r++)t=this.animationsList[r],t.active&&(i=t.duration/t.length,t.time+=t.direction*n,t.mirroredLoop?(t.time>t.duration||0>t.time)&&(t.direction*=-1,t.time>t.duration&&(t.time=t.duration,t.directionBackwards=!0),0>t.time&&(t.time=0,t.directionBackwards=!1)):(t.time%=t.duration,0>t.time&&(t.time+=t.duration)),u=t.startFrame+THREE.Math.clamp(Math.floor(t.time/i),0,t.length-1),f=t.weight,u!==t.currentFrame&&(this.morphTargetInfluences[t.lastFrame]=0,this.morphTargetInfluences[t.currentFrame]=1*f,this.morphTargetInfluences[u]=0,t.lastFrame=t.currentFrame,t.currentFrame=u),i=t.time%i/i,t.directionBackwards&&(i=1-i),this.morphTargetInfluences[t.currentFrame]=i*f,this.morphTargetInfluences[t.lastFrame]=(1-i)*f)};THREE.LensFlarePlugin=function(){function v(t,i){var r=n.createProgram(),u=n.createShader(n.FRAGMENT_SHADER),f=n.createShader(n.VERTEX_SHADER),e="precision "+i+" float;\n";return n.shaderSource(u,e+t.fragmentShader),n.shaderSource(f,e+t.vertexShader),n.compileShader(u),n.compileShader(f),n.attachShader(r,u),n.attachShader(r,f),n.linkProgram(r),r}var n,o,s,t,u,h,c,e,l,a,i,f,r;this.init=function(y){n=y.context;o=y;s=y.getPrecision();t=new Float32Array(16);u=new Uint16Array(6);y=0;t[y++]=-1;t[y++]=-1;t[y++]=0;t[y++]=0;t[y++]=1;t[y++]=-1;t[y++]=1;t[y++]=0;t[y++]=1;t[y++]=1;t[y++]=1;t[y++]=1;t[y++]=-1;t[y++]=1;t[y++]=0;t[y++]=1;y=0;u[y++]=0;u[y++]=1;u[y++]=2;u[y++]=0;u[y++]=2;u[y++]=3;h=n.createBuffer();c=n.createBuffer();n.bindBuffer(n.ARRAY_BUFFER,h);n.bufferData(n.ARRAY_BUFFER,t,n.STATIC_DRAW);n.bindBuffer(n.ELEMENT_ARRAY_BUFFER,c);n.bufferData(n.ELEMENT_ARRAY_BUFFER,u,n.STATIC_DRAW);e=n.createTexture();l=n.createTexture();n.bindTexture(n.TEXTURE_2D,e);n.texImage2D(n.TEXTURE_2D,0,n.RGB,16,16,0,n.RGB,n.UNSIGNED_BYTE,null);n.texParameteri(n.TEXTURE_2D,n.TEXTURE_WRAP_S,n.CLAMP_TO_EDGE);n.texParameteri(n.TEXTURE_2D,n.TEXTURE_WRAP_T,n.CLAMP_TO_EDGE);n.texParameteri(n.TEXTURE_2D,n.TEXTURE_MAG_FILTER,n.NEAREST);n.texParameteri(n.TEXTURE_2D,n.TEXTURE_MIN_FILTER,n.NEAREST);n.bindTexture(n.TEXTURE_2D,l);n.texImage2D(n.TEXTURE_2D,0,n.RGBA,16,16,0,n.RGBA,n.UNSIGNED_BYTE,null);n.texParameteri(n.TEXTURE_2D,n.TEXTURE_WRAP_S,n.CLAMP_TO_EDGE);n.texParameteri(n.TEXTURE_2D,n.TEXTURE_WRAP_T,n.CLAMP_TO_EDGE);n.texParameteri(n.TEXTURE_2D,n.TEXTURE_MAG_FILTER,n.NEAREST);n.texParameteri(n.TEXTURE_2D,n.TEXTURE_MIN_FILTER,n.NEAREST);0>=n.getParameter(n.MAX_VERTEX_TEXTURE_IMAGE_UNITS)?(a=!1,i=v(THREE.ShaderFlares.lensFlare,s)):(a=!0,i=v(THREE.ShaderFlares.lensFlareVertexTexture,s));f={};r={};f.vertex=n.getAttribLocation(i,"position");f.uv=n.getAttribLocation(i,"uv");r.renderType=n.getUniformLocation(i,"renderType");r.map=n.getUniformLocation(i,"map");r.occlusionMap=n.getUniformLocation(i,"occlusionMap");r.opacity=n.getUniformLocation(i,"opacity");r.color=n.getUniformLocation(i,"color");r.scale=n.getUniformLocation(i,"scale");r.rotation=n.getUniformLocation(i,"rotation");r.screenPosition=n.getUniformLocation(i,"screenPosition")};this.render=function(t,u,s,v){var t=t.__webglFlares,ut=t.length,tt,it,ot,b,y;if(ut){var nt=new THREE.Vector3,rt=v/s,ft=.5*s,et=.5*v,k=16/v,g=new THREE.Vector2(k*rt,k),p=new THREE.Vector3(1,1,0),d=new THREE.Vector2(1,1),w=r,k=f;for(n.useProgram(i),n.enableVertexAttribArray(f.vertex),n.enableVertexAttribArray(f.uv),n.uniform1i(w.occlusionMap,0),n.uniform1i(w.map,1),n.bindBuffer(n.ARRAY_BUFFER,h),n.vertexAttribPointer(k.vertex,2,n.FLOAT,!1,16,0),n.vertexAttribPointer(k.uv,2,n.FLOAT,!1,16,8),n.bindBuffer(n.ELEMENT_ARRAY_BUFFER,c),n.disable(n.CULL_FACE),n.depthMask(!1),tt=0;tt<ut;tt++)if(k=16/v,g.set(k*rt,k),b=t[tt],nt.set(b.matrixWorld.elements[12],b.matrixWorld.elements[13],b.matrixWorld.elements[14]),nt.applyMatrix4(u.matrixWorldInverse),nt.applyProjection(u.projectionMatrix),p.copy(nt),d.x=p.x*ft+ft,d.y=p.y*et+et,a||0<d.x&&d.x<s&&0<d.y&&d.y<v)for(n.activeTexture(n.TEXTURE1),n.bindTexture(n.TEXTURE_2D,e),n.copyTexImage2D(n.TEXTURE_2D,0,n.RGB,d.x-8,d.y-8,16,16,0),n.uniform1i(w.renderType,0),n.uniform2f(w.scale,g.x,g.y),n.uniform3f(w.screenPosition,p.x,p.y,p.z),n.disable(n.BLEND),n.enable(n.DEPTH_TEST),n.drawElements(n.TRIANGLES,6,n.UNSIGNED_SHORT,0),n.activeTexture(n.TEXTURE0),n.bindTexture(n.TEXTURE_2D,l),n.copyTexImage2D(n.TEXTURE_2D,0,n.RGBA,d.x-8,d.y-8,16,16,0),n.uniform1i(w.renderType,1),n.disable(n.DEPTH_TEST),n.activeTexture(n.TEXTURE1),n.bindTexture(n.TEXTURE_2D,e),n.drawElements(n.TRIANGLES,6,n.UNSIGNED_SHORT,0),b.positionScreen.copy(p),b.customUpdateCallback?b.customUpdateCallback(b):b.updateLensFlares(),n.uniform1i(w.renderType,2),n.enable(n.BLEND),it=0,ot=b.lensFlares.length;it<ot;it++)y=b.lensFlares[it],.001<y.opacity&&.001<y.scale&&(p.x=y.x,p.y=y.y,p.z=y.z,k=y.size*y.scale/v,g.x=k*rt,g.y=k,n.uniform3f(w.screenPosition,p.x,p.y,p.z),n.uniform2f(w.scale,g.x,g.y),n.uniform1f(w.rotation,y.rotation),n.uniform1f(w.opacity,y.opacity),n.uniform3f(w.color,y.color.r,y.color.g,y.color.b),o.setBlending(y.blending,y.blendEquation,y.blendSrc,y.blendDst),o.setTexture(y.texture,1),n.drawElements(n.TRIANGLES,6,n.UNSIGNED_SHORT,0));n.enable(n.CULL_FACE);n.enable(n.DEPTH_TEST);n.depthMask(!0)}}};THREE.ShadowMapPlugin=function(){var n,t,u,f,e,o,s=new THREE.Frustum,h=new THREE.Matrix4,i=new THREE.Vector3,r=new THREE.Vector3,c=new THREE.Vector3;this.init=function(i){n=i.context;t=i;var i=THREE.ShaderLib.depthRGBA,r=THREE.UniformsUtils.clone(i.uniforms);u=new THREE.ShaderMaterial({fragmentShader:i.fragmentShader,vertexShader:i.vertexShader,uniforms:r});f=new THREE.ShaderMaterial({fragmentShader:i.fragmentShader,vertexShader:i.vertexShader,uniforms:r,morphTargets:!0});e=new THREE.ShaderMaterial({fragmentShader:i.fragmentShader,vertexShader:i.vertexShader,uniforms:r,skinning:!0});o=new THREE.ShaderMaterial({fragmentShader:i.fragmentShader,vertexShader:i.vertexShader,uniforms:r,morphTargets:!0,skinning:!0});u._shadowPass=!0;f._shadowPass=!0;e._shadowPass=!0;o._shadowPass=!0};this.render=function(n,i){t.shadowMapEnabled&&t.shadowMapAutoUpdate&&this.update(n,i)};this.update=function(l,a){var nt,it,v,g,k,y,w,b,p,rt=[],d,tt;for(g=0,n.clearColor(1,1,1,1),n.disable(n.BLEND),n.enable(n.CULL_FACE),n.frontFace(n.CCW),t.shadowMapCullFace===THREE.CullFaceFront?n.cullFace(n.FRONT):n.cullFace(n.BACK),t.setDepthTest(!0),nt=0,it=l.__lights.length;nt<it;nt++)if(v=l.__lights[nt],v.castShadow)if(v instanceof THREE.DirectionalLight&&v.shadowCascade)for(k=0;k<v.shadowCascadeCount;k++){if(v.shadowCascadeArray[k])d=v.shadowCascadeArray[k];else{for(p=v,w=k,d=new THREE.DirectionalLight,d.isVirtual=!0,d.onlyShadow=!0,d.castShadow=!0,d.shadowCameraNear=p.shadowCameraNear,d.shadowCameraFar=p.shadowCameraFar,d.shadowCameraLeft=p.shadowCameraLeft,d.shadowCameraRight=p.shadowCameraRight,d.shadowCameraBottom=p.shadowCameraBottom,d.shadowCameraTop=p.shadowCameraTop,d.shadowCameraVisible=p.shadowCameraVisible,d.shadowDarkness=p.shadowDarkness,d.shadowBias=p.shadowCascadeBias[w],d.shadowMapWidth=p.shadowCascadeWidth[w],d.shadowMapHeight=p.shadowCascadeHeight[w],d.pointsWorld=[],d.pointsFrustum=[],b=d.pointsWorld,y=d.pointsFrustum,tt=0;8>tt;tt++)b[tt]=new THREE.Vector3,y[tt]=new THREE.Vector3;b=p.shadowCascadeNearZ[w];p=p.shadowCascadeFarZ[w];y[0].set(-1,-1,b);y[1].set(1,-1,b);y[2].set(-1,1,b);y[3].set(1,1,b);y[4].set(-1,-1,p);y[5].set(1,-1,p);y[6].set(-1,1,p);y[7].set(1,1,p);d.originalCamera=a;y=new THREE.Gyroscope;y.position=v.shadowCascadeOffset;y.add(d);y.add(d.target);a.add(y);v.shadowCascadeArray[k]=d;console.log("Created virtualLight",d)}w=v;b=k;p=w.shadowCascadeArray[b];p.position.copy(w.position);p.target.position.copy(w.target.position);p.lookAt(p.target);p.shadowCameraVisible=w.shadowCameraVisible;p.shadowDarkness=w.shadowDarkness;p.shadowBias=w.shadowCascadeBias[b];y=w.shadowCascadeNearZ[b];w=w.shadowCascadeFarZ[b];p=p.pointsFrustum;p[0].z=y;p[1].z=y;p[2].z=y;p[3].z=y;p[4].z=w;p[5].z=w;p[6].z=w;p[7].z=w;rt[g]=d;g++}else rt[g]=v,g++;for(nt=0,it=rt.length;nt<it;nt++){if(v=rt[nt],v.shadowMap||(k=THREE.LinearFilter,t.shadowMapType===THREE.PCFSoftShadowMap&&(k=THREE.NearestFilter),v.shadowMap=new THREE.WebGLRenderTarget(v.shadowMapWidth,v.shadowMapHeight,{minFilter:k,magFilter:k,format:THREE.RGBAFormat}),v.shadowMapSize=new THREE.Vector2(v.shadowMapWidth,v.shadowMapHeight),v.shadowMatrix=new THREE.Matrix4),!v.shadowCamera){if(v instanceof THREE.SpotLight)v.shadowCamera=new THREE.PerspectiveCamera(v.shadowCameraFov,v.shadowMapWidth/v.shadowMapHeight,v.shadowCameraNear,v.shadowCameraFar);else if(v instanceof THREE.DirectionalLight)v.shadowCamera=new THREE.OrthographicCamera(v.shadowCameraLeft,v.shadowCameraRight,v.shadowCameraTop,v.shadowCameraBottom,v.shadowCameraNear,v.shadowCameraFar);else{console.error("Unsupported light type for shadow");continue}l.add(v.shadowCamera);t.autoUpdateScene&&l.updateMatrixWorld()}if(v.shadowCameraVisible&&!v.cameraHelper&&(v.cameraHelper=new THREE.CameraHelper(v.shadowCamera),v.shadowCamera.add(v.cameraHelper)),v.isVirtual&&d.originalCamera==a){for(k=a,g=v.shadowCamera,y=v.pointsFrustum,p=v.pointsWorld,i.set(Infinity,Infinity,Infinity),r.set(-Infinity,-Infinity,-Infinity),w=0;8>w;w++)b=p[w],b.copy(y[w]),THREE.ShadowMapPlugin.__projector.unprojectVector(b,k),b.applyMatrix4(g.matrixWorldInverse),b.x<i.x&&(i.x=b.x),b.x>r.x&&(r.x=b.x),b.y<i.y&&(i.y=b.y),b.y>r.y&&(r.y=b.y),b.z<i.z&&(i.z=b.z),b.z>r.z&&(r.z=b.z);g.left=i.x;g.right=r.x;g.top=r.y;g.bottom=i.y;g.updateProjectionMatrix()}for(g=v.shadowMap,y=v.shadowMatrix,k=v.shadowCamera,k.position.getPositionFromMatrix(v.matrixWorld),c.getPositionFromMatrix(v.target.matrixWorld),k.lookAt(c),k.updateMatrixWorld(),k.matrixWorldInverse.getInverse(k.matrixWorld),v.cameraHelper&&(v.cameraHelper.visible=v.shadowCameraVisible),v.shadowCameraVisible&&v.cameraHelper.update(),y.set(.5,0,0,.5,0,.5,0,.5,0,0,.5,.5,0,0,0,1),y.multiply(k.projectionMatrix),y.multiply(k.matrixWorldInverse),h.multiplyMatrices(k.projectionMatrix,k.matrixWorldInverse),s.setFromMatrix(h),t.setRenderTarget(g),t.clear(),p=l.__webglObjects,v=0,g=p.length;v<g;v++)(w=p[v],y=w.object,w.render=!1,!y.visible||!y.castShadow||(y instanceof THREE.Mesh||y instanceof THREE.ParticleSystem)&&y.frustumCulled&&!s.intersectsObject(y))||(y._modelViewMatrix.multiplyMatrices(k.matrixWorldInverse,y.matrixWorld),w.render=!0);for(v=0,g=p.length;v<g;v++)w=p[v],w.render&&(y=w.object,w=w.buffer,tt=y.material instanceof THREE.MeshFaceMaterial?y.material.materials[0]:y.material,b=0<y.geometry.morphTargets.length&&tt.morphTargets,tt=y instanceof THREE.SkinnedMesh&&tt.skinning,b=y.customDepthMaterial?y.customDepthMaterial:tt?b?o:e:b?f:u,w instanceof THREE.BufferGeometry?t.renderBufferDirect(k,l.__lights,null,b,w,y):t.renderBuffer(k,l.__lights,null,b,w,y));for(p=l.__webglObjectsImmediate,v=0,g=p.length;v<g;v++)w=p[v],y=w.object,y.visible&&y.castShadow&&(y._modelViewMatrix.multiplyMatrices(k.matrixWorldInverse,y.matrixWorld),t.renderImmediateObject(k,l.__lights,null,u,y))}nt=t.getClearColor();it=t.getClearAlpha();n.clearColor(nt.r,nt.g,nt.b,it);n.enable(n.BLEND);t.shadowMapCullFace===THREE.CullFaceFront&&n.cullFace(n.BACK)}};THREE.ShadowMapPlugin.__projector=new THREE.Projector;THREE.SpritePlugin=function(){function c(n,t){return n.z!==t.z?t.z-n.z:t.id-n.id}var n,u,h,r,f,o,s,t,e,i;this.init=function(c){n=c.context;u=c;h=c.getPrecision();r=new Float32Array(16);f=new Uint16Array(6);c=0;r[c++]=-1;r[c++]=-1;r[c++]=0;r[c++]=0;r[c++]=1;r[c++]=-1;r[c++]=1;r[c++]=0;r[c++]=1;r[c++]=1;r[c++]=1;r[c++]=1;r[c++]=-1;r[c++]=1;r[c++]=0;r[c++]=1;c=0;f[c++]=0;f[c++]=1;f[c++]=2;f[c++]=0;f[c++]=2;f[c++]=3;o=n.createBuffer();s=n.createBuffer();n.bindBuffer(n.ARRAY_BUFFER,o);n.bufferData(n.ARRAY_BUFFER,r,n.STATIC_DRAW);n.bindBuffer(n.ELEMENT_ARRAY_BUFFER,s);n.bufferData(n.ELEMENT_ARRAY_BUFFER,f,n.STATIC_DRAW);var c=THREE.ShaderSprite.sprite,l=n.createProgram(),a=n.createShader(n.FRAGMENT_SHADER),v=n.createShader(n.VERTEX_SHADER),y="precision "+h+" float;\n";n.shaderSource(a,y+c.fragmentShader);n.shaderSource(v,y+c.vertexShader);n.compileShader(a);n.compileShader(v);n.attachShader(l,a);n.attachShader(l,v);n.linkProgram(l);t=l;e={};i={};e.position=n.getAttribLocation(t,"position");e.uv=n.getAttribLocation(t,"uv");i.uvOffset=n.getUniformLocation(t,"uvOffset");i.uvScale=n.getUniformLocation(t,"uvScale");i.rotation=n.getUniformLocation(t,"rotation");i.scale=n.getUniformLocation(t,"scale");i.alignment=n.getUniformLocation(t,"alignment");i.color=n.getUniformLocation(t,"color");i.map=n.getUniformLocation(t,"map");i.opacity=n.getUniformLocation(t,"opacity");i.useScreenCoordinates=n.getUniformLocation(t,"useScreenCoordinates");i.sizeAttenuation=n.getUniformLocation(t,"sizeAttenuation");i.screenPosition=n.getUniformLocation(t,"screenPosition");i.modelViewMatrix=n.getUniformLocation(t,"modelViewMatrix");i.projectionMatrix=n.getUniformLocation(t,"projectionMatrix");i.fogType=n.getUniformLocation(t,"fogType");i.fogDensity=n.getUniformLocation(t,"fogDensity");i.fogNear=n.getUniformLocation(t,"fogNear");i.fogFar=n.getUniformLocation(t,"fogFar");i.fogColor=n.getUniformLocation(t,"fogColor");i.alphaTest=n.getUniformLocation(t,"alphaTest")};this.render=function(r,f,h,l){var d=r.__webglSprites,g=d.length,k,y,a,b,p;if(g){var w=e,v=i,tt=l/h,h=.5*h,nt=.5*l;for(n.useProgram(t),n.enableVertexAttribArray(w.position),n.enableVertexAttribArray(w.uv),n.disable(n.CULL_FACE),n.enable(n.BLEND),n.bindBuffer(n.ARRAY_BUFFER,o),n.vertexAttribPointer(w.position,2,n.FLOAT,!1,16,0),n.vertexAttribPointer(w.uv,2,n.FLOAT,!1,16,8),n.bindBuffer(n.ELEMENT_ARRAY_BUFFER,s),n.uniformMatrix4fv(v.projectionMatrix,!1,f.projectionMatrix.elements),n.activeTexture(n.TEXTURE0),n.uniform1i(v.map,0),k=w=0,p=r.fog,p?(n.uniform3f(v.fogColor,p.color.r,p.color.g,p.color.b),p instanceof THREE.Fog?(n.uniform1f(v.fogNear,p.near),n.uniform1f(v.fogFar,p.far),n.uniform1i(v.fogType,1),k=w=1):p instanceof THREE.FogExp2&&(n.uniform1f(v.fogDensity,p.density),n.uniform1i(v.fogType,2),k=w=2)):(n.uniform1i(v.fogType,0),k=w=0),b=[],p=0;p<g;p++)y=d[p],a=y.material,y.visible&&0!==a.opacity&&(a.useScreenCoordinates?y.z=-y.position.z:(y._modelViewMatrix.multiplyMatrices(f.matrixWorldInverse,y.matrixWorld),y.z=-y._modelViewMatrix.elements[14]));for(d.sort(c),p=0;p<g;p++)y=d[p],a=y.material,y.visible&&0!==a.opacity&&a.map&&a.map.image&&a.map.image.width&&(n.uniform1f(v.alphaTest,a.alphaTest),!0===a.useScreenCoordinates?(n.uniform1i(v.useScreenCoordinates,1),n.uniform3f(v.screenPosition,(y.position.x*u.devicePixelRatio-h)/h,(nt-y.position.y*u.devicePixelRatio)/nt,Math.max(0,Math.min(1,y.position.z))),b[0]=u.devicePixelRatio,b[1]=u.devicePixelRatio):(n.uniform1i(v.useScreenCoordinates,0),n.uniform1i(v.sizeAttenuation,a.sizeAttenuation?1:0),n.uniformMatrix4fv(v.modelViewMatrix,!1,y._modelViewMatrix.elements),b[0]=1,b[1]=1),f=r.fog&&a.fog?k:0,w!==f&&(n.uniform1i(v.fogType,f),w=f),f=1/(a.scaleByViewport?l:1),b[0]*=f*tt*y.scale.x,b[1]*=f*y.scale.y,n.uniform2f(v.uvScale,a.uvScale.x,a.uvScale.y),n.uniform2f(v.uvOffset,a.uvOffset.x,a.uvOffset.y),n.uniform2f(v.alignment,a.alignment.x,a.alignment.y),n.uniform1f(v.opacity,a.opacity),n.uniform3f(v.color,a.color.r,a.color.g,a.color.b),n.uniform1f(v.rotation,y.rotation),n.uniform2fv(v.scale,b),u.setBlending(a.blending,a.blendEquation,a.blendSrc,a.blendDst),u.setDepthTest(a.depthTest),u.setDepthWrite(a.depthWrite),u.setTexture(a.map,0),n.drawElements(n.TRIANGLES,6,n.UNSIGNED_SHORT,0));n.enable(n.CULL_FACE)}}};THREE.DepthPassPlugin=function(){this.enabled=!1;this.renderTarget=null;var t,n,i,r,u,f,e=new THREE.Frustum,o=new THREE.Matrix4;this.init=function(e){t=e.context;n=e;var e=THREE.ShaderLib.depthRGBA,o=THREE.UniformsUtils.clone(e.uniforms);i=new THREE.ShaderMaterial({fragmentShader:e.fragmentShader,vertexShader:e.vertexShader,uniforms:o});r=new THREE.ShaderMaterial({fragmentShader:e.fragmentShader,vertexShader:e.vertexShader,uniforms:o,morphTargets:!0});u=new THREE.ShaderMaterial({fragmentShader:e.fragmentShader,vertexShader:e.vertexShader,uniforms:o,skinning:!0});f=new THREE.ShaderMaterial({fragmentShader:e.fragmentShader,vertexShader:e.vertexShader,uniforms:o,morphTargets:!0,skinning:!0});i._shadowPass=!0;r._shadowPass=!0;u._shadowPass=!0;f._shadowPass=!0};this.render=function(n,t){this.enabled&&this.update(n,t)};this.update=function(s,h){var l,v,a,p,c,y,w;for(t.clearColor(1,1,1,1),t.disable(t.BLEND),n.setDepthTest(!0),n.autoUpdateScene&&s.updateMatrixWorld(),h.matrixWorldInverse.getInverse(h.matrixWorld),o.multiplyMatrices(h.projectionMatrix,h.matrixWorldInverse),e.setFromMatrix(o),n.setRenderTarget(this.renderTarget),n.clear(),y=s.__webglObjects,l=0,v=y.length;l<v;l++)(a=y[l],c=a.object,a.render=!1,!c.visible||(c instanceof THREE.Mesh||c instanceof THREE.ParticleSystem)&&c.frustumCulled&&!e.intersectsObject(c))||(c._modelViewMatrix.multiplyMatrices(h.matrixWorldInverse,c.matrixWorld),a.render=!0);for(l=0,v=y.length;l<v;l++)(a=y[l],!a.render||(c=a.object,a=a.buffer,c instanceof THREE.ParticleSystem&&!c.customDepthMaterial))||((w=c.material instanceof THREE.MeshFaceMaterial?c.material.materials[0]:c.material)&&n.setMaterialFaces(c.material),p=0<c.geometry.morphTargets.length&&w.morphTargets,w=c instanceof THREE.SkinnedMesh&&w.skinning,p=c.customDepthMaterial?c.customDepthMaterial:w?p?f:u:p?r:i,a instanceof THREE.BufferGeometry?n.renderBufferDirect(h,s.__lights,null,p,a,c):n.renderBuffer(h,s.__lights,null,p,a,c));for(y=s.__webglObjectsImmediate,l=0,v=y.length;l<v;l++)a=y[l],c=a.object,c.visible&&(c._modelViewMatrix.multiplyMatrices(h.matrixWorldInverse,c.matrixWorld),n.renderImmediateObject(h,s.__lights,null,i,c));l=n.getClearColor();v=n.getClearAlpha();t.clearColor(l.r,l.g,l.b,v);t.enable(t.BLEND)}};THREE.ShaderFlares={lensFlareVertexTexture:{vertexShader:"uniform lowp int renderType;\nuniform vec3 screenPosition;\nuniform vec2 scale;\nuniform float rotation;\nuniform sampler2D occlusionMap;\nattribute vec2 position;\nattribute vec2 uv;\nvarying vec2 vUV;\nvarying float vVisibility;\nvoid main() {\nvUV = uv;\nvec2 pos = position;\nif( renderType == 2 ) {\nvec4 visibility = texture2D( occlusionMap, vec2( 0.1, 0.1 ) ) +\ntexture2D( occlusionMap, vec2( 0.5, 0.1 ) ) +\ntexture2D( occlusionMap, vec2( 0.9, 0.1 ) ) +\ntexture2D( occlusionMap, vec2( 0.9, 0.5 ) ) +\ntexture2D( occlusionMap, vec2( 0.9, 0.9 ) ) +\ntexture2D( occlusionMap, vec2( 0.5, 0.9 ) ) +\ntexture2D( occlusionMap, vec2( 0.1, 0.9 ) ) +\ntexture2D( occlusionMap, vec2( 0.1, 0.5 ) ) +\ntexture2D( occlusionMap, vec2( 0.5, 0.5 ) );\nvVisibility = (       visibility.r / 9.0 ) *\n( 1.0 - visibility.g / 9.0 ) *\n(       visibility.b / 9.0 ) *\n( 1.0 - visibility.a / 9.0 );\npos.x = cos( rotation ) * position.x - sin( rotation ) * position.y;\npos.y = sin( rotation ) * position.x + cos( rotation ) * position.y;\n}\ngl_Position = vec4( ( pos * scale + screenPosition.xy ).xy, screenPosition.z, 1.0 );\n}",fragmentShader:"uniform lowp int renderType;\nuniform sampler2D map;\nuniform float opacity;\nuniform vec3 color;\nvarying vec2 vUV;\nvarying float vVisibility;\nvoid main() {\nif( renderType == 0 ) {\ngl_FragColor = vec4( 1.0, 0.0, 1.0, 0.0 );\n} else if( renderType == 1 ) {\ngl_FragColor = texture2D( map, vUV );\n} else {\nvec4 texture = texture2D( map, vUV );\ntexture.a *= opacity * vVisibility;\ngl_FragColor = texture;\ngl_FragColor.rgb *= color;\n}\n}"},lensFlare:{vertexShader:"uniform lowp int renderType;\nuniform vec3 screenPosition;\nuniform vec2 scale;\nuniform float rotation;\nattribute vec2 position;\nattribute vec2 uv;\nvarying vec2 vUV;\nvoid main() {\nvUV = uv;\nvec2 pos = position;\nif( renderType == 2 ) {\npos.x = cos( rotation ) * position.x - sin( rotation ) * position.y;\npos.y = sin( rotation ) * position.x + cos( rotation ) * position.y;\n}\ngl_Position = vec4( ( pos * scale + screenPosition.xy ).xy, screenPosition.z, 1.0 );\n}",fragmentShader:"precision mediump float;\nuniform lowp int renderType;\nuniform sampler2D map;\nuniform sampler2D occlusionMap;\nuniform float opacity;\nuniform vec3 color;\nvarying vec2 vUV;\nvoid main() {\nif( renderType == 0 ) {\ngl_FragColor = vec4( texture2D( map, vUV ).rgb, 0.0 );\n} else if( renderType == 1 ) {\ngl_FragColor = texture2D( map, vUV );\n} else {\nfloat visibility = texture2D( occlusionMap, vec2( 0.5, 0.1 ) ).a +\ntexture2D( occlusionMap, vec2( 0.9, 0.5 ) ).a +\ntexture2D( occlusionMap, vec2( 0.5, 0.9 ) ).a +\ntexture2D( occlusionMap, vec2( 0.1, 0.5 ) ).a;\nvisibility = ( 1.0 - visibility / 4.0 );\nvec4 texture = texture2D( map, vUV );\ntexture.a *= opacity * visibility;\ngl_FragColor = texture;\ngl_FragColor.rgb *= color;\n}\n}"}};THREE.ShaderSprite={sprite:{vertexShader:"uniform int useScreenCoordinates;\nuniform int sizeAttenuation;\nuniform vec3 screenPosition;\nuniform mat4 modelViewMatrix;\nuniform mat4 projectionMatrix;\nuniform float rotation;\nuniform vec2 scale;\nuniform vec2 alignment;\nuniform vec2 uvOffset;\nuniform vec2 uvScale;\nattribute vec2 position;\nattribute vec2 uv;\nvarying vec2 vUV;\nvoid main() {\nvUV = uvOffset + uv * uvScale;\nvec2 alignedPosition = position + alignment;\nvec2 rotatedPosition;\nrotatedPosition.x = ( cos( rotation ) * alignedPosition.x - sin( rotation ) * alignedPosition.y ) * scale.x;\nrotatedPosition.y = ( sin( rotation ) * alignedPosition.x + cos( rotation ) * alignedPosition.y ) * scale.y;\nvec4 finalPosition;\nif( useScreenCoordinates != 0 ) {\nfinalPosition = vec4( screenPosition.xy + rotatedPosition, screenPosition.z, 1.0 );\n} else {\nfinalPosition = projectionMatrix * modelViewMatrix * vec4( 0.0, 0.0, 0.0, 1.0 );\nfinalPosition.xy += rotatedPosition * ( sizeAttenuation == 1 ? 1.0 : finalPosition.z );\n}\ngl_Position = finalPosition;\n}",fragmentShader:"uniform vec3 color;\nuniform sampler2D map;\nuniform float opacity;\nuniform int fogType;\nuniform vec3 fogColor;\nuniform float fogDensity;\nuniform float fogNear;\nuniform float fogFar;\nuniform float alphaTest;\nvarying vec2 vUV;\nvoid main() {\nvec4 texture = texture2D( map, vUV );\nif ( texture.a < alphaTest ) discard;\ngl_FragColor = vec4( color * texture.xyz, texture.a * opacity );\nif ( fogType > 0 ) {\nfloat depth = gl_FragCoord.z / gl_FragCoord.w;\nfloat fogFactor = 0.0;\nif ( fogType == 1 ) {\nfogFactor = smoothstep( fogNear, fogFar, depth );\n} else {\nconst float LOG2 = 1.442695;\nfloat fogFactor = exp2( - fogDensity * fogDensity * depth * depth * LOG2 );\nfogFactor = 1.0 - clamp( fogFactor, 0.0, 1.0 );\n}\ngl_FragColor = mix( gl_FragColor, vec4( fogColor, gl_FragColor.w ), fogFactor );\n}\n}"}};var SEPARATION=100,AMOUNTX=100,AMOUNTY=70,container,camera,scene,renderer,particles,particle,count=0,mouseX=85,mouseY=-342,windowHalfX=window.innerWidth/2,windowHalfY=window.innerHeight/2;init();animate();