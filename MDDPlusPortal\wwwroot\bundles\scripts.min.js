/*! jQuery v3.2.1 | (c) JS Foundation and other contributors | jquery.org/license */
function contactMessageSuccess(){const n=[document.querySelectorAll("#ContactForm input"),document.querySelectorAll("#ContactForm textarea")],t=[document.querySelectorAll("#ApplyForm input"),document.querySelectorAll("#ApplyForm textarea")];$(".js-message").fadeIn();setTimeout(function(){$(".js-message").fadeOut()},2500);$(".cs-fade-shadow").fadeOut();grecaptcha.reset();$("#ContactForm, #ApplyForm").find("button[type=submit]").prop("disabled",!0);setTimeout(()=>{n&&(n[0].forEach(n=>{n.value=""}),n[1].forEach(n=>{n.value=""})),t&&(t[0].forEach(n=>{n.value=""}),t[1].forEach(n=>{n.value=""}))},500)}function contactMessageFailure(){$(".js-message-failure").fadeIn();setTimeout(function(){$(".js-message-failure").fadeOut()},2500);$(".cs-fade-shadow").fadeOut()}function isAllowedFileType(n){return/\.(pdf|doc|docx)$/i.test(n)}function debounce(n,t,i){var r;return function(){var u=this,f=arguments,e=function(){r=null;i||n.apply(u,f)},o=i&&!r;clearTimeout(r);r=setTimeout(e,t);o&&n.apply(u,f)}}function formCaseStudy(){const n=document.querySelector("#case-study-submit"),t=document.querySelector("#case-study");n&&n.addEventListener("click",()=>{setTimeout(()=>{t.querySelectorAll("input").forEach(n=>{n.value=""})},1e3)})}function changeSharingMetatags(n,t,i,r,u){n!==undefined&&n!==null&&n!==""&&n!==" "&&$($("meta[name='description']")[0]).attr("content",n);t!==undefined&&t!==null&&t!==""&&t!==" "&&$($("meta[name='keywords']")[0]).attr("content",t);r!==undefined&&r!==null&&r!==""&&r!==" "&&$($("meta[property='og:image']")[0]).attr("content",location.host+r);i!==undefined&&i!==null&&i!==""&&i!==" "&&($("title")[0].innerText=i);u!==undefined&&changeVacancyEvent(u)}function zoomInit(){mediumZoom(document.querySelectorAll(".blogpost-zoom img"))}function menuToggle(n){var t,i;n.preventDefault();t=$(n.target).hasClass("navbar-toggle")?$(n.target):$(n.target).parent(".navbar-toggle");t.toggleClass("open");i=$(n.target).parents(".navbar-default");t.hasClass("open")?($("html").css("overflow","hidden"),$("body").css("height","100%"),$(".popup-menu").css("height",i.hasClass("header-small")?"100%":"100%"),$("body").addClass("popup-menu-active")):($("html").css("overflow",""),$("body").css("height",""),$(".popup-menu").css("height",""),$("body").removeClass("popup-menu-active"))}function stopBodyScrolling(n){n?document.body.addEventListener("touchmove",freezeVp,!1):document.body.removeEventListener("touchmove",freezeVp,!1)}function tooltipPopup(n){n.tooltip({title:"Copied to clipboard",placement:"bottom",trigger:"manual"});n.tooltip("show");setTimeout(function(){n.tooltip("hide")},750)}function CopyText(n){var i=$("<input>"),r,u;if($(n).append(i),isMobile.iOS()){i.val(n.text());var t=i.get(0),f=t.contentEditable,e=t.readOnly;t.contentEditable=!0;t.readOnly=!0;r=document.createRange();r.selectNodeContents(t);u=window.getSelection();u.removeAllRanges();u.addRange(r);t.setSelectionRange(0,999999);t.contentEditable=f;t.readOnly=e}else i.val(n.text()),i.select();document.execCommand("copy");i.remove()}function initTopBannerList(){$("#top-banner-list").owlCarousel({margin:20,items:1})}function initFeaturedClientsList(){$("#featured-clients").owlCarousel({margin:20,dots:!0,loop:!0,lazyLoad:!0,lazyLoadEager:1,responsive:{0:{items:1},480:{items:2},768:{items:4}}})}function initClientsList(){$("#clients-list").owlCarousel({margin:20,nav:!0,responsive:{0:{items:1},480:{items:2},768:{items:4},992:{items:5}}})}function initClientsListTwo(){$("#clients-lists").owlCarousel({margin:20,nav:!0,responsive:{0:{items:1},480:{items:2},768:{items:4},992:{items:5}}})}function initSmallRecruiterTeamListMobile(){if($("#recruiter-team-sm")){var n=$("#recruiter-team-sm .item").length>1,t=$("#recruiter-team-sm");t.owlCarousel({items:1,loop:n,pullDrag:n,touchDrag:n,mouseDrag:n});$(".slide-member-prev").click(function(){t.owlCarousel().trigger("prev.owl.carousel")});$(".slide-member-next").click(function(){t.owlCarousel().trigger("next.owl.carousel")})}}function initRecruiterTeamList(){var n,t;$("#recruiter-team")&&(n=$("#recruiter-team .item").length>1,$("#recruiter-team").owlCarousel({items:1,loop:n,pullDrag:n,touchDrag:n,mouseDrag:n}),t=$("#recruiter-team").owlCarousel(),$(".slide-member-prev").click(function(){t.trigger("prev.owl.carousel")}),$(".slide-member-next").click(function(){t.trigger("next.owl.carousel")}))}function initQuotesList(){$("#quotes-list").owlCarousel({items:1,loop:!0,autoplay:!0,autoplayHoverPause:!0});var n=$("#quotes-list").owlCarousel();$(".slide-quote-btn-prev").click(function(){n.trigger("prev.owl.carousel")});$(".slide-quote-btn-next").click(function(){n.trigger("next.owl.carousel")})}function initMethodologiesList(){if($("#methodologies-list")!==undefined){$("#methodologies-list").owlCarousel({items:1,loop:!0});var n=$("#methodologies-list").owlCarousel(),t=$("#methodologies-list .owl-dots").outerWidth(!0)-parseInt($("#methodologies-list .owl-dots").css("margin-left"),10);$(".slide-methodolody-btn-next").css("left",t);$(".slide-methodolody-btn-prev").click(function(){n.trigger("prev.owl.carousel")});$(".slide-methodolody-btn-next").click(function(){n.trigger("next.owl.carousel")})}}function initOurCasesList(){$("#our-cases-list").owlCarousel({items:1,loop:!0,autoplay:!0,autoplayHoverPause:!0,nav:!0,lazyLoad:!0,lazyLoadEager:1});var n=$("#our-cases-list").owlCarousel();$(".slide-case-btn-prev").click(function(){n.trigger("prev.owl.carousel")});$(".slide-case-btn-next").click(function(){n.trigger("next.owl.carousel")})}function initPortfolioFilter(){$(".portfolio-main-categories-tabs").find("a").on("click",function(){var t=$(this).attr("data-category"),n=$(".portfolio-item-list");if(t==="all"){n.find("div.item").removeClass("hide-project");n.find("div.item").removeClass("project-even");n.find("div.item").removeClass("project-odd");n.find("div.item:even").addClass("project-odd");n.find("div.item:odd").addClass("project-even");n.find("div.item").removeClass(function(n,t){return(t.match(/background-[0-9]*/)||[]).join(" ")});n.find("div.item").each(function(n){$(this).addClass("background-"+(n+1))});return}n.find("div.item").removeClass("project-even");n.find("div.item").removeClass("project-odd");n.find("div.item").not("."+t).addClass("hide-project");n.find("div.item."+t).removeClass("hide-project");n.find("div.item."+t+":even").addClass("project-odd");n.find("div.item."+t+":odd").addClass("project-even");n.find("div.item:not(.hide-project)").removeClass(function(n,t){return(t.match(/background-[0-9]*/)||[]).join(" ")});n.find("div.item:not(.hide-project)").each(function(n){$(this).addClass("background-"+(n+1))})})}function initIndexFilter(){$(".portfolio-categories-tabs").find("a").on("click",function(){console.log(111);var t=$(this).attr("data-category"),n=$(".glider");if(t==="all"){$(".slidecontainer").addClass("intro");$.when(n.find("div.item").show("fast")).done(function(){$(".glider").mCustomScrollbar("update")});return}$(".slidecontainer").removeClass("intro");n.find("div.item."+t+":even").addClass("juj-even");n.find("div.item").hide("fast");$.when(n.find("div.item."+t).show("fast")).done(function(){$.when(n.find("div.item").not("."+t).hide("fast")).done(function(){$(".glider").mCustomScrollbar("update")})})})}function destroyPortfolioList(){$(".portfolio-list").mCustomScrollbar("destroy")}function assignHoverSocialIcons(){if($(".social-icon").length!=0){var n=document.createElement("style"),t="";$(".social-icon").each(function(){t+="#"+$(this).attr("id")+":hover{background-image:"+$(this).data("backhover")+"!important;}"});n.appendChild(document.createTextNode(t));document.getElementsByTagName("head")[0].appendChild(n)}}function changeTags(n){n=="All"||n=="Alle"?$("#breadcrumb-name").empty():$("#breadcrumb-name").text(n);checkFilter(n)}function getService(n,t){window.history.pushState(n,n,n);$.ajax({url:"/umbraco/Surface/SurfaceService/GetServiceDescription/?url="+n+"&serviceId="+t,type:"GET",contentType:!1,processData:!1,success:function(n){onSuccessServices(n);onSuccessServicesMobile(n)},error:function(n,t){console.log(n,t)}})}function onSuccessServicesMobile(n){$("#section-mobile .panel-body .title").text(n.VacancyName);$("#section-mobile .panel-body .summary").html(n.VacancyDescription)}function onSuccessServices(n){$("#services .tab-content .title").text(n.VacancyName);$("#services .tab-content .summary").html(n.VacancyDescription)}function onMobileCheck(){isMobile.iOS()&&$("body").addClass("apple-ios")}function getVacancy(n){window.history.pushState(n,n,n);$.ajax({url:"/umbraco/Surface/SurfaceCareers/GetVacancyDescription/?url="+n,type:"GET",contentType:!1,processData:!1,success:function(n){onSuccessVacancy(n);onSuccessVacancyMobile(n)},error:function(n,t){console.log(n,t)}})}function onSuccessVacancy(n){$("#carriers .tab-content .title").text(n.vacancyName);$("#carriers .tab-content .summary").html(n.vacancyDescription)}function onSuccessVacancyMobile(n){$("#section-mobile .panel-body .title").text(n.vacancyName);$("#section-mobile .panel-body .summary").html(n.vacancyDescription)}function changeVacancyEvent(n){getVacancy(n);SocialShare.createShareBox(".ss-box",window.location.href,"facebook, pinterest, linkedIn, pinterest, telegram,  whatsapp, email")}function initBlogpostPage(){if($(".blogpost")!==undefined){var n=$("#blogposts .item").length>3;$("#blogposts").owlCarousel({items:3,dots:n,lazyLoad:!0,lazyLoadEager:1,margin:35,responsive:{992:{items:3},768:{items:2},600:{items:2},319:{items:1}}});loadMore=$("#loadMore").data("load-more")===!0}}function onChangeUrl(n){window.history.pushState(n,n,n)}function onLoadBlogs(){var n=$("ul.blog-tags li.active"),t;n.length!=0&&(t=n.children("a").attr("href").split("/").pop(),$.ajax({url:"/umbraco/Surface/SurfaceBlog/GetBlogs?tag="+t+"&page="+blogPageCount,type:"GET",contentType:!1,processData:!1,beforeSend:function(){$("#blogLoader").removeClass("hide-loader");$("#blogLoader").addClass("show-loader")},complete:function(){},success:function(n){$("#blogLoader").removeClass("show-loader");$("#blogLoader").addClass("hide-loader");onSuccessBlogLoad(n);loadPosts=!1},error:function(n,t){console.log(n,t);loadPosts=!1}}))}function onSuccessBlogLoad(n){$("#blogpostsList").append(n);loadMore=$("#loadMore").data("load-more")===!0;$("#loadMore").remove();$(".lazy").lazyload({effect:"fadeIn"});onChangeTagHeaderSelected()}function onMakeTagActive(n){$("ul.blog-tags li.active").removeClass("active");$("ul.blog-tags li").each(function(t,i){if($(i).text().trim()===n.text().trim()){$(i).addClass("active");return}});$(".blog .ba-category-list a.ba-active").removeClass("ba-active");$(".blog .ba-category-list a.ba-category").each(function(t,i){if($(i).text().trim()===n.text().trim()){$(i).addClass("ba-active");return}})}function onChangeTagHeaderSelected(){var t=$(".blog .ba-category-list a.ba-active"),i=t.text().trim(),r=t.data("blogpost-count");$("#tagHeaderSelected").text(i+" ("+r+")");$(".ba-message").hide();let n=$('.ba-message[data-ba-tag="'+i+'"]');(!n||n.length<=0)&&(n=$('.ba-message[data-ba-tag="ALL"]'));n.show()}function subscribeMessageSuccess(){$(".js-subscribe-message").fadeIn();setTimeout(function(){$(".js-subscribe-message").fadeOut()},2500);setTimeout(()=>{document.querySelector("#SubscriberEmail")&&document.querySelectorAll("#SubscriberEmail").forEach(n=>{n.value=""})},500);console.log("?")}function subscribeMessageFailure(){$(".js-subscribe-message-failure").fadeIn();setTimeout(function(){$(".js-subscribe-message-failure").fadeOut()},2500)}function checkFilter(n){n!=="All"&&n!=="Alle"?($("#blog-not-filter").hide(),$("#blog-with-filter").show()):($("#blog-not-filter").show(),$("#blog-with-filter").hide())}var $jscomp,$jscomp$this,SocialShare,recaptchaElements,onReturnCaptchaCallback,onRecaptchaExpired,blogPageCount,loadMore,mapClicked,map,popup,freezeVp,applyRotate,isMobile,getPosition,loadPosts,onScrollLoadBlog;!function(n,t){"use strict";"object"==typeof module&&"object"==typeof module.exports?module.exports=n.document?t(n,!0):function(n){if(!n.document)throw new Error("jQuery requires a window with a document");return t(n)}:t(n)}("undefined"!=typeof window?window:this,function(n,t){"use strict";function ir(n,t){t=t||u;var i=t.createElement("script");i.text=n;t.head.appendChild(i).parentNode.removeChild(i)}function fi(n){var t=!!n&&"length"in n&&n.length,r=i.type(n);return"function"!==r&&!i.isWindow(n)&&("array"===r||0===t||"number"==typeof t&&t>0&&t-1 in n)}function l(n,t){return n.nodeName&&n.nodeName.toLowerCase()===t.toLowerCase()}function oi(n,t,r){return i.isFunction(t)?i.grep(n,function(n,i){return!!t.call(n,i,n)!==r}):t.nodeType?i.grep(n,function(n){return n===t!==r}):"string"!=typeof t?i.grep(n,function(n){return ot.call(t,n)>-1!==r}):er.test(t)?i.filter(t,n,r):(t=i.filter(t,n),i.grep(n,function(n){return ot.call(t,n)>-1!==r&&1===n.nodeType}))}function ar(n,t){while((n=n[t])&&1!==n.nodeType);return n}function ne(n){var t={};return i.each(n.match(h)||[],function(n,i){t[i]=!0}),t}function nt(n){return n}function pt(n){throw n;}function vr(n,t,r,u){var f;try{n&&i.isFunction(f=n.promise)?f.call(n).done(t).fail(r):n&&i.isFunction(f=n.then)?f.call(n,t,r):t.apply(void 0,[n].slice(u))}catch(n){r.apply(void 0,[n])}}function bt(){u.removeEventListener("DOMContentLoaded",bt);n.removeEventListener("load",bt);i.ready()}function ht(){this.expando=i.expando+ht.uid++}function re(n){return"true"===n||"false"!==n&&("null"===n?null:n===+n+""?+n:te.test(n)?JSON.parse(n):n)}function pr(n,t,i){var r;if(void 0===i&&1===n.nodeType)if(r="data-"+t.replace(ie,"-$&").toLowerCase(),i=n.getAttribute(r),"string"==typeof i){try{i=re(i)}catch(u){}e.set(n,t,i)}else i=void 0;return i}function kr(n,t,r,u){var h,e=1,l=20,c=u?function(){return u.cur()}:function(){return i.css(n,t,"")},s=c(),o=r&&r[3]||(i.cssNumber[t]?"":"px"),f=(i.cssNumber[t]||"px"!==o&&+s)&&ct.exec(i.css(n,t));if(f&&f[3]!==o){o=o||f[3];r=r||[];f=+s||1;do e=e||".5",f/=e,i.style(n,t,f+o);while(e!==(e=c()/s)&&1!==e&&--l)}return r&&(f=+f||+s||0,h=r[1]?f+(r[1]+1)*r[2]:+r[2],u&&(u.unit=o,u.start=f,u.end=h)),h}function ue(n){var r,f=n.ownerDocument,u=n.nodeName,t=si[u];return t?t:(r=f.body.appendChild(f.createElement(u)),t=i.css(r,"display"),r.parentNode.removeChild(r),"none"===t&&(t="block"),si[u]=t,t)}function tt(n,t){for(var e,u,f=[],i=0,o=n.length;i<o;i++)u=n[i],u.style&&(e=u.style.display,t?("none"===e&&(f[i]=r.get(u,"display")||null,f[i]||(u.style.display="")),""===u.style.display&&kt(u)&&(f[i]=ue(u))):"none"!==e&&(f[i]="none",r.set(u,"display",e)));for(i=0;i<o;i++)null!=f[i]&&(n[i].style.display=f[i]);return n}function o(n,t){var r;return r="undefined"!=typeof n.getElementsByTagName?n.getElementsByTagName(t||"*"):"undefined"!=typeof n.querySelectorAll?n.querySelectorAll(t||"*"):[],void 0===t||t&&l(n,t)?i.merge([n],r):r}function hi(n,t){for(var i=0,u=n.length;i<u;i++)r.set(n[i],"globalEval",!t||r.get(t[i],"globalEval"))}function iu(n,t,r,u,f){for(var e,s,p,a,w,v,h=t.createDocumentFragment(),y=[],l=0,b=n.length;l<b;l++)if(e=n[l],e||0===e)if("object"===i.type(e))i.merge(y,e.nodeType?[e]:e);else if(tu.test(e)){for(s=s||h.appendChild(t.createElement("div")),p=(gr.exec(e)||["",""])[1].toLowerCase(),a=c[p]||c._default,s.innerHTML=a[1]+i.htmlPrefilter(e)+a[2],v=a[0];v--;)s=s.lastChild;i.merge(y,s.childNodes);s=h.firstChild;s.textContent=""}else y.push(t.createTextNode(e));for(h.textContent="",l=0;e=y[l++];)if(u&&i.inArray(e,u)>-1)f&&f.push(e);else if(w=i.contains(e.ownerDocument,e),s=o(h.appendChild(e),"script"),w&&hi(s),r)for(v=0;e=s[v++];)nu.test(e.type||"")&&r.push(e);return h}function gt(){return!0}function it(){return!1}function uu(){try{return u.activeElement}catch(n){}}function ci(n,t,r,u,f,e){var o,s;if("object"==typeof t){"string"!=typeof r&&(u=u||r,r=void 0);for(s in t)ci(n,s,r,u,t[s],e);return n}if(null==u&&null==f?(f=r,u=r=void 0):null==f&&("string"==typeof r?(f=u,u=void 0):(f=u,u=r,r=void 0)),f===!1)f=it;else if(!f)return n;return 1===e&&(o=f,f=function(n){return i().off(n),o.apply(this,arguments)},f.guid=o.guid||(o.guid=i.guid++)),n.each(function(){i.event.add(this,t,f,u,r)})}function fu(n,t){return l(n,"table")&&l(11!==t.nodeType?t:t.firstChild,"tr")?i(">tbody",n)[0]||n:n}function ae(n){return n.type=(null!==n.getAttribute("type"))+"/"+n.type,n}function ve(n){var t=ce.exec(n.type);return t?n.type=t[1]:n.removeAttribute("type"),n}function eu(n,t){var u,c,f,s,h,l,a,o;if(1===t.nodeType){if(r.hasData(n)&&(s=r.access(n),h=r.set(t,s),o=s.events)){delete h.handle;h.events={};for(f in o)for(u=0,c=o[f].length;u<c;u++)i.event.add(t,f,o[f][u])}e.hasData(n)&&(l=e.access(n),a=i.extend({},l),e.set(t,a))}}function ye(n,t){var i=t.nodeName.toLowerCase();"input"===i&&dr.test(n.type)?t.checked=n.checked:"input"!==i&&"textarea"!==i||(t.defaultValue=n.defaultValue)}function rt(n,t,u,e){t=gi.apply([],t);var l,p,c,a,s,w,h=0,v=n.length,k=v-1,y=t[0],b=i.isFunction(y);if(b||v>1&&"string"==typeof y&&!f.checkClone&&he.test(y))return n.each(function(i){var r=n.eq(i);b&&(t[0]=y.call(this,i,r.html()));rt(r,t,u,e)});if(v&&(l=iu(t,n[0].ownerDocument,!1,n,e),p=l.firstChild,1===l.childNodes.length&&(l=p),p||e)){for(c=i.map(o(l,"script"),ae),a=c.length;h<v;h++)s=l,h!==k&&(s=i.clone(s,!0,!0),a&&i.merge(c,o(s,"script"))),u.call(n[h],s,h);if(a)for(w=c[c.length-1].ownerDocument,i.map(c,ve),h=0;h<a;h++)s=c[h],nu.test(s.type||"")&&!r.access(s,"globalEval")&&i.contains(w,s)&&(s.src?i._evalUrl&&i._evalUrl(s.src):ir(s.textContent.replace(le,""),w))}return n}function ou(n,t,r){for(var u,e=t?i.filter(t,n):n,f=0;null!=(u=e[f]);f++)r||1!==u.nodeType||i.cleanData(o(u)),u.parentNode&&(r&&i.contains(u.ownerDocument,u)&&hi(o(u,"script")),u.parentNode.removeChild(u));return n}function lt(n,t,r){var o,s,h,u,e=n.style;return r=r||ni(n),r&&(u=r.getPropertyValue(t)||r[t],""!==u||i.contains(n.ownerDocument,n)||(u=i.style(n,t)),!f.pixelMarginRight()&&li.test(u)&&su.test(t)&&(o=e.width,s=e.minWidth,h=e.maxWidth,e.minWidth=e.maxWidth=e.width=u,u=r.width,e.width=o,e.minWidth=s,e.maxWidth=h)),void 0!==u?u+"":u}function hu(n,t){return{get:function(){return n()?void delete this.get:(this.get=t).apply(this,arguments)}}}function be(n){if(n in vu)return n;for(var i=n[0].toUpperCase()+n.slice(1),t=au.length;t--;)if(n=au[t]+i,n in vu)return n}function yu(n){var t=i.cssProps[n];return t||(t=i.cssProps[n]=be(n)||n),t}function pu(n,t,i){var r=ct.exec(t);return r?Math.max(0,r[2]-(i||0))+(r[3]||"px"):t}function wu(n,t,r,u,f){for(var o=0,e=r===(u?"border":"content")?4:"width"===t?1:0;e<4;e+=2)"margin"===r&&(o+=i.css(n,r+b[e],!0,f)),u?("content"===r&&(o-=i.css(n,"padding"+b[e],!0,f)),"margin"!==r&&(o-=i.css(n,"border"+b[e]+"Width",!0,f))):(o+=i.css(n,"padding"+b[e],!0,f),"padding"!==r&&(o+=i.css(n,"border"+b[e]+"Width",!0,f)));return o}function bu(n,t,r){var o,e=ni(n),u=lt(n,t,e),s="border-box"===i.css(n,"boxSizing",!1,e);return li.test(u)?u:(o=s&&(f.boxSizingReliable()||u===n.style[t]),"auto"===u&&(u=n["offset"+t[0].toUpperCase()+t.slice(1)]),u=parseFloat(u)||0,u+wu(n,t,r||(s?"border":"content"),o,e)+"px")}function s(n,t,i,r,u){return new s.prototype.init(n,t,i,r,u)}function ai(){ti&&(u.hidden===!1&&n.requestAnimationFrame?n.requestAnimationFrame(ai):n.setTimeout(ai,i.fx.interval),i.fx.tick())}function gu(){return n.setTimeout(function(){ut=void 0}),ut=i.now()}function ii(n,t){var r,u=0,i={height:n};for(t=t?1:0;u<4;u+=2-t)r=b[u],i["margin"+r]=i["padding"+r]=n;return t&&(i.opacity=i.width=n),i}function nf(n,t,i){for(var u,f=(a.tweeners[t]||[]).concat(a.tweeners["*"]),r=0,e=f.length;r<e;r++)if(u=f[r].call(i,t,n))return u}function ke(n,t,u){var f,y,w,c,b,s,o,l,k="width"in t||"height"in t,v=this,p={},h=n.style,a=n.nodeType&&kt(n),e=r.get(n,"fxshow");u.queue||(c=i._queueHooks(n,"fx"),null==c.unqueued&&(c.unqueued=0,b=c.empty.fire,c.empty.fire=function(){c.unqueued||b()}),c.unqueued++,v.always(function(){v.always(function(){c.unqueued--;i.queue(n,"fx").length||c.empty.fire()})}));for(f in t)if(y=t[f],ku.test(y)){if(delete t[f],w=w||"toggle"===y,y===(a?"hide":"show")){if("show"!==y||!e||void 0===e[f])continue;a=!0}p[f]=e&&e[f]||i.style(n,f)}if(s=!i.isEmptyObject(t),s||!i.isEmptyObject(p)){k&&1===n.nodeType&&(u.overflow=[h.overflow,h.overflowX,h.overflowY],o=e&&e.display,null==o&&(o=r.get(n,"display")),l=i.css(n,"display"),"none"===l&&(o?l=o:(tt([n],!0),o=n.style.display||o,l=i.css(n,"display"),tt([n]))),("inline"===l||"inline-block"===l&&null!=o)&&"none"===i.css(n,"float")&&(s||(v.done(function(){h.display=o}),null==o&&(l=h.display,o="none"===l?"":l)),h.display="inline-block"));u.overflow&&(h.overflow="hidden",v.always(function(){h.overflow=u.overflow[0];h.overflowX=u.overflow[1];h.overflowY=u.overflow[2]}));s=!1;for(f in p)s||(e?"hidden"in e&&(a=e.hidden):e=r.access(n,"fxshow",{display:o}),w&&(e.hidden=!a),a&&tt([n],!0),v.done(function(){a||tt([n]);r.remove(n,"fxshow");for(f in p)i.style(n,f,p[f])})),s=nf(a?e[f]:0,f,v),f in e||(e[f]=s.start,a&&(s.end=s.start,s.start=0))}}function de(n,t){var r,f,e,u,o;for(r in n)if(f=i.camelCase(r),e=t[f],u=n[r],Array.isArray(u)&&(e=u[1],u=n[r]=u[0]),r!==f&&(n[f]=u,delete n[r]),o=i.cssHooks[f],o&&"expand"in o){u=o.expand(u);delete n[f];for(r in u)r in n||(n[r]=u[r],t[r]=e)}else t[f]=e}function a(n,t,r){var e,o,s=0,l=a.prefilters.length,f=i.Deferred().always(function(){delete c.elem}),c=function(){if(o)return!1;for(var s=ut||gu(),t=Math.max(0,u.startTime+u.duration-s),h=t/u.duration||0,i=1-h,r=0,e=u.tweens.length;r<e;r++)u.tweens[r].run(i);return f.notifyWith(n,[u,i,t]),i<1&&e?t:(e||f.notifyWith(n,[u,1,0]),f.resolveWith(n,[u]),!1)},u=f.promise({elem:n,props:i.extend({},t),opts:i.extend(!0,{specialEasing:{},easing:i.easing._default},r),originalProperties:t,originalOptions:r,startTime:ut||gu(),duration:r.duration,tweens:[],createTween:function(t,r){var f=i.Tween(n,u.opts,t,r,u.opts.specialEasing[t]||u.opts.easing);return u.tweens.push(f),f},stop:function(t){var i=0,r=t?u.tweens.length:0;if(o)return this;for(o=!0;i<r;i++)u.tweens[i].run(1);return t?(f.notifyWith(n,[u,1,0]),f.resolveWith(n,[u,t])):f.rejectWith(n,[u,t]),this}}),h=u.props;for(de(h,u.opts.specialEasing);s<l;s++)if(e=a.prefilters[s].call(u,n,h,u.opts))return i.isFunction(e.stop)&&(i._queueHooks(u.elem,u.opts.queue).stop=i.proxy(e.stop,e)),e;return i.map(h,nf,u),i.isFunction(u.opts.start)&&u.opts.start.call(n,u),u.progress(u.opts.progress).done(u.opts.done,u.opts.complete).fail(u.opts.fail).always(u.opts.always),i.fx.timer(i.extend(c,{elem:n,anim:u,queue:u.opts.queue})),u}function k(n){var t=n.match(h)||[];return t.join(" ")}function d(n){return n.getAttribute&&n.getAttribute("class")||""}function pi(n,t,r,u){var f;if(Array.isArray(t))i.each(t,function(t,i){r||ge.test(n)?u(n,i):pi(n+"["+("object"==typeof i&&null!=i?t:"")+"]",i,r,u)});else if(r||"object"!==i.type(t))u(n,t);else for(f in t)pi(n+"["+f+"]",t[f],r,u)}function cf(n){return function(t,r){"string"!=typeof t&&(r=t,t="*");var u,f=0,e=t.toLowerCase().match(h)||[];if(i.isFunction(r))while(u=e[f++])"+"===u[0]?(u=u.slice(1)||"*",(n[u]=n[u]||[]).unshift(r)):(n[u]=n[u]||[]).push(r)}}function lf(n,t,r,u){function e(s){var h;return f[s]=!0,i.each(n[s]||[],function(n,i){var s=i(t,r,u);return"string"!=typeof s||o||f[s]?o?!(h=s):void 0:(t.dataTypes.unshift(s),e(s),!1)}),h}var f={},o=n===wi;return e(t.dataTypes[0])||!f["*"]&&e("*")}function ki(n,t){var r,u,f=i.ajaxSettings.flatOptions||{};for(r in t)void 0!==t[r]&&((f[r]?n:u||(u={}))[r]=t[r]);return u&&i.extend(!0,n,u),n}function so(n,t,i){for(var e,u,f,o,s=n.contents,r=n.dataTypes;"*"===r[0];)r.shift(),void 0===e&&(e=n.mimeType||t.getResponseHeader("Content-Type"));if(e)for(u in s)if(s[u]&&s[u].test(e)){r.unshift(u);break}if(r[0]in i)f=r[0];else{for(u in i){if(!r[0]||n.converters[u+" "+r[0]]){f=u;break}o||(o=u)}f=f||o}if(f)return f!==r[0]&&r.unshift(f),i[f]}function ho(n,t,i,r){var h,u,f,s,e,o={},c=n.dataTypes.slice();if(c[1])for(f in n.converters)o[f.toLowerCase()]=n.converters[f];for(u=c.shift();u;)if(n.responseFields[u]&&(i[n.responseFields[u]]=t),!e&&r&&n.dataFilter&&(t=n.dataFilter(t,n.dataType)),e=u,u=c.shift())if("*"===u)u=e;else if("*"!==e&&e!==u){if(f=o[e+" "+u]||o["* "+u],!f)for(h in o)if(s=h.split(" "),s[1]===u&&(f=o[e+" "+s[0]]||o["* "+s[0]])){f===!0?f=o[h]:o[h]!==!0&&(u=s[0],c.unshift(s[1]));break}if(f!==!0)if(f&&n.throws)t=f(t);else try{t=f(t)}catch(l){return{state:"parsererror",error:f?l:"No conversion from "+e+" to "+u}}}return{state:"success",data:t}}var p=[],u=n.document,pf=Object.getPrototypeOf,w=p.slice,gi=p.concat,ui=p.push,ot=p.indexOf,vt={},nr=vt.toString,yt=vt.hasOwnProperty,tr=yt.toString,wf=tr.call(Object),f={},rr="3.2.1",i=function(n,t){return new i.fn.init(n,t)},bf=/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,kf=/^-ms-/,df=/-([a-z])/g,gf=function(n,t){return t.toUpperCase()},y,ei,er,or,sr,hr,cr,lr,h,yr,wt,v,st,si,tu,ut,ti,ku,du,tf,ft,rf,uf,ff,vi,af,et,di,ri,vf,yf;i.fn=i.prototype={jquery:rr,constructor:i,length:0,toArray:function(){return w.call(this)},get:function(n){return null==n?w.call(this):n<0?this[n+this.length]:this[n]},pushStack:function(n){var t=i.merge(this.constructor(),n);return t.prevObject=this,t},each:function(n){return i.each(this,n)},map:function(n){return this.pushStack(i.map(this,function(t,i){return n.call(t,i,t)}))},slice:function(){return this.pushStack(w.apply(this,arguments))},first:function(){return this.eq(0)},last:function(){return this.eq(-1)},eq:function(n){var i=this.length,t=+n+(n<0?i:0);return this.pushStack(t>=0&&t<i?[this[t]]:[])},end:function(){return this.prevObject||this.constructor()},push:ui,sort:p.sort,splice:p.splice};i.extend=i.fn.extend=function(){var e,f,r,t,o,s,n=arguments[0]||{},u=1,c=arguments.length,h=!1;for("boolean"==typeof n&&(h=n,n=arguments[u]||{},u++),"object"==typeof n||i.isFunction(n)||(n={}),u===c&&(n=this,u--);u<c;u++)if(null!=(e=arguments[u]))for(f in e)r=n[f],t=e[f],n!==t&&(h&&t&&(i.isPlainObject(t)||(o=Array.isArray(t)))?(o?(o=!1,s=r&&Array.isArray(r)?r:[]):s=r&&i.isPlainObject(r)?r:{},n[f]=i.extend(h,s,t)):void 0!==t&&(n[f]=t));return n};i.extend({expando:"jQuery"+(rr+Math.random()).replace(/\D/g,""),isReady:!0,error:function(n){throw new Error(n);},noop:function(){},isFunction:function(n){return"function"===i.type(n)},isWindow:function(n){return null!=n&&n===n.window},isNumeric:function(n){var t=i.type(n);return("number"===t||"string"===t)&&!isNaN(n-parseFloat(n))},isPlainObject:function(n){var t,i;return!(!n||"[object Object]"!==nr.call(n))&&(!(t=pf(n))||(i=yt.call(t,"constructor")&&t.constructor,"function"==typeof i&&tr.call(i)===wf))},isEmptyObject:function(n){for(var t in n)return!1;return!0},type:function(n){return null==n?n+"":"object"==typeof n||"function"==typeof n?vt[nr.call(n)]||"object":typeof n},globalEval:function(n){ir(n)},camelCase:function(n){return n.replace(kf,"ms-").replace(df,gf)},each:function(n,t){var r,i=0;if(fi(n)){for(r=n.length;i<r;i++)if(t.call(n[i],i,n[i])===!1)break}else for(i in n)if(t.call(n[i],i,n[i])===!1)break;return n},trim:function(n){return null==n?"":(n+"").replace(bf,"")},makeArray:function(n,t){var r=t||[];return null!=n&&(fi(Object(n))?i.merge(r,"string"==typeof n?[n]:n):ui.call(r,n)),r},inArray:function(n,t,i){return null==t?-1:ot.call(t,n,i)},merge:function(n,t){for(var u=+t.length,i=0,r=n.length;i<u;i++)n[r++]=t[i];return n.length=r,n},grep:function(n,t,i){for(var u,f=[],r=0,e=n.length,o=!i;r<e;r++)u=!t(n[r],r),u!==o&&f.push(n[r]);return f},map:function(n,t,i){var e,u,r=0,f=[];if(fi(n))for(e=n.length;r<e;r++)u=t(n[r],r,i),null!=u&&f.push(u);else for(r in n)u=t(n[r],r,i),null!=u&&f.push(u);return gi.apply([],f)},guid:1,proxy:function(n,t){var u,f,r;if("string"==typeof t&&(u=n[t],t=n,n=u),i.isFunction(n))return f=w.call(arguments,2),r=function(){return n.apply(t||this,f.concat(w.call(arguments)))},r.guid=n.guid=n.guid||i.guid++,r},now:Date.now,support:f});"function"==typeof Symbol&&(i.fn[Symbol.iterator]=p[Symbol.iterator]);i.each("Boolean Number String Function Array Date RegExp Object Error Symbol".split(" "),function(n,t){vt["[object "+t+"]"]=t.toLowerCase()});y=function(n){function u(n,t,r,u){var s,w,l,a,d,y,g,p=t&&t.ownerDocument,v=t?t.nodeType:9;if(r=r||[],"string"!=typeof n||!n||1!==v&&9!==v&&11!==v)return r;if(!u&&((t?t.ownerDocument||t:c)!==i&&b(t),t=t||i,h)){if(11!==v&&(d=cr.exec(n)))if(s=d[1]){if(9===v){if(!(l=t.getElementById(s)))return r;if(l.id===s)return r.push(l),r}else if(p&&(l=p.getElementById(s))&&et(t,l)&&l.id===s)return r.push(l),r}else{if(d[2])return k.apply(r,t.getElementsByTagName(n)),r;if((s=d[3])&&e.getElementsByClassName&&t.getElementsByClassName)return k.apply(r,t.getElementsByClassName(s)),r}if(e.qsa&&!lt[n+" "]&&(!o||!o.test(n))){if(1!==v)p=t,g=n;else if("object"!==t.nodeName.toLowerCase()){for((a=t.getAttribute("id"))?a=a.replace(vi,yi):t.setAttribute("id",a=f),y=ft(n),w=y.length;w--;)y[w]="#"+a+" "+yt(y[w]);g=y.join(",");p=ni.test(n)&&ri(t.parentNode)||t}if(g)try{return k.apply(r,p.querySelectorAll(g)),r}catch(nt){}finally{a===f&&t.removeAttribute("id")}}}return si(n.replace(at,"$1"),t,r,u)}function ti(){function n(r,u){return i.push(r+" ")>t.cacheLength&&delete n[i.shift()],n[r+" "]=u}var i=[];return n}function l(n){return n[f]=!0,n}function a(n){var t=i.createElement("fieldset");try{return!!n(t)}catch(r){return!1}finally{t.parentNode&&t.parentNode.removeChild(t);t=null}}function ii(n,i){for(var r=n.split("|"),u=r.length;u--;)t.attrHandle[r[u]]=i}function wi(n,t){var i=t&&n,r=i&&1===n.nodeType&&1===t.nodeType&&n.sourceIndex-t.sourceIndex;if(r)return r;if(i)while(i=i.nextSibling)if(i===t)return-1;return n?1:-1}function ar(n){return function(t){var i=t.nodeName.toLowerCase();return"input"===i&&t.type===n}}function vr(n){return function(t){var i=t.nodeName.toLowerCase();return("input"===i||"button"===i)&&t.type===n}}function bi(n){return function(t){return"form"in t?t.parentNode&&t.disabled===!1?"label"in t?"label"in t.parentNode?t.parentNode.disabled===n:t.disabled===n:t.isDisabled===n||t.isDisabled!==!n&&lr(t)===n:t.disabled===n:"label"in t&&t.disabled===n}}function it(n){return l(function(t){return t=+t,l(function(i,r){for(var u,f=n([],i.length,t),e=f.length;e--;)i[u=f[e]]&&(i[u]=!(r[u]=i[u]))})})}function ri(n){return n&&"undefined"!=typeof n.getElementsByTagName&&n}function ki(){}function yt(n){for(var t=0,r=n.length,i="";t<r;t++)i+=n[t].value;return i}function pt(n,t,i){var r=t.dir,u=t.next,e=u||r,o=i&&"parentNode"===e,s=di++;return t.first?function(t,i,u){while(t=t[r])if(1===t.nodeType||o)return n(t,i,u);return!1}:function(t,i,h){var c,l,a,y=[v,s];if(h){while(t=t[r])if((1===t.nodeType||o)&&n(t,i,h))return!0}else while(t=t[r])if(1===t.nodeType||o)if(a=t[f]||(t[f]={}),l=a[t.uniqueID]||(a[t.uniqueID]={}),u&&u===t.nodeName.toLowerCase())t=t[r]||t;else{if((c=l[e])&&c[0]===v&&c[1]===s)return y[2]=c[2];if(l[e]=y,y[2]=n(t,i,h))return!0}return!1}}function ui(n){return n.length>1?function(t,i,r){for(var u=n.length;u--;)if(!n[u](t,i,r))return!1;return!0}:n[0]}function yr(n,t,i){for(var r=0,f=t.length;r<f;r++)u(n,t[r],i);return i}function wt(n,t,i,r,u){for(var e,o=[],f=0,s=n.length,h=null!=t;f<s;f++)(e=n[f])&&(i&&!i(e,r,u)||(o.push(e),h&&t.push(f)));return o}function fi(n,t,i,r,u,e){return r&&!r[f]&&(r=fi(r)),u&&!u[f]&&(u=fi(u,e)),l(function(f,e,o,s){var l,c,a,p=[],y=[],w=e.length,b=f||yr(t||"*",o.nodeType?[o]:o,[]),v=!n||!f&&t?b:wt(b,p,n,o,s),h=i?u||(f?n:w||r)?[]:e:v;if(i&&i(v,h,o,s),r)for(l=wt(h,y),r(l,[],o,s),c=l.length;c--;)(a=l[c])&&(h[y[c]]=!(v[y[c]]=a));if(f){if(u||n){if(u){for(l=[],c=h.length;c--;)(a=h[c])&&l.push(v[c]=a);u(null,h=[],l,s)}for(c=h.length;c--;)(a=h[c])&&(l=u?nt(f,a):p[c])>-1&&(f[l]=!(e[l]=a))}}else h=wt(h===e?h.splice(w,h.length):h),u?u(null,e,h,s):k.apply(e,h)})}function ei(n){for(var o,u,r,s=n.length,h=t.relative[n[0].type],c=h||t.relative[" "],i=h?1:0,l=pt(function(n){return n===o},c,!0),a=pt(function(n){return nt(o,n)>-1},c,!0),e=[function(n,t,i){var r=!h&&(i||t!==ht)||((o=t).nodeType?l(n,t,i):a(n,t,i));return o=null,r}];i<s;i++)if(u=t.relative[n[i].type])e=[pt(ui(e),u)];else{if(u=t.filter[n[i].type].apply(null,n[i].matches),u[f]){for(r=++i;r<s;r++)if(t.relative[n[r].type])break;return fi(i>1&&ui(e),i>1&&yt(n.slice(0,i-1).concat({value:" "===n[i-2].type?"*":""})).replace(at,"$1"),u,i<r&&ei(n.slice(i,r)),r<s&&ei(n=n.slice(r)),r<s&&yt(n))}e.push(u)}return ui(e)}function pr(n,r){var f=r.length>0,e=n.length>0,o=function(o,s,c,l,a){var y,nt,d,g=0,p="0",tt=o&&[],w=[],it=ht,rt=o||e&&t.find.TAG("*",a),ut=v+=null==it?1:Math.random()||.1,ft=rt.length;for(a&&(ht=s===i||s||a);p!==ft&&null!=(y=rt[p]);p++){if(e&&y){for(nt=0,s||y.ownerDocument===i||(b(y),c=!h);d=n[nt++];)if(d(y,s||i,c)){l.push(y);break}a&&(v=ut)}f&&((y=!d&&y)&&g--,o&&tt.push(y))}if(g+=p,f&&p!==g){for(nt=0;d=r[nt++];)d(tt,w,s,c);if(o){if(g>0)while(p--)tt[p]||w[p]||(w[p]=nr.call(l));w=wt(w)}k.apply(l,w);a&&!o&&w.length>0&&g+r.length>1&&u.uniqueSort(l)}return a&&(v=ut,ht=it),tt};return f?l(o):o}var rt,e,t,st,oi,ft,bt,si,ht,w,ut,b,i,s,h,o,d,ct,et,f="sizzle"+1*new Date,c=n.document,v=0,di=0,hi=ti(),ci=ti(),lt=ti(),kt=function(n,t){return n===t&&(ut=!0),0},gi={}.hasOwnProperty,g=[],nr=g.pop,tr=g.push,k=g.push,li=g.slice,nt=function(n,t){for(var i=0,r=n.length;i<r;i++)if(n[i]===t)return i;return-1},dt="checked|selected|async|autofocus|autoplay|controls|defer|disabled|hidden|ismap|loop|multiple|open|readonly|required|scoped",r="[\\x20\\t\\r\\n\\f]",tt="(?:\\\\.|[\\w-]|[^\0-\\xa0])+",ai="\\["+r+"*("+tt+")(?:"+r+"*([*^$|!~]?=)"+r+"*(?:'((?:\\\\.|[^\\\\'])*)'|\"((?:\\\\.|[^\\\\\"])*)\"|("+tt+"))|)"+r+"*\\]",gt=":("+tt+")(?:\\((('((?:\\\\.|[^\\\\'])*)'|\"((?:\\\\.|[^\\\\\"])*)\")|((?:\\\\.|[^\\\\()[\\]]|"+ai+")*)|.*)\\)|)",ir=new RegExp(r+"+","g"),at=new RegExp("^"+r+"+|((?:^|[^\\\\])(?:\\\\.)*)"+r+"+$","g"),rr=new RegExp("^"+r+"*,"+r+"*"),ur=new RegExp("^"+r+"*([>+~]|"+r+")"+r+"*"),fr=new RegExp("="+r+"*([^\\]'\"]*?)"+r+"*\\]","g"),er=new RegExp(gt),or=new RegExp("^"+tt+"$"),vt={ID:new RegExp("^#("+tt+")"),CLASS:new RegExp("^\\.("+tt+")"),TAG:new RegExp("^("+tt+"|[*])"),ATTR:new RegExp("^"+ai),PSEUDO:new RegExp("^"+gt),CHILD:new RegExp("^:(only|first|last|nth|nth-last)-(child|of-type)(?:\\("+r+"*(even|odd|(([+-]|)(\\d*)n|)"+r+"*(?:([+-]|)"+r+"*(\\d+)|))"+r+"*\\)|)","i"),bool:new RegExp("^(?:"+dt+")$","i"),needsContext:new RegExp("^"+r+"*[>+~]|:(even|odd|eq|gt|lt|nth|first|last)(?:\\("+r+"*((?:-\\d)?\\d*)"+r+"*\\)|)(?=[^-]|$)","i")},sr=/^(?:input|select|textarea|button)$/i,hr=/^h\d$/i,ot=/^[^{]+\{\s*\[native \w/,cr=/^(?:#([\w-]+)|(\w+)|\.([\w-]+))$/,ni=/[+~]/,y=new RegExp("\\\\([\\da-f]{1,6}"+r+"?|("+r+")|.)","ig"),p=function(n,t,i){var r="0x"+t-65536;return r!==r||i?t:r<0?String.fromCharCode(r+65536):String.fromCharCode(r>>10|55296,1023&r|56320)},vi=/([\0-\x1f\x7f]|^-?\d)|^-$|[^\0-\x1f\x7f-\uFFFF\w-]/g,yi=function(n,t){return t?"\0"===n?"�":n.slice(0,-1)+"\\"+n.charCodeAt(n.length-1).toString(16)+" ":"\\"+n},pi=function(){b()},lr=pt(function(n){return n.disabled===!0&&("form"in n||"label"in n)},{dir:"parentNode",next:"legend"});try{k.apply(g=li.call(c.childNodes),c.childNodes);g[c.childNodes.length].nodeType}catch(wr){k={apply:g.length?function(n,t){tr.apply(n,li.call(t))}:function(n,t){for(var i=n.length,r=0;n[i++]=t[r++];);n.length=i-1}}}e=u.support={};oi=u.isXML=function(n){var t=n&&(n.ownerDocument||n).documentElement;return!!t&&"HTML"!==t.nodeName};b=u.setDocument=function(n){var v,u,l=n?n.ownerDocument||n:c;return l!==i&&9===l.nodeType&&l.documentElement?(i=l,s=i.documentElement,h=!oi(i),c!==i&&(u=i.defaultView)&&u.top!==u&&(u.addEventListener?u.addEventListener("unload",pi,!1):u.attachEvent&&u.attachEvent("onunload",pi)),e.attributes=a(function(n){return n.className="i",!n.getAttribute("className")}),e.getElementsByTagName=a(function(n){return n.appendChild(i.createComment("")),!n.getElementsByTagName("*").length}),e.getElementsByClassName=ot.test(i.getElementsByClassName),e.getById=a(function(n){return s.appendChild(n).id=f,!i.getElementsByName||!i.getElementsByName(f).length}),e.getById?(t.filter.ID=function(n){var t=n.replace(y,p);return function(n){return n.getAttribute("id")===t}},t.find.ID=function(n,t){if("undefined"!=typeof t.getElementById&&h){var i=t.getElementById(n);return i?[i]:[]}}):(t.filter.ID=function(n){var t=n.replace(y,p);return function(n){var i="undefined"!=typeof n.getAttributeNode&&n.getAttributeNode("id");return i&&i.value===t}},t.find.ID=function(n,t){if("undefined"!=typeof t.getElementById&&h){var i,u,f,r=t.getElementById(n);if(r){if(i=r.getAttributeNode("id"),i&&i.value===n)return[r];for(f=t.getElementsByName(n),u=0;r=f[u++];)if(i=r.getAttributeNode("id"),i&&i.value===n)return[r]}return[]}}),t.find.TAG=e.getElementsByTagName?function(n,t){return"undefined"!=typeof t.getElementsByTagName?t.getElementsByTagName(n):e.qsa?t.querySelectorAll(n):void 0}:function(n,t){var i,r=[],f=0,u=t.getElementsByTagName(n);if("*"===n){while(i=u[f++])1===i.nodeType&&r.push(i);return r}return u},t.find.CLASS=e.getElementsByClassName&&function(n,t){if("undefined"!=typeof t.getElementsByClassName&&h)return t.getElementsByClassName(n)},d=[],o=[],(e.qsa=ot.test(i.querySelectorAll))&&(a(function(n){s.appendChild(n).innerHTML="<a id='"+f+"'><\/a><select id='"+f+"-\r\\' msallowcapture=''><option selected=''><\/option><\/select>";n.querySelectorAll("[msallowcapture^='']").length&&o.push("[*^$]="+r+"*(?:''|\"\")");n.querySelectorAll("[selected]").length||o.push("\\["+r+"*(?:value|"+dt+")");n.querySelectorAll("[id~="+f+"-]").length||o.push("~=");n.querySelectorAll(":checked").length||o.push(":checked");n.querySelectorAll("a#"+f+"+*").length||o.push(".#.+[+~]")}),a(function(n){n.innerHTML="<a href='' disabled='disabled'><\/a><select disabled='disabled'><option/><\/select>";var t=i.createElement("input");t.setAttribute("type","hidden");n.appendChild(t).setAttribute("name","D");n.querySelectorAll("[name=d]").length&&o.push("name"+r+"*[*^$|!~]?=");2!==n.querySelectorAll(":enabled").length&&o.push(":enabled",":disabled");s.appendChild(n).disabled=!0;2!==n.querySelectorAll(":disabled").length&&o.push(":enabled",":disabled");n.querySelectorAll("*,:x");o.push(",.*:")})),(e.matchesSelector=ot.test(ct=s.matches||s.webkitMatchesSelector||s.mozMatchesSelector||s.oMatchesSelector||s.msMatchesSelector))&&a(function(n){e.disconnectedMatch=ct.call(n,"*");ct.call(n,"[s!='']:x");d.push("!=",gt)}),o=o.length&&new RegExp(o.join("|")),d=d.length&&new RegExp(d.join("|")),v=ot.test(s.compareDocumentPosition),et=v||ot.test(s.contains)?function(n,t){var r=9===n.nodeType?n.documentElement:n,i=t&&t.parentNode;return n===i||!(!i||1!==i.nodeType||!(r.contains?r.contains(i):n.compareDocumentPosition&&16&n.compareDocumentPosition(i)))}:function(n,t){if(t)while(t=t.parentNode)if(t===n)return!0;return!1},kt=v?function(n,t){if(n===t)return ut=!0,0;var r=!n.compareDocumentPosition-!t.compareDocumentPosition;return r?r:(r=(n.ownerDocument||n)===(t.ownerDocument||t)?n.compareDocumentPosition(t):1,1&r||!e.sortDetached&&t.compareDocumentPosition(n)===r?n===i||n.ownerDocument===c&&et(c,n)?-1:t===i||t.ownerDocument===c&&et(c,t)?1:w?nt(w,n)-nt(w,t):0:4&r?-1:1)}:function(n,t){if(n===t)return ut=!0,0;var r,u=0,o=n.parentNode,s=t.parentNode,f=[n],e=[t];if(!o||!s)return n===i?-1:t===i?1:o?-1:s?1:w?nt(w,n)-nt(w,t):0;if(o===s)return wi(n,t);for(r=n;r=r.parentNode;)f.unshift(r);for(r=t;r=r.parentNode;)e.unshift(r);while(f[u]===e[u])u++;return u?wi(f[u],e[u]):f[u]===c?-1:e[u]===c?1:0},i):i};u.matches=function(n,t){return u(n,null,null,t)};u.matchesSelector=function(n,t){if((n.ownerDocument||n)!==i&&b(n),t=t.replace(fr,"='$1']"),e.matchesSelector&&h&&!lt[t+" "]&&(!d||!d.test(t))&&(!o||!o.test(t)))try{var r=ct.call(n,t);if(r||e.disconnectedMatch||n.document&&11!==n.document.nodeType)return r}catch(f){}return u(t,i,null,[n]).length>0};u.contains=function(n,t){return(n.ownerDocument||n)!==i&&b(n),et(n,t)};u.attr=function(n,r){(n.ownerDocument||n)!==i&&b(n);var f=t.attrHandle[r.toLowerCase()],u=f&&gi.call(t.attrHandle,r.toLowerCase())?f(n,r,!h):void 0;return void 0!==u?u:e.attributes||!h?n.getAttribute(r):(u=n.getAttributeNode(r))&&u.specified?u.value:null};u.escape=function(n){return(n+"").replace(vi,yi)};u.error=function(n){throw new Error("Syntax error, unrecognized expression: "+n);};u.uniqueSort=function(n){var r,u=[],t=0,i=0;if(ut=!e.detectDuplicates,w=!e.sortStable&&n.slice(0),n.sort(kt),ut){while(r=n[i++])r===n[i]&&(t=u.push(i));while(t--)n.splice(u[t],1)}return w=null,n};st=u.getText=function(n){var r,i="",u=0,t=n.nodeType;if(t){if(1===t||9===t||11===t){if("string"==typeof n.textContent)return n.textContent;for(n=n.firstChild;n;n=n.nextSibling)i+=st(n)}else if(3===t||4===t)return n.nodeValue}else while(r=n[u++])i+=st(r);return i};t=u.selectors={cacheLength:50,createPseudo:l,match:vt,attrHandle:{},find:{},relative:{">":{dir:"parentNode",first:!0}," ":{dir:"parentNode"},"+":{dir:"previousSibling",first:!0},"~":{dir:"previousSibling"}},preFilter:{ATTR:function(n){return n[1]=n[1].replace(y,p),n[3]=(n[3]||n[4]||n[5]||"").replace(y,p),"~="===n[2]&&(n[3]=" "+n[3]+" "),n.slice(0,4)},CHILD:function(n){return n[1]=n[1].toLowerCase(),"nth"===n[1].slice(0,3)?(n[3]||u.error(n[0]),n[4]=+(n[4]?n[5]+(n[6]||1):2*("even"===n[3]||"odd"===n[3])),n[5]=+(n[7]+n[8]||"odd"===n[3])):n[3]&&u.error(n[0]),n},PSEUDO:function(n){var i,t=!n[6]&&n[2];return vt.CHILD.test(n[0])?null:(n[3]?n[2]=n[4]||n[5]||"":t&&er.test(t)&&(i=ft(t,!0))&&(i=t.indexOf(")",t.length-i)-t.length)&&(n[0]=n[0].slice(0,i),n[2]=t.slice(0,i)),n.slice(0,3))}},filter:{TAG:function(n){var t=n.replace(y,p).toLowerCase();return"*"===n?function(){return!0}:function(n){return n.nodeName&&n.nodeName.toLowerCase()===t}},CLASS:function(n){var t=hi[n+" "];return t||(t=new RegExp("(^|"+r+")"+n+"("+r+"|$)"))&&hi(n,function(n){return t.test("string"==typeof n.className&&n.className||"undefined"!=typeof n.getAttribute&&n.getAttribute("class")||"")})},ATTR:function(n,t,i){return function(r){var f=u.attr(r,n);return null==f?"!="===t:!t||(f+="","="===t?f===i:"!="===t?f!==i:"^="===t?i&&0===f.indexOf(i):"*="===t?i&&f.indexOf(i)>-1:"$="===t?i&&f.slice(-i.length)===i:"~="===t?(" "+f.replace(ir," ")+" ").indexOf(i)>-1:"|="===t&&(f===i||f.slice(0,i.length+1)===i+"-"))}},CHILD:function(n,t,i,r,u){var s="nth"!==n.slice(0,3),o="last"!==n.slice(-4),e="of-type"===t;return 1===r&&0===u?function(n){return!!n.parentNode}:function(t,i,h){var p,w,y,c,a,b,k=s!==o?"nextSibling":"previousSibling",d=t.parentNode,nt=e&&t.nodeName.toLowerCase(),g=!h&&!e,l=!1;if(d){if(s){while(k){for(c=t;c=c[k];)if(e?c.nodeName.toLowerCase()===nt:1===c.nodeType)return!1;b=k="only"===n&&!b&&"nextSibling"}return!0}if(b=[o?d.firstChild:d.lastChild],o&&g){for(c=d,y=c[f]||(c[f]={}),w=y[c.uniqueID]||(y[c.uniqueID]={}),p=w[n]||[],a=p[0]===v&&p[1],l=a&&p[2],c=a&&d.childNodes[a];c=++a&&c&&c[k]||(l=a=0)||b.pop();)if(1===c.nodeType&&++l&&c===t){w[n]=[v,a,l];break}}else if(g&&(c=t,y=c[f]||(c[f]={}),w=y[c.uniqueID]||(y[c.uniqueID]={}),p=w[n]||[],a=p[0]===v&&p[1],l=a),l===!1)while(c=++a&&c&&c[k]||(l=a=0)||b.pop())if((e?c.nodeName.toLowerCase()===nt:1===c.nodeType)&&++l&&(g&&(y=c[f]||(c[f]={}),w=y[c.uniqueID]||(y[c.uniqueID]={}),w[n]=[v,l]),c===t))break;return l-=u,l===r||l%r==0&&l/r>=0}}},PSEUDO:function(n,i){var e,r=t.pseudos[n]||t.setFilters[n.toLowerCase()]||u.error("unsupported pseudo: "+n);return r[f]?r(i):r.length>1?(e=[n,n,"",i],t.setFilters.hasOwnProperty(n.toLowerCase())?l(function(n,t){for(var u,f=r(n,i),e=f.length;e--;)u=nt(n,f[e]),n[u]=!(t[u]=f[e])}):function(n){return r(n,0,e)}):r}},pseudos:{not:l(function(n){var t=[],r=[],i=bt(n.replace(at,"$1"));return i[f]?l(function(n,t,r,u){for(var e,o=i(n,null,u,[]),f=n.length;f--;)(e=o[f])&&(n[f]=!(t[f]=e))}):function(n,u,f){return t[0]=n,i(t,null,f,r),t[0]=null,!r.pop()}}),has:l(function(n){return function(t){return u(n,t).length>0}}),contains:l(function(n){return n=n.replace(y,p),function(t){return(t.textContent||t.innerText||st(t)).indexOf(n)>-1}}),lang:l(function(n){return or.test(n||"")||u.error("unsupported lang: "+n),n=n.replace(y,p).toLowerCase(),function(t){var i;do if(i=h?t.lang:t.getAttribute("xml:lang")||t.getAttribute("lang"))return i=i.toLowerCase(),i===n||0===i.indexOf(n+"-");while((t=t.parentNode)&&1===t.nodeType);return!1}}),target:function(t){var i=n.location&&n.location.hash;return i&&i.slice(1)===t.id},root:function(n){return n===s},focus:function(n){return n===i.activeElement&&(!i.hasFocus||i.hasFocus())&&!!(n.type||n.href||~n.tabIndex)},enabled:bi(!1),disabled:bi(!0),checked:function(n){var t=n.nodeName.toLowerCase();return"input"===t&&!!n.checked||"option"===t&&!!n.selected},selected:function(n){return n.parentNode&&n.parentNode.selectedIndex,n.selected===!0},empty:function(n){for(n=n.firstChild;n;n=n.nextSibling)if(n.nodeType<6)return!1;return!0},parent:function(n){return!t.pseudos.empty(n)},header:function(n){return hr.test(n.nodeName)},input:function(n){return sr.test(n.nodeName)},button:function(n){var t=n.nodeName.toLowerCase();return"input"===t&&"button"===n.type||"button"===t},text:function(n){var t;return"input"===n.nodeName.toLowerCase()&&"text"===n.type&&(null==(t=n.getAttribute("type"))||"text"===t.toLowerCase())},first:it(function(){return[0]}),last:it(function(n,t){return[t-1]}),eq:it(function(n,t,i){return[i<0?i+t:i]}),even:it(function(n,t){for(var i=0;i<t;i+=2)n.push(i);return n}),odd:it(function(n,t){for(var i=1;i<t;i+=2)n.push(i);return n}),lt:it(function(n,t,i){for(var r=i<0?i+t:i;--r>=0;)n.push(r);return n}),gt:it(function(n,t,i){for(var r=i<0?i+t:i;++r<t;)n.push(r);return n})}};t.pseudos.nth=t.pseudos.eq;for(rt in{radio:!0,checkbox:!0,file:!0,password:!0,image:!0})t.pseudos[rt]=ar(rt);for(rt in{submit:!0,reset:!0})t.pseudos[rt]=vr(rt);return ki.prototype=t.filters=t.pseudos,t.setFilters=new ki,ft=u.tokenize=function(n,i){var e,f,s,o,r,h,c,l=ci[n+" "];if(l)return i?0:l.slice(0);for(r=n,h=[],c=t.preFilter;r;){(!e||(f=rr.exec(r)))&&(f&&(r=r.slice(f[0].length)||r),h.push(s=[]));e=!1;(f=ur.exec(r))&&(e=f.shift(),s.push({value:e,type:f[0].replace(at," ")}),r=r.slice(e.length));for(o in t.filter)(f=vt[o].exec(r))&&(!c[o]||(f=c[o](f)))&&(e=f.shift(),s.push({value:e,type:o,matches:f}),r=r.slice(e.length));if(!e)break}return i?r.length:r?u.error(n):ci(n,h).slice(0)},bt=u.compile=function(n,t){var r,u=[],e=[],i=lt[n+" "];if(!i){for(t||(t=ft(n)),r=t.length;r--;)i=ei(t[r]),i[f]?u.push(i):e.push(i);i=lt(n,pr(e,u));i.selector=n}return i},si=u.select=function(n,i,r,u){var o,f,e,l,a,c="function"==typeof n&&n,s=!u&&ft(n=c.selector||n);if(r=r||[],1===s.length){if(f=s[0]=s[0].slice(0),f.length>2&&"ID"===(e=f[0]).type&&9===i.nodeType&&h&&t.relative[f[1].type]){if(i=(t.find.ID(e.matches[0].replace(y,p),i)||[])[0],!i)return r;c&&(i=i.parentNode);n=n.slice(f.shift().value.length)}for(o=vt.needsContext.test(n)?0:f.length;o--;){if(e=f[o],t.relative[l=e.type])break;if((a=t.find[l])&&(u=a(e.matches[0].replace(y,p),ni.test(f[0].type)&&ri(i.parentNode)||i))){if(f.splice(o,1),n=u.length&&yt(f),!n)return k.apply(r,u),r;break}}}return(c||bt(n,s))(u,i,!h,r,!i||ni.test(n)&&ri(i.parentNode)||i),r},e.sortStable=f.split("").sort(kt).join("")===f,e.detectDuplicates=!!ut,b(),e.sortDetached=a(function(n){return 1&n.compareDocumentPosition(i.createElement("fieldset"))}),a(function(n){return n.innerHTML="<a href='#'><\/a>","#"===n.firstChild.getAttribute("href")})||ii("type|href|height|width",function(n,t,i){if(!i)return n.getAttribute(t,"type"===t.toLowerCase()?1:2)}),e.attributes&&a(function(n){return n.innerHTML="<input/>",n.firstChild.setAttribute("value",""),""===n.firstChild.getAttribute("value")})||ii("value",function(n,t,i){if(!i&&"input"===n.nodeName.toLowerCase())return n.defaultValue}),a(function(n){return null==n.getAttribute("disabled")})||ii(dt,function(n,t,i){var r;if(!i)return n[t]===!0?t.toLowerCase():(r=n.getAttributeNode(t))&&r.specified?r.value:null}),u}(n);i.find=y;i.expr=y.selectors;i.expr[":"]=i.expr.pseudos;i.uniqueSort=i.unique=y.uniqueSort;i.text=y.getText;i.isXMLDoc=y.isXML;i.contains=y.contains;i.escapeSelector=y.escape;var g=function(n,t,r){for(var u=[],f=void 0!==r;(n=n[t])&&9!==n.nodeType;)if(1===n.nodeType){if(f&&i(n).is(r))break;u.push(n)}return u},ur=function(n,t){for(var i=[];n;n=n.nextSibling)1===n.nodeType&&n!==t&&i.push(n);return i},fr=i.expr.match.needsContext;ei=/^<([a-z][^\/\0>:\x20\t\r\n\f]*)[\x20\t\r\n\f]*\/?>(?:<\/\1>|)$/i;er=/^.[^:#\[\.,]*$/;i.filter=function(n,t,r){var u=t[0];return r&&(n=":not("+n+")"),1===t.length&&1===u.nodeType?i.find.matchesSelector(u,n)?[u]:[]:i.find.matches(n,i.grep(t,function(n){return 1===n.nodeType}))};i.fn.extend({find:function(n){var t,r,u=this.length,f=this;if("string"!=typeof n)return this.pushStack(i(n).filter(function(){for(t=0;t<u;t++)if(i.contains(f[t],this))return!0}));for(r=this.pushStack([]),t=0;t<u;t++)i.find(n,f[t],r);return u>1?i.uniqueSort(r):r},filter:function(n){return this.pushStack(oi(this,n||[],!1))},not:function(n){return this.pushStack(oi(this,n||[],!0))},is:function(n){return!!oi(this,"string"==typeof n&&fr.test(n)?i(n):n||[],!1).length}});sr=/^(?:\s*(<[\w\W]+>)[^>]*|#([\w-]+))$/;hr=i.fn.init=function(n,t,r){var f,e;if(!n)return this;if(r=r||or,"string"==typeof n){if(f="<"===n[0]&&">"===n[n.length-1]&&n.length>=3?[null,n,null]:sr.exec(n),!f||!f[1]&&t)return!t||t.jquery?(t||r).find(n):this.constructor(t).find(n);if(f[1]){if(t=t instanceof i?t[0]:t,i.merge(this,i.parseHTML(f[1],t&&t.nodeType?t.ownerDocument||t:u,!0)),ei.test(f[1])&&i.isPlainObject(t))for(f in t)i.isFunction(this[f])?this[f](t[f]):this.attr(f,t[f]);return this}return e=u.getElementById(f[2]),e&&(this[0]=e,this.length=1),this}return n.nodeType?(this[0]=n,this.length=1,this):i.isFunction(n)?void 0!==r.ready?r.ready(n):n(i):i.makeArray(n,this)};hr.prototype=i.fn;or=i(u);cr=/^(?:parents|prev(?:Until|All))/;lr={children:!0,contents:!0,next:!0,prev:!0};i.fn.extend({has:function(n){var t=i(n,this),r=t.length;return this.filter(function(){for(var n=0;n<r;n++)if(i.contains(this,t[n]))return!0})},closest:function(n,t){var r,f=0,o=this.length,u=[],e="string"!=typeof n&&i(n);if(!fr.test(n))for(;f<o;f++)for(r=this[f];r&&r!==t;r=r.parentNode)if(r.nodeType<11&&(e?e.index(r)>-1:1===r.nodeType&&i.find.matchesSelector(r,n))){u.push(r);break}return this.pushStack(u.length>1?i.uniqueSort(u):u)},index:function(n){return n?"string"==typeof n?ot.call(i(n),this[0]):ot.call(this,n.jquery?n[0]:n):this[0]&&this[0].parentNode?this.first().prevAll().length:-1},add:function(n,t){return this.pushStack(i.uniqueSort(i.merge(this.get(),i(n,t))))},addBack:function(n){return this.add(null==n?this.prevObject:this.prevObject.filter(n))}});i.each({parent:function(n){var t=n.parentNode;return t&&11!==t.nodeType?t:null},parents:function(n){return g(n,"parentNode")},parentsUntil:function(n,t,i){return g(n,"parentNode",i)},next:function(n){return ar(n,"nextSibling")},prev:function(n){return ar(n,"previousSibling")},nextAll:function(n){return g(n,"nextSibling")},prevAll:function(n){return g(n,"previousSibling")},nextUntil:function(n,t,i){return g(n,"nextSibling",i)},prevUntil:function(n,t,i){return g(n,"previousSibling",i)},siblings:function(n){return ur((n.parentNode||{}).firstChild,n)},children:function(n){return ur(n.firstChild)},contents:function(n){return l(n,"iframe")?n.contentDocument:(l(n,"template")&&(n=n.content||n),i.merge([],n.childNodes))}},function(n,t){i.fn[n]=function(r,u){var f=i.map(this,t,r);return"Until"!==n.slice(-5)&&(u=r),u&&"string"==typeof u&&(f=i.filter(u,f)),this.length>1&&(lr[n]||i.uniqueSort(f),cr.test(n)&&f.reverse()),this.pushStack(f)}});h=/[^\x20\t\r\n\f]+/g;i.Callbacks=function(n){n="string"==typeof n?ne(n):i.extend({},n);var e,r,h,u,t=[],o=[],f=-1,c=function(){for(u=u||n.once,h=e=!0;o.length;f=-1)for(r=o.shift();++f<t.length;)t[f].apply(r[0],r[1])===!1&&n.stopOnFalse&&(f=t.length,r=!1);n.memory||(r=!1);e=!1;u&&(t=r?[]:"")},s={add:function(){return t&&(r&&!e&&(f=t.length-1,o.push(r)),function u(r){i.each(r,function(r,f){i.isFunction(f)?n.unique&&s.has(f)||t.push(f):f&&f.length&&"string"!==i.type(f)&&u(f)})}(arguments),r&&!e&&c()),this},remove:function(){return i.each(arguments,function(n,r){for(var u;(u=i.inArray(r,t,u))>-1;)t.splice(u,1),u<=f&&f--}),this},has:function(n){return n?i.inArray(n,t)>-1:t.length>0},empty:function(){return t&&(t=[]),this},disable:function(){return u=o=[],t=r="",this},disabled:function(){return!t},lock:function(){return u=o=[],r||e||(t=r=""),this},locked:function(){return!!u},fireWith:function(n,t){return u||(t=t||[],t=[n,t.slice?t.slice():t],o.push(t),e||c()),this},fire:function(){return s.fireWith(this,arguments),this},fired:function(){return!!h}};return s};i.extend({Deferred:function(t){var u=[["notify","progress",i.Callbacks("memory"),i.Callbacks("memory"),2],["resolve","done",i.Callbacks("once memory"),i.Callbacks("once memory"),0,"resolved"],["reject","fail",i.Callbacks("once memory"),i.Callbacks("once memory"),1,"rejected"]],e="pending",f={state:function(){return e},always:function(){return r.done(arguments).fail(arguments),this},"catch":function(n){return f.then(null,n)},pipe:function(){var n=arguments;return i.Deferred(function(t){i.each(u,function(u,f){var e=i.isFunction(n[f[4]])&&n[f[4]];r[f[1]](function(){var n=e&&e.apply(this,arguments);n&&i.isFunction(n.promise)?n.promise().progress(t.notify).done(t.resolve).fail(t.reject):t[f[0]+"With"](this,e?[n]:arguments)})});n=null}).promise()},then:function(t,r,f){function o(t,r,u,f){return function(){var s=this,h=arguments,l=function(){var n,c;if(!(t<e)){if(n=u.apply(s,h),n===r.promise())throw new TypeError("Thenable self-resolution");c=n&&("object"==typeof n||"function"==typeof n)&&n.then;i.isFunction(c)?f?c.call(n,o(e,r,nt,f),o(e,r,pt,f)):(e++,c.call(n,o(e,r,nt,f),o(e,r,pt,f),o(e,r,nt,r.notifyWith))):(u!==nt&&(s=void 0,h=[n]),(f||r.resolveWith)(s,h))}},c=f?l:function(){try{l()}catch(n){i.Deferred.exceptionHook&&i.Deferred.exceptionHook(n,c.stackTrace);t+1>=e&&(u!==pt&&(s=void 0,h=[n]),r.rejectWith(s,h))}};t?c():(i.Deferred.getStackHook&&(c.stackTrace=i.Deferred.getStackHook()),n.setTimeout(c))}}var e=0;return i.Deferred(function(n){u[0][3].add(o(0,n,i.isFunction(f)?f:nt,n.notifyWith));u[1][3].add(o(0,n,i.isFunction(t)?t:nt));u[2][3].add(o(0,n,i.isFunction(r)?r:pt))}).promise()},promise:function(n){return null!=n?i.extend(n,f):f}},r={};return i.each(u,function(n,t){var i=t[2],o=t[5];f[t[1]]=i.add;o&&i.add(function(){e=o},u[3-n][2].disable,u[0][2].lock);i.add(t[3].fire);r[t[0]]=function(){return r[t[0]+"With"](this===r?void 0:this,arguments),this};r[t[0]+"With"]=i.fireWith}),f.promise(r),t&&t.call(r,r),r},when:function(n){var f=arguments.length,t=f,e=Array(t),u=w.call(arguments),r=i.Deferred(),o=function(n){return function(t){e[n]=this;u[n]=arguments.length>1?w.call(arguments):t;--f||r.resolveWith(e,u)}};if(f<=1&&(vr(n,r.done(o(t)).resolve,r.reject,!f),"pending"===r.state()||i.isFunction(u[t]&&u[t].then)))return r.then();while(t--)vr(u[t],o(t),r.reject);return r.promise()}});yr=/^(Eval|Internal|Range|Reference|Syntax|Type|URI)Error$/;i.Deferred.exceptionHook=function(t,i){n.console&&n.console.warn&&t&&yr.test(t.name)&&n.console.warn("jQuery.Deferred exception: "+t.message,t.stack,i)};i.readyException=function(t){n.setTimeout(function(){throw t;})};wt=i.Deferred();i.fn.ready=function(n){return wt.then(n)["catch"](function(n){i.readyException(n)}),this};i.extend({isReady:!1,readyWait:1,ready:function(n){(n===!0?--i.readyWait:i.isReady)||(i.isReady=!0,n!==!0&&--i.readyWait>0||wt.resolveWith(u,[i]))}});i.ready.then=wt.then;"complete"===u.readyState||"loading"!==u.readyState&&!u.documentElement.doScroll?n.setTimeout(i.ready):(u.addEventListener("DOMContentLoaded",bt),n.addEventListener("load",bt));v=function(n,t,r,u,f,e,o){var s=0,c=n.length,h=null==r;if("object"===i.type(r)){f=!0;for(s in r)v(n,t,s,r[s],!0,e,o)}else if(void 0!==u&&(f=!0,i.isFunction(u)||(o=!0),h&&(o?(t.call(n,u),t=null):(h=t,t=function(n,t,r){return h.call(i(n),r)})),t))for(;s<c;s++)t(n[s],r,o?u:u.call(n[s],s,t(n[s],r)));return f?n:h?t.call(n):c?t(n[0],r):e};st=function(n){return 1===n.nodeType||9===n.nodeType||!+n.nodeType};ht.uid=1;ht.prototype={cache:function(n){var t=n[this.expando];return t||(t={},st(n)&&(n.nodeType?n[this.expando]=t:Object.defineProperty(n,this.expando,{value:t,configurable:!0}))),t},set:function(n,t,r){var u,f=this.cache(n);if("string"==typeof t)f[i.camelCase(t)]=r;else for(u in t)f[i.camelCase(u)]=t[u];return f},get:function(n,t){return void 0===t?this.cache(n):n[this.expando]&&n[this.expando][i.camelCase(t)]},access:function(n,t,i){return void 0===t||t&&"string"==typeof t&&void 0===i?this.get(n,t):(this.set(n,t,i),void 0!==i?i:t)},remove:function(n,t){var u,r=n[this.expando];if(void 0!==r){if(void 0!==t)for(Array.isArray(t)?t=t.map(i.camelCase):(t=i.camelCase(t),t=(t in r)?[t]:t.match(h)||[]),u=t.length;u--;)delete r[t[u]];(void 0===t||i.isEmptyObject(r))&&(n.nodeType?n[this.expando]=void 0:delete n[this.expando])}},hasData:function(n){var t=n[this.expando];return void 0!==t&&!i.isEmptyObject(t)}};var r=new ht,e=new ht,te=/^(?:\{[\w\W]*\}|\[[\w\W]*\])$/,ie=/[A-Z]/g;i.extend({hasData:function(n){return e.hasData(n)||r.hasData(n)},data:function(n,t,i){return e.access(n,t,i)},removeData:function(n,t){e.remove(n,t)},_data:function(n,t,i){return r.access(n,t,i)},_removeData:function(n,t){r.remove(n,t)}});i.fn.extend({data:function(n,t){var o,f,s,u=this[0],h=u&&u.attributes;if(void 0===n){if(this.length&&(s=e.get(u),1===u.nodeType&&!r.get(u,"hasDataAttrs"))){for(o=h.length;o--;)h[o]&&(f=h[o].name,0===f.indexOf("data-")&&(f=i.camelCase(f.slice(5)),pr(u,f,s[f])));r.set(u,"hasDataAttrs",!0)}return s}return"object"==typeof n?this.each(function(){e.set(this,n)}):v(this,function(t){var i;if(u&&void 0===t){if((i=e.get(u,n),void 0!==i)||(i=pr(u,n),void 0!==i))return i}else this.each(function(){e.set(this,n,t)})},null,t,arguments.length>1,null,!0)},removeData:function(n){return this.each(function(){e.remove(this,n)})}});i.extend({queue:function(n,t,u){var f;if(n)return t=(t||"fx")+"queue",f=r.get(n,t),u&&(!f||Array.isArray(u)?f=r.access(n,t,i.makeArray(u)):f.push(u)),f||[]},dequeue:function(n,t){t=t||"fx";var r=i.queue(n,t),e=r.length,u=r.shift(),f=i._queueHooks(n,t),o=function(){i.dequeue(n,t)};"inprogress"===u&&(u=r.shift(),e--);u&&("fx"===t&&r.unshift("inprogress"),delete f.stop,u.call(n,o,f));!e&&f&&f.empty.fire()},_queueHooks:function(n,t){var u=t+"queueHooks";return r.get(n,u)||r.access(n,u,{empty:i.Callbacks("once memory").add(function(){r.remove(n,[t+"queue",u])})})}});i.fn.extend({queue:function(n,t){var r=2;return"string"!=typeof n&&(t=n,n="fx",r--),arguments.length<r?i.queue(this[0],n):void 0===t?this:this.each(function(){var r=i.queue(this,n,t);i._queueHooks(this,n);"fx"===n&&"inprogress"!==r[0]&&i.dequeue(this,n)})},dequeue:function(n){return this.each(function(){i.dequeue(this,n)})},clearQueue:function(n){return this.queue(n||"fx",[])},promise:function(n,t){var u,e=1,o=i.Deferred(),f=this,s=this.length,h=function(){--e||o.resolveWith(f,[f])};for("string"!=typeof n&&(t=n,n=void 0),n=n||"fx";s--;)u=r.get(f[s],n+"queueHooks"),u&&u.empty&&(e++,u.empty.add(h));return h(),o.promise(t)}});var wr=/[+-]?(?:\d*\.|)\d+(?:[eE][+-]?\d+|)/.source,ct=new RegExp("^(?:([+-])=|)("+wr+")([a-z%]*)$","i"),b=["Top","Right","Bottom","Left"],kt=function(n,t){return n=t||n,"none"===n.style.display||""===n.style.display&&i.contains(n.ownerDocument,n)&&"none"===i.css(n,"display")},br=function(n,t,i,r){var f,u,e={};for(u in t)e[u]=n.style[u],n.style[u]=t[u];f=i.apply(n,r||[]);for(u in t)n.style[u]=e[u];return f};si={};i.fn.extend({show:function(){return tt(this,!0)},hide:function(){return tt(this)},toggle:function(n){return"boolean"==typeof n?n?this.show():this.hide():this.each(function(){kt(this)?i(this).show():i(this).hide()})}});var dr=/^(?:checkbox|radio)$/i,gr=/<([a-z][^\/\0>\x20\t\r\n\f]+)/i,nu=/^$|\/(?:java|ecma)script/i,c={option:[1,"<select multiple='multiple'>","<\/select>"],thead:[1,"<table>","<\/table>"],col:[2,"<table><colgroup>","<\/colgroup><\/table>"],tr:[2,"<table><tbody>","<\/tbody><\/table>"],td:[3,"<table><tbody><tr>","<\/tr><\/tbody><\/table>"],_default:[0,"",""]};c.optgroup=c.option;c.tbody=c.tfoot=c.colgroup=c.caption=c.thead;c.th=c.td;tu=/<|&#?\w+;/;!function(){var i=u.createDocumentFragment(),n=i.appendChild(u.createElement("div")),t=u.createElement("input");t.setAttribute("type","radio");t.setAttribute("checked","checked");t.setAttribute("name","t");n.appendChild(t);f.checkClone=n.cloneNode(!0).cloneNode(!0).lastChild.checked;n.innerHTML="<textarea>x<\/textarea>";f.noCloneChecked=!!n.cloneNode(!0).lastChild.defaultValue}();var dt=u.documentElement,fe=/^key/,ee=/^(?:mouse|pointer|contextmenu|drag|drop)|click/,ru=/^([^.]*)(?:\.(.+)|)/;i.event={global:{},add:function(n,t,u,f,e){var v,y,w,p,b,c,s,l,o,k,d,a=r.get(n);if(a)for(u.handler&&(v=u,u=v.handler,e=v.selector),e&&i.find.matchesSelector(dt,e),u.guid||(u.guid=i.guid++),(p=a.events)||(p=a.events={}),(y=a.handle)||(y=a.handle=function(t){if("undefined"!=typeof i&&i.event.triggered!==t.type)return i.event.dispatch.apply(n,arguments)}),t=(t||"").match(h)||[""],b=t.length;b--;)w=ru.exec(t[b])||[],o=d=w[1],k=(w[2]||"").split(".").sort(),o&&(s=i.event.special[o]||{},o=(e?s.delegateType:s.bindType)||o,s=i.event.special[o]||{},c=i.extend({type:o,origType:d,data:f,handler:u,guid:u.guid,selector:e,needsContext:e&&i.expr.match.needsContext.test(e),namespace:k.join(".")},v),(l=p[o])||(l=p[o]=[],l.delegateCount=0,s.setup&&s.setup.call(n,f,k,y)!==!1||n.addEventListener&&n.addEventListener(o,y)),s.add&&(s.add.call(n,c),c.handler.guid||(c.handler.guid=u.guid)),e?l.splice(l.delegateCount++,0,c):l.push(c),i.event.global[o]=!0)},remove:function(n,t,u,f,e){var y,k,c,v,p,s,l,a,o,b,d,w=r.hasData(n)&&r.get(n);if(w&&(v=w.events)){for(t=(t||"").match(h)||[""],p=t.length;p--;)if(c=ru.exec(t[p])||[],o=d=c[1],b=(c[2]||"").split(".").sort(),o){for(l=i.event.special[o]||{},o=(f?l.delegateType:l.bindType)||o,a=v[o]||[],c=c[2]&&new RegExp("(^|\\.)"+b.join("\\.(?:.*\\.|)")+"(\\.|$)"),k=y=a.length;y--;)s=a[y],!e&&d!==s.origType||u&&u.guid!==s.guid||c&&!c.test(s.namespace)||f&&f!==s.selector&&("**"!==f||!s.selector)||(a.splice(y,1),s.selector&&a.delegateCount--,l.remove&&l.remove.call(n,s));k&&!a.length&&(l.teardown&&l.teardown.call(n,b,w.handle)!==!1||i.removeEvent(n,o,w.handle),delete v[o])}else for(o in v)i.event.remove(n,o+t[p],u,f,!0);i.isEmptyObject(v)&&r.remove(n,"handle events")}},dispatch:function(n){var t=i.event.fix(n),u,c,s,e,f,l,h=new Array(arguments.length),a=(r.get(this,"events")||{})[t.type]||[],o=i.event.special[t.type]||{};for(h[0]=t,u=1;u<arguments.length;u++)h[u]=arguments[u];if(t.delegateTarget=this,!o.preDispatch||o.preDispatch.call(this,t)!==!1){for(l=i.event.handlers.call(this,t,a),u=0;(e=l[u++])&&!t.isPropagationStopped();)for(t.currentTarget=e.elem,c=0;(f=e.handlers[c++])&&!t.isImmediatePropagationStopped();)t.rnamespace&&!t.rnamespace.test(f.namespace)||(t.handleObj=f,t.data=f.data,s=((i.event.special[f.origType]||{}).handle||f.handler).apply(e.elem,h),void 0!==s&&(t.result=s)===!1&&(t.preventDefault(),t.stopPropagation()));return o.postDispatch&&o.postDispatch.call(this,t),t.result}},handlers:function(n,t){var f,e,u,o,s,c=[],h=t.delegateCount,r=n.target;if(h&&r.nodeType&&!("click"===n.type&&n.button>=1))for(;r!==this;r=r.parentNode||this)if(1===r.nodeType&&("click"!==n.type||r.disabled!==!0)){for(o=[],s={},f=0;f<h;f++)e=t[f],u=e.selector+" ",void 0===s[u]&&(s[u]=e.needsContext?i(u,this).index(r)>-1:i.find(u,this,null,[r]).length),s[u]&&o.push(e);o.length&&c.push({elem:r,handlers:o})}return r=this,h<t.length&&c.push({elem:r,handlers:t.slice(h)}),c},addProp:function(n,t){Object.defineProperty(i.Event.prototype,n,{enumerable:!0,configurable:!0,get:i.isFunction(t)?function(){if(this.originalEvent)return t(this.originalEvent)}:function(){if(this.originalEvent)return this.originalEvent[n]},set:function(t){Object.defineProperty(this,n,{enumerable:!0,configurable:!0,writable:!0,value:t})}})},fix:function(n){return n[i.expando]?n:new i.Event(n)},special:{load:{noBubble:!0},focus:{trigger:function(){if(this!==uu()&&this.focus)return this.focus(),!1},delegateType:"focusin"},blur:{trigger:function(){if(this===uu()&&this.blur)return this.blur(),!1},delegateType:"focusout"},click:{trigger:function(){if("checkbox"===this.type&&this.click&&l(this,"input"))return this.click(),!1},_default:function(n){return l(n.target,"a")}},beforeunload:{postDispatch:function(n){void 0!==n.result&&n.originalEvent&&(n.originalEvent.returnValue=n.result)}}}};i.removeEvent=function(n,t,i){n.removeEventListener&&n.removeEventListener(t,i)};i.Event=function(n,t){return this instanceof i.Event?(n&&n.type?(this.originalEvent=n,this.type=n.type,this.isDefaultPrevented=n.defaultPrevented||void 0===n.defaultPrevented&&n.returnValue===!1?gt:it,this.target=n.target&&3===n.target.nodeType?n.target.parentNode:n.target,this.currentTarget=n.currentTarget,this.relatedTarget=n.relatedTarget):this.type=n,t&&i.extend(this,t),this.timeStamp=n&&n.timeStamp||i.now(),void(this[i.expando]=!0)):new i.Event(n,t)};i.Event.prototype={constructor:i.Event,isDefaultPrevented:it,isPropagationStopped:it,isImmediatePropagationStopped:it,isSimulated:!1,preventDefault:function(){var n=this.originalEvent;this.isDefaultPrevented=gt;n&&!this.isSimulated&&n.preventDefault()},stopPropagation:function(){var n=this.originalEvent;this.isPropagationStopped=gt;n&&!this.isSimulated&&n.stopPropagation()},stopImmediatePropagation:function(){var n=this.originalEvent;this.isImmediatePropagationStopped=gt;n&&!this.isSimulated&&n.stopImmediatePropagation();this.stopPropagation()}};i.each({altKey:!0,bubbles:!0,cancelable:!0,changedTouches:!0,ctrlKey:!0,detail:!0,eventPhase:!0,metaKey:!0,pageX:!0,pageY:!0,shiftKey:!0,view:!0,char:!0,charCode:!0,key:!0,keyCode:!0,button:!0,buttons:!0,clientX:!0,clientY:!0,offsetX:!0,offsetY:!0,pointerId:!0,pointerType:!0,screenX:!0,screenY:!0,targetTouches:!0,toElement:!0,touches:!0,which:function(n){var t=n.button;return null==n.which&&fe.test(n.type)?null!=n.charCode?n.charCode:n.keyCode:!n.which&&void 0!==t&&ee.test(n.type)?1&t?1:2&t?3:4&t?2:0:n.which}},i.event.addProp);i.each({mouseenter:"mouseover",mouseleave:"mouseout",pointerenter:"pointerover",pointerleave:"pointerout"},function(n,t){i.event.special[n]={delegateType:t,bindType:t,handle:function(n){var u,f=this,r=n.relatedTarget,e=n.handleObj;return r&&(r===f||i.contains(f,r))||(n.type=e.origType,u=e.handler.apply(this,arguments),n.type=t),u}}});i.fn.extend({on:function(n,t,i,r){return ci(this,n,t,i,r)},one:function(n,t,i,r){return ci(this,n,t,i,r,1)},off:function(n,t,r){var u,f;if(n&&n.preventDefault&&n.handleObj)return u=n.handleObj,i(n.delegateTarget).off(u.namespace?u.origType+"."+u.namespace:u.origType,u.selector,u.handler),this;if("object"==typeof n){for(f in n)this.off(f,t,n[f]);return this}return t!==!1&&"function"!=typeof t||(r=t,t=void 0),r===!1&&(r=it),this.each(function(){i.event.remove(this,n,r,t)})}});var oe=/<(?!area|br|col|embed|hr|img|input|link|meta|param)(([a-z][^\/\0>\x20\t\r\n\f]*)[^>]*)\/>/gi,se=/<script|<style|<link/i,he=/checked\s*(?:[^=]|=\s*.checked.)/i,ce=/^true\/(.*)/,le=/^\s*<!(?:\[CDATA\[|--)|(?:\]\]|--)>\s*$/g;i.extend({htmlPrefilter:function(n){return n.replace(oe,"<$1><\/$2>")},clone:function(n,t,r){var u,c,s,e,h=n.cloneNode(!0),l=i.contains(n.ownerDocument,n);if(!(f.noCloneChecked||1!==n.nodeType&&11!==n.nodeType||i.isXMLDoc(n)))for(e=o(h),s=o(n),u=0,c=s.length;u<c;u++)ye(s[u],e[u]);if(t)if(r)for(s=s||o(n),e=e||o(h),u=0,c=s.length;u<c;u++)eu(s[u],e[u]);else eu(n,h);return e=o(h,"script"),e.length>0&&hi(e,!l&&o(n,"script")),h},cleanData:function(n){for(var u,t,f,s=i.event.special,o=0;void 0!==(t=n[o]);o++)if(st(t)){if(u=t[r.expando]){if(u.events)for(f in u.events)s[f]?i.event.remove(t,f):i.removeEvent(t,f,u.handle);t[r.expando]=void 0}t[e.expando]&&(t[e.expando]=void 0)}}});i.fn.extend({detach:function(n){return ou(this,n,!0)},remove:function(n){return ou(this,n)},text:function(n){return v(this,function(n){return void 0===n?i.text(this):this.empty().each(function(){1!==this.nodeType&&11!==this.nodeType&&9!==this.nodeType||(this.textContent=n)})},null,n,arguments.length)},append:function(){return rt(this,arguments,function(n){if(1===this.nodeType||11===this.nodeType||9===this.nodeType){var t=fu(this,n);t.appendChild(n)}})},prepend:function(){return rt(this,arguments,function(n){if(1===this.nodeType||11===this.nodeType||9===this.nodeType){var t=fu(this,n);t.insertBefore(n,t.firstChild)}})},before:function(){return rt(this,arguments,function(n){this.parentNode&&this.parentNode.insertBefore(n,this)})},after:function(){return rt(this,arguments,function(n){this.parentNode&&this.parentNode.insertBefore(n,this.nextSibling)})},empty:function(){for(var n,t=0;null!=(n=this[t]);t++)1===n.nodeType&&(i.cleanData(o(n,!1)),n.textContent="");return this},clone:function(n,t){return n=null!=n&&n,t=null==t?n:t,this.map(function(){return i.clone(this,n,t)})},html:function(n){return v(this,function(n){var t=this[0]||{},r=0,u=this.length;if(void 0===n&&1===t.nodeType)return t.innerHTML;if("string"==typeof n&&!se.test(n)&&!c[(gr.exec(n)||["",""])[1].toLowerCase()]){n=i.htmlPrefilter(n);try{for(;r<u;r++)t=this[r]||{},1===t.nodeType&&(i.cleanData(o(t,!1)),t.innerHTML=n);t=0}catch(f){}}t&&this.empty().append(n)},null,n,arguments.length)},replaceWith:function(){var n=[];return rt(this,arguments,function(t){var r=this.parentNode;i.inArray(this,n)<0&&(i.cleanData(o(this)),r&&r.replaceChild(t,this))},n)}});i.each({appendTo:"append",prependTo:"prepend",insertBefore:"before",insertAfter:"after",replaceAll:"replaceWith"},function(n,t){i.fn[n]=function(n){for(var u,f=[],e=i(n),o=e.length-1,r=0;r<=o;r++)u=r===o?this:this.clone(!0),i(e[r])[t](u),ui.apply(f,u.get());return this.pushStack(f)}});var su=/^margin/,li=new RegExp("^("+wr+")(?!px)[a-z%]+$","i"),ni=function(t){var i=t.ownerDocument.defaultView;return i&&i.opener||(i=n),i.getComputedStyle(t)};!function(){function r(){if(t){t.style.cssText="box-sizing:border-box;position:relative;display:block;margin:auto;border:1px;padding:1px;top:1%;width:50%";t.innerHTML="";dt.appendChild(e);var i=n.getComputedStyle(t);o="1%"!==i.top;c="2px"===i.marginLeft;s="4px"===i.width;t.style.marginRight="50%";h="4px"===i.marginRight;dt.removeChild(e);t=null}}var o,s,h,c,e=u.createElement("div"),t=u.createElement("div");t.style&&(t.style.backgroundClip="content-box",t.cloneNode(!0).style.backgroundClip="",f.clearCloneStyle="content-box"===t.style.backgroundClip,e.style.cssText="border:0;width:8px;height:0;top:0;left:-9999px;padding:0;margin-top:1px;position:absolute",e.appendChild(t),i.extend(f,{pixelPosition:function(){return r(),o},boxSizingReliable:function(){return r(),s},pixelMarginRight:function(){return r(),h},reliableMarginLeft:function(){return r(),c}}))}();var pe=/^(none|table(?!-c[ea]).+)/,cu=/^--/,we={position:"absolute",visibility:"hidden",display:"block"},lu={letterSpacing:"0",fontWeight:"400"},au=["Webkit","Moz","ms"],vu=u.createElement("div").style;i.extend({cssHooks:{opacity:{get:function(n,t){if(t){var i=lt(n,"opacity");return""===i?"1":i}}}},cssNumber:{animationIterationCount:!0,columnCount:!0,fillOpacity:!0,flexGrow:!0,flexShrink:!0,fontWeight:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,widows:!0,zIndex:!0,zoom:!0},cssProps:{float:"cssFloat"},style:function(n,t,r,u){if(n&&3!==n.nodeType&&8!==n.nodeType&&n.style){var e,s,o,c=i.camelCase(t),l=cu.test(t),h=n.style;return l||(t=yu(c)),o=i.cssHooks[t]||i.cssHooks[c],void 0===r?o&&"get"in o&&void 0!==(e=o.get(n,!1,u))?e:h[t]:(s=typeof r,"string"===s&&(e=ct.exec(r))&&e[1]&&(r=kr(n,t,e),s="number"),null!=r&&r===r&&("number"===s&&(r+=e&&e[3]||(i.cssNumber[c]?"":"px")),f.clearCloneStyle||""!==r||0!==t.indexOf("background")||(h[t]="inherit"),o&&"set"in o&&void 0===(r=o.set(n,r,u))||(l?h.setProperty(t,r):h[t]=r)),void 0)}},css:function(n,t,r,u){var f,o,e,s=i.camelCase(t),h=cu.test(t);return h||(t=yu(s)),e=i.cssHooks[t]||i.cssHooks[s],e&&"get"in e&&(f=e.get(n,!0,r)),void 0===f&&(f=lt(n,t,u)),"normal"===f&&t in lu&&(f=lu[t]),""===r||r?(o=parseFloat(f),r===!0||isFinite(o)?o||0:f):f}});i.each(["height","width"],function(n,t){i.cssHooks[t]={get:function(n,r,u){if(r)return!pe.test(i.css(n,"display"))||n.getClientRects().length&&n.getBoundingClientRect().width?bu(n,t,u):br(n,we,function(){return bu(n,t,u)})},set:function(n,r,u){var f,e=u&&ni(n),o=u&&wu(n,t,u,"border-box"===i.css(n,"boxSizing",!1,e),e);return o&&(f=ct.exec(r))&&"px"!==(f[3]||"px")&&(n.style[t]=r,r=i.css(n,t)),pu(n,r,o)}}});i.cssHooks.marginLeft=hu(f.reliableMarginLeft,function(n,t){if(t)return(parseFloat(lt(n,"marginLeft"))||n.getBoundingClientRect().left-br(n,{marginLeft:0},function(){return n.getBoundingClientRect().left}))+"px"});i.each({margin:"",padding:"",border:"Width"},function(n,t){i.cssHooks[n+t]={expand:function(i){for(var r=0,f={},u="string"==typeof i?i.split(" "):[i];r<4;r++)f[n+b[r]+t]=u[r]||u[r-2]||u[0];return f}};su.test(n)||(i.cssHooks[n+t].set=pu)});i.fn.extend({css:function(n,t){return v(this,function(n,t,r){var f,e,o={},u=0;if(Array.isArray(t)){for(f=ni(n),e=t.length;u<e;u++)o[t[u]]=i.css(n,t[u],!1,f);return o}return void 0!==r?i.style(n,t,r):i.css(n,t)},n,t,arguments.length>1)}});i.Tween=s;s.prototype={constructor:s,init:function(n,t,r,u,f,e){this.elem=n;this.prop=r;this.easing=f||i.easing._default;this.options=t;this.start=this.now=this.cur();this.end=u;this.unit=e||(i.cssNumber[r]?"":"px")},cur:function(){var n=s.propHooks[this.prop];return n&&n.get?n.get(this):s.propHooks._default.get(this)},run:function(n){var t,r=s.propHooks[this.prop];return this.pos=this.options.duration?t=i.easing[this.easing](n,this.options.duration*n,0,1,this.options.duration):t=n,this.now=(this.end-this.start)*t+this.start,this.options.step&&this.options.step.call(this.elem,this.now,this),r&&r.set?r.set(this):s.propHooks._default.set(this),this}};s.prototype.init.prototype=s.prototype;s.propHooks={_default:{get:function(n){var t;return 1!==n.elem.nodeType||null!=n.elem[n.prop]&&null==n.elem.style[n.prop]?n.elem[n.prop]:(t=i.css(n.elem,n.prop,""),t&&"auto"!==t?t:0)},set:function(n){i.fx.step[n.prop]?i.fx.step[n.prop](n):1!==n.elem.nodeType||null==n.elem.style[i.cssProps[n.prop]]&&!i.cssHooks[n.prop]?n.elem[n.prop]=n.now:i.style(n.elem,n.prop,n.now+n.unit)}}};s.propHooks.scrollTop=s.propHooks.scrollLeft={set:function(n){n.elem.nodeType&&n.elem.parentNode&&(n.elem[n.prop]=n.now)}};i.easing={linear:function(n){return n},swing:function(n){return.5-Math.cos(n*Math.PI)/2},_default:"swing"};i.fx=s.prototype.init;i.fx.step={};ku=/^(?:toggle|show|hide)$/;du=/queueHooks$/;i.Animation=i.extend(a,{tweeners:{"*":[function(n,t){var i=this.createTween(n,t);return kr(i.elem,n,ct.exec(t),i),i}]},tweener:function(n,t){i.isFunction(n)?(t=n,n=["*"]):n=n.match(h);for(var r,u=0,f=n.length;u<f;u++)r=n[u],a.tweeners[r]=a.tweeners[r]||[],a.tweeners[r].unshift(t)},prefilters:[ke],prefilter:function(n,t){t?a.prefilters.unshift(n):a.prefilters.push(n)}});i.speed=function(n,t,r){var u=n&&"object"==typeof n?i.extend({},n):{complete:r||!r&&t||i.isFunction(n)&&n,duration:n,easing:r&&t||t&&!i.isFunction(t)&&t};return i.fx.off?u.duration=0:"number"!=typeof u.duration&&(u.duration=u.duration in i.fx.speeds?i.fx.speeds[u.duration]:i.fx.speeds._default),null!=u.queue&&u.queue!==!0||(u.queue="fx"),u.old=u.complete,u.complete=function(){i.isFunction(u.old)&&u.old.call(this);u.queue&&i.dequeue(this,u.queue)},u};i.fn.extend({fadeTo:function(n,t,i,r){return this.filter(kt).css("opacity",0).show().end().animate({opacity:t},n,i,r)},animate:function(n,t,u,f){var s=i.isEmptyObject(n),o=i.speed(t,u,f),e=function(){var t=a(this,i.extend({},n),o);(s||r.get(this,"finish"))&&t.stop(!0)};return e.finish=e,s||o.queue===!1?this.each(e):this.queue(o.queue,e)},stop:function(n,t,u){var f=function(n){var t=n.stop;delete n.stop;t(u)};return"string"!=typeof n&&(u=t,t=n,n=void 0),t&&n!==!1&&this.queue(n||"fx",[]),this.each(function(){var s=!0,t=null!=n&&n+"queueHooks",o=i.timers,e=r.get(this);if(t)e[t]&&e[t].stop&&f(e[t]);else for(t in e)e[t]&&e[t].stop&&du.test(t)&&f(e[t]);for(t=o.length;t--;)o[t].elem!==this||null!=n&&o[t].queue!==n||(o[t].anim.stop(u),s=!1,o.splice(t,1));!s&&u||i.dequeue(this,n)})},finish:function(n){return n!==!1&&(n=n||"fx"),this.each(function(){var t,e=r.get(this),u=e[n+"queue"],o=e[n+"queueHooks"],f=i.timers,s=u?u.length:0;for(e.finish=!0,i.queue(this,n,[]),o&&o.stop&&o.stop.call(this,!0),t=f.length;t--;)f[t].elem===this&&f[t].queue===n&&(f[t].anim.stop(!0),f.splice(t,1));for(t=0;t<s;t++)u[t]&&u[t].finish&&u[t].finish.call(this);delete e.finish})}});i.each(["toggle","show","hide"],function(n,t){var r=i.fn[t];i.fn[t]=function(n,i,u){return null==n||"boolean"==typeof n?r.apply(this,arguments):this.animate(ii(t,!0),n,i,u)}});i.each({slideDown:ii("show"),slideUp:ii("hide"),slideToggle:ii("toggle"),fadeIn:{opacity:"show"},fadeOut:{opacity:"hide"},fadeToggle:{opacity:"toggle"}},function(n,t){i.fn[n]=function(n,i,r){return this.animate(t,n,i,r)}});i.timers=[];i.fx.tick=function(){var r,n=0,t=i.timers;for(ut=i.now();n<t.length;n++)r=t[n],r()||t[n]!==r||t.splice(n--,1);t.length||i.fx.stop();ut=void 0};i.fx.timer=function(n){i.timers.push(n);i.fx.start()};i.fx.interval=13;i.fx.start=function(){ti||(ti=!0,ai())};i.fx.stop=function(){ti=null};i.fx.speeds={slow:600,fast:200,_default:400};i.fn.delay=function(t,r){return t=i.fx?i.fx.speeds[t]||t:t,r=r||"fx",this.queue(r,function(i,r){var u=n.setTimeout(i,t);r.stop=function(){n.clearTimeout(u)}})},function(){var n=u.createElement("input"),t=u.createElement("select"),i=t.appendChild(u.createElement("option"));n.type="checkbox";f.checkOn=""!==n.value;f.optSelected=i.selected;n=u.createElement("input");n.value="t";n.type="radio";f.radioValue="t"===n.value}();ft=i.expr.attrHandle;i.fn.extend({attr:function(n,t){return v(this,i.attr,n,t,arguments.length>1)},removeAttr:function(n){return this.each(function(){i.removeAttr(this,n)})}});i.extend({attr:function(n,t,r){var u,f,e=n.nodeType;if(3!==e&&8!==e&&2!==e)return"undefined"==typeof n.getAttribute?i.prop(n,t,r):(1===e&&i.isXMLDoc(n)||(f=i.attrHooks[t.toLowerCase()]||(i.expr.match.bool.test(t)?tf:void 0)),void 0!==r?null===r?void i.removeAttr(n,t):f&&"set"in f&&void 0!==(u=f.set(n,r,t))?u:(n.setAttribute(t,r+""),r):f&&"get"in f&&null!==(u=f.get(n,t))?u:(u=i.find.attr(n,t),null==u?void 0:u))},attrHooks:{type:{set:function(n,t){if(!f.radioValue&&"radio"===t&&l(n,"input")){var i=n.value;return n.setAttribute("type",t),i&&(n.value=i),t}}}},removeAttr:function(n,t){var i,u=0,r=t&&t.match(h);if(r&&1===n.nodeType)while(i=r[u++])n.removeAttribute(i)}});tf={set:function(n,t,r){return t===!1?i.removeAttr(n,r):n.setAttribute(r,r),r}};i.each(i.expr.match.bool.source.match(/\w+/g),function(n,t){var r=ft[t]||i.find.attr;ft[t]=function(n,t,i){var f,e,u=t.toLowerCase();return i||(e=ft[u],ft[u]=f,f=null!=r(n,t,i)?u:null,ft[u]=e),f}});rf=/^(?:input|select|textarea|button)$/i;uf=/^(?:a|area)$/i;i.fn.extend({prop:function(n,t){return v(this,i.prop,n,t,arguments.length>1)},removeProp:function(n){return this.each(function(){delete this[i.propFix[n]||n]})}});i.extend({prop:function(n,t,r){var f,u,e=n.nodeType;if(3!==e&&8!==e&&2!==e)return 1===e&&i.isXMLDoc(n)||(t=i.propFix[t]||t,u=i.propHooks[t]),void 0!==r?u&&"set"in u&&void 0!==(f=u.set(n,r,t))?f:n[t]=r:u&&"get"in u&&null!==(f=u.get(n,t))?f:n[t]},propHooks:{tabIndex:{get:function(n){var t=i.find.attr(n,"tabindex");return t?parseInt(t,10):rf.test(n.nodeName)||uf.test(n.nodeName)&&n.href?0:-1}}},propFix:{"for":"htmlFor","class":"className"}});f.optSelected||(i.propHooks.selected={get:function(n){var t=n.parentNode;return t&&t.parentNode&&t.parentNode.selectedIndex,null},set:function(n){var t=n.parentNode;t&&(t.selectedIndex,t.parentNode&&t.parentNode.selectedIndex)}});i.each(["tabIndex","readOnly","maxLength","cellSpacing","cellPadding","rowSpan","colSpan","useMap","frameBorder","contentEditable"],function(){i.propFix[this.toLowerCase()]=this});i.fn.extend({addClass:function(n){var o,t,r,u,f,s,e,c=0;if(i.isFunction(n))return this.each(function(t){i(this).addClass(n.call(this,t,d(this)))});if("string"==typeof n&&n)for(o=n.match(h)||[];t=this[c++];)if(u=d(t),r=1===t.nodeType&&" "+k(u)+" "){for(s=0;f=o[s++];)r.indexOf(" "+f+" ")<0&&(r+=f+" ");e=k(r);u!==e&&t.setAttribute("class",e)}return this},removeClass:function(n){var o,r,t,u,f,s,e,c=0;if(i.isFunction(n))return this.each(function(t){i(this).removeClass(n.call(this,t,d(this)))});if(!arguments.length)return this.attr("class","");if("string"==typeof n&&n)for(o=n.match(h)||[];r=this[c++];)if(u=d(r),t=1===r.nodeType&&" "+k(u)+" "){for(s=0;f=o[s++];)while(t.indexOf(" "+f+" ")>-1)t=t.replace(" "+f+" "," ");e=k(t);u!==e&&r.setAttribute("class",e)}return this},toggleClass:function(n,t){var u=typeof n;return"boolean"==typeof t&&"string"===u?t?this.addClass(n):this.removeClass(n):i.isFunction(n)?this.each(function(r){i(this).toggleClass(n.call(this,r,d(this),t),t)}):this.each(function(){var t,e,f,o;if("string"===u)for(e=0,f=i(this),o=n.match(h)||[];t=o[e++];)f.hasClass(t)?f.removeClass(t):f.addClass(t);else void 0!==n&&"boolean"!==u||(t=d(this),t&&r.set(this,"__className__",t),this.setAttribute&&this.setAttribute("class",t||n===!1?"":r.get(this,"__className__")||""))})},hasClass:function(n){for(var t,r=0,i=" "+n+" ";t=this[r++];)if(1===t.nodeType&&(" "+k(d(t))+" ").indexOf(i)>-1)return!0;return!1}});ff=/\r/g;i.fn.extend({val:function(n){var t,r,f,u=this[0];return arguments.length?(f=i.isFunction(n),this.each(function(r){var u;1===this.nodeType&&(u=f?n.call(this,r,i(this).val()):n,null==u?u="":"number"==typeof u?u+="":Array.isArray(u)&&(u=i.map(u,function(n){return null==n?"":n+""})),t=i.valHooks[this.type]||i.valHooks[this.nodeName.toLowerCase()],t&&"set"in t&&void 0!==t.set(this,u,"value")||(this.value=u))})):u?(t=i.valHooks[u.type]||i.valHooks[u.nodeName.toLowerCase()],t&&"get"in t&&void 0!==(r=t.get(u,"value"))?r:(r=u.value,"string"==typeof r?r.replace(ff,""):null==r?"":r)):void 0}});i.extend({valHooks:{option:{get:function(n){var t=i.find.attr(n,"value");return null!=t?t:k(i.text(n))}},select:{get:function(n){for(var e,t,o=n.options,u=n.selectedIndex,f="select-one"===n.type,s=f?null:[],h=f?u+1:o.length,r=u<0?h:f?u:0;r<h;r++)if(t=o[r],(t.selected||r===u)&&!t.disabled&&(!t.parentNode.disabled||!l(t.parentNode,"optgroup"))){if(e=i(t).val(),f)return e;s.push(e)}return s},set:function(n,t){for(var u,r,f=n.options,e=i.makeArray(t),o=f.length;o--;)r=f[o],(r.selected=i.inArray(i.valHooks.option.get(r),e)>-1)&&(u=!0);return u||(n.selectedIndex=-1),e}}}});i.each(["radio","checkbox"],function(){i.valHooks[this]={set:function(n,t){if(Array.isArray(t))return n.checked=i.inArray(i(n).val(),t)>-1}};f.checkOn||(i.valHooks[this].get=function(n){return null===n.getAttribute("value")?"on":n.value})});vi=/^(?:focusinfocus|focusoutblur)$/;i.extend(i.event,{trigger:function(t,f,e,o){var w,s,c,b,a,v,l,p=[e||u],h=yt.call(t,"type")?t.type:t,y=yt.call(t,"namespace")?t.namespace.split("."):[];if(s=c=e=e||u,3!==e.nodeType&&8!==e.nodeType&&!vi.test(h+i.event.triggered)&&(h.indexOf(".")>-1&&(y=h.split("."),h=y.shift(),y.sort()),a=h.indexOf(":")<0&&"on"+h,t=t[i.expando]?t:new i.Event(h,"object"==typeof t&&t),t.isTrigger=o?2:3,t.namespace=y.join("."),t.rnamespace=t.namespace?new RegExp("(^|\\.)"+y.join("\\.(?:.*\\.|)")+"(\\.|$)"):null,t.result=void 0,t.target||(t.target=e),f=null==f?[t]:i.makeArray(f,[t]),l=i.event.special[h]||{},o||!l.trigger||l.trigger.apply(e,f)!==!1)){if(!o&&!l.noBubble&&!i.isWindow(e)){for(b=l.delegateType||h,vi.test(b+h)||(s=s.parentNode);s;s=s.parentNode)p.push(s),c=s;c===(e.ownerDocument||u)&&p.push(c.defaultView||c.parentWindow||n)}for(w=0;(s=p[w++])&&!t.isPropagationStopped();)t.type=w>1?b:l.bindType||h,v=(r.get(s,"events")||{})[t.type]&&r.get(s,"handle"),v&&v.apply(s,f),v=a&&s[a],v&&v.apply&&st(s)&&(t.result=v.apply(s,f),t.result===!1&&t.preventDefault());return t.type=h,o||t.isDefaultPrevented()||l._default&&l._default.apply(p.pop(),f)!==!1||!st(e)||a&&i.isFunction(e[h])&&!i.isWindow(e)&&(c=e[a],c&&(e[a]=null),i.event.triggered=h,e[h](),i.event.triggered=void 0,c&&(e[a]=c)),t.result}},simulate:function(n,t,r){var u=i.extend(new i.Event,r,{type:n,isSimulated:!0});i.event.trigger(u,null,t)}});i.fn.extend({trigger:function(n,t){return this.each(function(){i.event.trigger(n,t,this)})},triggerHandler:function(n,t){var r=this[0];if(r)return i.event.trigger(n,t,r,!0)}});i.each("blur focus focusin focusout resize scroll click dblclick mousedown mouseup mousemove mouseover mouseout mouseenter mouseleave change select submit keydown keypress keyup contextmenu".split(" "),function(n,t){i.fn[t]=function(n,i){return arguments.length>0?this.on(t,null,n,i):this.trigger(t)}});i.fn.extend({hover:function(n,t){return this.mouseenter(n).mouseleave(t||n)}});f.focusin="onfocusin"in n;f.focusin||i.each({focus:"focusin",blur:"focusout"},function(n,t){var u=function(n){i.event.simulate(t,n.target,i.event.fix(n))};i.event.special[t]={setup:function(){var i=this.ownerDocument||this,f=r.access(i,t);f||i.addEventListener(n,u,!0);r.access(i,t,(f||0)+1)},teardown:function(){var i=this.ownerDocument||this,f=r.access(i,t)-1;f?r.access(i,t,f):(i.removeEventListener(n,u,!0),r.remove(i,t))}}});var at=n.location,ef=i.now(),yi=/\?/;i.parseXML=function(t){var r;if(!t||"string"!=typeof t)return null;try{r=(new n.DOMParser).parseFromString(t,"text/xml")}catch(u){r=void 0}return r&&!r.getElementsByTagName("parsererror").length||i.error("Invalid XML: "+t),r};var ge=/\[\]$/,of=/\r?\n/g,no=/^(?:submit|button|image|reset|file)$/i,to=/^(?:input|select|textarea|keygen)/i;i.param=function(n,t){var r,u=[],f=function(n,t){var r=i.isFunction(t)?t():t;u[u.length]=encodeURIComponent(n)+"="+encodeURIComponent(null==r?"":r)};if(Array.isArray(n)||n.jquery&&!i.isPlainObject(n))i.each(n,function(){f(this.name,this.value)});else for(r in n)pi(r,n[r],t,f);return u.join("&")};i.fn.extend({serialize:function(){return i.param(this.serializeArray())},serializeArray:function(){return this.map(function(){var n=i.prop(this,"elements");return n?i.makeArray(n):this}).filter(function(){var n=this.type;return this.name&&!i(this).is(":disabled")&&to.test(this.nodeName)&&!no.test(n)&&(this.checked||!dr.test(n))}).map(function(n,t){var r=i(this).val();return null==r?null:Array.isArray(r)?i.map(r,function(n){return{name:t.name,value:n.replace(of,"\r\n")}}):{name:t.name,value:r.replace(of,"\r\n")}}).get()}});var io=/%20/g,ro=/#.*$/,uo=/([?&])_=[^&]*/,fo=/^(.*?):[ \t]*([^\r\n]*)$/gm,eo=/^(?:GET|HEAD)$/,oo=/^\/\//,sf={},wi={},hf="*/".concat("*"),bi=u.createElement("a");return bi.href=at.href,i.extend({active:0,lastModified:{},etag:{},ajaxSettings:{url:at.href,type:"GET",isLocal:/^(?:about|app|app-storage|.+-extension|file|res|widget):$/.test(at.protocol),global:!0,processData:!0,"async":!0,contentType:"application/x-www-form-urlencoded; charset=UTF-8",accepts:{"*":hf,text:"text/plain",html:"text/html",xml:"application/xml, text/xml",json:"application/json, text/javascript"},contents:{xml:/\bxml\b/,html:/\bhtml/,json:/\bjson\b/},responseFields:{xml:"responseXML",text:"responseText",json:"responseJSON"},converters:{"* text":String,"text html":!0,"text json":JSON.parse,"text xml":i.parseXML},flatOptions:{url:!0,context:!0}},ajaxSetup:function(n,t){return t?ki(ki(n,i.ajaxSettings),t):ki(i.ajaxSettings,n)},ajaxPrefilter:cf(sf),ajaxTransport:cf(wi),ajax:function(t,r){function b(t,r,u,h){var y,rt,g,p,b,l=r;s||(s=!0,d&&n.clearTimeout(d),a=void 0,k=h||"",e.readyState=t>0?4:0,y=t>=200&&t<300||304===t,u&&(p=so(f,e,u)),p=ho(f,p,e,y),y?(f.ifModified&&(b=e.getResponseHeader("Last-Modified"),b&&(i.lastModified[o]=b),b=e.getResponseHeader("etag"),b&&(i.etag[o]=b)),204===t||"HEAD"===f.type?l="nocontent":304===t?l="notmodified":(l=p.state,rt=p.data,g=p.error,y=!g)):(g=l,!t&&l||(l="error",t<0&&(t=0))),e.status=t,e.statusText=(r||l)+"",y?tt.resolveWith(c,[rt,l,e]):tt.rejectWith(c,[e,l,g]),e.statusCode(w),w=void 0,v&&nt.trigger(y?"ajaxSuccess":"ajaxError",[e,f,y?rt:g]),it.fireWith(c,[e,l]),v&&(nt.trigger("ajaxComplete",[e,f]),--i.active||i.event.trigger("ajaxStop")))}"object"==typeof t&&(r=t,t=void 0);r=r||{};var a,o,k,y,d,l,s,v,g,p,f=i.ajaxSetup({},r),c=f.context||f,nt=f.context&&(c.nodeType||c.jquery)?i(c):i.event,tt=i.Deferred(),it=i.Callbacks("once memory"),w=f.statusCode||{},rt={},ut={},ft="canceled",e={readyState:0,getResponseHeader:function(n){var t;if(s){if(!y)for(y={};t=fo.exec(k);)y[t[1].toLowerCase()]=t[2];t=y[n.toLowerCase()]}return null==t?null:t},getAllResponseHeaders:function(){return s?k:null},setRequestHeader:function(n,t){return null==s&&(n=ut[n.toLowerCase()]=ut[n.toLowerCase()]||n,rt[n]=t),this},overrideMimeType:function(n){return null==s&&(f.mimeType=n),this},statusCode:function(n){var t;if(n)if(s)e.always(n[e.status]);else for(t in n)w[t]=[w[t],n[t]];return this},abort:function(n){var t=n||ft;return a&&a.abort(t),b(0,t),this}};if(tt.promise(e),f.url=((t||f.url||at.href)+"").replace(oo,at.protocol+"//"),f.type=r.method||r.type||f.method||f.type,f.dataTypes=(f.dataType||"*").toLowerCase().match(h)||[""],null==f.crossDomain){l=u.createElement("a");try{l.href=f.url;l.href=l.href;f.crossDomain=bi.protocol+"//"+bi.host!=l.protocol+"//"+l.host}catch(et){f.crossDomain=!0}}if(f.data&&f.processData&&"string"!=typeof f.data&&(f.data=i.param(f.data,f.traditional)),lf(sf,f,r,e),s)return e;v=i.event&&f.global;v&&0==i.active++&&i.event.trigger("ajaxStart");f.type=f.type.toUpperCase();f.hasContent=!eo.test(f.type);o=f.url.replace(ro,"");f.hasContent?f.data&&f.processData&&0===(f.contentType||"").indexOf("application/x-www-form-urlencoded")&&(f.data=f.data.replace(io,"+")):(p=f.url.slice(o.length),f.data&&(o+=(yi.test(o)?"&":"?")+f.data,delete f.data),f.cache===!1&&(o=o.replace(uo,"$1"),p=(yi.test(o)?"&":"?")+"_="+ef+++p),f.url=o+p);f.ifModified&&(i.lastModified[o]&&e.setRequestHeader("If-Modified-Since",i.lastModified[o]),i.etag[o]&&e.setRequestHeader("If-None-Match",i.etag[o]));(f.data&&f.hasContent&&f.contentType!==!1||r.contentType)&&e.setRequestHeader("Content-Type",f.contentType);e.setRequestHeader("Accept",f.dataTypes[0]&&f.accepts[f.dataTypes[0]]?f.accepts[f.dataTypes[0]]+("*"!==f.dataTypes[0]?", "+hf+"; q=0.01":""):f.accepts["*"]);for(g in f.headers)e.setRequestHeader(g,f.headers[g]);if(f.beforeSend&&(f.beforeSend.call(c,e,f)===!1||s))return e.abort();if(ft="abort",it.add(f.complete),e.done(f.success),e.fail(f.error),a=lf(wi,f,r,e)){if(e.readyState=1,v&&nt.trigger("ajaxSend",[e,f]),s)return e;f.async&&f.timeout>0&&(d=n.setTimeout(function(){e.abort("timeout")},f.timeout));try{s=!1;a.send(rt,b)}catch(et){if(s)throw et;b(-1,et)}}else b(-1,"No Transport");return e},getJSON:function(n,t,r){return i.get(n,t,r,"json")},getScript:function(n,t){return i.get(n,void 0,t,"script")}}),i.each(["get","post"],function(n,t){i[t]=function(n,r,u,f){return i.isFunction(r)&&(f=f||u,u=r,r=void 0),i.ajax(i.extend({url:n,type:t,dataType:f,data:r,success:u},i.isPlainObject(n)&&n))}}),i._evalUrl=function(n){return i.ajax({url:n,type:"GET",dataType:"script",cache:!0,"async":!1,global:!1,throws:!0})},i.fn.extend({wrapAll:function(n){var t;return this[0]&&(i.isFunction(n)&&(n=n.call(this[0])),t=i(n,this[0].ownerDocument).eq(0).clone(!0),this[0].parentNode&&t.insertBefore(this[0]),t.map(function(){for(var n=this;n.firstElementChild;)n=n.firstElementChild;return n}).append(this)),this},wrapInner:function(n){return i.isFunction(n)?this.each(function(t){i(this).wrapInner(n.call(this,t))}):this.each(function(){var t=i(this),r=t.contents();r.length?r.wrapAll(n):t.append(n)})},wrap:function(n){var t=i.isFunction(n);return this.each(function(r){i(this).wrapAll(t?n.call(this,r):n)})},unwrap:function(n){return this.parent(n).not("body").each(function(){i(this).replaceWith(this.childNodes)}),this}}),i.expr.pseudos.hidden=function(n){return!i.expr.pseudos.visible(n)},i.expr.pseudos.visible=function(n){return!!(n.offsetWidth||n.offsetHeight||n.getClientRects().length)},i.ajaxSettings.xhr=function(){try{return new n.XMLHttpRequest}catch(t){}},af={0:200,1223:204},et=i.ajaxSettings.xhr(),f.cors=!!et&&"withCredentials"in et,f.ajax=et=!!et,i.ajaxTransport(function(t){var i,r;if(f.cors||et&&!t.crossDomain)return{send:function(u,f){var o,e=t.xhr();if(e.open(t.type,t.url,t.async,t.username,t.password),t.xhrFields)for(o in t.xhrFields)e[o]=t.xhrFields[o];t.mimeType&&e.overrideMimeType&&e.overrideMimeType(t.mimeType);t.crossDomain||u["X-Requested-With"]||(u["X-Requested-With"]="XMLHttpRequest");for(o in u)e.setRequestHeader(o,u[o]);i=function(n){return function(){i&&(i=r=e.onload=e.onerror=e.onabort=e.onreadystatechange=null,"abort"===n?e.abort():"error"===n?"number"!=typeof e.status?f(0,"error"):f(e.status,e.statusText):f(af[e.status]||e.status,e.statusText,"text"!==(e.responseType||"text")||"string"!=typeof e.responseText?{binary:e.response}:{text:e.responseText},e.getAllResponseHeaders()))}};e.onload=i();r=e.onerror=i("error");void 0!==e.onabort?e.onabort=r:e.onreadystatechange=function(){4===e.readyState&&n.setTimeout(function(){i&&r()})};i=i("abort");try{e.send(t.hasContent&&t.data||null)}catch(s){if(i)throw s;}},abort:function(){i&&i()}}}),i.ajaxPrefilter(function(n){n.crossDomain&&(n.contents.script=!1)}),i.ajaxSetup({accepts:{script:"text/javascript, application/javascript, application/ecmascript, application/x-ecmascript"},contents:{script:/\b(?:java|ecma)script\b/},converters:{"text script":function(n){return i.globalEval(n),n}}}),i.ajaxPrefilter("script",function(n){void 0===n.cache&&(n.cache=!1);n.crossDomain&&(n.type="GET")}),i.ajaxTransport("script",function(n){if(n.crossDomain){var r,t;return{send:function(f,e){r=i("<script>").prop({charset:n.scriptCharset,src:n.url}).on("load error",t=function(n){r.remove();t=null;n&&e("error"===n.type?404:200,n.type)});u.head.appendChild(r[0])},abort:function(){t&&t()}}}}),di=[],ri=/(=)\?(?=&|$)|\?\?/,i.ajaxSetup({jsonp:"callback",jsonpCallback:function(){var n=di.pop()||i.expando+"_"+ef++;return this[n]=!0,n}}),i.ajaxPrefilter("json jsonp",function(t,r,u){var f,e,o,s=t.jsonp!==!1&&(ri.test(t.url)?"url":"string"==typeof t.data&&0===(t.contentType||"").indexOf("application/x-www-form-urlencoded")&&ri.test(t.data)&&"data");if(s||"jsonp"===t.dataTypes[0])return f=t.jsonpCallback=i.isFunction(t.jsonpCallback)?t.jsonpCallback():t.jsonpCallback,s?t[s]=t[s].replace(ri,"$1"+f):t.jsonp!==!1&&(t.url+=(yi.test(t.url)?"&":"?")+t.jsonp+"="+f),t.converters["script json"]=function(){return o||i.error(f+" was not called"),o[0]},t.dataTypes[0]="json",e=n[f],n[f]=function(){o=arguments},u.always(function(){void 0===e?i(n).removeProp(f):n[f]=e;t[f]&&(t.jsonpCallback=r.jsonpCallback,di.push(f));o&&i.isFunction(e)&&e(o[0]);o=e=void 0}),"script"}),f.createHTMLDocument=function(){var n=u.implementation.createHTMLDocument("").body;return n.innerHTML="<form><\/form><form><\/form>",2===n.childNodes.length}(),i.parseHTML=function(n,t,r){if("string"!=typeof n)return[];"boolean"==typeof t&&(r=t,t=!1);var s,e,o;return t||(f.createHTMLDocument?(t=u.implementation.createHTMLDocument(""),s=t.createElement("base"),s.href=u.location.href,t.head.appendChild(s)):t=u),e=ei.exec(n),o=!r&&[],e?[t.createElement(e[1])]:(e=iu([n],t,o),o&&o.length&&i(o).remove(),i.merge([],e.childNodes))},i.fn.load=function(n,t,r){var u,o,s,f=this,e=n.indexOf(" ");return e>-1&&(u=k(n.slice(e)),n=n.slice(0,e)),i.isFunction(t)?(r=t,t=void 0):t&&"object"==typeof t&&(o="POST"),f.length>0&&i.ajax({url:n,type:o||"GET",dataType:"html",data:t}).done(function(n){s=arguments;f.html(u?i("<div>").append(i.parseHTML(n)).find(u):n)}).always(r&&function(n,t){f.each(function(){r.apply(this,s||[n.responseText,t,n])})}),this},i.each(["ajaxStart","ajaxStop","ajaxComplete","ajaxError","ajaxSuccess","ajaxSend"],function(n,t){i.fn[t]=function(n){return this.on(t,n)}}),i.expr.pseudos.animated=function(n){return i.grep(i.timers,function(t){return n===t.elem}).length},i.offset={setOffset:function(n,t,r){var e,o,s,h,u,c,v,l=i.css(n,"position"),a=i(n),f={};"static"===l&&(n.style.position="relative");u=a.offset();s=i.css(n,"top");c=i.css(n,"left");v=("absolute"===l||"fixed"===l)&&(s+c).indexOf("auto")>-1;v?(e=a.position(),h=e.top,o=e.left):(h=parseFloat(s)||0,o=parseFloat(c)||0);i.isFunction(t)&&(t=t.call(n,r,i.extend({},u)));null!=t.top&&(f.top=t.top-u.top+h);null!=t.left&&(f.left=t.left-u.left+o);"using"in t?t.using.call(n,f):a.css(f)}},i.fn.extend({offset:function(n){if(arguments.length)return void 0===n?this:this.each(function(t){i.offset.setOffset(this,n,t)});var r,u,f,e,t=this[0];if(t)return t.getClientRects().length?(f=t.getBoundingClientRect(),r=t.ownerDocument,u=r.documentElement,e=r.defaultView,{top:f.top+e.pageYOffset-u.clientTop,left:f.left+e.pageXOffset-u.clientLeft}):{top:0,left:0}},position:function(){if(this[0]){var t,r,u=this[0],n={top:0,left:0};return"fixed"===i.css(u,"position")?r=u.getBoundingClientRect():(t=this.offsetParent(),r=this.offset(),l(t[0],"html")||(n=t.offset()),n={top:n.top+i.css(t[0],"borderTopWidth",!0),left:n.left+i.css(t[0],"borderLeftWidth",!0)}),{top:r.top-n.top-i.css(u,"marginTop",!0),left:r.left-n.left-i.css(u,"marginLeft",!0)}}},offsetParent:function(){return this.map(function(){for(var n=this.offsetParent;n&&"static"===i.css(n,"position");)n=n.offsetParent;return n||dt})}}),i.each({scrollLeft:"pageXOffset",scrollTop:"pageYOffset"},function(n,t){var r="pageYOffset"===t;i.fn[n]=function(u){return v(this,function(n,u,f){var e;return i.isWindow(n)?e=n:9===n.nodeType&&(e=n.defaultView),void 0===f?e?e[t]:n[u]:void(e?e.scrollTo(r?e.pageXOffset:f,r?f:e.pageYOffset):n[u]=f)},n,u,arguments.length)}}),i.each(["top","left"],function(n,t){i.cssHooks[t]=hu(f.pixelPosition,function(n,r){if(r)return r=lt(n,t),li.test(r)?i(n).position()[t]+"px":r})}),i.each({Height:"height",Width:"width"},function(n,t){i.each({padding:"inner"+n,content:t,"":"outer"+n},function(r,u){i.fn[u]=function(f,e){var o=arguments.length&&(r||"boolean"!=typeof f),s=r||(f===!0||e===!0?"margin":"border");return v(this,function(t,r,f){var e;return i.isWindow(t)?0===u.indexOf("outer")?t["inner"+n]:t.document.documentElement["client"+n]:9===t.nodeType?(e=t.documentElement,Math.max(t.body["scroll"+n],e["scroll"+n],t.body["offset"+n],e["offset"+n],e["client"+n])):void 0===f?i.css(t,r,s):i.style(t,r,f,s)},t,o?f:void 0,o)}})}),i.fn.extend({bind:function(n,t,i){return this.on(n,null,t,i)},unbind:function(n,t){return this.off(n,null,t)},delegate:function(n,t,i,r){return this.on(t,n,i,r)},undelegate:function(n,t,i){return 1===arguments.length?this.off(n,"**"):this.off(t,n||"**",i)}}),i.holdReady=function(n){n?i.readyWait++:i.ready(!0)},i.isArray=Array.isArray,i.parseJSON=JSON.parse,i.nodeName=l,"function"==typeof define&&define.amd&&define("jquery",[],function(){return i}),vf=n.jQuery,yf=n.$,i.noConflict=function(t){return n.$===i&&(n.$=yf),t&&n.jQuery===i&&(n.jQuery=vf),i},t||(n.jQuery=n.$=i),i});
/*!
 * Bootstrap v3.3.7 (http://getbootstrap.com)
 * Copyright 2011-2016 Twitter, Inc.
 * Licensed under the MIT license
 */
if("undefined"==typeof jQuery)throw new Error("Bootstrap's JavaScript requires jQuery");+function(n){"use strict";var t=n.fn.jquery.split(" ")[0].split(".");if(t[0]<2&&t[1]<9||1==t[0]&&9==t[1]&&t[2]<1||t[0]>3)throw new Error("Bootstrap's JavaScript requires jQuery version 1.9.1 or higher, but lower than version 4");}(jQuery);+function(n){"use strict";function t(){var i=document.createElement("bootstrap"),n={WebkitTransition:"webkitTransitionEnd",MozTransition:"transitionend",OTransition:"oTransitionEnd otransitionend",transition:"transitionend"};for(var t in n)if(void 0!==i.style[t])return{end:n[t]};return!1}n.fn.emulateTransitionEnd=function(t){var i=!1,u=this,r;n(this).one("bsTransitionEnd",function(){i=!0});return r=function(){i||n(u).trigger(n.support.transition.end)},setTimeout(r,t),this};n(function(){n.support.transition=t();n.support.transition&&(n.event.special.bsTransitionEnd={bindType:n.support.transition.end,delegateType:n.support.transition.end,handle:function(t){if(n(t.target).is(this))return t.handleObj.handler.apply(this,arguments)}})})}(jQuery);+function(n){"use strict";function u(i){return this.each(function(){var r=n(this),u=r.data("bs.alert");u||r.data("bs.alert",u=new t(this));"string"==typeof i&&u[i].call(r)})}var i='[data-dismiss="alert"]',t=function(t){n(t).on("click",i,this.close)},r;t.VERSION="3.3.7";t.TRANSITION_DURATION=150;t.prototype.close=function(i){function e(){r.detach().trigger("closed.bs.alert").remove()}var f=n(this),u=f.attr("data-target"),r;u||(u=f.attr("href"),u=u&&u.replace(/.*(?=#[^\s]*$)/,""));r=n("#"===u?[]:u);i&&i.preventDefault();r.length||(r=f.closest(".alert"));r.trigger(i=n.Event("close.bs.alert"));i.isDefaultPrevented()||(r.removeClass("in"),n.support.transition&&r.hasClass("fade")?r.one("bsTransitionEnd",e).emulateTransitionEnd(t.TRANSITION_DURATION):e())};r=n.fn.alert;n.fn.alert=u;n.fn.alert.Constructor=t;n.fn.alert.noConflict=function(){return n.fn.alert=r,this};n(document).on("click.bs.alert.data-api",i,t.prototype.close)}(jQuery);+function(n){"use strict";function i(i){return this.each(function(){var u=n(this),r=u.data("bs.button"),f="object"==typeof i&&i;r||u.data("bs.button",r=new t(this,f));"toggle"==i?r.toggle():i&&r.setState(i)})}var t=function(i,r){this.$element=n(i);this.options=n.extend({},t.DEFAULTS,r);this.isLoading=!1},r;t.VERSION="3.3.7";t.DEFAULTS={loadingText:"loading..."};t.prototype.setState=function(t){var i="disabled",r=this.$element,f=r.is("input")?"val":"html",u=r.data();t+="Text";null==u.resetText&&r.data("resetText",r[f]());setTimeout(n.proxy(function(){r[f](null==u[t]?this.options[t]:u[t]);"loadingText"==t?(this.isLoading=!0,r.addClass(i).attr(i,i).prop(i,!0)):this.isLoading&&(this.isLoading=!1,r.removeClass(i).removeAttr(i).prop(i,!1))},this),0)};t.prototype.toggle=function(){var t=!0,i=this.$element.closest('[data-toggle="buttons"]'),n;i.length?(n=this.$element.find("input"),"radio"==n.prop("type")?(n.prop("checked")&&(t=!1),i.find(".active").removeClass("active"),this.$element.addClass("active")):"checkbox"==n.prop("type")&&(n.prop("checked")!==this.$element.hasClass("active")&&(t=!1),this.$element.toggleClass("active")),n.prop("checked",this.$element.hasClass("active")),t&&n.trigger("change")):(this.$element.attr("aria-pressed",!this.$element.hasClass("active")),this.$element.toggleClass("active"))};r=n.fn.button;n.fn.button=i;n.fn.button.Constructor=t;n.fn.button.noConflict=function(){return n.fn.button=r,this};n(document).on("click.bs.button.data-api",'[data-toggle^="button"]',function(t){var r=n(t.target).closest(".btn");i.call(r,"toggle");n(t.target).is('input[type="radio"], input[type="checkbox"]')||(t.preventDefault(),r.is("input,button")?r.trigger("focus"):r.find("input:visible,button:visible").first().trigger("focus"))}).on("focus.bs.button.data-api blur.bs.button.data-api",'[data-toggle^="button"]',function(t){n(t.target).closest(".btn").toggleClass("focus",/^focus(in)?$/.test(t.type))})}(jQuery);+function(n){"use strict";function i(i){return this.each(function(){var u=n(this),r=u.data("bs.carousel"),f=n.extend({},t.DEFAULTS,u.data(),"object"==typeof i&&i),e="string"==typeof i?i:f.slide;r||u.data("bs.carousel",r=new t(this,f));"number"==typeof i?r.to(i):e?r[e]():f.interval&&r.pause().cycle()})}var t=function(t,i){this.$element=n(t);this.$indicators=this.$element.find(".carousel-indicators");this.options=i;this.paused=null;this.sliding=null;this.interval=null;this.$active=null;this.$items=null;this.options.keyboard&&this.$element.on("keydown.bs.carousel",n.proxy(this.keydown,this));"hover"==this.options.pause&&!("ontouchstart"in document.documentElement)&&this.$element.on("mouseenter.bs.carousel",n.proxy(this.pause,this)).on("mouseleave.bs.carousel",n.proxy(this.cycle,this))},u,r;t.VERSION="3.3.7";t.TRANSITION_DURATION=600;t.DEFAULTS={interval:5e3,pause:"hover",wrap:!0,keyboard:!0};t.prototype.keydown=function(n){if(!/input|textarea/i.test(n.target.tagName)){switch(n.which){case 37:this.prev();break;case 39:this.next();break;default:return}n.preventDefault()}};t.prototype.cycle=function(t){return t||(this.paused=!1),this.interval&&clearInterval(this.interval),this.options.interval&&!this.paused&&(this.interval=setInterval(n.proxy(this.next,this),this.options.interval)),this};t.prototype.getItemIndex=function(n){return this.$items=n.parent().children(".item"),this.$items.index(n||this.$active)};t.prototype.getItemForDirection=function(n,t){var i=this.getItemIndex(t),f="prev"==n&&0===i||"next"==n&&i==this.$items.length-1,r,u;return f&&!this.options.wrap?t:(r="prev"==n?-1:1,u=(i+r)%this.$items.length,this.$items.eq(u))};t.prototype.to=function(n){var i=this,t=this.getItemIndex(this.$active=this.$element.find(".item.active"));if(!(n>this.$items.length-1||n<0))return this.sliding?this.$element.one("slid.bs.carousel",function(){i.to(n)}):t==n?this.pause().cycle():this.slide(n>t?"next":"prev",this.$items.eq(n))};t.prototype.pause=function(t){return t||(this.paused=!0),this.$element.find(".next, .prev").length&&n.support.transition&&(this.$element.trigger(n.support.transition.end),this.cycle(!0)),this.interval=clearInterval(this.interval),this};t.prototype.next=function(){if(!this.sliding)return this.slide("next")};t.prototype.prev=function(){if(!this.sliding)return this.slide("prev")};t.prototype.slide=function(i,r){var e=this.$element.find(".item.active"),u=r||this.getItemForDirection(i,e),l=this.interval,f="next"==i?"left":"right",a=this,o,s,h,c;return u.hasClass("active")?this.sliding=!1:(o=u[0],s=n.Event("slide.bs.carousel",{relatedTarget:o,direction:f}),(this.$element.trigger(s),!s.isDefaultPrevented())?((this.sliding=!0,l&&this.pause(),this.$indicators.length)&&(this.$indicators.find(".active").removeClass("active"),h=n(this.$indicators.children()[this.getItemIndex(u)]),h&&h.addClass("active")),c=n.Event("slid.bs.carousel",{relatedTarget:o,direction:f}),n.support.transition&&this.$element.hasClass("slide")?(u.addClass(i),u[0].offsetWidth,e.addClass(f),u.addClass(f),e.one("bsTransitionEnd",function(){u.removeClass([i,f].join(" ")).addClass("active");e.removeClass(["active",f].join(" "));a.sliding=!1;setTimeout(function(){a.$element.trigger(c)},0)}).emulateTransitionEnd(t.TRANSITION_DURATION)):(e.removeClass("active"),u.addClass("active"),this.sliding=!1,this.$element.trigger(c)),l&&this.cycle(),this):void 0)};u=n.fn.carousel;n.fn.carousel=i;n.fn.carousel.Constructor=t;n.fn.carousel.noConflict=function(){return n.fn.carousel=u,this};r=function(t){var o,r=n(this),u=n(r.attr("data-target")||(o=r.attr("href"))&&o.replace(/.*(?=#[^\s]+$)/,"")),e,f;u.hasClass("carousel")&&(e=n.extend({},u.data(),r.data()),f=r.attr("data-slide-to"),f&&(e.interval=!1),i.call(u,e),f&&u.data("bs.carousel").to(f),t.preventDefault())};n(document).on("click.bs.carousel.data-api","[data-slide]",r).on("click.bs.carousel.data-api","[data-slide-to]",r);n(window).on("load",function(){n('[data-ride="carousel"]').each(function(){var t=n(this);i.call(t,t.data())})})}(jQuery);+function(n){"use strict";function r(t){var i,r=t.attr("data-target")||(i=t.attr("href"))&&i.replace(/.*(?=#[^\s]+$)/,"");return n(r)}function i(i){return this.each(function(){var u=n(this),r=u.data("bs.collapse"),f=n.extend({},t.DEFAULTS,u.data(),"object"==typeof i&&i);!r&&f.toggle&&/show|hide/.test(i)&&(f.toggle=!1);r||u.data("bs.collapse",r=new t(this,f));"string"==typeof i&&r[i]()})}var t=function(i,r){this.$element=n(i);this.options=n.extend({},t.DEFAULTS,r);this.$trigger=n('[data-toggle="collapse"][href="#'+i.id+'"],[data-toggle="collapse"][data-target="#'+i.id+'"]');this.transitioning=null;this.options.parent?this.$parent=this.getParent():this.addAriaAndCollapsedClass(this.$element,this.$trigger);this.options.toggle&&this.toggle()},u;t.VERSION="3.3.7";t.TRANSITION_DURATION=350;t.DEFAULTS={toggle:!0};t.prototype.dimension=function(){var n=this.$element.hasClass("width");return n?"width":"height"};t.prototype.show=function(){var f,r,e,u,o,s;if(!this.transitioning&&!this.$element.hasClass("in")&&(r=this.$parent&&this.$parent.children(".panel").children(".in, .collapsing"),!(r&&r.length&&(f=r.data("bs.collapse"),f&&f.transitioning))&&(e=n.Event("show.bs.collapse"),this.$element.trigger(e),!e.isDefaultPrevented()))){if(r&&r.length&&(i.call(r,"hide"),f||r.data("bs.collapse",null)),u=this.dimension(),this.$element.removeClass("collapse").addClass("collapsing")[u](0).attr("aria-expanded",!0),this.$trigger.removeClass("collapsed").attr("aria-expanded",!0),this.transitioning=1,o=function(){this.$element.removeClass("collapsing").addClass("collapse in")[u]("");this.transitioning=0;this.$element.trigger("shown.bs.collapse")},!n.support.transition)return o.call(this);s=n.camelCase(["scroll",u].join("-"));this.$element.one("bsTransitionEnd",n.proxy(o,this)).emulateTransitionEnd(t.TRANSITION_DURATION)[u](this.$element[0][s])}};t.prototype.hide=function(){var r,i,u;if(!this.transitioning&&this.$element.hasClass("in")&&(r=n.Event("hide.bs.collapse"),this.$element.trigger(r),!r.isDefaultPrevented()))return i=this.dimension(),this.$element[i](this.$element[i]())[0].offsetHeight,this.$element.addClass("collapsing").removeClass("collapse in").attr("aria-expanded",!1),this.$trigger.addClass("collapsed").attr("aria-expanded",!1),this.transitioning=1,u=function(){this.transitioning=0;this.$element.removeClass("collapsing").addClass("collapse").trigger("hidden.bs.collapse")},n.support.transition?void this.$element[i](0).one("bsTransitionEnd",n.proxy(u,this)).emulateTransitionEnd(t.TRANSITION_DURATION):u.call(this)};t.prototype.toggle=function(){this[this.$element.hasClass("in")?"hide":"show"]()};t.prototype.getParent=function(){return n(this.options.parent).find('[data-toggle="collapse"][data-parent="'+this.options.parent+'"]').each(n.proxy(function(t,i){var u=n(i);this.addAriaAndCollapsedClass(r(u),u)},this)).end()};t.prototype.addAriaAndCollapsedClass=function(n,t){var i=n.hasClass("in");n.attr("aria-expanded",i);t.toggleClass("collapsed",!i).attr("aria-expanded",i)};u=n.fn.collapse;n.fn.collapse=i;n.fn.collapse.Constructor=t;n.fn.collapse.noConflict=function(){return n.fn.collapse=u,this};n(document).on("click.bs.collapse.data-api",'[data-toggle="collapse"]',function(t){var u=n(this);u.attr("data-target")||t.preventDefault();var f=r(u),e=f.data("bs.collapse"),o=e?"toggle":u.data();i.call(f,o)})}(jQuery);+function(n){"use strict";function r(t){var i=t.attr("data-target"),r;return i||(i=t.attr("href"),i=i&&/#[A-Za-z]/.test(i)&&i.replace(/.*(?=#[^\s]*$)/,"")),r=i&&n(i),r&&r.length?r:t.parent()}function u(t){t&&3===t.which||(n(o).remove(),n(i).each(function(){var u=n(this),i=r(u),f={relatedTarget:this};i.hasClass("open")&&(t&&"click"==t.type&&/input|textarea/i.test(t.target.tagName)&&n.contains(i[0],t.target)||(i.trigger(t=n.Event("hide.bs.dropdown",f)),t.isDefaultPrevented()||(u.attr("aria-expanded","false"),i.removeClass("open").trigger(n.Event("hidden.bs.dropdown",f)))))}))}function e(i){return this.each(function(){var r=n(this),u=r.data("bs.dropdown");u||r.data("bs.dropdown",u=new t(this));"string"==typeof i&&u[i].call(r)})}var o=".dropdown-backdrop",i='[data-toggle="dropdown"]',t=function(t){n(t).on("click.bs.dropdown",this.toggle)},f;t.VERSION="3.3.7";t.prototype.toggle=function(t){var f=n(this),i,o,e;if(!f.is(".disabled, :disabled")){if(i=r(f),o=i.hasClass("open"),u(),!o){if("ontouchstart"in document.documentElement&&!i.closest(".navbar-nav").length&&n(document.createElement("div")).addClass("dropdown-backdrop").insertAfter(n(this)).on("click",u),e={relatedTarget:this},i.trigger(t=n.Event("show.bs.dropdown",e)),t.isDefaultPrevented())return;f.trigger("focus").attr("aria-expanded","true");i.toggleClass("open").trigger(n.Event("shown.bs.dropdown",e))}return!1}};t.prototype.keydown=function(t){var e,o,s,h,f,u;if(/(38|40|27|32)/.test(t.which)&&!/input|textarea/i.test(t.target.tagName)&&(e=n(this),t.preventDefault(),t.stopPropagation(),!e.is(".disabled, :disabled"))){if(o=r(e),s=o.hasClass("open"),!s&&27!=t.which||s&&27==t.which)return 27==t.which&&o.find(i).trigger("focus"),e.trigger("click");h=" li:not(.disabled):visible a";f=o.find(".dropdown-menu"+h);f.length&&(u=f.index(t.target),38==t.which&&u>0&&u--,40==t.which&&u<f.length-1&&u++,~u||(u=0),f.eq(u).trigger("focus"))}};f=n.fn.dropdown;n.fn.dropdown=e;n.fn.dropdown.Constructor=t;n.fn.dropdown.noConflict=function(){return n.fn.dropdown=f,this};n(document).on("click.bs.dropdown.data-api",u).on("click.bs.dropdown.data-api",".dropdown form",function(n){n.stopPropagation()}).on("click.bs.dropdown.data-api",i,t.prototype.toggle).on("keydown.bs.dropdown.data-api",i,t.prototype.keydown).on("keydown.bs.dropdown.data-api",".dropdown-menu",t.prototype.keydown)}(jQuery);+function(n){"use strict";function i(i,r){return this.each(function(){var f=n(this),u=f.data("bs.modal"),e=n.extend({},t.DEFAULTS,f.data(),"object"==typeof i&&i);u||f.data("bs.modal",u=new t(this,e));"string"==typeof i?u[i](r):e.show&&u.show(r)})}var t=function(t,i){this.options=i;this.$body=n(document.body);this.$element=n(t);this.$dialog=this.$element.find(".modal-dialog");this.$backdrop=null;this.isShown=null;this.originalBodyPad=null;this.scrollbarWidth=0;this.ignoreBackdropClick=!1;this.options.remote&&this.$element.find(".modal-content").load(this.options.remote,n.proxy(function(){this.$element.trigger("loaded.bs.modal")},this))},r;t.VERSION="3.3.7";t.TRANSITION_DURATION=300;t.BACKDROP_TRANSITION_DURATION=150;t.DEFAULTS={backdrop:!0,keyboard:!0,show:!0};t.prototype.toggle=function(n){return this.isShown?this.hide():this.show(n)};t.prototype.show=function(i){var r=this,u=n.Event("show.bs.modal",{relatedTarget:i});this.$element.trigger(u);this.isShown||u.isDefaultPrevented()||(this.isShown=!0,this.checkScrollbar(),this.setScrollbar(),this.$body.addClass("modal-open"),this.escape(),this.resize(),this.$element.on("click.dismiss.bs.modal",'[data-dismiss="modal"]',n.proxy(this.hide,this)),this.$dialog.on("mousedown.dismiss.bs.modal",function(){r.$element.one("mouseup.dismiss.bs.modal",function(t){n(t.target).is(r.$element)&&(r.ignoreBackdropClick=!0)})}),this.backdrop(function(){var f=n.support.transition&&r.$element.hasClass("fade"),u;r.$element.parent().length||r.$element.appendTo(r.$body);r.$element.show().scrollTop(0);r.adjustDialog();f&&r.$element[0].offsetWidth;r.$element.addClass("in");r.enforceFocus();u=n.Event("shown.bs.modal",{relatedTarget:i});f?r.$dialog.one("bsTransitionEnd",function(){r.$element.trigger("focus").trigger(u)}).emulateTransitionEnd(t.TRANSITION_DURATION):r.$element.trigger("focus").trigger(u)}))};t.prototype.hide=function(i){i&&i.preventDefault();i=n.Event("hide.bs.modal");this.$element.trigger(i);this.isShown&&!i.isDefaultPrevented()&&(this.isShown=!1,this.escape(),this.resize(),n(document).off("focusin.bs.modal"),this.$element.removeClass("in").off("click.dismiss.bs.modal").off("mouseup.dismiss.bs.modal"),this.$dialog.off("mousedown.dismiss.bs.modal"),n.support.transition&&this.$element.hasClass("fade")?this.$element.one("bsTransitionEnd",n.proxy(this.hideModal,this)).emulateTransitionEnd(t.TRANSITION_DURATION):this.hideModal())};t.prototype.enforceFocus=function(){n(document).off("focusin.bs.modal").on("focusin.bs.modal",n.proxy(function(n){document===n.target||this.$element[0]===n.target||this.$element.has(n.target).length||this.$element.trigger("focus")},this))};t.prototype.escape=function(){this.isShown&&this.options.keyboard?this.$element.on("keydown.dismiss.bs.modal",n.proxy(function(n){27==n.which&&this.hide()},this)):this.isShown||this.$element.off("keydown.dismiss.bs.modal")};t.prototype.resize=function(){this.isShown?n(window).on("resize.bs.modal",n.proxy(this.handleUpdate,this)):n(window).off("resize.bs.modal")};t.prototype.hideModal=function(){var n=this;this.$element.hide();this.backdrop(function(){n.$body.removeClass("modal-open");n.resetAdjustments();n.resetScrollbar();n.$element.trigger("hidden.bs.modal")})};t.prototype.removeBackdrop=function(){this.$backdrop&&this.$backdrop.remove();this.$backdrop=null};t.prototype.backdrop=function(i){var e=this,f=this.$element.hasClass("fade")?"fade":"",r,u;if(this.isShown&&this.options.backdrop){if(r=n.support.transition&&f,this.$backdrop=n(document.createElement("div")).addClass("modal-backdrop "+f).appendTo(this.$body),this.$element.on("click.dismiss.bs.modal",n.proxy(function(n){return this.ignoreBackdropClick?void(this.ignoreBackdropClick=!1):void(n.target===n.currentTarget&&("static"==this.options.backdrop?this.$element[0].focus():this.hide()))},this)),r&&this.$backdrop[0].offsetWidth,this.$backdrop.addClass("in"),!i)return;r?this.$backdrop.one("bsTransitionEnd",i).emulateTransitionEnd(t.BACKDROP_TRANSITION_DURATION):i()}else!this.isShown&&this.$backdrop?(this.$backdrop.removeClass("in"),u=function(){e.removeBackdrop();i&&i()},n.support.transition&&this.$element.hasClass("fade")?this.$backdrop.one("bsTransitionEnd",u).emulateTransitionEnd(t.BACKDROP_TRANSITION_DURATION):u()):i&&i()};t.prototype.handleUpdate=function(){this.adjustDialog()};t.prototype.adjustDialog=function(){var n=this.$element[0].scrollHeight>document.documentElement.clientHeight;this.$element.css({paddingLeft:!this.bodyIsOverflowing&&n?this.scrollbarWidth:"",paddingRight:this.bodyIsOverflowing&&!n?this.scrollbarWidth:""})};t.prototype.resetAdjustments=function(){this.$element.css({paddingLeft:"",paddingRight:""})};t.prototype.checkScrollbar=function(){var n=window.innerWidth,t;n||(t=document.documentElement.getBoundingClientRect(),n=t.right-Math.abs(t.left));this.bodyIsOverflowing=document.body.clientWidth<n;this.scrollbarWidth=this.measureScrollbar()};t.prototype.setScrollbar=function(){var n=parseInt(this.$body.css("padding-right")||0,10);this.originalBodyPad=document.body.style.paddingRight||"";this.bodyIsOverflowing&&this.$body.css("padding-right",n+this.scrollbarWidth)};t.prototype.resetScrollbar=function(){this.$body.css("padding-right",this.originalBodyPad)};t.prototype.measureScrollbar=function(){var n=document.createElement("div"),t;return n.className="modal-scrollbar-measure",this.$body.append(n),t=n.offsetWidth-n.clientWidth,this.$body[0].removeChild(n),t};r=n.fn.modal;n.fn.modal=i;n.fn.modal.Constructor=t;n.fn.modal.noConflict=function(){return n.fn.modal=r,this};n(document).on("click.bs.modal.data-api",'[data-toggle="modal"]',function(t){var r=n(this),f=r.attr("href"),u=n(r.attr("data-target")||f&&f.replace(/.*(?=#[^\s]+$)/,"")),e=u.data("bs.modal")?"toggle":n.extend({remote:!/#/.test(f)&&f},u.data(),r.data());r.is("a")&&t.preventDefault();u.one("show.bs.modal",function(n){n.isDefaultPrevented()||u.one("hidden.bs.modal",function(){r.is(":visible")&&r.trigger("focus")})});i.call(u,e,this)})}(jQuery);+function(n){"use strict";function r(i){return this.each(function(){var u=n(this),r=u.data("bs.tooltip"),f="object"==typeof i&&i;!r&&/destroy|hide/.test(i)||(r||u.data("bs.tooltip",r=new t(this,f)),"string"==typeof i&&r[i]())})}var t=function(n,t){this.type=null;this.options=null;this.enabled=null;this.timeout=null;this.hoverState=null;this.$element=null;this.inState=null;this.init("tooltip",n,t)},i;t.VERSION="3.3.7";t.TRANSITION_DURATION=150;t.DEFAULTS={animation:!0,placement:"top",selector:!1,template:'<div class="tooltip" role="tooltip"><div class="tooltip-arrow"><\/div><div class="tooltip-inner"><\/div><\/div>',trigger:"hover focus",title:"",delay:0,html:!1,container:!1,viewport:{selector:"body",padding:0}};t.prototype.init=function(t,i,r){var f,e,u,o,s;if(this.enabled=!0,this.type=t,this.$element=n(i),this.options=this.getOptions(r),this.$viewport=this.options.viewport&&n(n.isFunction(this.options.viewport)?this.options.viewport.call(this,this.$element):this.options.viewport.selector||this.options.viewport),this.inState={click:!1,hover:!1,focus:!1},this.$element[0]instanceof document.constructor&&!this.options.selector)throw new Error("`selector` option must be specified when initializing "+this.type+" on the window.document object!");for(f=this.options.trigger.split(" "),e=f.length;e--;)if(u=f[e],"click"==u)this.$element.on("click."+this.type,this.options.selector,n.proxy(this.toggle,this));else"manual"!=u&&(o="hover"==u?"mouseenter":"focusin",s="hover"==u?"mouseleave":"focusout",this.$element.on(o+"."+this.type,this.options.selector,n.proxy(this.enter,this)),this.$element.on(s+"."+this.type,this.options.selector,n.proxy(this.leave,this)));this.options.selector?this._options=n.extend({},this.options,{trigger:"manual",selector:""}):this.fixTitle()};t.prototype.getDefaults=function(){return t.DEFAULTS};t.prototype.getOptions=function(t){return t=n.extend({},this.getDefaults(),this.$element.data(),t),t.delay&&"number"==typeof t.delay&&(t.delay={show:t.delay,hide:t.delay}),t};t.prototype.getDelegateOptions=function(){var t={},i=this.getDefaults();return this._options&&n.each(this._options,function(n,r){i[n]!=r&&(t[n]=r)}),t};t.prototype.enter=function(t){var i=t instanceof this.constructor?t:n(t.currentTarget).data("bs."+this.type);return i||(i=new this.constructor(t.currentTarget,this.getDelegateOptions()),n(t.currentTarget).data("bs."+this.type,i)),t instanceof n.Event&&(i.inState["focusin"==t.type?"focus":"hover"]=!0),i.tip().hasClass("in")||"in"==i.hoverState?void(i.hoverState="in"):(clearTimeout(i.timeout),i.hoverState="in",i.options.delay&&i.options.delay.show?void(i.timeout=setTimeout(function(){"in"==i.hoverState&&i.show()},i.options.delay.show)):i.show())};t.prototype.isInStateTrue=function(){for(var n in this.inState)if(this.inState[n])return!0;return!1};t.prototype.leave=function(t){var i=t instanceof this.constructor?t:n(t.currentTarget).data("bs."+this.type);if(i||(i=new this.constructor(t.currentTarget,this.getDelegateOptions()),n(t.currentTarget).data("bs."+this.type,i)),t instanceof n.Event&&(i.inState["focusout"==t.type?"focus":"hover"]=!1),!i.isInStateTrue())return clearTimeout(i.timeout),i.hoverState="out",i.options.delay&&i.options.delay.hide?void(i.timeout=setTimeout(function(){"out"==i.hoverState&&i.hide()},i.options.delay.hide)):i.hide()};t.prototype.show=function(){var c=n.Event("show.bs."+this.type),l,p,e,w,h;if(this.hasContent()&&this.enabled){if(this.$element.trigger(c),l=n.contains(this.$element[0].ownerDocument.documentElement,this.$element[0]),c.isDefaultPrevented()||!l)return;var u=this,r=this.tip(),a=this.getUID(this.type);this.setContent();r.attr("id",a);this.$element.attr("aria-describedby",a);this.options.animation&&r.addClass("fade");var i="function"==typeof this.options.placement?this.options.placement.call(this,r[0],this.$element[0]):this.options.placement,v=/\s?auto?\s?/i,y=v.test(i);y&&(i=i.replace(v,"")||"top");r.detach().css({top:0,left:0,display:"block"}).addClass(i).data("bs."+this.type,this);this.options.container?r.appendTo(this.options.container):r.insertAfter(this.$element);this.$element.trigger("inserted.bs."+this.type);var f=this.getPosition(),o=r[0].offsetWidth,s=r[0].offsetHeight;y&&(p=i,e=this.getPosition(this.$viewport),i="bottom"==i&&f.bottom+s>e.bottom?"top":"top"==i&&f.top-s<e.top?"bottom":"right"==i&&f.right+o>e.width?"left":"left"==i&&f.left-o<e.left?"right":i,r.removeClass(p).addClass(i));w=this.getCalculatedOffset(i,f,o,s);this.applyPlacement(w,i);h=function(){var n=u.hoverState;u.$element.trigger("shown.bs."+u.type);u.hoverState=null;"out"==n&&u.leave(u)};n.support.transition&&this.$tip.hasClass("fade")?r.one("bsTransitionEnd",h).emulateTransitionEnd(t.TRANSITION_DURATION):h()}};t.prototype.applyPlacement=function(t,i){var r=this.tip(),l=r[0].offsetWidth,e=r[0].offsetHeight,o=parseInt(r.css("margin-top"),10),s=parseInt(r.css("margin-left"),10),h,f,u;isNaN(o)&&(o=0);isNaN(s)&&(s=0);t.top+=o;t.left+=s;n.offset.setOffset(r[0],n.extend({using:function(n){r.css({top:Math.round(n.top),left:Math.round(n.left)})}},t),0);r.addClass("in");h=r[0].offsetWidth;f=r[0].offsetHeight;"top"==i&&f!=e&&(t.top=t.top+e-f);u=this.getViewportAdjustedDelta(i,t,h,f);u.left?t.left+=u.left:t.top+=u.top;var c=/top|bottom/.test(i),a=c?2*u.left-l+h:2*u.top-e+f,v=c?"offsetWidth":"offsetHeight";r.offset(t);this.replaceArrow(a,r[0][v],c)};t.prototype.replaceArrow=function(n,t,i){this.arrow().css(i?"left":"top",50*(1-n/t)+"%").css(i?"top":"left","")};t.prototype.setContent=function(){var n=this.tip(),t=this.getTitle();n.find(".tooltip-inner")[this.options.html?"html":"text"](t);n.removeClass("fade in top bottom left right")};t.prototype.hide=function(i){function f(){"in"!=r.hoverState&&u.detach();r.$element&&r.$element.removeAttr("aria-describedby").trigger("hidden.bs."+r.type);i&&i()}var r=this,u=n(this.$tip),e=n.Event("hide.bs."+this.type);if(this.$element.trigger(e),!e.isDefaultPrevented())return u.removeClass("in"),n.support.transition&&u.hasClass("fade")?u.one("bsTransitionEnd",f).emulateTransitionEnd(t.TRANSITION_DURATION):f(),this.hoverState=null,this};t.prototype.fixTitle=function(){var n=this.$element;(n.attr("title")||"string"!=typeof n.attr("data-original-title"))&&n.attr("data-original-title",n.attr("title")||"").attr("title","")};t.prototype.hasContent=function(){return this.getTitle()};t.prototype.getPosition=function(t){t=t||this.$element;var r=t[0],u="BODY"==r.tagName,i=r.getBoundingClientRect();null==i.width&&(i=n.extend({},i,{width:i.right-i.left,height:i.bottom-i.top}));var f=window.SVGElement&&r instanceof window.SVGElement,e=u?{top:0,left:0}:f?null:t.offset(),o={scroll:u?document.documentElement.scrollTop||document.body.scrollTop:t.scrollTop()},s=u?{width:n(window).width(),height:n(window).height()}:null;return n.extend({},i,o,s,e)};t.prototype.getCalculatedOffset=function(n,t,i,r){return"bottom"==n?{top:t.top+t.height,left:t.left+t.width/2-i/2}:"top"==n?{top:t.top-r,left:t.left+t.width/2-i/2}:"left"==n?{top:t.top+t.height/2-r/2,left:t.left-i}:{top:t.top+t.height/2-r/2,left:t.left+t.width}};t.prototype.getViewportAdjustedDelta=function(n,t,i,r){var f={top:0,left:0},e,u,o,s,h,c;return this.$viewport?(e=this.options.viewport&&this.options.viewport.padding||0,u=this.getPosition(this.$viewport),/right|left/.test(n)?(o=t.top-e-u.scroll,s=t.top+e-u.scroll+r,o<u.top?f.top=u.top-o:s>u.top+u.height&&(f.top=u.top+u.height-s)):(h=t.left-e,c=t.left+e+i,h<u.left?f.left=u.left-h:c>u.right&&(f.left=u.left+u.width-c)),f):f};t.prototype.getTitle=function(){var t=this.$element,n=this.options;return t.attr("data-original-title")||("function"==typeof n.title?n.title.call(t[0]):n.title)};t.prototype.getUID=function(n){do n+=~~(1e6*Math.random());while(document.getElementById(n));return n};t.prototype.tip=function(){if(!this.$tip&&(this.$tip=n(this.options.template),1!=this.$tip.length))throw new Error(this.type+" `template` option must consist of exactly 1 top-level element!");return this.$tip};t.prototype.arrow=function(){return this.$arrow=this.$arrow||this.tip().find(".tooltip-arrow")};t.prototype.enable=function(){this.enabled=!0};t.prototype.disable=function(){this.enabled=!1};t.prototype.toggleEnabled=function(){this.enabled=!this.enabled};t.prototype.toggle=function(t){var i=this;t&&(i=n(t.currentTarget).data("bs."+this.type),i||(i=new this.constructor(t.currentTarget,this.getDelegateOptions()),n(t.currentTarget).data("bs."+this.type,i)));t?(i.inState.click=!i.inState.click,i.isInStateTrue()?i.enter(i):i.leave(i)):i.tip().hasClass("in")?i.leave(i):i.enter(i)};t.prototype.destroy=function(){var n=this;clearTimeout(this.timeout);this.hide(function(){n.$element.off("."+n.type).removeData("bs."+n.type);n.$tip&&n.$tip.detach();n.$tip=null;n.$arrow=null;n.$viewport=null;n.$element=null})};i=n.fn.tooltip;n.fn.tooltip=r;n.fn.tooltip.Constructor=t;n.fn.tooltip.noConflict=function(){return n.fn.tooltip=i,this}}(jQuery);+function(n){"use strict";function r(i){return this.each(function(){var u=n(this),r=u.data("bs.popover"),f="object"==typeof i&&i;!r&&/destroy|hide/.test(i)||(r||u.data("bs.popover",r=new t(this,f)),"string"==typeof i&&r[i]())})}var t=function(n,t){this.init("popover",n,t)},i;if(!n.fn.tooltip)throw new Error("Popover requires tooltip.js");t.VERSION="3.3.7";t.DEFAULTS=n.extend({},n.fn.tooltip.Constructor.DEFAULTS,{placement:"right",trigger:"click",content:"",template:'<div class="popover" role="tooltip"><div class="arrow"><\/div><h3 class="popover-title"><\/h3><div class="popover-content"><\/div><\/div>'});t.prototype=n.extend({},n.fn.tooltip.Constructor.prototype);t.prototype.constructor=t;t.prototype.getDefaults=function(){return t.DEFAULTS};t.prototype.setContent=function(){var n=this.tip(),i=this.getTitle(),t=this.getContent();n.find(".popover-title")[this.options.html?"html":"text"](i);n.find(".popover-content").children().detach().end()[this.options.html?"string"==typeof t?"html":"append":"text"](t);n.removeClass("fade top bottom left right in");n.find(".popover-title").html()||n.find(".popover-title").hide()};t.prototype.hasContent=function(){return this.getTitle()||this.getContent()};t.prototype.getContent=function(){var t=this.$element,n=this.options;return t.attr("data-content")||("function"==typeof n.content?n.content.call(t[0]):n.content)};t.prototype.arrow=function(){return this.$arrow=this.$arrow||this.tip().find(".arrow")};i=n.fn.popover;n.fn.popover=r;n.fn.popover.Constructor=t;n.fn.popover.noConflict=function(){return n.fn.popover=i,this}}(jQuery);+function(n){"use strict";function t(i,r){this.$body=n(document.body);this.$scrollElement=n(n(i).is(document.body)?window:i);this.options=n.extend({},t.DEFAULTS,r);this.selector=(this.options.target||"")+" .nav li > a";this.offsets=[];this.targets=[];this.activeTarget=null;this.scrollHeight=0;this.$scrollElement.on("scroll.bs.scrollspy",n.proxy(this.process,this));this.refresh();this.process()}function i(i){return this.each(function(){var u=n(this),r=u.data("bs.scrollspy"),f="object"==typeof i&&i;r||u.data("bs.scrollspy",r=new t(this,f));"string"==typeof i&&r[i]()})}t.VERSION="3.3.7";t.DEFAULTS={offset:10};t.prototype.getScrollHeight=function(){return this.$scrollElement[0].scrollHeight||Math.max(this.$body[0].scrollHeight,document.documentElement.scrollHeight)};t.prototype.refresh=function(){var t=this,i="offset",r=0;this.offsets=[];this.targets=[];this.scrollHeight=this.getScrollHeight();n.isWindow(this.$scrollElement[0])||(i="position",r=this.$scrollElement.scrollTop());this.$body.find(this.selector).map(function(){var f=n(this),u=f.data("target")||f.attr("href"),t=/^#./.test(u)&&n(u);return t&&t.length&&t.is(":visible")&&[[t[i]().top+r,u]]||null}).sort(function(n,t){return n[0]-t[0]}).each(function(){t.offsets.push(this[0]);t.targets.push(this[1])})};t.prototype.process=function(){var n,i=this.$scrollElement.scrollTop()+this.options.offset,f=this.getScrollHeight(),e=this.options.offset+f-this.$scrollElement.height(),t=this.offsets,r=this.targets,u=this.activeTarget;if(this.scrollHeight!=f&&this.refresh(),i>=e)return u!=(n=r[r.length-1])&&this.activate(n);if(u&&i<t[0])return this.activeTarget=null,this.clear();for(n=t.length;n--;)u!=r[n]&&i>=t[n]&&(void 0===t[n+1]||i<t[n+1])&&this.activate(r[n])};t.prototype.activate=function(t){this.activeTarget=t;this.clear();var r=this.selector+'[data-target="'+t+'"],'+this.selector+'[href="'+t+'"]',i=n(r).parents("li").addClass("active");i.parent(".dropdown-menu").length&&(i=i.closest("li.dropdown").addClass("active"));i.trigger("activate.bs.scrollspy")};t.prototype.clear=function(){n(this.selector).parentsUntil(this.options.target,".active").removeClass("active")};var r=n.fn.scrollspy;n.fn.scrollspy=i;n.fn.scrollspy.Constructor=t;n.fn.scrollspy.noConflict=function(){return n.fn.scrollspy=r,this};n(window).on("load.bs.scrollspy.data-api",function(){n('[data-spy="scroll"]').each(function(){var t=n(this);i.call(t,t.data())})})}(jQuery);+function(n){"use strict";function r(i){return this.each(function(){var u=n(this),r=u.data("bs.tab");r||u.data("bs.tab",r=new t(this));"string"==typeof i&&r[i]()})}var t=function(t){this.element=n(t)},u,i;t.VERSION="3.3.7";t.TRANSITION_DURATION=150;t.prototype.show=function(){var t=this.element,f=t.closest("ul:not(.dropdown-menu)"),i=t.data("target"),u;if(i||(i=t.attr("href"),i=i&&i.replace(/.*(?=#[^\s]*$)/,"")),!t.parent("li").hasClass("active")){var r=f.find(".active:last a"),e=n.Event("hide.bs.tab",{relatedTarget:t[0]}),o=n.Event("show.bs.tab",{relatedTarget:r[0]});(r.trigger(e),t.trigger(o),o.isDefaultPrevented()||e.isDefaultPrevented())||(u=n(i),this.activate(t.closest("li"),f),this.activate(u,u.parent(),function(){r.trigger({type:"hidden.bs.tab",relatedTarget:t[0]});t.trigger({type:"shown.bs.tab",relatedTarget:r[0]})}))}};t.prototype.activate=function(i,r,u){function e(){f.removeClass("active").find("> .dropdown-menu > .active").removeClass("active").end().find('[data-toggle="tab"]').attr("aria-expanded",!1);i.addClass("active").find('[data-toggle="tab"]').attr("aria-expanded",!0);o?(i[0].offsetWidth,i.addClass("in")):i.removeClass("fade");i.parent(".dropdown-menu").length&&i.closest("li.dropdown").addClass("active").end().find('[data-toggle="tab"]').attr("aria-expanded",!0);u&&u()}var f=r.find("> .active"),o=u&&n.support.transition&&(f.length&&f.hasClass("fade")||!!r.find("> .fade").length);f.length&&o?f.one("bsTransitionEnd",e).emulateTransitionEnd(t.TRANSITION_DURATION):e();f.removeClass("in")};u=n.fn.tab;n.fn.tab=r;n.fn.tab.Constructor=t;n.fn.tab.noConflict=function(){return n.fn.tab=u,this};i=function(t){t.preventDefault();r.call(n(this),"show")};n(document).on("click.bs.tab.data-api",'[data-toggle="tab"]',i).on("click.bs.tab.data-api",'[data-toggle="pill"]',i)}(jQuery);+function(n){"use strict";function i(i){return this.each(function(){var u=n(this),r=u.data("bs.affix"),f="object"==typeof i&&i;r||u.data("bs.affix",r=new t(this,f));"string"==typeof i&&r[i]()})}var t=function(i,r){this.options=n.extend({},t.DEFAULTS,r);this.$target=n(this.options.target).on("scroll.bs.affix.data-api",n.proxy(this.checkPosition,this)).on("click.bs.affix.data-api",n.proxy(this.checkPositionWithEventLoop,this));this.$element=n(i);this.affixed=null;this.unpin=null;this.pinnedOffset=null;this.checkPosition()},r;t.VERSION="3.3.7";t.RESET="affix affix-top affix-bottom";t.DEFAULTS={offset:0,target:window};t.prototype.getState=function(n,t,i,r){var u=this.$target.scrollTop(),f=this.$element.offset(),e=this.$target.height();if(null!=i&&"top"==this.affixed)return u<i&&"top";if("bottom"==this.affixed)return null!=i?!(u+this.unpin<=f.top)&&"bottom":!(u+e<=n-r)&&"bottom";var o=null==this.affixed,s=o?u:f.top,h=o?e:t;return null!=i&&u<=i?"top":null!=r&&s+h>=n-r&&"bottom"};t.prototype.getPinnedOffset=function(){if(this.pinnedOffset)return this.pinnedOffset;this.$element.removeClass(t.RESET).addClass("affix");var n=this.$target.scrollTop(),i=this.$element.offset();return this.pinnedOffset=i.top-n};t.prototype.checkPositionWithEventLoop=function(){setTimeout(n.proxy(this.checkPosition,this),1)};t.prototype.checkPosition=function(){var i,e,o;if(this.$element.is(":visible")){var s=this.$element.height(),r=this.options.offset,f=r.top,u=r.bottom,h=Math.max(n(document).height(),n(document.body).height());if("object"!=typeof r&&(u=f=r),"function"==typeof f&&(f=r.top(this.$element)),"function"==typeof u&&(u=r.bottom(this.$element)),i=this.getState(h,s,f,u),this.affixed!=i){if(null!=this.unpin&&this.$element.css("top",""),e="affix"+(i?"-"+i:""),o=n.Event(e+".bs.affix"),this.$element.trigger(o),o.isDefaultPrevented())return;this.affixed=i;this.unpin="bottom"==i?this.getPinnedOffset():null;this.$element.removeClass(t.RESET).addClass(e).trigger(e.replace("affix","affixed")+".bs.affix")}"bottom"==i&&this.$element.offset({top:h-s-u})}};r=n.fn.affix;n.fn.affix=i;n.fn.affix.Constructor=t;n.fn.affix.noConflict=function(){return n.fn.affix=r,this};n(window).on("load",function(){n('[data-spy="affix"]').each(function(){var r=n(this),t=r.data();t.offset=t.offset||{};null!=t.offsetBottom&&(t.offset.bottom=t.offsetBottom);null!=t.offsetTop&&(t.offset.top=t.offsetTop);i.call(r,t)})})}(jQuery);$jscomp={scope:{}};$jscomp.defineProperty="function"==typeof Object.defineProperties?Object.defineProperty:function(n,t,i){if(i.get||i.set)throw new TypeError("ES3 does not support getters and setters.");n!=Array.prototype&&n!=Object.prototype&&(n[t]=i.value)};$jscomp.getGlobal=function(n){return"undefined"!=typeof window&&window===n?n:"undefined"!=typeof global&&null!=global?global:n};$jscomp.global=$jscomp.getGlobal(this);$jscomp.SYMBOL_PREFIX="jscomp_symbol_";$jscomp.initSymbol=function(){$jscomp.initSymbol=function(){};$jscomp.global.Symbol||($jscomp.global.Symbol=$jscomp.Symbol)};$jscomp.symbolCounter_=0;$jscomp.Symbol=function(n){return $jscomp.SYMBOL_PREFIX+(n||"")+$jscomp.symbolCounter_++};$jscomp.initSymbolIterator=function(){$jscomp.initSymbol();var n=$jscomp.global.Symbol.iterator;n||(n=$jscomp.global.Symbol.iterator=$jscomp.global.Symbol("iterator"));"function"!=typeof Array.prototype[n]&&$jscomp.defineProperty(Array.prototype,n,{configurable:!0,writable:!0,value:function(){return $jscomp.arrayIterator(this)}});$jscomp.initSymbolIterator=function(){}};$jscomp.arrayIterator=function(n){var t=0;return $jscomp.iteratorPrototype(function(){return t<n.length?{done:!1,value:n[t++]}:{done:!0}})};$jscomp.iteratorPrototype=function(n){return $jscomp.initSymbolIterator(),n={next:n},n[$jscomp.global.Symbol.iterator]=function(){return this},n};$jscomp.array=$jscomp.array||{};$jscomp.iteratorFromArray=function(n,t){$jscomp.initSymbolIterator();n instanceof String&&(n+="");var r=0,i={next:function(){if(r<n.length){var u=r++;return{value:t(u,n[u]),done:!1}}return i.next=function(){return{done:!0,value:void 0}},i.next()}};return i[Symbol.iterator]=function(){return i},i};$jscomp.polyfill=function(n,t,i,r){if(t){for(i=$jscomp.global,n=n.split("."),r=0;r<n.length-1;r++){var u=n[r];u in i||(i[u]={});i=i[u]}n=n[n.length-1];r=i[n];t=t(r);t!=r&&null!=t&&$jscomp.defineProperty(i,n,{configurable:!0,writable:!0,value:t})}};$jscomp.polyfill("Array.prototype.keys",function(n){return n?n:function(){return $jscomp.iteratorFromArray(this,function(n){return n})}},"es6-impl","es3");$jscomp$this=this,function(n,t){"function"==typeof define&&define.amd?define([],t):"object"==typeof module&&module.exports?module.exports=t():n.anime=t()}(this,function(){function p(t){if(!n.col(t))try{return document.querySelectorAll(t)}catch(i){}}function u(n,t){for(var r,f=n.length,e=2<=arguments.length?arguments[1]:void 0,u=[],i=0;i<f;i++)i in n&&(r=n[i],t.call(e,r,i,n)&&u.push(r));return u}function h(t){return t.reduce(function(t,i){return t.concat(n.arr(i)?h(i):i)},[])}function f(t){return n.arr(t)?t:(n.str(t)&&(t=p(t)||t),t instanceof NodeList||t instanceof HTMLCollection?[].slice.call(t):[t])}function w(n,t){return n.some(function(n){return n===t})}function c(n){var t={};for(var i in n)t[i]=n[i];return t}function l(n,t){var r=c(n);for(var i in n)r[i]=t.hasOwnProperty(i)?t[i]:n[i];return r}function e(t,i){var u=c(t);for(var r in i)u[r]=n.und(t[r])?i[r]:t[r];return u}function ot(n){var i,t;return n=n.replace(/^#?([a-f\d])([a-f\d])([a-f\d])$/i,function(n,t,i,r){return t+t+i+i+r+r}),t=/^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(n),n=parseInt(t[1],16),i=parseInt(t[2],16),t=parseInt(t[3],16),"rgba("+n+","+i+","+t+",1)"}function st(n){function f(n,t,i){return 0>i&&(i+=1),1<i&&--i,i<1/6?n+6*(t-n)*i:.5>i?t:i<2/3?n+(t-n)*(2/3-i)*6:n}var r=/hsl\((\d+),\s*([\d.]+)%,\s*([\d.]+)%\)/g.exec(n)||/hsla\((\d+),\s*([\d.]+)%,\s*([\d.]+)%,\s*([\d.]+)\)/g.exec(n);n=parseInt(r[1])/360;var i=parseInt(r[2])/100,t=parseInt(r[3])/100,r=r[4]||1;if(0==i)t=i=n=t;else{var u=.5>t?t*(1+i):t+i-t*i,e=2*t-u,t=f(e,u,n+1/3),i=f(e,u,n);n=f(e,u,n-1/3)}return"rgba("+255*t+","+255*i+","+255*n+","+r+")"}function r(n){if(n=/([\+\-]?[0-9#\.]+)(%|px|pt|em|rem|in|cm|mm|ex|ch|pc|vw|vh|vmin|vmax|deg|rad|turn)?$/.exec(n))return n[2]}function ht(n){return-1<n.indexOf("translate")||"perspective"===n?"px":-1<n.indexOf("rotate")||-1<n.indexOf("skew")?"deg":void 0}function b(t,i){return n.fnc(t)?t(i.target,i.id,i.total):t}function a(n,t){if(t in n.style)return getComputedStyle(n).getPropertyValue(t.replace(/([a-z])([A-Z])/g,"$1-$2").toLowerCase())||"0"}function k(t,i){return n.dom(t)&&w(gt,i)?"transform":n.dom(t)&&(t.getAttribute(i)||n.svg(t)&&t[i])?"attribute":n.dom(t)&&"transform"!==i&&a(t,i)?"css":null!=t[i]?"object":void 0}function ct(n,t){var i=ht(t),i=-1<t.indexOf("scale")?1:0+i;if(n=n.style.transform,!n)return i;for(var r=[],f=[],e=[],o=/(\w+)\((.+?)\)/g;r=o.exec(n);)f.push(r[1]),e.push(r[2]);return n=u(e,function(n,i){return f[i]===t}),n.length?n[0]:i}function d(n,t){switch(k(n,t)){case"transform":return ct(n,t);case"css":return a(n,t);case"attribute":return n.getAttribute(t)}return n[t]||0}function g(n,t){var u=/^(\*=|\+=|-=)/.exec(n),i;if(!u)return n;i=r(n)||0;t=parseFloat(t);n=parseFloat(n.replace(u[0],""));switch(u[0][0]){case"+":return t+n+i;case"-":return t-n+i;case"*":return t*n+i}}function v(n,t){return Math.sqrt(Math.pow(t.x-n.x,2)+Math.pow(t.y-n.y,2))}function nt(n){var i,u,t,r;for(n=n.points,i=0,t=0;t<n.numberOfItems;t++)r=n.getItem(t),0<t&&(i+=v(u,r)),u=r;return i}function tt(n){if(n.getTotalLength)return n.getTotalLength();switch(n.tagName.toLowerCase()){case"circle":return 2*Math.PI*n.getAttribute("r");case"rect":return 2*n.getAttribute("width")+2*n.getAttribute("height");case"line":return v({x:n.getAttribute("x1"),y:n.getAttribute("y1")},{x:n.getAttribute("x2"),y:n.getAttribute("y2")});case"polyline":return nt(n);case"polygon":var t=n.points;return nt(n)+v(t.getItem(t.numberOfItems-1),t.getItem(0))}}function lt(n,t){function i(i){return i=void 0===i?0:i,n.el.getPointAtLength(1<=t+i?t+i:0)}var r=i(),u=i(-1),f=i(1);switch(n.property){case"x":return r.x;case"y":return r.y;case"angle":return 180*Math.atan2(f.y-u.y,f.x-u.x)/Math.PI}}function it(t,i){var e=/-?\d*\.?\d+/g,u,f;return u=n.pth(t)?t.totalLength:t,n.col(u)?n.rgb(u)?(f=/rgb\((\d+,\s*[\d]+,\s*[\d]+)\)/g.exec(u),u=f?"rgba("+f[1]+",1)":u):u=n.hex(u)?ot(u):n.hsl(u)?st(u):void 0:(f=(f=r(u))?u.substr(0,u.length-f.length):u,u=i&&!/\s/g.test(u)?f+i:f),u+="",{original:u,numbers:u.match(e)?u.match(e).map(Number):[0],strings:n.str(t)||i?u.split(e):[]}}function rt(t){return t=t?h(n.arr(t)?t.map(f):f(t)):[],u(t,function(n,t,i){return i.indexOf(n)===t})}function at(n){var t=rt(n);return t.map(function(n,i){return{target:n,id:i,total:t.length}})}function vt(t,i){var u=c(i),r;return n.arr(t)&&(r=t.length,2!==r||n.obj(t[0])?n.fnc(i.duration)||(u.duration=i.duration/r):t={value:t}),f(t).map(function(t,r){return r=r?0:i.delay,t=n.obj(t)&&!n.pth(t)?t:{value:t},n.und(t.delay)&&(t.delay=r),t}).map(function(n){return e(n,u)})}function yt(t,i){var u={},f,r;for(f in t)r=b(t[f],i),n.arr(r)&&(r=r.map(function(n){return b(n,i)}),1===r.length&&(r=r[0])),u[f]=r;return u.duration=parseFloat(u.duration),u.delay=parseFloat(u.delay),u}function pt(t){return n.arr(t)?o.apply(this,t):et[t]}function wt(t,i){var u;return t.tweens.map(function(f){f=yt(f,i);var e=f.value,o=d(i.target,t.name),s=u?u.to.original:o,s=n.arr(e)?e[0]:s,h=g(n.arr(e)?e[1]:e,s),o=r(h)||r(s)||r(o);return f.from=it(s,o),f.to=it(h,o),f.start=u?u.end:t.offset,f.end=f.start+f.delay+f.duration,f.easing=pt(f.easing),f.elasticity=(1e3-Math.min(Math.max(f.elasticity,1),999))/1e3,f.isPath=n.pth(e),f.isColor=n.col(f.from.original),f.isColor&&(f.round=1),u=f})}function bt(t,i){return u(h(t.map(function(n){return i.map(function(t){var r=k(n.target,t.name),i;return r?(i=wt(t,n),t={type:r,property:t.name,animatable:n,tweens:i,duration:i[i.length-1].end,delay:i[0].delay}):t=void 0,t})})),function(t){return!n.und(t)})}function ut(n,t,i,r){var u="delay"===n;return t.length?(u?Math.min:Math.max).apply(Math,t.map(function(t){return t[n]})):u?r.delay:i.offset+r.delay+r.duration}function kt(n){var t=l(dt,n),i=l(ft,n),u=at(n.targets),f=[],o=e(t,i);for(var r in n)o.hasOwnProperty(r)||"targets"===r||f.push({name:r,offset:o.offset,tweens:vt(n[r],i)});return n=bt(u,f),e(t,{children:[],animatables:u,animations:n,duration:ut("duration",n,t,i),delay:ut("delay",n,t,i)})}function t(n){function v(){return window.Promise&&new Promise(function(n){return b=n})}function e(n){return r.reversed?r.duration-n:n}function o(n){for(var c=0,w={},o=r.animations,d=o.length;c<d;){var l=o[c],b=l.animatable,f=l.tweens,v=f.length-1,t=f[v];v&&(t=u(f,function(t){return n<t.end})[0]||t);for(var f=Math.min(Math.max(n-t.start-t.delay,0),t.duration)/t.duration,s=isNaN(f)?1:t.easing(f,t.elasticity),f=t.to.strings,h=t.round,v=[],e=void 0,e=t.to.numbers.length,i=0;i<e;i++){var p=void 0,p=t.to.numbers[i],k=t.from.numbers[i],p=t.isPath?lt(t.value,s*p):k+s*(p-k);h&&(t.isColor&&2<i||(p=Math.round(p*h)/h));v.push(p)}if(t=f.length)for(e=f[0],s=0;s<t;s++)h=f[s+1],i=v[s],isNaN(i)||(e=h?e+(i+h):e+(i+" "));else e=v[0];ni[l.type](b.target,l.property,e,w,b.id);l.currentValue=e;c++}if(c=Object.keys(w).length)for(o=0;o<c;o++)y||(y=a(document.body,"transform")?"transform":"-webkit-transform"),r.animatables[o].target.style[y]=w[o].join(" ");r.currentTime=n;r.progress=n/r.duration*100}function h(n){r[n]&&r[n](r)}function p(){r.remaining&&!0!==r.remaining&&r.remaining--}function w(n){var i=r.duration,y=r.offset,g=y+r.delay,w=r.currentTime,d=r.reversed,t=e(n),u,s,a;if(r.children.length)if(u=r.children,s=u.length,t>=r.currentTime)for(a=0;a<s;a++)u[a].seek(t);else for(;s--;)u[s].seek(t);(t>=g||!i)&&(r.began||(r.began=!0,h("begin")),h("run"));t>y&&t<i?o(t):(t<=y&&0!==w&&(o(0),d&&p()),t>=i&&w!==i||!i)&&(o(i),d||p());h("update");n>=i&&(r.remaining?(f=c,"alternate"===r.direction&&(r.reversed=!r.reversed)):(r.pause(),r.completed||(r.completed=!0,h("complete"),"Promise"in window&&(b(),k=v()))),l=0)}n=void 0===n?{}:n;var c,f,l=0,b=null,k=v(),r=kt(n);return r.reset=function(){var n=r.direction,t=r.loop;for(r.currentTime=0,r.progress=0,r.paused=!0,r.began=!1,r.completed=!1,r.reversed="reverse"===n,r.remaining="alternate"===n&&1===t?2:t,o(0),n=r.children.length;n--;)r.children[n].reset()},r.tick=function(n){c=n;f||(f=c);w((l+c-f)*t.speed)},r.seek=function(n){w(e(n))},r.pause=function(){var n=i.indexOf(r);-1<n&&i.splice(n,1);r.paused=!0},r.play=function(){r.paused&&(r.paused=!1,f=0,l=e(r.currentTime),i.push(r),s||ti())},r.reverse=function(){r.reversed=!r.reversed;f=0;l=e(r.currentTime)},r.restart=function(){r.pause();r.reset();r.play()},r.finished=k,r.reset(),r.autoplay&&r.play(),r}var dt={update:void 0,begin:void 0,run:void 0,complete:void 0,loop:1,direction:"normal",autoplay:!0,offset:0},ft={duration:1e3,delay:0,easing:"easeOutElastic",elasticity:500,round:0},gt="translateX translateY translateZ rotate rotateX rotateY rotateZ scale scaleX scaleY scaleZ skewX skewY perspective".split(" "),y,n={arr:function(n){return Array.isArray(n)},obj:function(n){return-1<Object.prototype.toString.call(n).indexOf("Object")},pth:function(t){return n.obj(t)&&t.hasOwnProperty("totalLength")},svg:function(n){return n instanceof SVGElement},dom:function(t){return t.nodeType||n.svg(t)},str:function(n){return"string"==typeof n},fnc:function(n){return"function"==typeof n},und:function(n){return"undefined"==typeof n},hex:function(n){return/(^#[0-9A-F]{6}$)|(^#[0-9A-F]{3}$)/i.test(n)},rgb:function(n){return/^rgb/.test(n)},hsl:function(n){return/^hsl/.test(n)},col:function(t){return n.hex(t)||n.rgb(t)||n.hsl(t)}},o=function(){function n(n,t,i){return(((1-3*i+3*t)*n+(3*i-6*t))*n+3*t)*n}return function(t,i,r,u){var f,e;if(0<=t&&1>=t&&0<=r&&1>=r){if(f=new Float32Array(11),t!==i||r!==u)for(e=0;11>e;++e)f[e]=n(.1*e,t,r);return function(e){var s,h,c,o;if(t===i&&r===u)return e;if(0===e)return 0;if(1===e)return 1;for(s=0,o=1;10!==o&&f[o]<=e;++o)s+=.1;if(--o,o=s+(e-f[o])/(f[o+1]-f[o])*.1,h=3*(1-3*r+3*t)*o*o+2*(3*r-6*t)*o+3*t,.001<=h){for(s=0;4>s;++s){if(h=3*(1-3*r+3*t)*o*o+2*(3*r-6*t)*o+3*t,0===h)break;c=n(o,t,r)-e;o=o-c/h}e=o}else if(0===h)e=o;else{var o=s,s=s+.1,l=0;do c=o+(s-o)/2,h=n(c,t,r)-e,0<h?s=c:o=c;while(1e-7<Math.abs(h)&&10>++l);e=c}return n(e,i,u)}}}}(),et=function(){function i(n,t){return 0===n||1===n?n:-Math.pow(2,10*(n-1))*Math.sin(2*(n-1-t/(2*Math.PI)*Math.asin(1))*Math.PI/t)}var f="Quad Cubic Quart Quint Sine Expo Circ Back Elastic".split(" "),r={In:[[.55,.085,.68,.53],[.55,.055,.675,.19],[.895,.03,.685,.22],[.755,.05,.855,.06],[.47,0,.745,.715],[.95,.05,.795,.035],[.6,.04,.98,.335],[.6,-.28,.735,.045],i],Out:[[.25,.46,.45,.94],[.215,.61,.355,1],[.165,.84,.44,1],[.23,1,.32,1],[.39,.575,.565,1],[.19,1,.22,1],[.075,.82,.165,1],[.175,.885,.32,1.275],function(n,t){return 1-i(1-n,t)}],InOut:[[.455,.03,.515,.955],[.645,.045,.355,1],[.77,0,.175,1],[.86,0,.07,1],[.445,.05,.55,.95],[1,0,0,1],[.785,.135,.15,.86],[.68,-.55,.265,1.55],function(n,t){return.5>n?i(2*n,t)/2:1-i(-2*n+2,t)/2}]},u={linear:o(.25,.25,.75,.75)},t={};for(var e in r)t.type=e,r[t.type].forEach(function(t){return function(i,r){u["ease"+t.type+f[r]]=n.fnc(i)?i:o.apply($jscomp$this,i)}}(t)),t={type:t.type};return u}(),ni={css:function(n,t,i){return n.style[t]=i},attribute:function(n,t,i){return n.setAttribute(t,i)},object:function(n,t,i){return n[t]=i},transform:function(n,t,i,r,u){r[u]||(r[u]=[]);r[u].push(t+"("+i+")")}},i=[],s=0,ti=function(){function n(){s=requestAnimationFrame(t)}function t(t){var u=i.length,r;if(u){for(r=0;r<u;)i[r]&&i[r].tick(t),r++;n()}else cancelAnimationFrame(s),s=0}return n}();return t.version="2.2.0",t.speed=1,t.running=i,t.remove=function(n){var r;for(n=rt(n),r=i.length;r--;)for(var f=i[r],t=f.animations,u=t.length;u--;)w(n,t[u].animatable.target)&&(t.splice(u,1),t.length||f.pause())},t.getValue=d,t.path=function(t,i){var r=n.str(t)?p(t)[0]:t,u=i||100;return function(n){return{el:r,property:n,totalLength:tt(r)*(u/100)}}},t.setDashoffset=function(n){var t=tt(n);return n.setAttribute("stroke-dasharray",t),t},t.bezier=o,t.easings=et,t.timeline=function(i){var r=t(i);return r.pause(),r.duration=0,r.add=function(u){return r.children.forEach(function(n){n.began=!0;n.completed=!0}),f(u).forEach(function(u){var f=e(u,l(ft,i||{})),o;f.targets=f.targets||i.targets;u=r.duration;o=f.offset;f.autoplay=!1;f.direction=r.direction;f.offset=n.und(o)?u:g(o,u);r.began=!0;r.completed=!0;r.seek(f.offset);f=t(f);f.began=!0;f.completed=!0;f.duration>u&&(r.duration=f.duration);r.children.push(f)}),r.seek(0),r.reset(),r.autoplay&&r.restart(),r},r},t.random=function(n,t){return Math.floor(Math.random()*(t-n+1))+n},t});!function(n,t,i,r){function u(t,i){this.settings=null;this.options=n.extend({},u.Defaults,i);this.$element=n(t);this._handlers={};this._plugins={};this._supress={};this._current=null;this._speed=null;this._coordinates=[];this._breakpoint=null;this._width=null;this._items=[];this._clones=[];this._mergers=[];this._widths=[];this._invalidated={};this._pipe=[];this._drag={time:null,target:null,pointer:null,stage:{start:null,current:null},direction:null};this._states={current:{},tags:{initializing:["busy"],animating:["busy"],dragging:["interacting"]}};n.each(["onResize","onThrottledResize"],n.proxy(function(t,i){this._handlers[i]=n.proxy(this[i],this)},this));n.each(u.Plugins,n.proxy(function(n,t){this._plugins[n.charAt(0).toLowerCase()+n.slice(1)]=new t(this)},this));n.each(u.Workers,n.proxy(function(t,i){this._pipe.push({filter:i.filter,run:n.proxy(i.run,this)})},this));this.setup();this.initialize()}u.Defaults={items:3,loop:!1,center:!1,rewind:!1,checkVisibility:!0,mouseDrag:!0,touchDrag:!0,pullDrag:!0,freeDrag:!1,margin:0,stagePadding:0,merge:!1,mergeFit:!0,autoWidth:!1,startPosition:0,rtl:!1,smartSpeed:250,fluidSpeed:!1,dragEndSpeed:!1,responsive:{},responsiveRefreshRate:200,responsiveBaseElement:t,fallbackEasing:"swing",slideTransition:"",info:!1,nestedItemSelector:!1,itemElement:"div",stageElement:"div",refreshClass:"owl-refresh",loadedClass:"owl-loaded",loadingClass:"owl-loading",rtlClass:"owl-rtl",responsiveClass:"owl-responsive",dragClass:"owl-drag",itemClass:"owl-item",stageClass:"owl-stage",stageOuterClass:"owl-stage-outer",grabClass:"owl-grab"};u.Width={Default:"default",Inner:"inner",Outer:"outer"};u.Type={Event:"event",State:"state"};u.Plugins={};u.Workers=[{filter:["width","settings"],run:function(){this._width=this.$element.width()}},{filter:["width","items","settings"],run:function(n){n.current=this._items&&this._items[this.relative(this._current)]}},{filter:["items","settings"],run:function(){this.$stage.children(".cloned").remove()}},{filter:["width","items","settings"],run:function(n){var t=this.settings.margin||"",u=!this.settings.autoWidth,i=this.settings.rtl,r={width:"auto","margin-left":i?t:"","margin-right":i?"":t};u||this.$stage.children().css(r);n.css=r}},{filter:["width","items","settings"],run:function(n){var r=(this.width()/this.settings.items).toFixed(3)-this.settings.margin,t=null,i=this._items.length,f=!this.settings.autoWidth,u=[];for(n.items={merge:!1,width:r};i--;)t=this._mergers[i],t=this.settings.mergeFit&&Math.min(t,this.settings.items)||t,n.items.merge=t>1||n.items.merge,u[i]=f?r*t:this._items[i].width();this._widths=u}},{filter:["items","settings"],run:function(){var t=[],i=this._items,r=this.settings,e=Math.max(2*r.items,4),s=2*Math.ceil(i.length/2),u=r.loop&&i.length?r.rewind?e:Math.max(e,s):0,o="",f="";for(u/=2;u>0;)t.push(this.normalize(t.length/2,!0)),o+=i[t[t.length-1]][0].outerHTML,t.push(this.normalize(i.length-1-(t.length-1)/2,!0)),f=i[t[t.length-1]][0].outerHTML+f,u-=1;this._clones=t;n(o).addClass("cloned").appendTo(this.$stage);n(f).addClass("cloned").prependTo(this.$stage)}},{filter:["width","items","settings"],run:function(){for(var u=this.settings.rtl?1:-1,f=this._clones.length+this._items.length,n=-1,i=0,r=0,t=[];++n<f;)i=t[n-1]||0,r=this._widths[this.relative(n)]+this.settings.margin,t.push(i+r*u);this._coordinates=t}},{filter:["width","items","settings"],run:function(){var n=this.settings.stagePadding,t=this._coordinates,i={width:Math.ceil(Math.abs(t[t.length-1]))+2*n,"padding-left":n||"","padding-right":n||""};this.$stage.css(i)}},{filter:["width","items","settings"],run:function(n){var t=this._coordinates.length,i=!this.settings.autoWidth,r=this.$stage.children();if(i&&n.items.merge)for(;t--;)n.css.width=this._widths[this.relative(t)],r.eq(t).css(n.css);else i&&(n.css.width=n.items.width,r.css(n.css))}},{filter:["items"],run:function(){this._coordinates.length<1&&this.$stage.removeAttr("style")}},{filter:["width","items","settings"],run:function(n){n.current=n.current?this.$stage.children().index(n.current):0;n.current=Math.max(this.minimum(),Math.min(this.maximum(),n.current));this.reset(n.current)}},{filter:["position"],run:function(){this.animate(this.coordinates(this._current))}},{filter:["width","position","items","settings"],run:function(){for(var t,i,f=this.settings.rtl?1:-1,e=2*this.settings.stagePadding,r=this.coordinates(this.current())+e,o=r+this.width()*f,s=[],n=0,u=this._coordinates.length;n<u;n++)t=this._coordinates[n-1]||0,i=Math.abs(this._coordinates[n])+e*f,(this.op(t,"<=",r)&&this.op(t,">",o)||this.op(i,"<",r)&&this.op(i,">",o))&&s.push(n);this.$stage.children(".active").removeClass("active");this.$stage.children(":eq("+s.join("), :eq(")+")").addClass("active");this.$stage.children(".center").removeClass("center");this.settings.center&&this.$stage.children().eq(this.current()).addClass("center")}}];u.prototype.initializeStage=function(){this.$stage=this.$element.find("."+this.settings.stageClass);this.$stage.length||(this.$element.addClass(this.options.loadingClass),this.$stage=n("<"+this.settings.stageElement+">",{"class":this.settings.stageClass}).wrap(n("<div/>",{"class":this.settings.stageOuterClass})),this.$element.append(this.$stage.parent()))};u.prototype.initializeItems=function(){var t=this.$element.find(".owl-item");if(t.length)return this._items=t.get().map(function(t){return n(t)}),this._mergers=this._items.map(function(){return 1}),void this.refresh();this.replace(this.$element.children().not(this.$stage.parent()));this.isVisible()?this.refresh():this.invalidate("width");this.$element.removeClass(this.options.loadingClass).addClass(this.options.loadedClass)};u.prototype.initialize=function(){if(this.enter("initializing"),this.trigger("initialize"),this.$element.toggleClass(this.settings.rtlClass,this.settings.rtl),this.settings.autoWidth&&!this.is("pre-loading")){var n,t,i;n=this.$element.find("img");t=this.settings.nestedItemSelector?"."+this.settings.nestedItemSelector:r;i=this.$element.children(t).width();n.length&&i<=0&&this.preloadAutoWidthImages(n)}this.initializeStage();this.initializeItems();this.registerEventHandlers();this.leave("initializing");this.trigger("initialized")};u.prototype.isVisible=function(){return!this.settings.checkVisibility||this.$element.is(":visible")};u.prototype.setup=function(){var u=this.viewport(),r=this.options.responsive,i=-1,t=null;r?(n.each(r,function(n){n<=u&&n>i&&(i=Number(n))}),t=n.extend({},this.options,r[i]),"function"==typeof t.stagePadding&&(t.stagePadding=t.stagePadding()),delete t.responsive,t.responsiveClass&&this.$element.attr("class",this.$element.attr("class").replace(new RegExp("("+this.options.responsiveClass+"-)\\S+\\s","g"),"$1"+i))):t=n.extend({},this.options);this.trigger("change",{property:{name:"settings",value:t}});this._breakpoint=i;this.settings=t;this.invalidate("settings");this.trigger("changed",{property:{name:"settings",value:this.settings}})};u.prototype.optionsLogic=function(){this.settings.autoWidth&&(this.settings.stagePadding=!1,this.settings.merge=!1)};u.prototype.prepare=function(t){var i=this.trigger("prepare",{content:t});return i.data||(i.data=n("<"+this.settings.itemElement+"/>").addClass(this.options.itemClass).append(t)),this.trigger("prepared",{content:i.data}),i.data};u.prototype.update=function(){for(var t=0,i=this._pipe.length,r=n.proxy(function(n){return this[n]},this._invalidated),u={};t<i;)(this._invalidated.all||n.grep(this._pipe[t].filter,r).length>0)&&this._pipe[t].run(u),t++;this._invalidated={};this.is("valid")||this.enter("valid")};u.prototype.width=function(n){switch(n=n||u.Width.Default){case u.Width.Inner:case u.Width.Outer:return this._width;default:return this._width-2*this.settings.stagePadding+this.settings.margin}};u.prototype.refresh=function(){this.enter("refreshing");this.trigger("refresh");this.setup();this.optionsLogic();this.$element.addClass(this.options.refreshClass);this.update();this.$element.removeClass(this.options.refreshClass);this.leave("refreshing");this.trigger("refreshed")};u.prototype.onThrottledResize=function(){t.clearTimeout(this.resizeTimer);this.resizeTimer=t.setTimeout(this._handlers.onResize,this.settings.responsiveRefreshRate)};u.prototype.onResize=function(){return!!this._items.length&&this._width!==this.$element.width()&&!!this.isVisible()&&(this.enter("resizing"),this.trigger("resize").isDefaultPrevented()?(this.leave("resizing"),!1):(this.invalidate("width"),this.refresh(),this.leave("resizing"),void this.trigger("resized")))};u.prototype.registerEventHandlers=function(){n.support.transition&&this.$stage.on(n.support.transition.end+".owl.core",n.proxy(this.onTransitionEnd,this));!1!==this.settings.responsive&&this.on(t,"resize",this._handlers.onThrottledResize);this.settings.mouseDrag&&(this.$element.addClass(this.options.dragClass),this.$stage.on("mousedown.owl.core",n.proxy(this.onDragStart,this)),this.$stage.on("dragstart.owl.core selectstart.owl.core",function(){return!1}));this.settings.touchDrag&&(this.$stage.on("touchstart.owl.core",n.proxy(this.onDragStart,this)),this.$stage.on("touchcancel.owl.core",n.proxy(this.onDragEnd,this)))};u.prototype.onDragStart=function(t){var r=null;3!==t.which&&(n.support.transform?(r=this.$stage.css("transform").replace(/.*\(|\)| /g,"").split(","),r={x:r[16===r.length?12:4],y:r[16===r.length?13:5]}):(r=this.$stage.position(),r={x:this.settings.rtl?r.left+this.$stage.width()-this.width()+this.settings.margin:r.left,y:r.top}),this.is("animating")&&(n.support.transform?this.animate(r.x):this.$stage.stop(),this.invalidate("position")),this.$element.toggleClass(this.options.grabClass,"mousedown"===t.type),this.speed(0),this._drag.time=(new Date).getTime(),this._drag.target=n(t.target),this._drag.stage.start=r,this._drag.stage.current=r,this._drag.pointer=this.pointer(t),n(i).on("mouseup.owl.core touchend.owl.core",n.proxy(this.onDragEnd,this)),n(i).one("mousemove.owl.core touchmove.owl.core",n.proxy(function(t){var r=this.difference(this._drag.pointer,this.pointer(t));n(i).on("mousemove.owl.core touchmove.owl.core",n.proxy(this.onDragMove,this));Math.abs(r.x)<Math.abs(r.y)&&this.is("valid")||(t.preventDefault(),this.enter("dragging"),this.trigger("drag"))},this)))};u.prototype.onDragMove=function(n){var t=null,i=null,u=null,f=this.difference(this._drag.pointer,this.pointer(n)),r=this.difference(this._drag.stage.start,f);this.is("dragging")&&(n.preventDefault(),this.settings.loop?(t=this.coordinates(this.minimum()),i=this.coordinates(this.maximum()+1)-t,r.x=((r.x-t)%i+i)%i+t):(t=this.settings.rtl?this.coordinates(this.maximum()):this.coordinates(this.minimum()),i=this.settings.rtl?this.coordinates(this.minimum()):this.coordinates(this.maximum()),u=this.settings.pullDrag?f.x/-5:0,r.x=Math.max(Math.min(r.x,t+u),i+u)),this._drag.stage.current=r,this.animate(r.x))};u.prototype.onDragEnd=function(t){var r=this.difference(this._drag.pointer,this.pointer(t)),f=this._drag.stage.current,u=r.x>0^this.settings.rtl?"left":"right";n(i).off(".owl.core");this.$element.removeClass(this.options.grabClass);(0!==r.x&&this.is("dragging")||!this.is("valid"))&&(this.speed(this.settings.dragEndSpeed||this.settings.smartSpeed),this.current(this.closest(f.x,0!==r.x?u:this._drag.direction)),this.invalidate("position"),this.update(),this._drag.direction=u,(Math.abs(r.x)>3||(new Date).getTime()-this._drag.time>300)&&this._drag.target.one("click.owl.core",function(){return!1}));this.is("dragging")&&(this.leave("dragging"),this.trigger("dragged"))};u.prototype.closest=function(t,i){var u=-1,e=30,o=this.width(),f=this.coordinates();return this.settings.freeDrag||n.each(f,n.proxy(function(n,s){return"left"===i&&t>s-e&&t<s+e?u=n:"right"===i&&t>s-o-e&&t<s-o+e?u=n+1:this.op(t,"<",s)&&this.op(t,">",f[n+1]!==r?f[n+1]:s-o)&&(u="left"===i?n+1:n),-1===u},this)),this.settings.loop||(this.op(t,">",f[this.minimum()])?u=t=this.minimum():this.op(t,"<",f[this.maximum()])&&(u=t=this.maximum())),u};u.prototype.animate=function(t){var i=this.speed()>0;this.is("animating")&&this.onTransitionEnd();i&&(this.enter("animating"),this.trigger("translate"));n.support.transform3d&&n.support.transition?this.$stage.css({transform:"translate3d("+t+"px,0px,0px)",transition:this.speed()/1e3+"s"+(this.settings.slideTransition?" "+this.settings.slideTransition:"")}):i?this.$stage.animate({left:t+"px"},this.speed(),this.settings.fallbackEasing,n.proxy(this.onTransitionEnd,this)):this.$stage.css({left:t+"px"})};u.prototype.is=function(n){return this._states.current[n]&&this._states.current[n]>0};u.prototype.current=function(n){if(n===r)return this._current;if(0===this._items.length)return r;if(n=this.normalize(n),this._current!==n){var t=this.trigger("change",{property:{name:"position",value:n}});t.data!==r&&(n=this.normalize(t.data));this._current=n;this.invalidate("position");this.trigger("changed",{property:{name:"position",value:this._current}})}return this._current};u.prototype.invalidate=function(t){return"string"===n.type(t)&&(this._invalidated[t]=!0,this.is("valid")&&this.leave("valid")),n.map(this._invalidated,function(n,t){return t})};u.prototype.reset=function(n){(n=this.normalize(n))!==r&&(this._speed=0,this._current=n,this.suppress(["translate","translated"]),this.animate(this.coordinates(n)),this.release(["translate","translated"]))};u.prototype.normalize=function(n,t){var i=this._items.length,u=t?0:this._clones.length;return!this.isNumeric(n)||i<1?n=r:(n<0||n>=i+u)&&(n=((n-u/2)%i+i)%i+u/2),n};u.prototype.relative=function(n){return n-=this._clones.length/2,this.normalize(n,!0)};u.prototype.maximum=function(n){var t,u,f,i=this.settings,r=this._coordinates.length;if(i.loop)r=this._clones.length/2+this._items.length-1;else if(i.autoWidth||i.merge){if(t=this._items.length)for(u=this._items[--t].width(),f=this.$element.width();t--&&!((u+=this._items[t].width()+this.settings.margin)>f););r=t+1}else r=i.center?this._items.length-1:this._items.length-i.items;return n&&(r-=this._clones.length/2),Math.max(r,0)};u.prototype.minimum=function(n){return n?0:this._clones.length/2};u.prototype.items=function(n){return n===r?this._items.slice():(n=this.normalize(n,!0),this._items[n])};u.prototype.mergers=function(n){return n===r?this._mergers.slice():(n=this.normalize(n,!0),this._mergers[n])};u.prototype.clones=function(t){var i=this._clones.length/2,f=i+this._items.length,u=function(n){return n%2==0?f+n/2:i-(n+1)/2};return t===r?n.map(this._clones,function(n,t){return u(t)}):n.map(this._clones,function(n,i){return n===t?u(i):null})};u.prototype.speed=function(n){return n!==r&&(this._speed=n),this._speed};u.prototype.coordinates=function(t){var i,f=1,u=t-1;return t===r?n.map(this._coordinates,n.proxy(function(n,t){return this.coordinates(t)},this)):(this.settings.center?(this.settings.rtl&&(f=-1,u=t+1),i=this._coordinates[t],i+=(this.width()-i+(this._coordinates[u]||0))/2*f):i=this._coordinates[u]||0,i=Math.ceil(i))};u.prototype.duration=function(n,t,i){return 0===i?0:Math.min(Math.max(Math.abs(t-n),1),6)*Math.abs(i||this.settings.smartSpeed)};u.prototype.to=function(n,t){var u=this.current(),f=null,i=n-this.relative(u),s=(i>0)-(i<0),e=this._items.length,o=this.minimum(),r=this.maximum();this.settings.loop?(!this.settings.rewind&&Math.abs(i)>e/2&&(i+=-1*s*e),n=u+i,(f=((n-o)%e+e)%e+o)!==n&&f-i<=r&&f-i>0&&(u=f-i,n=f,this.reset(u))):this.settings.rewind?(r+=1,n=(n%r+r)%r):n=Math.max(o,Math.min(r,n));this.speed(this.duration(u,n,t));this.current(n);this.isVisible()&&this.update()};u.prototype.next=function(n){n=n||!1;this.to(this.relative(this.current())+1,n)};u.prototype.prev=function(n){n=n||!1;this.to(this.relative(this.current())-1,n)};u.prototype.onTransitionEnd=function(n){if(n!==r&&(n.stopPropagation(),(n.target||n.srcElement||n.originalTarget)!==this.$stage.get(0)))return!1;this.leave("animating");this.trigger("translated")};u.prototype.viewport=function(){var r;return this.options.responsiveBaseElement!==t?r=n(this.options.responsiveBaseElement).width():t.innerWidth?r=t.innerWidth:i.documentElement&&i.documentElement.clientWidth?r=i.documentElement.clientWidth:console.warn("Can not detect viewport width."),r};u.prototype.replace=function(t){this.$stage.empty();this._items=[];t&&(t=t instanceof jQuery?t:n(t));this.settings.nestedItemSelector&&(t=t.find("."+this.settings.nestedItemSelector));t.filter(function(){return 1===this.nodeType}).each(n.proxy(function(n,t){t=this.prepare(t);this.$stage.append(t);this._items.push(t);this._mergers.push(1*t.find("[data-merge]").addBack("[data-merge]").attr("data-merge")||1)},this));this.reset(this.isNumeric(this.settings.startPosition)?this.settings.startPosition:0);this.invalidate("items")};u.prototype.add=function(t,i){var u=this.relative(this._current);i=i===r?this._items.length:this.normalize(i,!0);t=t instanceof jQuery?t:n(t);this.trigger("add",{content:t,position:i});t=this.prepare(t);0===this._items.length||i===this._items.length?(0===this._items.length&&this.$stage.append(t),0!==this._items.length&&this._items[i-1].after(t),this._items.push(t),this._mergers.push(1*t.find("[data-merge]").addBack("[data-merge]").attr("data-merge")||1)):(this._items[i].before(t),this._items.splice(i,0,t),this._mergers.splice(i,0,1*t.find("[data-merge]").addBack("[data-merge]").attr("data-merge")||1));this._items[u]&&this.reset(this._items[u].index());this.invalidate("items");this.trigger("added",{content:t,position:i})};u.prototype.remove=function(n){(n=this.normalize(n,!0))!==r&&(this.trigger("remove",{content:this._items[n],position:n}),this._items[n].remove(),this._items.splice(n,1),this._mergers.splice(n,1),this.invalidate("items"),this.trigger("removed",{content:null,position:n}))};u.prototype.preloadAutoWidthImages=function(t){t.each(n.proxy(function(t,i){this.enter("pre-loading");i=n(i);n(new Image).one("load",n.proxy(function(n){i.attr("src",n.target.src);i.css("opacity",1);this.leave("pre-loading");!this.is("pre-loading")&&!this.is("initializing")&&this.refresh()},this)).attr("src",i.attr("src")||i.attr("data-src")||i.attr("data-src-retina"))},this))};u.prototype.destroy=function(){this.$element.off(".owl.core");this.$stage.off(".owl.core");n(i).off(".owl.core");!1!==this.settings.responsive&&(t.clearTimeout(this.resizeTimer),this.off(t,"resize",this._handlers.onThrottledResize));for(var r in this._plugins)this._plugins[r].destroy();this.$stage.children(".cloned").remove();this.$stage.unwrap();this.$stage.children().contents().unwrap();this.$stage.children().unwrap();this.$stage.remove();this.$element.removeClass(this.options.refreshClass).removeClass(this.options.loadingClass).removeClass(this.options.loadedClass).removeClass(this.options.rtlClass).removeClass(this.options.dragClass).removeClass(this.options.grabClass).attr("class",this.$element.attr("class").replace(new RegExp(this.options.responsiveClass+"-\\S+\\s","g"),"")).removeData("owl.carousel")};u.prototype.op=function(n,t,i){var r=this.settings.rtl;switch(t){case"<":return r?n>i:n<i;case">":return r?n<i:n>i;case">=":return r?n<=i:n>=i;case"<=":return r?n>=i:n<=i}};u.prototype.on=function(n,t,i,r){n.addEventListener?n.addEventListener(t,i,r):n.attachEvent&&n.attachEvent("on"+t,i)};u.prototype.off=function(n,t,i,r){n.removeEventListener?n.removeEventListener(t,i,r):n.detachEvent&&n.detachEvent("on"+t,i)};u.prototype.trigger=function(t,i,r){var o={item:{count:this._items.length,index:this.current()}},e=n.camelCase(n.grep(["on",t,r],function(n){return n}).join("-").toLowerCase()),f=n.Event([t,"owl",r||"carousel"].join(".").toLowerCase(),n.extend({relatedTarget:this},o,i));return this._supress[t]||(n.each(this._plugins,function(n,t){t.onTrigger&&t.onTrigger(f)}),this.register({type:u.Type.Event,name:t}),this.$element.trigger(f),this.settings&&"function"==typeof this.settings[e]&&this.settings[e].call(this,f)),f};u.prototype.enter=function(t){n.each([t].concat(this._states.tags[t]||[]),n.proxy(function(n,t){this._states.current[t]===r&&(this._states.current[t]=0);this._states.current[t]++},this))};u.prototype.leave=function(t){n.each([t].concat(this._states.tags[t]||[]),n.proxy(function(n,t){this._states.current[t]--},this))};u.prototype.register=function(t){if(t.type===u.Type.Event){if(n.event.special[t.name]||(n.event.special[t.name]={}),!n.event.special[t.name].owl){var i=n.event.special[t.name]._default;n.event.special[t.name]._default=function(n){return!i||!i.apply||n.namespace&&-1!==n.namespace.indexOf("owl")?n.namespace&&n.namespace.indexOf("owl")>-1:i.apply(this,arguments)};n.event.special[t.name].owl=!0}}else t.type===u.Type.State&&(this._states.tags[t.name]=this._states.tags[t.name]?this._states.tags[t.name].concat(t.tags):t.tags,this._states.tags[t.name]=n.grep(this._states.tags[t.name],n.proxy(function(i,r){return n.inArray(i,this._states.tags[t.name])===r},this)))};u.prototype.suppress=function(t){n.each(t,n.proxy(function(n,t){this._supress[t]=!0},this))};u.prototype.release=function(t){n.each(t,n.proxy(function(n,t){delete this._supress[t]},this))};u.prototype.pointer=function(n){var i={x:null,y:null};return n=n.originalEvent||n||t.event,n=n.touches&&n.touches.length?n.touches[0]:n.changedTouches&&n.changedTouches.length?n.changedTouches[0]:n,n.pageX?(i.x=n.pageX,i.y=n.pageY):(i.x=n.clientX,i.y=n.clientY),i};u.prototype.isNumeric=function(n){return!isNaN(parseFloat(n))};u.prototype.difference=function(n,t){return{x:n.x-t.x,y:n.y-t.y}};n.fn.owlCarousel=function(t){var i=Array.prototype.slice.call(arguments,1);return this.each(function(){var f=n(this),r=f.data("owl.carousel");r||(r=new u(this,"object"==typeof t&&t),f.data("owl.carousel",r),n.each(["next","prev","to","destroy","refresh","replace","add","remove"],function(t,i){r.register({type:u.Type.Event,name:i});r.$element.on(i+".owl.carousel.core",n.proxy(function(n){n.namespace&&n.relatedTarget!==this&&(this.suppress([i]),r[i].apply(this,[].slice.call(arguments,1)),this.release([i]))},r))}));"string"==typeof t&&"_"!==t.charAt(0)&&r[t].apply(r,i)})};n.fn.owlCarousel.Constructor=u}(window.Zepto||window.jQuery,window,document),function(n,t){var i=function(t){this._core=t;this._interval=null;this._visible=null;this._handlers={"initialized.owl.carousel":n.proxy(function(n){n.namespace&&this._core.settings.autoRefresh&&this.watch()},this)};this._core.options=n.extend({},i.Defaults,this._core.options);this._core.$element.on(this._handlers)};i.Defaults={autoRefresh:!0,autoRefreshInterval:500};i.prototype.watch=function(){this._interval||(this._visible=this._core.isVisible(),this._interval=t.setInterval(n.proxy(this.refresh,this),this._core.settings.autoRefreshInterval))};i.prototype.refresh=function(){this._core.isVisible()!==this._visible&&(this._visible=!this._visible,this._core.$element.toggleClass("owl-hidden",!this._visible),this._visible&&this._core.invalidate("width")&&this._core.refresh())};i.prototype.destroy=function(){var n,i;t.clearInterval(this._interval);for(n in this._handlers)this._core.$element.off(n,this._handlers[n]);for(i in Object.getOwnPropertyNames(this))"function"!=typeof this[i]&&(this[i]=null)};n.fn.owlCarousel.Constructor.Plugins.AutoRefresh=i}(window.Zepto||window.jQuery,window,document),function(n,t,i,r){var u=function(t){this._core=t;this._loaded=[];this._handlers={"initialized.owl.carousel change.owl.carousel resized.owl.carousel":n.proxy(function(t){if(t.namespace&&this._core.settings&&this._core.settings.lazyLoad&&(t.property&&"position"==t.property.name||"initialized"==t.type)){var i=this._core.settings,u=i.center&&Math.ceil(i.items/2)||i.items,e=i.center&&-1*u||0,f=(t.property&&t.property.value!==r?t.property.value:this._core.current())+e,o=this._core.clones().length,s=n.proxy(function(n,t){this.load(t)},this);for(i.lazyLoadEager>0&&(u+=i.lazyLoadEager,i.loop&&(f-=i.lazyLoadEager,u++));e++<u;)this.load(o/2+this._core.relative(f)),o&&n.each(this._core.clones(this._core.relative(f)),s),f++}},this)};this._core.options=n.extend({},u.Defaults,this._core.options);this._core.$element.on(this._handlers)};u.Defaults={lazyLoad:!1,lazyLoadEager:0};u.prototype.load=function(i){var r=this._core.$stage.children().eq(i),u=r&&r.find(".owl-lazy");!u||n.inArray(r.get(0),this._loaded)>-1||(u.each(n.proxy(function(i,r){var e,u=n(r),f=t.devicePixelRatio>1&&u.attr("data-src-retina")||u.attr("data-src")||u.attr("data-srcset");this._core.trigger("load",{element:u,url:f},"lazy");u.is("img")?u.one("load.owl.lazy",n.proxy(function(){u.css("opacity",1);this._core.trigger("loaded",{element:u,url:f},"lazy")},this)).attr("src",f):u.is("source")?u.one("load.owl.lazy",n.proxy(function(){this._core.trigger("loaded",{element:u,url:f},"lazy")},this)).attr("srcset",f):(e=new Image,e.onload=n.proxy(function(){u.css({"background-image":'url("'+f+'")',opacity:"1"});this._core.trigger("loaded",{element:u,url:f},"lazy")},this),e.src=f)},this)),this._loaded.push(r.get(0)))};u.prototype.destroy=function(){var n,t;for(n in this.handlers)this._core.$element.off(n,this.handlers[n]);for(t in Object.getOwnPropertyNames(this))"function"!=typeof this[t]&&(this[t]=null)};n.fn.owlCarousel.Constructor.Plugins.Lazy=u}(window.Zepto||window.jQuery,window,document),function(n,t){var i=function(r){this._core=r;this._previousHeight=null;this._handlers={"initialized.owl.carousel refreshed.owl.carousel":n.proxy(function(n){n.namespace&&this._core.settings.autoHeight&&this.update()},this),"changed.owl.carousel":n.proxy(function(n){n.namespace&&this._core.settings.autoHeight&&"position"===n.property.name&&this.update()},this),"loaded.owl.lazy":n.proxy(function(n){n.namespace&&this._core.settings.autoHeight&&n.element.closest("."+this._core.settings.itemClass).index()===this._core.current()&&this.update()},this)};this._core.options=n.extend({},i.Defaults,this._core.options);this._core.$element.on(this._handlers);this._intervalId=null;var u=this;n(t).on("load",function(){u._core.settings.autoHeight&&u.update()});n(t).resize(function(){u._core.settings.autoHeight&&(null!=u._intervalId&&clearTimeout(u._intervalId),u._intervalId=setTimeout(function(){u.update()},250))})};i.Defaults={autoHeight:!1,autoHeightClass:"owl-height"};i.prototype.update=function(){var i=this._core._current,u=i+this._core.settings.items,f=this._core.settings.lazyLoad,e=this._core.$stage.children().toArray().slice(i,u),r=[],t=0;n.each(e,function(t,i){r.push(n(i).height())});t=Math.max.apply(null,r);t<=1&&f&&this._previousHeight&&(t=this._previousHeight);this._previousHeight=t;this._core.$stage.parent().height(t).addClass(this._core.settings.autoHeightClass)};i.prototype.destroy=function(){var n,t;for(n in this._handlers)this._core.$element.off(n,this._handlers[n]);for(t in Object.getOwnPropertyNames(this))"function"!=typeof this[t]&&(this[t]=null)};n.fn.owlCarousel.Constructor.Plugins.AutoHeight=i}(window.Zepto||window.jQuery,window,document),function(n,t,i){var r=function(t){this._core=t;this._videos={};this._playing=null;this._handlers={"initialized.owl.carousel":n.proxy(function(n){n.namespace&&this._core.register({type:"state",name:"playing",tags:["interacting"]})},this),"resize.owl.carousel":n.proxy(function(n){n.namespace&&this._core.settings.video&&this.isInFullScreen()&&n.preventDefault()},this),"refreshed.owl.carousel":n.proxy(function(n){n.namespace&&this._core.is("resizing")&&this._core.$stage.find(".cloned .owl-video-frame").remove()},this),"changed.owl.carousel":n.proxy(function(n){n.namespace&&"position"===n.property.name&&this._playing&&this.stop()},this),"prepared.owl.carousel":n.proxy(function(t){if(t.namespace){var i=n(t.content).find(".owl-video");i.length&&(i.css("display","none"),this.fetch(i,n(t.content)))}},this)};this._core.options=n.extend({},r.Defaults,this._core.options);this._core.$element.on(this._handlers);this._core.$element.on("click.owl.video",".owl-video-play-icon",n.proxy(function(n){this.play(n)},this))};r.Defaults={video:!1,videoHeight:!1,videoWidth:!1};r.prototype.fetch=function(n,t){var u=function(){return n.attr("data-vimeo-id")?"vimeo":n.attr("data-vzaar-id")?"vzaar":"youtube"}(),i=n.attr("data-vimeo-id")||n.attr("data-youtube-id")||n.attr("data-vzaar-id"),f=n.attr("data-width")||this._core.settings.videoWidth,e=n.attr("data-height")||this._core.settings.videoHeight,r=n.attr("href");if(!r)throw new Error("Missing video URL.");if(i=r.match(/(http:|https:|)\/\/(player.|www.|app.)?(vimeo\.com|youtu(be\.com|\.be|be\.googleapis\.com|be\-nocookie\.com)|vzaar\.com)\/(video\/|videos\/|embed\/|channels\/.+\/|groups\/.+\/|watch\?v=|v\/)?([A-Za-z0-9._%-]*)(\&\S+)?/),i[3].indexOf("youtu")>-1)u="youtube";else if(i[3].indexOf("vimeo")>-1)u="vimeo";else{if(!(i[3].indexOf("vzaar")>-1))throw new Error("Video URL not supported.");u="vzaar"}i=i[6];this._videos[r]={type:u,id:i,width:f,height:e};t.attr("data-video",r);this.thumbnail(n,this._videos[r])};r.prototype.thumbnail=function(t,i){var e,o,r,c=i.width&&i.height?"width:"+i.width+"px;height:"+i.height+"px;":"",f=t.find("img"),s="src",h="",l=this._core.settings,u=function(i){o='<div class="owl-video-play-icon"><\/div>';e=l.lazyLoad?n("<div/>",{"class":"owl-video-tn "+h,srcType:i}):n("<div/>",{"class":"owl-video-tn",style:"opacity:1;background-image:url("+i+")"});t.after(e);t.after(o)};if(t.wrap(n("<div/>",{"class":"owl-video-wrapper",style:c})),this._core.settings.lazyLoad&&(s="data-src",h="owl-lazy"),f.length)return u(f.attr(s)),f.remove(),!1;"youtube"===i.type?(r="//img.youtube.com/vi/"+i.id+"/hqdefault.jpg",u(r)):"vimeo"===i.type?n.ajax({type:"GET",url:"//vimeo.com/api/v2/video/"+i.id+".json",jsonp:"callback",dataType:"jsonp",success:function(n){r=n[0].thumbnail_large;u(r)}}):"vzaar"===i.type&&n.ajax({type:"GET",url:"//vzaar.com/api/videos/"+i.id+".json",jsonp:"callback",dataType:"jsonp",success:function(n){r=n.framegrab_url;u(r)}})};r.prototype.stop=function(){this._core.trigger("stop",null,"video");this._playing.find(".owl-video-frame").remove();this._playing.removeClass("owl-video-playing");this._playing=null;this._core.leave("playing");this._core.trigger("stopped",null,"video")};r.prototype.play=function(t){var r,f=n(t.target),u=f.closest("."+this._core.settings.itemClass),i=this._videos[u.attr("data-video")],e=i.width||"100%",o=i.height||this._core.$stage.height();this._playing||(this._core.enter("playing"),this._core.trigger("play",null,"video"),u=this._core.items(this._core.relative(u.index())),this._core.reset(u.index()),r=n('<iframe frameborder="0" allowfullscreen mozallowfullscreen webkitAllowFullScreen ><\/iframe>'),r.attr("height",o),r.attr("width",e),"youtube"===i.type?r.attr("src","//www.youtube.com/embed/"+i.id+"?autoplay=1&rel=0&v="+i.id):"vimeo"===i.type?r.attr("src","//player.vimeo.com/video/"+i.id+"?autoplay=1"):"vzaar"===i.type&&r.attr("src","//view.vzaar.com/"+i.id+"/player?autoplay=true"),n(r).wrap('<div class="owl-video-frame" />').insertAfter(u.find(".owl-video")),this._playing=u.addClass("owl-video-playing"))};r.prototype.isInFullScreen=function(){var t=i.fullscreenElement||i.mozFullScreenElement||i.webkitFullscreenElement;return t&&n(t).parent().hasClass("owl-video-frame")};r.prototype.destroy=function(){var n,t;this._core.$element.off("click.owl.video");for(n in this._handlers)this._core.$element.off(n,this._handlers[n]);for(t in Object.getOwnPropertyNames(this))"function"!=typeof this[t]&&(this[t]=null)};n.fn.owlCarousel.Constructor.Plugins.Video=r}(window.Zepto||window.jQuery,window,document),function(n,t,i,r){var u=function(t){this.core=t;this.core.options=n.extend({},u.Defaults,this.core.options);this.swapping=!0;this.previous=r;this.next=r;this.handlers={"change.owl.carousel":n.proxy(function(n){n.namespace&&"position"==n.property.name&&(this.previous=this.core.current(),this.next=n.property.value)},this),"drag.owl.carousel dragged.owl.carousel translated.owl.carousel":n.proxy(function(n){n.namespace&&(this.swapping="translated"==n.type)},this),"translate.owl.carousel":n.proxy(function(n){n.namespace&&this.swapping&&(this.core.options.animateOut||this.core.options.animateIn)&&this.swap()},this)};this.core.$element.on(this.handlers)};u.Defaults={animateOut:!1,animateIn:!1};u.prototype.swap=function(){if(1===this.core.settings.items&&n.support.animation&&n.support.transition){this.core.speed(0);var t,i=n.proxy(this.clear,this),f=this.core.$stage.children().eq(this.previous),e=this.core.$stage.children().eq(this.next),r=this.core.settings.animateIn,u=this.core.settings.animateOut;this.core.current()!==this.previous&&(u&&(t=this.core.coordinates(this.previous)-this.core.coordinates(this.next),f.one(n.support.animation.end,i).css({left:t+"px"}).addClass("animated owl-animated-out").addClass(u)),r&&e.one(n.support.animation.end,i).addClass("animated owl-animated-in").addClass(r))}};u.prototype.clear=function(t){n(t.target).css({left:""}).removeClass("animated owl-animated-out owl-animated-in").removeClass(this.core.settings.animateIn).removeClass(this.core.settings.animateOut);this.core.onTransitionEnd()};u.prototype.destroy=function(){var n,t;for(n in this.handlers)this.core.$element.off(n,this.handlers[n]);for(t in Object.getOwnPropertyNames(this))"function"!=typeof this[t]&&(this[t]=null)};n.fn.owlCarousel.Constructor.Plugins.Animate=u}(window.Zepto||window.jQuery,window,document),function(n,t,i){var r=function(t){this._core=t;this._call=null;this._time=0;this._timeout=0;this._paused=!0;this._handlers={"changed.owl.carousel":n.proxy(function(n){n.namespace&&"settings"===n.property.name?this._core.settings.autoplay?this.play():this.stop():n.namespace&&"position"===n.property.name&&this._paused&&(this._time=0)},this),"initialized.owl.carousel":n.proxy(function(n){n.namespace&&this._core.settings.autoplay&&this.play()},this),"play.owl.autoplay":n.proxy(function(n,t,i){n.namespace&&this.play(t,i)},this),"stop.owl.autoplay":n.proxy(function(n){n.namespace&&this.stop()},this),"mouseover.owl.autoplay":n.proxy(function(){this._core.settings.autoplayHoverPause&&this._core.is("rotating")&&this.pause()},this),"mouseleave.owl.autoplay":n.proxy(function(){this._core.settings.autoplayHoverPause&&this._core.is("rotating")&&this.play()},this),"touchstart.owl.core":n.proxy(function(){this._core.settings.autoplayHoverPause&&this._core.is("rotating")&&this.pause()},this),"touchend.owl.core":n.proxy(function(){this._core.settings.autoplayHoverPause&&this.play()},this)};this._core.$element.on(this._handlers);this._core.options=n.extend({},r.Defaults,this._core.options)};r.Defaults={autoplay:!1,autoplayTimeout:5e3,autoplayHoverPause:!1,autoplaySpeed:!1};r.prototype._next=function(r){this._call=t.setTimeout(n.proxy(this._next,this,r),this._timeout*(Math.round(this.read()/this._timeout)+1)-this.read());this._core.is("interacting")||i.hidden||this._core.next(r||this._core.settings.autoplaySpeed)};r.prototype.read=function(){return(new Date).getTime()-this._time};r.prototype.play=function(i,r){var u;this._core.is("rotating")||this._core.enter("rotating");i=i||this._core.settings.autoplayTimeout;u=Math.min(this._time%(this._timeout||i),i);this._paused?(this._time=this.read(),this._paused=!1):t.clearTimeout(this._call);this._time+=this.read()%i-u;this._timeout=i;this._call=t.setTimeout(n.proxy(this._next,this,r),i-u)};r.prototype.stop=function(){this._core.is("rotating")&&(this._time=0,this._paused=!0,t.clearTimeout(this._call),this._core.leave("rotating"))};r.prototype.pause=function(){this._core.is("rotating")&&!this._paused&&(this._time=this.read(),this._paused=!0,t.clearTimeout(this._call))};r.prototype.destroy=function(){var n,t;this.stop();for(n in this._handlers)this._core.$element.off(n,this._handlers[n]);for(t in Object.getOwnPropertyNames(this))"function"!=typeof this[t]&&(this[t]=null)};n.fn.owlCarousel.Constructor.Plugins.autoplay=r}(window.Zepto||window.jQuery,window,document),function(n){"use strict";var t=function(i){this._core=i;this._initialized=!1;this._pages=[];this._controls={};this._templates=[];this.$element=this._core.$element;this._overrides={next:this._core.next,prev:this._core.prev,to:this._core.to};this._handlers={"prepared.owl.carousel":n.proxy(function(t){t.namespace&&this._core.settings.dotsData&&this._templates.push('<div class="'+this._core.settings.dotClass+'">'+n(t.content).find("[data-dot]").addBack("[data-dot]").attr("data-dot")+"<\/div>")},this),"added.owl.carousel":n.proxy(function(n){n.namespace&&this._core.settings.dotsData&&this._templates.splice(n.position,0,this._templates.pop())},this),"remove.owl.carousel":n.proxy(function(n){n.namespace&&this._core.settings.dotsData&&this._templates.splice(n.position,1)},this),"changed.owl.carousel":n.proxy(function(n){n.namespace&&"position"==n.property.name&&this.draw()},this),"initialized.owl.carousel":n.proxy(function(n){n.namespace&&!this._initialized&&(this._core.trigger("initialize",null,"navigation"),this.initialize(),this.update(),this.draw(),this._initialized=!0,this._core.trigger("initialized",null,"navigation"))},this),"refreshed.owl.carousel":n.proxy(function(n){n.namespace&&this._initialized&&(this._core.trigger("refresh",null,"navigation"),this.update(),this.draw(),this._core.trigger("refreshed",null,"navigation"))},this)};this._core.options=n.extend({},t.Defaults,this._core.options);this.$element.on(this._handlers)};t.Defaults={nav:!1,navText:['<span aria-label="Previous">&#x2039;<\/span>','<span aria-label="Next">&#x203a;<\/span>'],navSpeed:!1,navElement:'button type="button" role="presentation"',navContainer:!1,navContainerClass:"owl-nav",navClass:["owl-prev","owl-next"],slideBy:1,dotClass:"owl-dot",dotsClass:"owl-dots",dots:!0,dotsEach:!1,dotsData:!1,dotsSpeed:!1,dotsContainer:!1};t.prototype.initialize=function(){var i,t=this._core.settings;this._controls.$relative=(t.navContainer?n(t.navContainer):n("<div>").addClass(t.navContainerClass).appendTo(this.$element)).addClass("disabled");this._controls.$previous=n("<"+t.navElement+">").addClass(t.navClass[0]).html(t.navText[0]).prependTo(this._controls.$relative).on("click",n.proxy(function(){this.prev(t.navSpeed)},this));this._controls.$next=n("<"+t.navElement+">").addClass(t.navClass[1]).html(t.navText[1]).appendTo(this._controls.$relative).on("click",n.proxy(function(){this.next(t.navSpeed)},this));t.dotsData||(this._templates=[n('<button role="button">').addClass(t.dotClass).append(n("<span>")).prop("outerHTML")]);this._controls.$absolute=(t.dotsContainer?n(t.dotsContainer):n("<div>").addClass(t.dotsClass).appendTo(this.$element)).addClass("disabled");this._controls.$absolute.on("click","button",n.proxy(function(i){var r=n(i.target).parent().is(this._controls.$absolute)?n(i.target).index():n(i.target).parent().index();i.preventDefault();this.to(r,t.dotsSpeed)},this));for(i in this._overrides)this._core[i]=n.proxy(this[i],this)};t.prototype.destroy=function(){var t,n,i,r,u=this._core.settings;for(t in this._handlers)this.$element.off(t,this._handlers[t]);for(n in this._controls)"$relative"===n&&u.navContainer?this._controls[n].html(""):this._controls[n].remove();for(r in this.overides)this._core[r]=this._overrides[r];for(i in Object.getOwnPropertyNames(this))"function"!=typeof this[i]&&(this[i]=null)};t.prototype.update=function(){var t,i,f,r=this._core.clones().length/2,o=r+this._core.items().length,u=this._core.maximum(!0),n=this._core.settings,e=n.center||n.autoWidth||n.dotsData?1:n.dotsEach||n.items;if("page"!==n.slideBy&&(n.slideBy=Math.min(n.slideBy,n.items)),n.dots||"page"==n.slideBy)for(this._pages=[],t=r,i=0,f=0;t<o;t++){if(i>=e||0===i){if(this._pages.push({start:Math.min(u,t-r),end:t-r+e-1}),Math.min(u,t-r)===u)break;i=0;++f}i+=this._core.mergers(this._core.relative(t))}};t.prototype.draw=function(){var i,t=this._core.settings,r=this._core.items().length<=t.items,u=this._core.relative(this._core.current()),f=t.loop||t.rewind;this._controls.$relative.toggleClass("disabled",!t.nav||r);t.nav&&(this._controls.$previous.toggleClass("disabled",!f&&u<=this._core.minimum(!0)),this._controls.$next.toggleClass("disabled",!f&&u>=this._core.maximum(!0)));this._controls.$absolute.toggleClass("disabled",!t.dots||r);t.dots&&(i=this._pages.length-this._controls.$absolute.children().length,t.dotsData&&0!==i?this._controls.$absolute.html(this._templates.join("")):i>0?this._controls.$absolute.append(new Array(i+1).join(this._templates[0])):i<0&&this._controls.$absolute.children().slice(i).remove(),this._controls.$absolute.find(".active").removeClass("active"),this._controls.$absolute.children().eq(n.inArray(this.current(),this._pages)).addClass("active"))};t.prototype.onTrigger=function(t){var i=this._core.settings;t.page={index:n.inArray(this.current(),this._pages),count:this._pages.length,size:i&&(i.center||i.autoWidth||i.dotsData?1:i.dotsEach||i.items)}};t.prototype.current=function(){var t=this._core.relative(this._core.current());return n.grep(this._pages,n.proxy(function(n){return n.start<=t&&n.end>=t},this)).pop()};t.prototype.getPosition=function(t){var i,r,u=this._core.settings;return"page"==u.slideBy?(i=n.inArray(this.current(),this._pages),r=this._pages.length,t?++i:--i,i=this._pages[(i%r+r)%r].start):(i=this._core.relative(this._core.current()),r=this._core.items().length,t?i+=u.slideBy:i-=u.slideBy),i};t.prototype.next=function(t){n.proxy(this._overrides.to,this._core)(this.getPosition(!0),t)};t.prototype.prev=function(t){n.proxy(this._overrides.to,this._core)(this.getPosition(!1),t)};t.prototype.to=function(t,i,r){var u;!r&&this._pages.length?(u=this._pages.length,n.proxy(this._overrides.to,this._core)(this._pages[(t%u+u)%u].start,i)):n.proxy(this._overrides.to,this._core)(t,i)};n.fn.owlCarousel.Constructor.Plugins.Navigation=t}(window.Zepto||window.jQuery,window,document),function(n,t,i,r){"use strict";var u=function(i){this._core=i;this._hashes={};this.$element=this._core.$element;this._handlers={"initialized.owl.carousel":n.proxy(function(i){i.namespace&&"URLHash"===this._core.settings.startPosition&&n(t).trigger("hashchange.owl.navigation")},this),"prepared.owl.carousel":n.proxy(function(t){if(t.namespace){var i=n(t.content).find("[data-hash]").addBack("[data-hash]").attr("data-hash");if(!i)return;this._hashes[i]=t.content}},this),"changed.owl.carousel":n.proxy(function(i){if(i.namespace&&"position"===i.property.name){var u=this._core.items(this._core.relative(this._core.current())),r=n.map(this._hashes,function(n,t){return n===u?t:null}).join();if(!r||t.location.hash.slice(1)===r)return;t.location.hash=r}},this)};this._core.options=n.extend({},u.Defaults,this._core.options);this.$element.on(this._handlers);n(t).on("hashchange.owl.navigation",n.proxy(function(){var i=t.location.hash.substring(1),u=this._core.$stage.children(),n=this._hashes[i]&&u.index(this._hashes[i]);n!==r&&n!==this._core.current()&&this._core.to(this._core.relative(n),!1,!0)},this))};u.Defaults={URLhashListener:!1};u.prototype.destroy=function(){var i,r;n(t).off("hashchange.owl.navigation");for(i in this._handlers)this._core.$element.off(i,this._handlers[i]);for(r in Object.getOwnPropertyNames(this))"function"!=typeof this[r]&&(this[r]=null)};n.fn.owlCarousel.Constructor.Plugins.Hash=u}(window.Zepto||window.jQuery,window,document),function(n,t,i,r){function u(t,i){var u=!1,f=t.charAt(0).toUpperCase()+t.slice(1);return n.each((t+" "+h.join(f+" ")+f).split(" "),function(n,t){if(s[t]!==r)return u=!i||t,!1}),u}function e(n){return u(n,!0)}var s=n("<support>").get(0).style,h="Webkit Moz O ms".split(" "),o={transition:{end:{WebkitTransition:"webkitTransitionEnd",MozTransition:"transitionend",OTransition:"oTransitionEnd",transition:"transitionend"}},animation:{end:{WebkitAnimation:"webkitAnimationEnd",MozAnimation:"animationend",OAnimation:"oAnimationEnd",animation:"animationend"}}},f={csstransforms:function(){return!!u("transform")},csstransforms3d:function(){return!!u("perspective")},csstransitions:function(){return!!u("transition")},cssanimations:function(){return!!u("animation")}};f.csstransitions()&&(n.support.transition=new String(e("transition")),n.support.transition.end=o.transition.end[n.support.transition]);f.cssanimations()&&(n.support.animation=new String(e("animation")),n.support.animation.end=o.animation.end[n.support.animation]);f.csstransforms()&&(n.support.transform=new String(e("transform")),n.support.transform3d=f.csstransforms3d())}(window.Zepto||window.jQuery,window,document);
/*!
 * jQuery Form Plugin
 * version: 3.51.0-2014.06.20
 * Requires jQuery v1.5 or later
 * Copyright (c) 2014 M. Alsup
 * Examples and documentation at: http://malsup.com/jquery/form/
 * Project repository: https://github.com/malsup/form
 * Dual licensed under the MIT and GPL licenses.
 * https://github.com/malsup/form#copyright-and-license
 */
(function(n){"use strict";typeof define=="function"&&define.amd?define(["jquery"],n):n(typeof jQuery!="undefined"?jQuery:window.Zepto)})(function(n){"use strict";function u(t){var i=t.data;t.isDefaultPrevented()||(t.preventDefault(),n(t.target).ajaxSubmit(i))}function f(t){var r=t.target,u=n(r),f,i,e;if(!u.is("[type=submit],[type=image]")){if(f=u.closest("[type=submit]"),f.length===0)return;r=f[0]}i=this;i.clk=r;r.type=="image"&&(t.offsetX!==undefined?(i.clk_x=t.offsetX,i.clk_y=t.offsetY):typeof n.fn.offset=="function"?(e=u.offset(),i.clk_x=t.pageX-e.left,i.clk_y=t.pageY-e.top):(i.clk_x=t.pageX-r.offsetLeft,i.clk_y=t.pageY-r.offsetTop));setTimeout(function(){i.clk=i.clk_x=i.clk_y=null},100)}function t(){if(n.fn.ajaxSubmit.debug){var t="[jquery.form] "+Array.prototype.join.call(arguments,"");window.console&&window.console.log?window.console.log(t):window.opera&&window.opera.postError&&window.opera.postError(t)}}var i={},r;i.fileapi=n("<input type='file'/>").get(0).files!==undefined;i.formdata=window.FormData!==undefined;r=!!n.fn.prop;n.fn.attr2=function(){if(!r)return this.attr.apply(this,arguments);var n=this.prop.apply(this,arguments);return n&&n.jquery||typeof n=="string"?n:this.attr.apply(this,arguments)};n.fn.ajaxSubmit=function(u){function ot(t){for(var r=n.param(t,u.traditional).split("&"),o=r.length,e=[],f,i=0;i<o;i++)r[i]=r[i].replace(/\+/g," "),f=r[i].split("="),e.push([decodeURIComponent(f[0]),decodeURIComponent(f[1])]);return e}function st(t){for(var f,r,s,o=new FormData,i=0;i<t.length;i++)o.append(t[i].name,t[i].value);if(u.extraData)for(f=ot(u.extraData),i=0;i<f.length;i++)f[i]&&o.append(f[i][0],f[i][1]);return u.data=null,r=n.extend(!0,{},n.ajaxSettings,u,{contentType:!1,processData:!1,cache:!1,type:e||"POST"}),u.uploadProgress&&(r.xhr=function(){var t=n.ajaxSettings.xhr();return t.upload&&t.upload.addEventListener("progress",function(n){var t=0,i=n.loaded||n.position,r=n.total;n.lengthComputable&&(t=Math.ceil(i/r*100));u.uploadProgress(n,i,r,t)},!1),t}),r.data=null,s=r.beforeSend,r.beforeSend=function(n,t){t.data=u.formData?u.formData:o;s&&s.call(this,n,t)},n.ajax(r)}function ft(i){function ot(n){var i=null;try{n.contentWindow&&(i=n.contentWindow.document)}catch(r){t("cannot get iframe.contentWindow document: "+r)}if(i)return i;try{i=n.contentDocument?n.contentDocument:n.document}catch(r){t("cannot get iframe.contentDocument: "+r);i=n.document}return i}function st(){function h(){try{var n=ot(a).readyState;t("state = "+n);n&&n.toLowerCase()=="uninitialized"&&setTimeout(h,50)}catch(i){t("Server abort: ",i," (",i.name,")");b(tt);g&&clearTimeout(g);g=undefined}}var u=f.attr2("target"),s=f.attr2("action"),y=f.attr("enctype")||f.attr("encoding")||"multipart/form-data",r,i,c;l.setAttribute("target",d);(!e||/post/i.test(e))&&l.setAttribute("method","POST");s!=o.url&&l.setAttribute("action",o.url);o.skipEncodingOverride||e&&!/post/i.test(e)||f.attr({encoding:"multipart/form-data",enctype:"multipart/form-data"});o.timeout&&(g=setTimeout(function(){rt=!0;b(ut)},o.timeout));r=[];try{if(o.extraData)for(i in o.extraData)o.extraData.hasOwnProperty(i)&&(n.isPlainObject(o.extraData[i])&&o.extraData[i].hasOwnProperty("name")&&o.extraData[i].hasOwnProperty("value")?r.push(n('<input type="hidden" name="'+o.extraData[i].name+'">').val(o.extraData[i].value).appendTo(l)[0]):r.push(n('<input type="hidden" name="'+i+'">').val(o.extraData[i]).appendTo(l)[0]));o.iframeTarget||v.appendTo("body");a.attachEvent?a.attachEvent("onload",b):a.addEventListener("load",b,!1);setTimeout(h,15);try{l.submit()}catch(p){c=document.createElement("form").submit;c.apply(l)}}finally{l.setAttribute("action",s);l.setAttribute("enctype",y);u?l.setAttribute("target",u):f.removeAttr("target");n(r).remove()}}function b(i){var r,u,w,f,k,d,e,c,l;if(!s.aborted&&!lt){if(h=ot(a),h||(t("cannot access response document"),i=tt),i===ut&&s){s.abort("timeout");y.reject(s,"timeout");return}if(i==tt&&s){s.abort("server abort");y.reject(s,"error","server abort");return}if(h&&h.location.href!=o.iframeSrc||rt){a.detachEvent?a.detachEvent("onload",b):a.removeEventListener("load",b,!1);r="success";try{if(rt)throw"timeout";if(w=o.dataType=="xml"||h.XMLDocument||n.isXMLDoc(h),t("isXml="+w),!w&&window.opera&&(h.body===null||!h.body.innerHTML)&&--ct){t("requeing onLoad callback, DOM not available");setTimeout(b,250);return}f=h.body?h.body:h.documentElement;s.responseText=f?f.innerHTML:null;s.responseXML=h.XMLDocument?h.XMLDocument:h;w&&(o.dataType="xml");s.getResponseHeader=function(n){var t={"content-type":o.dataType};return t[n.toLowerCase()]};f&&(s.status=Number(f.getAttribute("status"))||s.status,s.statusText=f.getAttribute("statusText")||s.statusText);k=(o.dataType||"").toLowerCase();d=/(json|script|text)/.test(k);d||o.textarea?(e=h.getElementsByTagName("textarea")[0],e?(s.responseText=e.value,s.status=Number(e.getAttribute("status"))||s.status,s.statusText=e.getAttribute("statusText")||s.statusText):d&&(c=h.getElementsByTagName("pre")[0],l=h.getElementsByTagName("body")[0],c?s.responseText=c.textContent?c.textContent:c.innerText:l&&(s.responseText=l.textContent?l.textContent:l.innerText))):k=="xml"&&!s.responseXML&&s.responseText&&(s.responseXML=at(s.responseText));try{ht=yt(s,k,o)}catch(nt){r="parsererror";s.error=u=nt||r}}catch(nt){t("error caught: ",nt);r="error";s.error=u=nt||r}s.aborted&&(t("upload aborted"),r=null);s.status&&(r=s.status>=200&&s.status<300||s.status===304?"success":"error");r==="success"?(o.success&&o.success.call(o.context,ht,"success",s),y.resolve(s.responseText,"success",s),p&&n.event.trigger("ajaxSuccess",[s,o])):r&&(u===undefined&&(u=s.statusText),o.error&&o.error.call(o.context,s,r,u),y.reject(s,"error",u),p&&n.event.trigger("ajaxError",[s,o,u]));p&&n.event.trigger("ajaxComplete",[s,o]);p&&!--n.active&&n.event.trigger("ajaxStop");o.complete&&o.complete.call(o.context,s,r);lt=!0;o.timeout&&clearTimeout(g);setTimeout(function(){o.iframeTarget?v.attr("src",o.iframeSrc):v.remove();s.responseXML=null},100)}}}var l=f[0],it,nt,o,p,d,v,a,s,k,w,rt,g,y=n.Deferred(),ut,tt,ft,et,ht,h,ct,lt;if(y.abort=function(n){s.abort(n)},i)for(nt=0;nt<c.length;nt++)it=n(c[nt]),r?it.prop("disabled",!1):it.removeAttr("disabled");if(o=n.extend(!0,{},n.ajaxSettings,u),o.context=o.context||o,d="jqFormIO"+(new Date).getTime(),o.iframeTarget?(v=n(o.iframeTarget),w=v.attr2("name"),w?d=w:v.attr2("name",d)):(v=n('<iframe name="'+d+'" src="'+o.iframeSrc+'" />'),v.css({position:"absolute",top:"-1000px",left:"-1000px"})),a=v[0],s={aborted:0,responseText:null,responseXML:null,status:0,statusText:"n/a",getAllResponseHeaders:function(){},getResponseHeader:function(){},setRequestHeader:function(){},abort:function(i){var r=i==="timeout"?"timeout":"aborted";t("aborting upload... "+r);this.aborted=1;try{a.contentWindow.document.execCommand&&a.contentWindow.document.execCommand("Stop")}catch(u){}v.attr("src",o.iframeSrc);s.error=r;o.error&&o.error.call(o.context,s,r,i);p&&n.event.trigger("ajaxError",[s,o,r]);o.complete&&o.complete.call(o.context,s,r)}},p=o.global,p&&0==n.active++&&n.event.trigger("ajaxStart"),p&&n.event.trigger("ajaxSend",[s,o]),o.beforeSend&&o.beforeSend.call(o.context,s,o)===!1)return o.global&&n.active--,y.reject(),y;if(s.aborted)return y.reject(),y;k=l.clk;k&&(w=k.name,w&&!k.disabled&&(o.extraData=o.extraData||{},o.extraData[w]=k.value,k.type=="image"&&(o.extraData[w+".x"]=l.clk_x,o.extraData[w+".y"]=l.clk_y)));ut=1;tt=2;ft=n("meta[name=csrf-token]").attr("content");et=n("meta[name=csrf-param]").attr("content");et&&ft&&(o.extraData=o.extraData||{},o.extraData[et]=ft);o.forceSync?st():setTimeout(st,10);ct=50;var at=n.parseXML||function(n,t){return window.ActiveXObject?(t=new ActiveXObject("Microsoft.XMLDOM"),t.async="false",t.loadXML(n)):t=(new DOMParser).parseFromString(n,"text/xml"),t&&t.documentElement&&t.documentElement.nodeName!="parsererror"?t:null},vt=n.parseJSON||function(n){return window.eval("("+n+")")},yt=function(t,i,r){var f=t.getResponseHeader("content-type")||"",e=i==="xml"||!i&&f.indexOf("xml")>=0,u=e?t.responseXML:t.responseText;return e&&u.documentElement.nodeName==="parsererror"&&n.error&&n.error("parsererror"),r&&r.dataFilter&&(u=r.dataFilter(u,i)),typeof u=="string"&&(i==="json"||!i&&f.indexOf("json")>=0?u=vt(u):(i==="script"||!i&&f.indexOf("javascript")>=0)&&n.globalEval(u)),u};return y}var e,b,o,f,a,v,c,y,s,l,h,d,g,nt,ut,p,w;if(!this.length)return t("ajaxSubmit: skipping submit process - no element selected"),this;if(f=this,typeof u=="function"?u={success:u}:u===undefined&&(u={}),e=u.type||this.attr2("method"),b=u.url||this.attr2("action"),o=typeof b=="string"?n.trim(b):"",o=o||window.location.href||"",o&&(o=(o.match(/^([^#]+)/)||[])[1]),u=n.extend(!0,{url:o,success:n.ajaxSettings.success,type:e||n.ajaxSettings.type,iframeSrc:/^https/i.test(window.location.href||"")?"javascript:false":"about:blank"},u),a={},this.trigger("form-pre-serialize",[this,u,a]),a.veto)return t("ajaxSubmit: submit vetoed via form-pre-serialize trigger"),this;if(u.beforeSerialize&&u.beforeSerialize(this,u)===!1)return t("ajaxSubmit: submit aborted via beforeSerialize callback"),this;if(v=u.traditional,v===undefined&&(v=n.ajaxSettings.traditional),c=[],s=this.formToArray(u.semantic,c),u.data&&(u.extraData=u.data,y=n.param(u.data,v)),u.beforeSubmit&&u.beforeSubmit(s,this,u)===!1)return t("ajaxSubmit: submit aborted via beforeSubmit callback"),this;if(this.trigger("form-submit-validate",[s,this,u,a]),a.veto)return t("ajaxSubmit: submit vetoed via form-submit-validate trigger"),this;l=n.param(s,v);y&&(l=l?l+"&"+y:y);u.type.toUpperCase()=="GET"?(u.url+=(u.url.indexOf("?")>=0?"&":"?")+l,u.data=null):u.data=l;h=[];u.resetForm&&h.push(function(){f.resetForm()});u.clearForm&&h.push(function(){f.clearForm(u.includeHidden)});!u.dataType&&u.target?(d=u.success||function(){},h.push(function(t){var i=u.replaceTarget?"replaceWith":"html";n(u.target)[i](t).each(d,arguments)})):u.success&&h.push(u.success);u.success=function(n,t,i){for(var e=u.context||this,r=0,o=h.length;r<o;r++)h[r].apply(e,[n,t,i||f,f])};u.error&&(g=u.error,u.error=function(n,t,i){var r=u.context||this;g.apply(r,[n,t,i,f])});u.complete&&(nt=u.complete,u.complete=function(n,t){var i=u.context||this;nt.apply(i,[n,t,f])});var et=n("input[type=file]:enabled",this).filter(function(){return n(this).val()!==""}),tt=et.length>0,it="multipart/form-data",rt=f.attr("enctype")==it||f.attr("encoding")==it,k=i.fileapi&&i.formdata;for(t("fileAPI :"+k),ut=(tt||rt)&&!k,u.iframe!==!1&&(u.iframe||ut)?u.closeKeepAlive?n.get(u.closeKeepAlive,function(){p=ft(s)}):p=ft(s):p=(tt||rt)&&k?st(s):n.ajax(u),f.removeData("jqxhr").data("jqxhr",p),w=0;w<c.length;w++)c[w]=null;return this.trigger("form-submit-notify",[this,u]),this};n.fn.ajaxForm=function(i){if(i=i||{},i.delegation=i.delegation&&n.isFunction(n.fn.on),!i.delegation&&this.length===0){var r={s:this.selector,c:this.context};return!n.isReady&&r.s?(t("DOM not ready, queuing ajaxForm"),n(function(){n(r.s,r.c).ajaxForm(i)}),this):(t("terminating; zero elements found by selector"+(n.isReady?"":" (DOM not ready)")),this)}if(i.delegation){n(document).off("submit.form-plugin",this.selector,u).off("click.form-plugin",this.selector,f).on("submit.form-plugin",this.selector,i,u).on("click.form-plugin",this.selector,i,f);return this}return this.ajaxFormUnbind().bind("submit.form-plugin",i,u).bind("click.form-plugin",i,f)};n.fn.ajaxFormUnbind=function(){return this.unbind("submit.form-plugin click.form-plugin")};n.fn.formToArray=function(t,r){var e=[],l,h,f,c,u,b,k,a,p,v;if(this.length===0)return e;var o=this[0],w=this.attr("id"),s=t?o.getElementsByTagName("*"):o.elements,y;if(s&&!/MSIE [678]/.test(navigator.userAgent)&&(s=n(s).get()),w&&(y=n(':input[form="'+w+'"]').get(),y.length&&(s=(s||[]).concat(y))),!s||!s.length)return e;for(l=0,b=s.length;l<b;l++)if(u=s[l],f=u.name,f&&!u.disabled){if(t&&o.clk&&u.type=="image"){o.clk==u&&(e.push({name:f,value:n(u).val(),type:u.type}),e.push({name:f+".x",value:o.clk_x},{name:f+".y",value:o.clk_y}));continue}if(c=n.fieldValue(u,!0),c&&c.constructor==Array)for(r&&r.push(u),h=0,k=c.length;h<k;h++)e.push({name:f,value:c[h]});else if(i.fileapi&&u.type=="file")if(r&&r.push(u),a=u.files,a.length)for(h=0;h<a.length;h++)e.push({name:f,value:a[h],type:u.type});else e.push({name:f,value:"",type:u.type});else c!==null&&typeof c!="undefined"&&(r&&r.push(u),e.push({name:f,value:c,type:u.type,required:u.required}))}return!t&&o.clk&&(p=n(o.clk),v=p[0],f=v.name,f&&!v.disabled&&v.type=="image"&&(e.push({name:f,value:p.val()}),e.push({name:f+".x",value:o.clk_x},{name:f+".y",value:o.clk_y}))),e};n.fn.formSerialize=function(t){return n.param(this.formToArray(t))};n.fn.fieldSerialize=function(t){var i=[];return this.each(function(){var f=this.name,r,u,e;if(f)if(r=n.fieldValue(this,t),r&&r.constructor==Array)for(u=0,e=r.length;u<e;u++)i.push({name:f,value:r[u]});else r!==null&&typeof r!="undefined"&&i.push({name:this.name,value:r})}),n.param(i)};n.fn.fieldValue=function(t){for(var f,i,r=[],u=0,e=this.length;u<e;u++)(f=this[u],i=n.fieldValue(f,t),i!==null&&typeof i!="undefined"&&(i.constructor!=Array||i.length))&&(i.constructor==Array?n.merge(r,i):r.push(i));return r};n.fieldValue=function(t,i){var a=t.name,u=t.type,h=t.tagName.toLowerCase(),e,o,r,f;if(i===undefined&&(i=!0),i&&(!a||t.disabled||u=="reset"||u=="button"||(u=="checkbox"||u=="radio")&&!t.checked||(u=="submit"||u=="image")&&t.form&&t.form.clk!=t||h=="select"&&t.selectedIndex==-1))return null;if(h=="select"){if(e=t.selectedIndex,e<0)return null;var c=[],l=t.options,s=u=="select-one",v=s?e+1:l.length;for(o=s?e:0;o<v;o++)if(r=l[o],r.selected){if(f=r.value,f||(f=r.attributes&&r.attributes.value&&!r.attributes.value.specified?r.text:r.value),s)return f;c.push(f)}return c}return n(t).val()};n.fn.clearForm=function(t){return this.each(function(){n("input,select,textarea",this).clearFields(t)})};n.fn.clearFields=n.fn.clearInputs=function(t){var i=/^(?:color|date|datetime|email|month|number|password|range|search|tel|text|time|url|week)$/i;return this.each(function(){var r=this.type,u=this.tagName.toLowerCase();i.test(r)||u=="textarea"?this.value="":r=="checkbox"||r=="radio"?this.checked=!1:u=="select"?this.selectedIndex=-1:r=="file"?/MSIE/.test(navigator.userAgent)?n(this).replaceWith(n(this).clone(!0)):n(this).val(""):t&&(t===!0&&/hidden/.test(r)||typeof t=="string"&&n(this).is(t))&&(this.value="")})};n.fn.resetForm=function(){return this.each(function(){typeof this.reset!="function"&&(typeof this.reset!="object"||this.reset.nodeType)||this.reset()})};n.fn.enable=function(n){return n===undefined&&(n=!0),this.each(function(){this.disabled=!n})};n.fn.selected=function(t){return t===undefined&&(t=!0),this.each(function(){var r=this.type,i;r=="checkbox"||r=="radio"?this.checked=t:this.tagName.toLowerCase()=="option"&&(i=n(this).parent("select"),t&&i[0]&&i[0].type=="select-one"&&i.find("option").selected(!1),this.selected=t)})};n.fn.ajaxSubmit.debug=!1});!function(n){var t=!1,r,i;("function"==typeof define&&define.amd&&(define(n),t=!0),"object"==typeof exports&&(module.exports=n(),t=!0),t)||(r=window.Cookies,i=window.Cookies=n(),i.noConflict=function(){return window.Cookies=r,i})}(function(){function n(){for(var i,r,n=0,t={};n<arguments.length;n++){i=arguments[n];for(r in i)t[r]=i[r]}return t}return function t(i){function r(t,u,f){var o,c,l,s,v,e,h;if("undefined"!=typeof document){if(1<arguments.length){"number"==typeof(f=n({path:"/"},r.defaults,f)).expires&&(c=new Date,c.setMilliseconds(c.getMilliseconds()+864e5*f.expires),f.expires=c);f.expires=f.expires?f.expires.toUTCString():"";try{o=JSON.stringify(u);/^[\{\[]/.test(o)&&(u=o)}catch(t){}u=i.write?i.write(u,t):encodeURIComponent(String(u)).replace(/%(23|24|26|2B|3A|3C|3E|3D|2F|3F|40|5B|5D|5E|60|7B|7D|7C)/g,decodeURIComponent);t=(t=(t=encodeURIComponent(String(t))).replace(/%(23|24|26|2B|5E|60|7C)/g,decodeURIComponent)).replace(/[\(\)]/g,escape);l="";for(s in f)f[s]&&(l+="; "+s,!0!==f[s]&&(l+="="+f[s]));return document.cookie=t+"="+u+l}t||(o={});for(var y=document.cookie?document.cookie.split("; "):[],p=/(%[0-9A-Z]{2})+/g,a=0;a<y.length;a++){v=y[a].split("=");e=v.slice(1).join("=");this.json||'"'!==e.charAt(0)||(e=e.slice(1,-1));try{if(h=v[0].replace(p,decodeURIComponent),e=i.read?i.read(e,h):i(e,h)||e.replace(p,decodeURIComponent),this.json)try{e=JSON.parse(e)}catch(t){}if(t===h){o=e;break}t||(o[h]=e)}catch(t){}}return o}}return(r.set=r).get=function(n){return r.call(r,n)},r.getJSON=function(){return r.apply({json:!0},[].slice.call(arguments))},r.defaults={},r.remove=function(t,i){r(t,"",n(i,{expires:-1}))},r.withConverter=t,r}(function(){})});
/*!
* Clamp.js 0.5.1
*
* Copyright 2011-2013, Joseph Schmitt http://joe.sh
* Released under the WTFPL license
* http://sam.zoy.org/wtfpl/
*/
(function(){window.$clamp=function(n,t){function y(n,t){return a.getComputedStyle||(a.getComputedStyle=function(n){return this.el=n,this.getPropertyValue=function(t){var i=/(\-([a-z]){1})/g;return"float"==t&&(t="styleFloat"),i.test(t)&&(t=t.replace(i,function(n,t,i){return i.toUpperCase()})),n.currentStyle&&n.currentStyle[t]?n.currentStyle[t]:null},this}),a.getComputedStyle(n,null).getPropertyValue(t)}function p(t){t=t||n.clientHeight;var i=w(n);return Math.max(Math.floor(t/i),0)}function d(t){return w(n)*t}function w(n){var t=y(n,"line-height");return"normal"==t&&(t=1.2*parseInt(y(n,"font-size"))),parseInt(t)}function s(t){return t.lastChild.children&&0<t.lastChild.children.length?s(Array.prototype.slice.call(t.children).pop()):t.lastChild&&t.lastChild.nodeValue&&""!=t.lastChild.nodeValue&&t.lastChild.nodeValue!=i.truncationChar?t.lastChild:(t.lastChild.parentNode.removeChild(t.lastChild),s(n))}function c(t,u){if(u){var f=t.nodeValue.replace(i.truncationChar,"");if(r||(e=0<o.length?o.shift():"",r=f.split(e)),1<r.length?(v=r.pop(),l(t,r.join(e))):r=null,h&&(t.nodeValue=t.nodeValue.replace(i.truncationChar,""),n.innerHTML=t.nodeValue+" "+h.innerHTML+i.truncationChar),r){if(n.clientHeight<=u)if(0<=o.length&&""!=e)l(t,r.join(e)+e+v),r=null;else return n.innerHTML}else""==e&&(l(t,""),t=s(n),o=i.splitOnChars.slice(0),e=o[0],v=r=null);if(i.animate)setTimeout(function(){c(t,u)},!0===i.animate?10:i.animate);else return c(t,u)}}function l(n,t){n.nodeValue=t+i.truncationChar}var o,e,r,v,k;t=t||{};var a=window,i={clamp:t.clamp||2,useNativeClamp:"undefined"!=typeof t.useNativeClamp?t.useNativeClamp:!0,splitOnChars:t.splitOnChars||[".","-","–","—"," "],animate:t.animate||!1,truncationChar:t.truncationChar||"…",truncationHTML:t.truncationHTML},u=n.style,g=n.innerHTML,nt="undefined"!=typeof n.style.webkitLineClamp,f=i.clamp,b=f.indexOf&&(-1<f.indexOf("px")||-1<f.indexOf("em")),h;return i.truncationHTML&&(h=document.createElement("span"),h.innerHTML=i.truncationHTML),o=i.splitOnChars.slice(0),e=o[0],"auto"==f?f=p():b&&(f=p(parseInt(f))),nt&&i.useNativeClamp?(u.overflow="hidden",u.textOverflow="ellipsis",u.webkitBoxOrient="vertical",u.display="-webkit-box",u.webkitLineClamp=f,b&&(u.height=i.clamp+"px")):(u=d(f),u<=n.clientHeight&&(k=c(s(n),u))),{original:g,clamped:k}}})();!function(n){function i(n,t){for(var i=window,r=(n||"").split(".");i&&r.length;)i=i[r.shift()];return"function"==typeof i?i:(t.push(n),Function.constructor.apply(null,t))}function u(n){return"GET"===n||"POST"===n}function e(n,t){u(t)||n.setRequestHeader("X-HTTP-Method-Override",t)}function o(t,i,r){var u;r.indexOf("application/x-javascript")===-1&&(u=(t.getAttribute("data-ajax-mode")||"").toUpperCase(),n(t.getAttribute("data-ajax-update")).each(function(t,r){switch(u){case"BEFORE":n(r).prepend(i);break;case"AFTER":n(r).append(i);break;case"REPLACE-WITH":n(r).replaceWith(i);break;default:n(r).html(i)}}))}function f(t,r){var s,h,f,c;s=t.getAttribute("data-ajax-confirm");s&&!window.confirm(s)||(h=n(t.getAttribute("data-ajax-loading")),c=parseInt(t.getAttribute("data-ajax-loading-duration"),10)||0,n.extend(r,{type:t.getAttribute("data-ajax-method")||void 0,url:t.getAttribute("data-ajax-url")||void 0,cache:"true"===(t.getAttribute("data-ajax-cache")||"").toLowerCase(),beforeSend:function(n){var r;return e(n,f),r=i(t.getAttribute("data-ajax-begin"),["xhr"]).apply(t,arguments),r!==!1&&h.show(c),r},complete:function(){h.hide(c);i(t.getAttribute("data-ajax-complete"),["xhr","status"]).apply(t,arguments)},success:function(n,r,u){o(t,n,u.getResponseHeader("Content-Type")||"text/html");i(t.getAttribute("data-ajax-success"),["data","status","xhr"]).apply(t,arguments)},error:function(){i(t.getAttribute("data-ajax-failure"),["xhr","status","error"]).apply(t,arguments)}}),r.data.push({name:"X-Requested-With",value:"XMLHttpRequest"}),f=r.type.toUpperCase(),u(f)||(r.type="POST",r.data.push({name:"X-HTTP-Method-Override",value:f})),n.ajax(r))}function s(t){var i=n(t).data(h);return!i||!i.validate||i.validate()}var t="unobtrusiveAjaxClick",r="unobtrusiveAjaxClickTarget",h="unobtrusiveValidation";n(document).on("click","a[data-ajax=true]",function(n){n.preventDefault();f(this,{url:this.href,type:"GET",data:[]})});n(document).on("click","form[data-ajax=true] input[type=image]",function(i){var r=i.target.name,u=n(i.target),f=n(u.parents("form")[0]),e=u.offset();f.data(t,[{name:r+".x",value:Math.round(i.pageX-e.left)},{name:r+".y",value:Math.round(i.pageY-e.top)}]);setTimeout(function(){f.removeData(t)},0)});n(document).on("click","form[data-ajax=true] :submit",function(i){var f=i.currentTarget.name,e=n(i.target),u=n(e.parents("form")[0]);u.data(t,f?[{name:f,value:i.currentTarget.value}]:[]);u.data(r,e);setTimeout(function(){u.removeData(t);u.removeData(r)},0)});n(document).on("submit","form[data-ajax=true]",function(i){var e=n(this).data(t)||[],u=n(this).data(r),o=u&&(u.hasClass("cancel")||void 0!==u.attr("formnovalidate"));i.preventDefault();(o||s(this))&&f(this,{url:this.action,type:this.method||"GET",data:e.concat(n(this).serializeArray())})})}(jQuery);
/*! jQuery Validation Plugin - v1.14.0 - 6/30/2015
 * http://jqueryvalidation.org/
 * Copyright (c) 2015 Jörn Zaefferer; Licensed MIT */
!function(n){"function"==typeof define&&define.amd?define(["jquery"],n):n(jQuery)}(function(n){n.extend(n.fn,{validate:function(t){if(!this.length)return void(t&&t.debug&&window.console&&console.warn("Nothing selected, can't validate, returning nothing."));var i=n.data(this[0],"validator");return i?i:(this.attr("novalidate","novalidate"),i=new n.validator(t,this[0]),n.data(this[0],"validator",i),i.settings.onsubmit&&(this.on("click.validate",":submit",function(t){i.settings.submitHandler&&(i.submitButton=t.target);n(this).hasClass("cancel")&&(i.cancelSubmit=!0);void 0!==n(this).attr("formnovalidate")&&(i.cancelSubmit=!0)}),this.on("submit.validate",function(t){function r(){var u,r;return i.settings.submitHandler?(i.submitButton&&(u=n("<input type='hidden'/>").attr("name",i.submitButton.name).val(n(i.submitButton).val()).appendTo(i.currentForm)),r=i.settings.submitHandler.call(i,i.currentForm,t),i.submitButton&&u.remove(),void 0!==r?r:!1):!0}return i.settings.debug&&t.preventDefault(),i.cancelSubmit?(i.cancelSubmit=!1,r()):i.form()?i.pendingRequest?(i.formSubmitted=!0,!1):r():(i.focusInvalid(),!1)})),i)},valid:function(){var t,i,r;return n(this[0]).is("form")?t=this.validate().form():(r=[],t=!0,i=n(this[0].form).validate(),this.each(function(){t=i.element(this)&&t;r=r.concat(i.errorList)}),i.errorList=r),t},rules:function(t,i){var e,s,f,u,o,h,r=this[0];if(t)switch(e=n.data(r.form,"validator").settings,s=e.rules,f=n.validator.staticRules(r),t){case"add":n.extend(f,n.validator.normalizeRule(i));delete f.messages;s[r.name]=f;i.messages&&(e.messages[r.name]=n.extend(e.messages[r.name],i.messages));break;case"remove":return i?(h={},n.each(i.split(/\s/),function(t,i){h[i]=f[i];delete f[i];"required"===i&&n(r).removeAttr("aria-required")}),h):(delete s[r.name],f)}return u=n.validator.normalizeRules(n.extend({},n.validator.classRules(r),n.validator.attributeRules(r),n.validator.dataRules(r),n.validator.staticRules(r)),r),u.required&&(o=u.required,delete u.required,u=n.extend({required:o},u),n(r).attr("aria-required","true")),u.remote&&(o=u.remote,delete u.remote,u=n.extend(u,{remote:o})),u}});n.extend(n.expr[":"],{blank:function(t){return!n.trim(""+n(t).val())},filled:function(t){return!!n.trim(""+n(t).val())},unchecked:function(t){return!n(t).prop("checked")}});n.validator=function(t,i){this.settings=n.extend(!0,{},n.validator.defaults,t);this.currentForm=i;this.init()};n.validator.format=function(t,i){return 1===arguments.length?function(){var i=n.makeArray(arguments);return i.unshift(t),n.validator.format.apply(this,i)}:(arguments.length>2&&i.constructor!==Array&&(i=n.makeArray(arguments).slice(1)),i.constructor!==Array&&(i=[i]),n.each(i,function(n,i){t=t.replace(new RegExp("\\{"+n+"\\}","g"),function(){return i})}),t)};n.extend(n.validator,{defaults:{messages:{},groups:{},rules:{},errorClass:"error",validClass:"valid",errorElement:"label",focusCleanup:!1,focusInvalid:!0,errorContainer:n([]),errorLabelContainer:n([]),onsubmit:!0,ignore:":hidden",ignoreTitle:!1,onfocusin:function(n){this.lastActive=n;this.settings.focusCleanup&&(this.settings.unhighlight&&this.settings.unhighlight.call(this,n,this.settings.errorClass,this.settings.validClass),this.hideThese(this.errorsFor(n)))},onfocusout:function(n){!this.checkable(n)&&(n.name in this.submitted||!this.optional(n))&&this.element(n)},onkeyup:function(t,i){9===i.which&&""===this.elementValue(t)||-1!==n.inArray(i.keyCode,[16,17,18,20,35,36,37,38,39,40,45,144,225])||(t.name in this.submitted||t===this.lastElement)&&this.element(t)},onclick:function(n){n.name in this.submitted?this.element(n):n.parentNode.name in this.submitted&&this.element(n.parentNode)},highlight:function(t,i,r){"radio"===t.type?this.findByName(t.name).addClass(i).removeClass(r):n(t).addClass(i).removeClass(r)},unhighlight:function(t,i,r){"radio"===t.type?this.findByName(t.name).removeClass(i).addClass(r):n(t).removeClass(i).addClass(r)}},setDefaults:function(t){n.extend(n.validator.defaults,t)},messages:{required:"This field is required.",remote:"Please fix this field.",email:"Please enter a valid email address.",url:"Please enter a valid URL.",date:"Please enter a valid date.",dateISO:"Please enter a valid date ( ISO ).",number:"Please enter a valid number.",digits:"Please enter only digits.",creditcard:"Please enter a valid credit card number.",equalTo:"Please enter the same value again.",maxlength:n.validator.format("Please enter no more than {0} characters."),minlength:n.validator.format("Please enter at least {0} characters."),rangelength:n.validator.format("Please enter a value between {0} and {1} characters long."),range:n.validator.format("Please enter a value between {0} and {1}."),max:n.validator.format("Please enter a value less than or equal to {0}."),min:n.validator.format("Please enter a value greater than or equal to {0}.")},autoCreateRanges:!1,prototype:{init:function(){function i(t){var r=n.data(this.form,"validator"),u="on"+t.type.replace(/^validate/,""),i=r.settings;i[u]&&!n(this).is(i.ignore)&&i[u].call(r,this,t)}this.labelContainer=n(this.settings.errorLabelContainer);this.errorContext=this.labelContainer.length&&this.labelContainer||n(this.currentForm);this.containers=n(this.settings.errorContainer).add(this.settings.errorLabelContainer);this.submitted={};this.valueCache={};this.pendingRequest=0;this.pending={};this.invalid={};this.reset();var t,r=this.groups={};n.each(this.settings.groups,function(t,i){"string"==typeof i&&(i=i.split(/\s/));n.each(i,function(n,i){r[i]=t})});t=this.settings.rules;n.each(t,function(i,r){t[i]=n.validator.normalizeRule(r)});n(this.currentForm).on("focusin.validate focusout.validate keyup.validate",":text, [type='password'], [type='file'], select, textarea, [type='number'], [type='search'], [type='tel'], [type='url'], [type='email'], [type='datetime'], [type='date'], [type='month'], [type='week'], [type='time'], [type='datetime-local'], [type='range'], [type='color'], [type='radio'], [type='checkbox']",i).on("click.validate","select, option, [type='radio'], [type='checkbox']",i);this.settings.invalidHandler&&n(this.currentForm).on("invalid-form.validate",this.settings.invalidHandler);n(this.currentForm).find("[required], [data-rule-required], .required").attr("aria-required","true")},form:function(){return this.checkForm(),n.extend(this.submitted,this.errorMap),this.invalid=n.extend({},this.errorMap),this.valid()||n(this.currentForm).triggerHandler("invalid-form",[this]),this.showErrors(),this.valid()},checkForm:function(){this.prepareForm();for(var n=0,t=this.currentElements=this.elements();t[n];n++)this.check(t[n]);return this.valid()},element:function(t){var u=this.clean(t),i=this.validationTargetFor(u),r=!0;return this.lastElement=i,void 0===i?delete this.invalid[u.name]:(this.prepareElement(i),this.currentElements=n(i),r=this.check(i)!==!1,r?delete this.invalid[i.name]:this.invalid[i.name]=!0),n(t).attr("aria-invalid",!r),this.numberOfInvalids()||(this.toHide=this.toHide.add(this.containers)),this.showErrors(),r},showErrors:function(t){if(t){n.extend(this.errorMap,t);this.errorList=[];for(var i in t)this.errorList.push({message:t[i],element:this.findByName(i)[0]});this.successList=n.grep(this.successList,function(n){return!(n.name in t)})}this.settings.showErrors?this.settings.showErrors.call(this,this.errorMap,this.errorList):this.defaultShowErrors()},resetForm:function(){n.fn.resetForm&&n(this.currentForm).resetForm();this.submitted={};this.lastElement=null;this.prepareForm();this.hideErrors();var t,i=this.elements().removeData("previousValue").removeAttr("aria-invalid");if(this.settings.unhighlight)for(t=0;i[t];t++)this.settings.unhighlight.call(this,i[t],this.settings.errorClass,"");else i.removeClass(this.settings.errorClass)},numberOfInvalids:function(){return this.objectLength(this.invalid)},objectLength:function(n){var i,t=0;for(i in n)t++;return t},hideErrors:function(){this.hideThese(this.toHide)},hideThese:function(n){n.not(this.containers).text("");this.addWrapper(n).hide()},valid:function(){return 0===this.size()},size:function(){return this.errorList.length},focusInvalid:function(){if(this.settings.focusInvalid)try{n(this.findLastActive()||this.errorList.length&&this.errorList[0].element||[]).filter(":visible").focus().trigger("focusin")}catch(t){}},findLastActive:function(){var t=this.lastActive;return t&&1===n.grep(this.errorList,function(n){return n.element.name===t.name}).length&&t},elements:function(){var t=this,i={};return n(this.currentForm).find("input, select, textarea").not(":submit, :reset, :image, :disabled").not(this.settings.ignore).filter(function(){return!this.name&&t.settings.debug&&window.console&&console.error("%o has no name assigned",this),this.name in i||!t.objectLength(n(this).rules())?!1:(i[this.name]=!0,!0)})},clean:function(t){return n(t)[0]},errors:function(){var t=this.settings.errorClass.split(" ").join(".");return n(this.settings.errorElement+"."+t,this.errorContext)},reset:function(){this.successList=[];this.errorList=[];this.errorMap={};this.toShow=n([]);this.toHide=n([]);this.currentElements=n([])},prepareForm:function(){this.reset();this.toHide=this.errors().add(this.containers)},prepareElement:function(n){this.reset();this.toHide=this.errorsFor(n)},elementValue:function(t){var i,u=n(t),r=t.type;return"radio"===r||"checkbox"===r?this.findByName(t.name).filter(":checked").val():"number"===r&&"undefined"!=typeof t.validity?t.validity.badInput?!1:u.val():(i=u.val(),"string"==typeof i?i.replace(/\r/g,""):i)},check:function(t){t=this.validationTargetFor(this.clean(t));var r,u,i,f=n(t).rules(),s=n.map(f,function(n,t){return t}).length,o=!1,h=this.elementValue(t);for(u in f){i={method:u,parameters:f[u]};try{if(r=n.validator.methods[u].call(this,h,t,i.parameters),"dependency-mismatch"===r&&1===s){o=!0;continue}if(o=!1,"pending"===r)return void(this.toHide=this.toHide.not(this.errorsFor(t)));if(!r)return this.formatAndAdd(t,i),!1}catch(e){throw this.settings.debug&&window.console&&console.log("Exception occurred when checking element "+t.id+", check the '"+i.method+"' method.",e),e instanceof TypeError&&(e.message+=".  Exception occurred when checking element "+t.id+", check the '"+i.method+"' method."),e;}}if(!o)return this.objectLength(f)&&this.successList.push(t),!0},customDataMessage:function(t,i){return n(t).data("msg"+i.charAt(0).toUpperCase()+i.substring(1).toLowerCase())||n(t).data("msg")},customMessage:function(n,t){var i=this.settings.messages[n];return i&&(i.constructor===String?i:i[t])},findDefined:function(){for(var n=0;n<arguments.length;n++)if(void 0!==arguments[n])return arguments[n];return void 0},defaultMessage:function(t,i){return this.findDefined(this.customMessage(t.name,i),this.customDataMessage(t,i),!this.settings.ignoreTitle&&t.title||void 0,n.validator.messages[i],"<strong>Warning: No message defined for "+t.name+"<\/strong>")},formatAndAdd:function(t,i){var r=this.defaultMessage(t,i.method),u=/\$?\{(\d+)\}/g;"function"==typeof r?r=r.call(this,i.parameters,t):u.test(r)&&(r=n.validator.format(r.replace(u,"{$1}"),i.parameters));this.errorList.push({message:r,element:t,method:i.method});this.errorMap[t.name]=r;this.submitted[t.name]=r},addWrapper:function(n){return this.settings.wrapper&&(n=n.add(n.parent(this.settings.wrapper))),n},defaultShowErrors:function(){for(var i,t,n=0;this.errorList[n];n++)t=this.errorList[n],this.settings.highlight&&this.settings.highlight.call(this,t.element,this.settings.errorClass,this.settings.validClass),this.showLabel(t.element,t.message);if(this.errorList.length&&(this.toShow=this.toShow.add(this.containers)),this.settings.success)for(n=0;this.successList[n];n++)this.showLabel(this.successList[n]);if(this.settings.unhighlight)for(n=0,i=this.validElements();i[n];n++)this.settings.unhighlight.call(this,i[n],this.settings.errorClass,this.settings.validClass);this.toHide=this.toHide.not(this.toShow);this.hideErrors();this.addWrapper(this.toShow).show()},validElements:function(){return this.currentElements.not(this.invalidElements())},invalidElements:function(){return n(this.errorList).map(function(){return this.element})},showLabel:function(t,i){var u,o,e,r=this.errorsFor(t),s=this.idOrName(t),f=n(t).attr("aria-describedby");r.length?(r.removeClass(this.settings.validClass).addClass(this.settings.errorClass),r.html(i)):(r=n("<"+this.settings.errorElement+">").attr("id",s+"-error").addClass(this.settings.errorClass).html(i||""),u=r,this.settings.wrapper&&(u=r.hide().show().wrap("<"+this.settings.wrapper+"/>").parent()),this.labelContainer.length?this.labelContainer.append(u):this.settings.errorPlacement?this.settings.errorPlacement(u,n(t)):u.insertAfter(t),r.is("label")?r.attr("for",s):0===r.parents("label[for='"+s+"']").length&&(e=r.attr("id").replace(/(:|\.|\[|\]|\$)/g,"\\$1"),f?f.match(new RegExp("\\b"+e+"\\b"))||(f+=" "+e):f=e,n(t).attr("aria-describedby",f),o=this.groups[t.name],o&&n.each(this.groups,function(t,i){i===o&&n("[name='"+t+"']",this.currentForm).attr("aria-describedby",r.attr("id"))})));!i&&this.settings.success&&(r.text(""),"string"==typeof this.settings.success?r.addClass(this.settings.success):this.settings.success(r,t));this.toShow=this.toShow.add(r)},errorsFor:function(t){var r=this.idOrName(t),u=n(t).attr("aria-describedby"),i="label[for='"+r+"'], label[for='"+r+"'] *";return u&&(i=i+", #"+u.replace(/\s+/g,", #")),this.errors().filter(i)},idOrName:function(n){return this.groups[n.name]||(this.checkable(n)?n.name:n.id||n.name)},validationTargetFor:function(t){return this.checkable(t)&&(t=this.findByName(t.name)),n(t).not(this.settings.ignore)[0]},checkable:function(n){return/radio|checkbox/i.test(n.type)},findByName:function(t){return n(this.currentForm).find("[name='"+t+"']")},getLength:function(t,i){switch(i.nodeName.toLowerCase()){case"select":return n("option:selected",i).length;case"input":if(this.checkable(i))return this.findByName(i.name).filter(":checked").length}return t.length},depend:function(n,t){return this.dependTypes[typeof n]?this.dependTypes[typeof n](n,t):!0},dependTypes:{boolean:function(n){return n},string:function(t,i){return!!n(t,i.form).length},"function":function(n,t){return n(t)}},optional:function(t){var i=this.elementValue(t);return!n.validator.methods.required.call(this,i,t)&&"dependency-mismatch"},startRequest:function(n){this.pending[n.name]||(this.pendingRequest++,this.pending[n.name]=!0)},stopRequest:function(t,i){this.pendingRequest--;this.pendingRequest<0&&(this.pendingRequest=0);delete this.pending[t.name];i&&0===this.pendingRequest&&this.formSubmitted&&this.form()?(n(this.currentForm).submit(),this.formSubmitted=!1):!i&&0===this.pendingRequest&&this.formSubmitted&&(n(this.currentForm).triggerHandler("invalid-form",[this]),this.formSubmitted=!1)},previousValue:function(t){return n.data(t,"previousValue")||n.data(t,"previousValue",{old:null,valid:!0,message:this.defaultMessage(t,"remote")})},destroy:function(){this.resetForm();n(this.currentForm).off(".validate").removeData("validator")}},classRuleSettings:{required:{required:!0},email:{email:!0},url:{url:!0},date:{date:!0},dateISO:{dateISO:!0},number:{number:!0},digits:{digits:!0},creditcard:{creditcard:!0}},addClassRules:function(t,i){t.constructor===String?this.classRuleSettings[t]=i:n.extend(this.classRuleSettings,t)},classRules:function(t){var i={},r=n(t).attr("class");return r&&n.each(r.split(" "),function(){this in n.validator.classRuleSettings&&n.extend(i,n.validator.classRuleSettings[this])}),i},normalizeAttributeRule:function(n,t,i,r){/min|max/.test(i)&&(null===t||/number|range|text/.test(t))&&(r=Number(r),isNaN(r)&&(r=void 0));r||0===r?n[i]=r:t===i&&"range"!==t&&(n[i]=!0)},attributeRules:function(t){var r,i,u={},f=n(t),e=t.getAttribute("type");for(r in n.validator.methods)"required"===r?(i=t.getAttribute(r),""===i&&(i=!0),i=!!i):i=f.attr(r),this.normalizeAttributeRule(u,e,r,i);return u.maxlength&&/-1|2147483647|524288/.test(u.maxlength)&&delete u.maxlength,u},dataRules:function(t){var i,r,u={},f=n(t),e=t.getAttribute("type");for(i in n.validator.methods)r=f.data("rule"+i.charAt(0).toUpperCase()+i.substring(1).toLowerCase()),this.normalizeAttributeRule(u,e,i,r);return u},staticRules:function(t){var i={},r=n.data(t.form,"validator");return r.settings.rules&&(i=n.validator.normalizeRule(r.settings.rules[t.name])||{}),i},normalizeRules:function(t,i){return n.each(t,function(r,u){if(u===!1)return void delete t[r];if(u.param||u.depends){var f=!0;switch(typeof u.depends){case"string":f=!!n(u.depends,i.form).length;break;case"function":f=u.depends.call(i,i)}f?t[r]=void 0!==u.param?u.param:!0:delete t[r]}}),n.each(t,function(r,u){t[r]=n.isFunction(u)?u(i):u}),n.each(["minlength","maxlength"],function(){t[this]&&(t[this]=Number(t[this]))}),n.each(["rangelength","range"],function(){var i;t[this]&&(n.isArray(t[this])?t[this]=[Number(t[this][0]),Number(t[this][1])]:"string"==typeof t[this]&&(i=t[this].replace(/[\[\]]/g,"").split(/[\s,]+/),t[this]=[Number(i[0]),Number(i[1])]))}),n.validator.autoCreateRanges&&(null!=t.min&&null!=t.max&&(t.range=[t.min,t.max],delete t.min,delete t.max),null!=t.minlength&&null!=t.maxlength&&(t.rangelength=[t.minlength,t.maxlength],delete t.minlength,delete t.maxlength)),t},normalizeRule:function(t){if("string"==typeof t){var i={};n.each(t.split(/\s/),function(){i[this]=!0});t=i}return t},addMethod:function(t,i,r){n.validator.methods[t]=i;n.validator.messages[t]=void 0!==r?r:n.validator.messages[t];i.length<3&&n.validator.addClassRules(t,n.validator.normalizeRule(t))},methods:{required:function(t,i,r){if(!this.depend(r,i))return"dependency-mismatch";if("select"===i.nodeName.toLowerCase()){var u=n(i).val();return u&&u.length>0}return this.checkable(i)?this.getLength(t,i)>0:t.length>0},email:function(n,t){return this.optional(t)||/^[a-zA-Z0-9.!#$%&'*+\/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/.test(n)},url:function(n,t){return this.optional(t)||/^(?:(?:(?:https?|ftp):)?\/\/)(?:\S+(?::\S*)?@)?(?:(?!(?:10|127)(?:\.\d{1,3}){3})(?!(?:169\.254|192\.168)(?:\.\d{1,3}){2})(?!172\.(?:1[6-9]|2\d|3[0-1])(?:\.\d{1,3}){2})(?:[1-9]\d?|1\d\d|2[01]\d|22[0-3])(?:\.(?:1?\d{1,2}|2[0-4]\d|25[0-5])){2}(?:\.(?:[1-9]\d?|1\d\d|2[0-4]\d|25[0-4]))|(?:(?:[a-z\u00a1-\uffff0-9]-*)*[a-z\u00a1-\uffff0-9]+)(?:\.(?:[a-z\u00a1-\uffff0-9]-*)*[a-z\u00a1-\uffff0-9]+)*(?:\.(?:[a-z\u00a1-\uffff]{2,})).?)(?::\d{2,5})?(?:[/?#]\S*)?$/i.test(n)},date:function(n,t){return this.optional(t)||!/Invalid|NaN/.test(new Date(n).toString())},dateISO:function(n,t){return this.optional(t)||/^\d{4}[\/\-](0?[1-9]|1[012])[\/\-](0?[1-9]|[12][0-9]|3[01])$/.test(n)},number:function(n,t){return this.optional(t)||/^(?:-?\d+|-?\d{1,3}(?:,\d{3})+)?(?:\.\d+)?$/.test(n)},digits:function(n,t){return this.optional(t)||/^\d+$/.test(n)},creditcard:function(n,t){if(this.optional(t))return"dependency-mismatch";if(/[^0-9 \-]+/.test(n))return!1;var i,f,e=0,r=0,u=!1;if(n=n.replace(/\D/g,""),n.length<13||n.length>19)return!1;for(i=n.length-1;i>=0;i--)f=n.charAt(i),r=parseInt(f,10),u&&(r*=2)>9&&(r-=9),e+=r,u=!u;return e%10==0},minlength:function(t,i,r){var u=n.isArray(t)?t.length:this.getLength(t,i);return this.optional(i)||u>=r},maxlength:function(t,i,r){var u=n.isArray(t)?t.length:this.getLength(t,i);return this.optional(i)||r>=u},rangelength:function(t,i,r){var u=n.isArray(t)?t.length:this.getLength(t,i);return this.optional(i)||u>=r[0]&&u<=r[1]},min:function(n,t,i){return this.optional(t)||n>=i},max:function(n,t,i){return this.optional(t)||i>=n},range:function(n,t,i){return this.optional(t)||n>=i[0]&&n<=i[1]},equalTo:function(t,i,r){var u=n(r);return this.settings.onfocusout&&u.off(".validate-equalTo").on("blur.validate-equalTo",function(){n(i).valid()}),t===u.val()},remote:function(t,i,r){if(this.optional(i))return"dependency-mismatch";var u,e,f=this.previousValue(i);return this.settings.messages[i.name]||(this.settings.messages[i.name]={}),f.originalMessage=this.settings.messages[i.name].remote,this.settings.messages[i.name].remote=f.message,r="string"==typeof r&&{url:r}||r,f.old===t?f.valid:(f.old=t,u=this,this.startRequest(i),e={},e[i.name]=t,n.ajax(n.extend(!0,{mode:"abort",port:"validate"+i.name,dataType:"json",data:e,context:u.currentForm,success:function(r){var o,e,h,s=r===!0||"true"===r;u.settings.messages[i.name].remote=f.originalMessage;s?(h=u.formSubmitted,u.prepareElement(i),u.formSubmitted=h,u.successList.push(i),delete u.invalid[i.name],u.showErrors()):(o={},e=r||u.defaultMessage(i,"remote"),o[i.name]=f.message=n.isFunction(e)?e(t):e,u.invalid[i.name]=!0,u.showErrors(o));f.valid=s;u.stopRequest(i,s)}},r)),"pending")}}});var i,t={};n.ajaxPrefilter?n.ajaxPrefilter(function(n,i,r){var u=n.port;"abort"===n.mode&&(t[u]&&t[u].abort(),t[u]=r)}):(i=n.ajax,n.ajax=function(r){var f=("mode"in r?r:n.ajaxSettings).mode,u=("port"in r?r:n.ajaxSettings).port;return"abort"===f?(t[u]&&t[u].abort(),t[u]=i.apply(this,arguments),t[u]):i.apply(this,arguments)})});!function(n){function i(n,t,i){n.rules[t]=i;n.message&&(n.messages[t]=n.message)}function h(n){return n.replace(/^\s+|\s+$/g,"").split(/\s*,\s*/g)}function f(n){return n.replace(/([!"#$%&'()*+,./:;<=>?@\[\\\]^`{|}~])/g,"\\$1")}function e(n){return n.substr(0,n.lastIndexOf(".")+1)}function o(n,t){return 0===n.indexOf("*.")&&(n=n.replace("*.",t)),n}function c(t,i){var r=n(this).find("[data-valmsg-for='"+f(i[0].name)+"']"),u=r.attr("data-valmsg-replace"),e=u?n.parseJSON(u)!==!1:null;r.removeClass("field-validation-valid").addClass("field-validation-error");t.data("unobtrusiveContainer",r);e?(r.empty(),t.removeClass("input-validation-error").appendTo(r)):t.hide()}function l(t,i){var u=n(this).find("[data-valmsg-summary=true]"),r=u.find("ul");r&&r.length&&i.errorList.length&&(r.empty(),u.addClass("validation-summary-errors").removeClass("validation-summary-valid"),n.each(i.errorList,function(){n("<li />").html(this.message).appendTo(r)}))}function a(t){var i=t.data("unobtrusiveContainer"),r,u;i&&(r=i.attr("data-valmsg-replace"),u=r?n.parseJSON(r):null,i.addClass("field-validation-valid").removeClass("field-validation-error"),t.removeData("unobtrusiveContainer"),u&&i.empty())}function v(){var t=n(this),i="__jquery_unobtrusive_validation_form_reset";if(!t.data(i)){t.data(i,!0);try{t.data("validator").resetForm()}finally{t.removeData(i)}t.find(".validation-summary-errors").addClass("validation-summary-valid").removeClass("validation-summary-errors");t.find(".field-validation-error").addClass("field-validation-valid").removeClass("field-validation-error").removeData("unobtrusiveContainer").find(">*").removeData("unobtrusiveContainer")}}function s(t){var i=n(t),f=i.data(u),s=n.proxy(v,t),e=r.unobtrusive.options||{},o=function(i,r){var u=e[i];u&&n.isFunction(u)&&u.apply(t,r)};return f||(f={options:{errorClass:e.errorClass||"input-validation-error",errorElement:e.errorElement||"span",errorPlacement:function(){c.apply(t,arguments);o("errorPlacement",arguments)},invalidHandler:function(){l.apply(t,arguments);o("invalidHandler",arguments)},messages:{},rules:{},success:function(){a.apply(t,arguments);o("success",arguments)}},attachValidation:function(){i.off("reset."+u,s).on("reset."+u,s).validate(this.options)},validate:function(){return i.validate(),i.valid()}},i.data(u,f)),f}var t,r=n.validator,u="unobtrusiveValidation";r.unobtrusive={adapters:[],parseElement:function(t,i){var r,u,o,f=n(t),e=f.parents("form")[0];e&&(r=s(e),r.options.rules[t.name]=u={},r.options.messages[t.name]=o={},n.each(this.adapters,function(){var i="data-val-"+this.name,r=f.attr(i),s={};void 0!==r&&(i+="-",n.each(this.params,function(){s[this]=f.attr(i+this)}),this.adapt({element:t,form:e,message:r,params:s,rules:u,messages:o}))}),n.extend(u,{__dummy__:!0}),i||r.attachValidation())},parse:function(t){var i=n(t),u=i.parents().addBack().filter("form").add(i.find("form")).has("[data-val=true]");i.find("[data-val=true]").each(function(){r.unobtrusive.parseElement(this,!0)});u.each(function(){var n=s(this);n&&n.attachValidation()})}};t=r.unobtrusive.adapters;t.add=function(n,t,i){return i||(i=t,t=[]),this.push({name:n,params:t,adapt:i}),this};t.addBool=function(n,t){return this.add(n,function(r){i(r,t||n,!0)})};t.addMinMax=function(n,t,r,u,f,e){return this.add(n,[f||"min",e||"max"],function(n){var f=n.params.min,e=n.params.max;f&&e?i(n,u,[f,e]):f?i(n,t,f):e&&i(n,r,e)})};t.addSingleVal=function(n,t,r){return this.add(n,[t||"val"],function(u){i(u,r||n,u.params[t])})};r.addMethod("__dummy__",function(){return!0});r.addMethod("regex",function(n,t,i){var r;return this.optional(t)?!0:(r=new RegExp(i).exec(n),r&&0===r.index&&r[0].length===n.length)});r.addMethod("nonalphamin",function(n,t,i){var r;return i&&(r=n.match(/\W/g),r=r&&r.length>=i),r});r.methods.extension?(t.addSingleVal("accept","mimtype"),t.addSingleVal("extension","extension")):t.addSingleVal("extension","extension","accept");t.addSingleVal("regex","pattern");t.addBool("creditcard").addBool("date").addBool("digits").addBool("email").addBool("number").addBool("url");t.addMinMax("length","minlength","maxlength","rangelength").addMinMax("range","min","max","range");t.addMinMax("minlength","minlength").addMinMax("maxlength","minlength","maxlength");t.add("equalto",["other"],function(t){var r=e(t.element.name),u=t.params.other,s=o(u,r),h=n(t.form).find(":input").filter("[name='"+f(s)+"']")[0];i(t,"equalTo",h)});t.add("required",function(n){("INPUT"!==n.element.tagName.toUpperCase()||"CHECKBOX"!==n.element.type.toUpperCase())&&i(n,"required",!0)});t.add("remote",["url","type","additionalfields"],function(t){var r={url:t.params.url,type:t.params.type||"GET",data:{}},u=e(t.element.name);n.each(h(t.params.additionalfields||t.element.name),function(i,e){var s=o(e,u);r.data[s]=function(){var i=n(t.form).find(":input").filter("[name='"+f(s)+"']");return i.is(":checkbox")?i.filter(":checked").val()||i.filter(":hidden").val()||"":i.is(":radio")?i.filter(":checked").val()||"":i.val()}});i(t,"remote",r)});t.add("password",["min","nonalphamin","regex"],function(n){n.params.min&&i(n,"minlength",n.params.min);n.params.nonalphamin&&i(n,"nonalphamin",n.params.nonalphamin);n.params.regex&&i(n,"regex",n.params.regex)});n(function(){r.unobtrusive.parse(document)})}(jQuery);
/*! medium-zoom 1.0.3 | MIT License | https://github.com/francoischalifour/medium-zoom */
!function(n,t){"object"==typeof exports&&"undefined"!=typeof module?module.exports=t():"function"==typeof define&&define.amd?define(t):n.mediumZoom=t()}(this,function(){"use strict";var n=Object.assign||function(n){for(var i,r,t=1;t<arguments.length;t++){i=arguments[t];for(r in i)Object.prototype.hasOwnProperty.call(i,r)&&(n[r]=i[r])}return n},i=function(n){return"IMG"===n.tagName},r=function(n){return n&&1===n.nodeType},u=function(n){return".svg"===(n.currentSrc||n.src).substr(-4).toLowerCase()},f=function(n){try{return Array.isArray(n)?n.filter(i):(t=n,NodeList.prototype.isPrototypeOf(t)?[].slice.call(n).filter(i):r(n)?[n].filter(i):"string"==typeof n?[].slice.call(document.querySelectorAll(n)).filter(i):[])}catch(n){throw new TypeError("The provided selector is invalid.\nExpects a CSS selector, a Node element, a NodeList or an array.\nSee: https://github.com/francoischalifour/medium-zoom");}var t},t=function(t,i){var r=n({bubbles:!1,cancelable:!1,detail:void 0},i),u;return"function"==typeof CustomEvent?new CustomEvent(t,r):(u=document.createEvent("CustomEvent"),u.initCustomEvent(t,r.bubbles,r.cancelable,r.detail),u)},e=window.Promise||function(n){function t(){}n(t,t)};return function(n,t){var u,r,i;void 0===t&&(t={});u=t.insertAt;n&&"undefined"!=typeof document&&(r=document.head||document.getElementsByTagName("head")[0],i=document.createElement("style"),i.type="text/css","top"===u&&r.firstChild?r.insertBefore(i,r.firstChild):r.appendChild(i),i.styleSheet?i.styleSheet.cssText=n:i.appendChild(document.createTextNode(n)))}(".medium-zoom-overlay{position:fixed;top:0;right:0;bottom:0;left:0;opacity:0;transition:opacity .3s;will-change:opacity}.medium-zoom--opened .medium-zoom-overlay{cursor:pointer;cursor:zoom-out;opacity:1}.medium-zoom-image{cursor:pointer;cursor:zoom-in;transition:transform .3s cubic-bezier(.2,0,.2,1)}.medium-zoom-image--hidden{visibility:hidden}.medium-zoom-image--opened{position:relative;cursor:pointer;cursor:zoom-out;will-change:transform}"),function o(i){var tt=1<arguments.length&&void 0!==arguments[1]?arguments[1]:{},b=function(){for(var t,i=arguments.length,r=Array(i),n=0;n<i;n++)r[n]=arguments[n];return t=r.reduce(function(n,t){return[].concat(n,f(t))},[]),t.filter(function(n){return-1===l.indexOf(n)}).forEach(function(n){l.push(n);n.classList.add("medium-zoom-image")}),p.forEach(function(n){var i=n.type,r=n.listener,u=n.options;t.forEach(function(n){n.addEventListener(i,r,u)})}),c},k=function(){var i=(0<arguments.length&&void 0!==arguments[0]?arguments[0]:{}).target,f=function(){var t={width:document.documentElement.clientWidth,height:document.documentElement.clientHeight,left:0,top:0,right:0,bottom:0},i=void 0,f=void 0;if(h.container)if(h.container instanceof Object)i=(t=n({},t,h.container)).width-t.left-t.right-2*h.margin,f=t.height-t.top-t.bottom-2*h.margin;else{var o=(r(h.container)?h.container:document.querySelector(h.container)).getBoundingClientRect(),p=o.width,w=o.height,b=o.left,k=o.top;t=n({},t,{width:p,height:w,left:b,top:k})}i=i||t.width-2*h.margin;f=f||t.height-2*h.margin;var e=s.zoomedHd||s.original,d=u(e)?i:e.naturalWidth||i,g=u(e)?f:e.naturalHeight||f,c=e.getBoundingClientRect(),nt=c.top,tt=c.left,a=c.width,v=c.height,it=Math.min(d,i)/a,rt=Math.min(g,f)/v,l=Math.min(it,rt),y="scale("+l+") translate3d("+((i-a)/2-tt+h.margin+t.left)/l+"px, "+((f-v)/2-nt+h.margin+t.top)/l+"px, 0)";s.zoomed.style.transform=y;s.zoomedHd&&(s.zoomedHd.style.transform=y)};return new e(function(n){var o,p,e,b,k,d,nt,u,tt,it,rt,w,ut;if(i&&-1===l.indexOf(i))n(c);else if(s.zoomed)n(c);else{if(i)s.original=i;else{if(!(0<l.length))return void n(c);o=l;s.original=o[0]}(s.original.dispatchEvent(t("medium-zoom:open",{detail:{zoom:c}})),g=window.pageYOffset||document.documentElement.scrollTop||document.body.scrollTop||0,v=!0,s.zoomed=(p=s.original,e=p.getBoundingClientRect(),b=e.top,k=e.left,d=e.width,nt=e.height,u=p.cloneNode(),tt=window.pageYOffset||document.documentElement.scrollTop||document.body.scrollTop||0,it=window.pageXOffset||document.documentElement.scrollLeft||document.body.scrollLeft||0,u.removeAttribute("id"),u.style.position="absolute",u.style.top=b+tt+"px",u.style.left=k+it+"px",u.style.width=d+"px",u.style.height=nt+"px",u.style.transform="",u),document.body.appendChild(y),h.template)&&(rt=r(h.template)?h.template:document.querySelector(h.template),s.template=document.createElement("div"),s.template.appendChild(rt.content.cloneNode(!0)),document.body.appendChild(s.template));(document.body.appendChild(s.zoomed),window.requestAnimationFrame(function(){document.body.classList.add("medium-zoom--opened")}),s.original.classList.add("medium-zoom-image--hidden"),s.zoomed.classList.add("medium-zoom-image--opened"),s.zoomed.addEventListener("click",a),s.zoomed.addEventListener("transitionend",function o(){v=!1;s.zoomed.removeEventListener("transitionend",o);s.original.dispatchEvent(t("medium-zoom:opened",{detail:{zoom:c}}));n(c)}),s.original.getAttribute("data-zoom-src"))?(s.zoomedHd=s.zoomed.cloneNode(),s.zoomedHd.removeAttribute("srcset"),s.zoomedHd.removeAttribute("sizes"),s.zoomedHd.src=s.zoomed.getAttribute("data-zoom-src"),s.zoomedHd.onerror=function(){clearInterval(w);console.warn("Unable to reach the zoom image target "+s.zoomedHd.src);s.zoomedHd=null;f()},w=setInterval(function(){s.zoomedHd.complete&&(clearInterval(w),s.zoomedHd.classList.add("medium-zoom-image--opened"),s.zoomedHd.addEventListener("click",a),document.body.appendChild(s.zoomedHd),f())},10)):s.original.hasAttribute("srcset")?(s.zoomedHd=s.zoomed.cloneNode(),s.zoomedHd.removeAttribute("sizes"),ut=s.zoomedHd.addEventListener("load",function(){s.zoomedHd.removeEventListener("load",ut);s.zoomedHd.classList.add("medium-zoom-image--opened");s.zoomedHd.addEventListener("click",a);document.body.appendChild(s.zoomedHd);f()})):f()}})},a=function(){return new e(function(n){!v&&s.original?(v=!0,document.body.classList.remove("medium-zoom--opened"),s.zoomed.style.transform="",s.zoomedHd&&(s.zoomedHd.style.transform=""),s.template&&(s.template.style.transition="opacity 150ms",s.template.style.opacity=0),s.original.dispatchEvent(t("medium-zoom:close",{detail:{zoom:c}})),s.zoomed.addEventListener("transitionend",function i(){s.original.classList.remove("medium-zoom-image--hidden");document.body.removeChild(s.zoomed);s.zoomedHd&&document.body.removeChild(s.zoomedHd);document.body.removeChild(y);s.zoomed.classList.remove("medium-zoom-image--opened");s.template&&document.body.removeChild(s.template);v=!1;s.zoomed.removeEventListener("transitionend",i);s.original.dispatchEvent(t("medium-zoom:closed",{detail:{zoom:c}}));s.original=null;s.zoomed=null;s.zoomedHd=null;s.template=null;n(c)})):n(c)})},d=function(){var n=(0<arguments.length&&void 0!==arguments[0]?arguments[0]:{}).target;return s.original?a():k({target:n})},l=[],p=[],v=!1,g=0,h=tt,s={original:null,zoomed:null,zoomedHd:null,template:null},nt,w,y,c;return"[object Object]"===Object.prototype.toString.call(i)?h=i:(i||"string"==typeof i)&&b(i),h=n({margin:0,background:"#fff",scrollOffset:40,container:null,template:null},h),y=(nt=h.background,(w=document.createElement("div")).classList.add("medium-zoom-overlay"),w.style.background=nt,w),document.addEventListener("click",function(n){var t=n.target;t!==y?-1!==l.indexOf(t)&&d({target:t}):a()}),document.addEventListener("keyup",function(n){27===(n.keyCode||n.which)&&a()}),document.addEventListener("scroll",function(){if(!v&&s.original){var n=window.pageYOffset||document.documentElement.scrollTop||document.body.scrollTop||0;Math.abs(g-n)>h.scrollOffset&&setTimeout(a,150)}}),window.addEventListener("resize",a),c={open:k,close:a,toggle:d,update:function(){var i=0<arguments.length&&void 0!==arguments[0]?arguments[0]:{},u=i,f;return(i.background&&(y.style.background=i.background),i.container&&i.container instanceof Object&&(u.container=n({},h.container,i.container)),i.template)&&(f=r(i.template)?i.template:document.querySelector(i.template),u.template=f),h=n({},h,u),l.forEach(function(n){n.dispatchEvent(t("medium-zoom:update",{detail:{zoom:c}}))}),c},clone:function(){var t=0<arguments.length&&void 0!==arguments[0]?arguments[0]:{};return o(n({},h,t))},attach:b,detach:function(){for(var r,u=arguments.length,i=Array(u),n=0;n<u;n++)i[n]=arguments[n];return s.zoomed&&a(),r=0<i.length?i.reduce(function(n,t){return[].concat(n,f(t))},[]):l,r.forEach(function(n){n.classList.remove("medium-zoom-image");n.dispatchEvent(t("medium-zoom:detach",{detail:{zoom:c}}))}),l=l.filter(function(n){return-1===r.indexOf(n)}),c},on:function(n,t){var i=2<arguments.length&&void 0!==arguments[2]?arguments[2]:{};return l.forEach(function(r){r.addEventListener("medium-zoom:"+n,t,i)}),p.push({type:"medium-zoom:"+n,listener:t,options:i}),c},off:function(n,t){var i=2<arguments.length&&void 0!==arguments[2]?arguments[2]:{};return l.forEach(function(r){r.removeEventListener("medium-zoom:"+n,t,i)}),p=p.filter(function(i){return!(i.type==="medium-zoom:"+n&&i.listener.toString()===t.toString())}),c},getOptions:function(){return h},getImages:function(){return l},getZoomedImage:function(){return s.original}}}});!function(n){var t={};n.cookieMessage=function(i){"true"!=function(n){for(var r,u,i=document.cookie.split(";"),t=0;t<i.length;t++)if(r=i[t].substr(0,i[t].indexOf("=")),u=i[t].substr(i[t].indexOf("=")+1),(r=r.replace(/^\s+|\s+$/g,""))==n)return unescape(u)}((t=n.extend({},{mainMessage:"",acceptButton:"Aceptar",expirationDays:20,backgroundColor:"#666",fontSize:"14px",fontColor:"white",btnBackgroundColor:"#f2a920",btnFontSize:"14px",btnFontColor:"white",linkFontColor:"#ffff00",cookieName:"cookieMessage"},i)).cookieName)&&n(document).ready(function(){var i;i='<div id="cookie-msg"><span class="msg">'+t.mainMessage+'<p class="btn-cook-wrap"><a href="" class="btn-aceptar">'+t.acceptButton+"<\/a><\/p><\/span><\/div>";n("body").append(i);n("#cookie-msg").css({position:"fixed",bottom:"0",width:"100%","text-align":"center","background-color":t.backgroundColor,color:t.fontColor});n("#cookie-msg a").css({});n("#cookie-msg a.btn-aceptar").css({padding:"3px 10px","border-radius":"20px","background-color":t.btnBackgroundColor,"text-decoration":"none"});n("#cookie-msg a.btn-aceptar").on("click",function(){return function(n,t,i){var r=new Date,u;r.setDate(r.getDate()+i);u=escape(t)+(null==i?"":"; expires="+r.toUTCString());document.cookie=n+"="+u+"; path=/"}(t.cookieName,!0,t.expirationDays),n("#cookie-msg").remove(),!1})})}}(jQuery);SocialShare={btns:[{social:"share",link:"",iconClass:"fas fa-share-alt",content:" Share","class":"ss-btn ss-btn-share",color:"#444"},{social:"facebook",link:"https://www.facebook.com/sharer.php?u=",iconClass:"fab fa-facebook-square",content:" Facebook","class":"ss-btn ss-btn-facebook",color:"#3b5998"},{social:"twitter",link:"https://twitter.com/share?url=",iconClass:"fab fa-twitter",content:" Twitter","class":"ss-btn ss-btn-twitter",color:"#1da1f2"},{social:"pinterest",link:"https://pinterest.com/pin/create/bookmarklet/?url=",iconClass:"fab fa-pinterest",content:" Pinterest","class":"ss-btn ss-btn-pinterest",color:"#bd081c"},{social:"linkedin",link:"https://www.linkedin.com/shareArticle?url=",iconClass:"fab fa-linkedin-in",content:" LinkedIn","class":"ss-btn ss-btn-linkedin",color:"#007bb5"},{social:"messenger",link:"fb-messenger://share/?link=",iconClass:"fab fa-facebook-messenger",content:" Messenger","class":"ss-btn ss-btn-messenger",color:"#0078FF"},{social:"whatsapp",link:"https://wa.me/?text=",iconClass:"fab fa-whatsapp",content:" WhatsApp","class":"ss-btn ss-btn-whatsapp",color:"#25d366"},{social:"viber",link:"viber://forward?text=",iconClass:"fab fa-viber",content:" Viber","class":"ss-btn ss-btn-viber",color:"#665CAC"},{social:"telegram",link:"tg://msg?text=",iconClass:"fab fa-telegram",content:" Telegram","class":"ss-btn ss-btn-telegram",color:"#0088CC"},{social:"tumblr",link:"https://www.tumblr.com/share/link?url=",iconClass:"fab fa-tumblr",content:" Tumblr","class":"ss-btn ss-btn-tumblr",color:"#35465d"},{social:"reddit",link:"https://reddit.com/submit?url=",iconClass:"fab fa-reddit",content:" Reddit","class":"ss-btn ss-btn-reddit",color:"#ff4500"},{social:"pocket",link:"https://getpocket.com/save?url=",iconClass:"fab fa-get-pocket",content:" Pocket","class":"ss-btn ss-btn-pocket",color:"#ee4056"},{social:"email",link:"mailto:?body=",iconClass:"fas fa-envelope",content:" Email","class":"ss-btn ss-btn-email",color:"#f04a43"},{social:"sms",link:"sms://?body=",iconClass:"fas fa-sms",content:" SMS","class":"ss-btn ss-btn-sms",color:"#1ba5e6"}],init:function(n){var t,i,r,h,c,o,u,l,a,v,s,f,y,p,w,b,k;for(SocialShare.boxs=document.querySelectorAll(n),t=0;t<SocialShare.boxs.length;t++)for(i=0;i<SocialShare.btns.length;i++){var d=SocialShare.boxs[t].getAttribute("data-ss-social"),g=SocialShare.boxs[t].getAttribute("data-ss-link"),e=window.location.href;if(g&&(e=g),r="",h=SocialShare.boxs[t].getAttribute("data-ss-"+SocialShare.btns[i].social),h)for(c=h.split(","),o=0;o<c.length;o++)(f=c[o].split(":")).length>1&&(r+=""==r?"":"&",r+=f[0].trim()+"="+encodeURIComponent(f[1].trim()));if(""!=r&&(r=(SocialShare.btns[i].link.includes("?")?"&":"?")+r),!d||d.toLowerCase().includes(SocialShare.btns[i].social)){if(u=document.createElement("a"),u.setAttribute("class",SocialShare.btns[i].class),u.setAttribute("href",SocialShare.btns[i].link+encodeURIComponent(e)+r),u.setAttribute("target","_blank"),u.style.background=SocialShare.btns[i].color,l=SocialShare.btns[i].iconClass,a=SocialShare.boxs[t].getAttribute("data-ss-icon-class"),a)for(v=a.split(","),s=0;s<v.length;s++)(f=v[s].split(":")).length>1&&f[0].trim().toLowerCase()==SocialShare.btns[i].social&&(l=f[1].trim());y=document.createElement("i");y.setAttribute("class",l);p=!0;w=SocialShare.boxs[t].getAttribute("data-ss-icon");w&&"false"==w&&(p=!1);p&&u.appendChild(y);b=!0;k=SocialShare.boxs[t].getAttribute("data-ss-content");k&&"false"==k&&(b=!1);b&&u.append(SocialShare.btns[i].content);SocialShare.boxs[t].appendChild(u)}}for(SocialShare.btnShares=document.querySelectorAll(".ss-btn-share"),t=0;t<SocialShare.btnShares.length;t++)e=decodeURIComponent(SocialShare.btnShares[t].getAttribute("href")),SocialShare.btnShares[t].removeAttribute("href"),SocialShare.btnShares[t].removeAttribute("target"),SocialShare.btnShares[t].setAttribute("data-ss-link",e),SocialShare.btnShares[t].addEventListener("click",function(){navigator.share?navigator.share({url:this.getAttribute("data-ss-link")}).then(()=>{console.log("Thanks for sharing!")}).catch(console.error):console.log("This brownser dont support native web share!")})},createShareBox:function(n,t=null,i=null,r=!0,u=!0,f=!0){for(var o=document.querySelectorAll(n),e=0;e<o.length;e++)o[e].classList.contains("ss-box")||o[e].classList.add("ss-box"),t&&o[e].setAttribute("data-ss-link",t),i&&o[e].setAttribute("data-ss-social",i),r||o[e].setAttribute("data-ss-icon",r),u||o[e].setAttribute("data-ss-content",u),f&&(o[e].innerHTML="");SocialShare.init(n)}};window.addEventListener("load",function(){SocialShare.init(".ss-box")}),function(){var n=!1,t={captchaCallback:function(){n=!0;$("#CaptchaValidateLabel").fadeOut()},bindSubmitHandler:function(){$("#ContactForm").submit(function(t){if(n)$("#CaptchaValidateLabel").fadeOut();else return t.preventDefault(),$("#CaptchaValidateLabel").fadeIn(),!1})}};window.ContactForm=t}();$(".input-file").on("change",function(){var t=$(this).val().toLowerCase(),n=isAllowedFileType(t);return $("#CV-error").text(n?"":"Send your CV using PDF or Word formats"),n});$("#file-trigger, .attach-file").on("click",function(){$("#file-trigger").focus();$(".input-file").trigger("click")});$("#file-trigger").on("keypress",function(n){n.preventDefault()});$(".input-file").on("change",function(){$("#file-trigger").val($("#CV")[0].files[0].name);$(".attach-file").css("display","none");$(".delete-file").css("display","block")});$(".delete-file").on("click",function(){$("#file-trigger").focus();$("#CV-error").text("");$("#file-trigger").val("");$("#CV").val("");$(".attach-file").css("display","block");$(".delete-file").css("display","none")});$("#ApplyForm").submit(function(){var i=$("#ApplyForm"),n=new FormData($("#ApplyForm")[0]),t=$("#CV")[0].files[0];if(i.valid()){if(typeof t!="undefined"&&!isAllowedFileType(t.name)){contactMessageFailure();return}n.append("CV",t);n.append("Position",getPosition());$.ajax({url:i.attr("action"),type:"POST",data:n,contentType:!1,processData:!1,success:function(){contactMessageSuccess()},error:function(){contactMessageFailure()}})}});getPosition=function(){return $("#carriers .nav-pills .active").find(".carrier-text").text().trim()};$("#carriers .hr-contact-btn").on("click",function(){var n=getPosition();$("#position-header").text(n)});var HEADER_HEIGHT=0,isIOS=/iPhone|iPad|iPod/i.test(navigator.userAgent),grecaptchaPosition,isScrolledIntoView=function(n){var t=n.getBoundingClientRect();return t.top-HEADER_HEIGHT>=0&&t.bottom<=window.innerHeight};isIOS&&(recaptchaElements=document.querySelectorAll(".g-recaptcha"),window.addEventListener("scroll",function(){Array.prototype.forEach.call(recaptchaElements,function(n){isScrolledIntoView(n)&&(grecaptchaPosition=document.documentElement.scrollTop||document.body.scrollTop)})},!1));onReturnCaptchaCallback=function(){ContactForm.captchaCallback();$("#ContactForm, #ApplyForm").find("button[type=submit]").prop("disabled",!1);isIOS&&grecaptchaPosition!==undefined&&window.scrollTo(0,grecaptchaPosition)};onRecaptchaExpired=function(){ContactForm.captchaCallback();$("#ContactForm,  #ApplyForm").find("button[type=submit]").prop("disabled",!0)};$(document).ready(function(){ContactForm.bindSubmitHandler()});
/*! Lazy Load 1.9.7 - MIT license - Copyright 2010-2015 Mika Tuupola */
!function(n,t,i,r){var u=n(t);n.fn.lazyload=function(f){function s(){var t=0;o.each(function(){var i=n(this);if((!e.skip_invisible||i.is(":visible"))&&!n.abovethetop(this,e)&&!n.leftofbegin(this,e))if(n.belowthefold(this,e)||n.rightoffold(this,e)){if(++t>e.failure_limit)return!1}else i.trigger("appear"),t=0})}var h,o=this,e={threshold:0,failure_limit:0,event:"scroll",effect:"show",container:t,data_attribute:"original",skip_invisible:!1,appear:null,load:null,placeholder:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsQAAA7EAZUrDhsAAAANSURBVBhXYzh8+PB/AAffA0nNPuCLAAAAAElFTkSuQmCC"};return f&&(r!==f.failurelimit&&(f.failure_limit=f.failurelimit,delete f.failurelimit),r!==f.effectspeed&&(f.effect_speed=f.effectspeed,delete f.effectspeed),n.extend(e,f)),h=e.container===r||e.container===t?u:n(e.container),0===e.event.indexOf("scroll")&&h.bind(e.event,function(){return s()}),this.each(function(){var i=this,t=n(i);i.loaded=!1;(t.attr("src")===r||t.attr("src")===!1)&&t.is("img")&&t.attr("src",e.placeholder);t.one("appear",function(){if(!this.loaded){if(e.appear){var r=o.length;e.appear.call(i,r,e)}n("<img />").bind("load",function(){var r=t.attr("data-"+e.data_attribute),u,f;t.hide();t.is("img")?t.attr("src",r):t.css("background-image","url('"+r+"')");t[e.effect](e.effect_speed);i.loaded=!0;u=n.grep(o,function(n){return!n.loaded});(o=n(u),e.load)&&(f=o.length,e.load.call(i,f,e))}).attr("src",t.attr("data-"+e.data_attribute))}});0!==e.event.indexOf("scroll")&&t.bind(e.event,function(){i.loaded||t.trigger("appear")})}),u.bind("resize",function(){s()}),/(?:iphone|ipod|ipad).*os 5/gi.test(navigator.appVersion)&&u.bind("pageshow",function(t){t.originalEvent&&t.originalEvent.persisted&&o.each(function(){n(this).trigger("appear")})}),n(i).ready(function(){s()}),this};n.belowthefold=function(i,f){var e;return e=f.container===r||f.container===t?(t.innerHeight?t.innerHeight:u.height())+u.scrollTop():n(f.container).offset().top+n(f.container).height(),e<=n(i).offset().top-f.threshold};n.rightoffold=function(i,f){var e;return e=f.container===r||f.container===t?u.width()+u.scrollLeft():n(f.container).offset().left+n(f.container).width(),e<=n(i).offset().left-f.threshold};n.abovethetop=function(i,f){var e;return e=f.container===r||f.container===t?u.scrollTop():n(f.container).offset().top,e>=n(i).offset().top+f.threshold+n(i).height()};n.leftofbegin=function(i,f){var e;return e=f.container===r||f.container===t?u.scrollLeft():n(f.container).offset().left,e>=n(i).offset().left+f.threshold+n(i).width()};n.inviewport=function(t,i){return!(n.rightoffold(t,i)||n.leftofbegin(t,i)||n.belowthefold(t,i)||n.abovethetop(t,i))};n.extend(n.expr[":"],{"below-the-fold":function(t){return n.belowthefold(t,{threshold:0})},"above-the-top":function(t){return!n.belowthefold(t,{threshold:0})},"right-of-screen":function(t){return n.rightoffold(t,{threshold:0})},"left-of-screen":function(t){return!n.rightoffold(t,{threshold:0})},"in-viewport":function(t){return n.inviewport(t,{threshold:0})},"above-the-fold":function(t){return!n.belowthefold(t,{threshold:0})},"right-of-fold":function(t){return n.rightoffold(t,{threshold:0})},"left-of-fold":function(t){return!n.rightoffold(t,{threshold:0})}})}(jQuery,window,document);$(function(){"use strict";$(".lazy").lazyload({effect:"fadeIn"});$(window).on("scroll",function(n){var i=$(window).scrollTop(),t;$("body").toggleClass("down",i>50);$("body").toggleClass("up",i<50);$(".benefits-btn").toggleClass("hidden-benefits",i<50);t=$(window).scrollTop()+$(window).height();t>$(document).height()-700&&$(window).width()>768&&$(".js-contact-home > div").addClass("animate");$(".benefits-btn").toggleClass("hidden-benefits",i<50);t>$(document).height()-900&&$(window).width()<=768&&$(".js-contact-home > div").addClass("animate");t>$(document).height()-1400&&$(window).width()>768&&$(".js-contact-blog > div").addClass("animate");t>$(document).height()-1600&&$(window).width()<=768&&$(".js-contact-blog > div").addClass("animate");$(this).scrollTop()>=500?$("#return-to-top").fadeIn(500):$("#return-to-top").fadeOut(500);loadPosts?(n.preventDefault(),n.stopPropagation()):onScrollLoadBlog()})});$(document).ready(function(){var t,n,i,r,u;onMobileCheck();assignHoverSocialIcons();$("#loader-wrp").fadeOut("slow");$("body").removeClass("overflow-hidden");t=$("#js-breadcrumb-get-value .ba-active").attr("name");checkFilter(t);n=$(location).attr("href");i=n.substr(-1);i!="/"&&(n=n+"/");history.pushState(n,n,n);r=document.querySelectorAll(".grid-section > div")[3];$("#baner-blog").insertAfter(r);u=document.querySelectorAll(".grid-section > div")[1];$(".mail-section").length>0&&$("h1").text().indexOf("Telemedicine App Development")!==-1&&$(".mail-section").insertAfter(u);$(".pulse").on("click",async function(){var n,t,i,r;const{Map:u,OverlayView:f,MapTypeId:e}=await google.maps.importLibrary("maps");if(n=$("#map").attr("data-location").split(","),mapClicked)map.setCenter(new google.maps.LatLng(n[0],n[1]));else{o();t={lat:parseFloat(n[0]),lng:parseFloat(n[1])};i={zoom:16,center:t,streetViewControl:!1,zoomControl:!1,fullscreenControl:!1,mapTypeId:e.ROADMAP};map=new u(document.getElementById("map"),i);function o(){Popup=function(n,t){this.position=n;t.classList.add("popup-bubble-content");var i=document.createElement("div");i.classList.add("popup-bubble-anchor");i.appendChild(t);this.anchor=document.createElement("div");this.anchor.classList.add("popup-tip-anchor");this.anchor.appendChild(i);this.stopEventPropagation()};Popup.prototype=Object.create(f.prototype);Popup.prototype.onAdd=function(){this.getPanes().floatPane.appendChild(this.anchor)};Popup.prototype.onRemove=function(){this.anchor.parentElement&&this.anchor.parentElement.removeChild(this.anchor)};Popup.prototype.draw=function(){var n=this.getProjection().fromLatLngToDivPixel(this.position),t=Math.abs(n.x)<4e3&&Math.abs(n.y)<4e3?"block":"none";t==="block"&&(this.anchor.style.left=n.x+"px",this.anchor.style.top=n.y+"px");this.anchor.style.display!==t&&(this.anchor.style.display=t)};Popup.prototype.stopEventPropagation=function(){var n=this.anchor;n.style.cursor="auto"}}r=document.getElementById("content");popup=new Popup(t,r);popup.setMap(map);mapClicked=!0}});$("#servises-careere .panel-heading").on("click",function(){if($(".panel-heading").hasClass("active")){$(".collapse").each(function(){$(".panel-collapse").attr("style","display: none !important")&&$(".panel-collapse").attr("style","");$(this).insertAfter($(this).parent().find(".active"))});var n=$(this).closest(".active");$("html,body").animate({scrollTop:n.offset().top-75},500)}else $(".panel-collapse").attr("style","display: none !important")});$("#accordion .active").length>0&&$(".collapse").each(function(){var n=$(this).parent().find(".active");n.length>0&&($(this).insertAfter(n),$("html, body").animate({scrollTop:$("#accordion .active").offset().top-75},"slow"))})});blogPageCount=1;loadMore=!1;$("#return-to-top").click(function(){$("body,html").animate({scrollTop:0},500)});mapClicked=!1;$(window).on("load",function(){var n=$(".latest-work-img").height()+105;$("#portfolio-tab").css("height",n);initFeaturedClientsList();initClientsList();initClientsListTwo();initQuotesList();initMethodologiesList();initOurCasesList();initIndexFilter();initPortfolioFilter();initTopBannerList();initRecruiterTeamList();initSmallRecruiterTeamListMobile();formCaseStudy()});$(window).on("load",function(){var n=$(".latest-work-img").height()+105;$("#portfolio-tab").css("height",n);zoomInit()});$(".modal").on("show.bs.modal",function(){isMobile.iOS()&&(scrollPos=$("body").scrollTop(),$("body").css({overflow:"hidden",position:"fixed",top:-scrollPos}));$(".header-small").css("padding-left","0");$(".header-small").css("padding-right","17px");$("#return-to-top").css("right","67px")});$(".modal").on("hidden.bs.modal",function(){isMobile.iOS()&&$("body").css({overflow:"",position:"",top:""}).scrollTop(scrollPos);$(".header-small").css("padding-left","0");$(".header-small").css("padding-right","0");$("#return-to-top").css("right","50px")});$(".navbar-toggle").on("click",function(n){menuToggle(n)});$(".btn-close-nav").on("click",function(){$("html").css("overflow","");$("body").css("height","");$(".popup-menu").css("height","");$(".navbar-toggle").removeClass("open");$("body").removeClass("popup-menu-active")});freezeVp=function(n){n.preventDefault()};$(".contact-us .contact-us-form input, .contact-us .contact-us-form textarea").focus(function(){var n=$(this);n.prev().css("top","0px");n.next().css("border-top","1px solid #1998a8")});$("form input, form textarea").focusout(function(){var n=$(this);n.val().length==0&&(n.prev().css("top","35px"),n.next().hasClass("js-no-animation")||n.next().css("border-top","1px solid #b4cbe0"))});$("form input, form textarea").focus(function(){var n=$(this);n.prev().css("top","0px");n.next().hasClass("js-no-animation")||n.next().css("border-top","1px solid #1998a8")});$("#hr-apply-modal .apply-form input, #hr-apply-modal .apply-form textarea").focusout(function(){var n=$(this);n.val().length==0&&(n.prev().css("top","35px"),n.next().css("border-top","1px solid #b4cbe0"))});$(".scroll").click(function(){var n=$(this).closest(".item").nextAll(".item").not(".hide-project").first(),t;n.length==1&&(t=n.offset().top-50,$("html, body").animate({scrollTop:t},"slow"))});$(".benefits-btn").on("click",function(){$("html, body").animate({scrollTop:$(".section-odd").first().offset().top-72},"slow")});$(".scroll-projects").click(function(){$("html, body").animate({scrollTop:$(".left-project").offset().top-50},"slow")});$(".person-email, .person-skype, .person-phone, .share-blog-link").on("click",function(){var n=$(this);CopyText(n);tooltipPopup(n)});applyRotate=function(n,t){return function(i){var r=i/t*360+90;n.style.transform="rotate(${degrees}deg)"}};$(".modal, .modal .close").on("click",function(){$(".embed-responsive-item").length&&$(".embed-responsive-item")[0].contentWindow.postMessage('{"event":"command","func":"pauseVideo","args":""}',"*")});$(".services #services .nav-pills a").on("click",function(){var n=$(this).attr("href"),t=$(this).attr("id");getService(n,t)});$(".services #section-mobile .panel-group .js-collapse").on("click",function(){var n=$(this).attr("href"),t=$(this).attr("id");getService(n,t)});$("#js-breadcrumb-get-value a").on("click",function(){var n=$(this).attr("name");n=="All"||n=="Alle"?$("#breadcrumb-name").empty():$("#breadcrumb-name").text(n)});$(".blog #js-breadcrumb-get-value a").on("click",function(){var n=$(this).attr("name");checkFilter(n)});isMobile={iOS:function(){var n=["iPad Simulator","iPhone Simulator","iPod Simulator","iPad","iPhone","iPod"];if(!!navigator.platform)while(n.length)if(navigator.platform===n.pop())return!0;return!1}};getPosition=function(){return $("#carriers .nav-pills .active").find(".carrier-text").text().trim()},function(){initBlogpostPage()}();$("body").on("click","div.share",function(){$("#blogPost .share-blog-link").text($(this).data("blog-link"))});$(".blog-tags li").on("click",function(n){n.preventDefault();n.stopPropagation();var t=$(n.target).closest("li"),i=t.children("a").attr("href").split("/").pop(),r=$(".blog").data("culture"),u=t.children("a").attr("href");onMakeTagActive(t);onChangeUrl(u);blogPageCount=1;$("#blogpostsList").load("/umbraco/Surface/SurfaceBlog/GetBlogs?tag="+i+"&culture="+r,function(){$(".lazy").lazyload({effect:"fadeIn"});SocialShare.createShareBox(".ss-box",window.location.href,"facebook, pinterest, linkedIn, pinterest, telegram,  whatsapp, email")});loadMore=$("#loadMore").data("load-more")===!0;onChangeTagHeaderSelected()});$(".blog .ba-category-list a").on("click",function(n){var t;n.preventDefault();n.stopPropagation();t=$(n.target);t.is("span")&&(t=t.parent());var i=t.attr("href").split("/").pop(),r=$(".blog").data("culture"),u=t.attr("href");onMakeTagActive(t);onChangeUrl(u);blogPageCount=1;$("#blogpostsList").load("/umbraco/Surface/SurfaceBlog/GetBlogs?tag="+i+"&culture="+r,function(){$(".lazy").lazyload({effect:"fadeIn"});SocialShare.createShareBox(".ss-box",window.location.href,"facebook, pinterest, linkedIn, pinterest, telegram,  whatsapp, email")});loadMore=$("#loadMore").data("load-more")===!0;onChangeTagHeaderSelected()});$("#blogpostsList").on("click",".post-categories li",function(n){n.preventDefault();n.stopPropagation();var t=$(n.target).closest("li"),i=t.children("a").attr("href").split("/").pop(),r=$(".blog").data("culture"),u=t.children("a").attr("href");blogPageCount=1;onMakeTagActive(t);onChangeUrl(u);$("#blogpostsList").load("/umbraco/Surface/SurfaceBlog/GetBlogs?tag="+i+"&culture="+r,function(){$(".lazy").lazyload({effect:"fadeIn"});SocialShare.createShareBox(".ss-box",window.location.href,"facebook, pinterest, linkedIn, pinterest, telegram,  whatsapp, email")});loadMore=$("#loadMore").data("load-more")===!0;onChangeTagHeaderSelected()});loadPosts=!1;onScrollLoadBlog=debounce(function(){var n=$(window).scrollTop(),t=$(window).height(),i=$(document).outerHeight(),r=$("footer").outerHeight();loadMore&&n+t>i-r-100&&(loadPosts=!0,blogPageCount++,onLoadBlogs())},1e3/60,0);$(".ba-media-selector .btn").on("click",function(n){$(".ba-media-selector .btn").removeClass("ba-btn-active");$(n.target).addClass("ba-btn-active");n.target.id==="btnTwitter"&&($(".js-twitter-feed-container").show(),$(".js-facebook-feed-container").hide(),$(".js-linkedin-feed-container").hide());n.target.id==="btnFacebook"&&($(".js-twitter-feed-container").hide(),$(".js-facebook-feed-container").show(),$(".js-linkedin-feed-container").hide());n.target.id==="btnLinkedIn"&&($(".js-twitter-feed-container").hide(),$(".js-facebook-feed-container").hide(),$(".js-linkedin-feed-container").show())});$(".js-collapse").click(function(){$(this).parent().hasClass("active")?$(this).parent().removeClass("active"):$(this).parent().addClass("active").siblings().removeClass("active")});$(".collapse").on("shown.bs.collapse",function(){var n=$(this).closest(".panel");n.length&&$("html,body").animate({scrollTop:n.offset().top-75},500)});$(".panel-heading a").click(function(){$(".panel-heading").removeClass("active");$(this).closest(".panel").find(".panel-collapse").hasClass("in")||$(this).parents(".panel-heading").addClass("active")});