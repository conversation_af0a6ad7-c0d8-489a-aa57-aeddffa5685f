/*.header-container {
    border-bottom: none;
}*/

.section-1 > div {
  min-height: 500px;
  max-height: 700px;
  background: url(/images/circles.png) no-repeat;
  background-size: cover;
}

.section-1 h3 {
  color: #fff;
  padding-top: 7%;
  padding-left: 11%;
}

.section-1 .title {
  color: #fff;
  padding-top: 15px;
  padding-left: 11%;
  font-size: 72px;
  font-weight: bold;
}

.section-1 .subtitle {
  color: #fff;
  padding-top: 10px;
  padding-left: 11%;
  text-transform: uppercase;
  font-size: 18px;
}

.section-1 .since {
  float: right;
  color: #00bed3;
  padding-top: 2%;
  padding-right: 11%;
  font-size: 40px;
  line-height: 32px;
}

.section-1 .since span {
  float: right;
  font-size: 18px;
}

.section-2 .title {
  font-size: 40px;
  margin-top: 80px;
  margin-bottom: 5px;
}

.section-2 .summary {
  margin-bottom: 15px;
  font-size: 18px;
}

.section-2 ul.list {
  list-style-image: url(/images/icon-list-item-arrow-blue.png);
  font-size: 16px;
}

.section-3 {
  margin-top: 70px;
  margin-bottom: 250px;
}

.section-3,
.section-3 > div {
  min-height: 900px;
}

.section-3 .left {
  background: linear-gradient(267deg, #007794 -25%, #00aeba 125%);
}

.section-3 .right {
  background-color: #f2f5f8;
}

.section-3 h3 {
  color: #fff;
  padding-top: 29%;
  padding-left: 40%;
}

.section-3 .title {
  color: #fff;
  padding-top: 15px;
  padding-left: 40%;
  font-size: 72px;
  font-weight: bold;
}

.section-3 .button {
  margin-top: 30px;
  padding-left: 40%;
}

.section-3 ul.list {
  padding-top: 100px;
}

.section-3 ul.list li {
  padding-bottom: 40px;
  background: url(/images/principes/cup.png) no-repeat 7px 7px;
  padding: 0px 0px 36px 62px;
  list-style-type: none;
}

.section-3 ul.list li:first-child {
  background: url(/images/principes/customer_focus.svg) no-repeat 7px 7px;
}

.section-3 ul.list li:nth-child(2) {
  background: url(/images/principes/dedication_to_quality.svg) no-repeat 7px 7px;
}

.section-3 ul.list li:nth-child(3) {
  background: url(/images/principes/think_bigger.svg) no-repeat 7px 7px;
}

.section-3 ul.list li:nth-child(4) {
  background: url(/images/principes/do_what_you_love.svg) no-repeat 7px 7px;
}

.section-3 ul.list li:nth-child(5) {
  background: url(/images/principes/think_as_one.svg) no-repeat 7px 7px;
}

.section-3 ul.list li:nth-child(6) {
  background: url(/images/principes/establish_trust.svg) no-repeat 7px 7px;
}

.section-3 ul.list li:nth-child(7) {
  background: url(/images/principes/never_stop_learning.svg) no-repeat 7px 7px;
}

.section-3 ul.list li:nth-child(8) {
  background: url(/images/principes/quoestion_everything.svg) no-repeat 7px 7px;
}

.section-3 ul.list .text-bold {
  font-size: 24px;
  text-transform: uppercase;
}

.section-4 .text-center {
  padding-bottom: 100px;
}

.section-4 {
  font-size: 15px;
  font-weight: bold;
  text-shadow: 2px 2px 0px #fff, -2px -2px 0px #fff, 2px -2px 0px #fff, -2px 2px 0px #fff;
}

/*map points positioning begin*/

.section-4 #canada span {
  position: absolute;
  left: 18.3%;
  top: 31.7%;
}

.section-4 #canada span img {
  height: 8px;
  padding-right: 4px;
}

.section-4 #miami span {
  position: absolute;
  left: 20.4%;
  top: 46.9%;
}

.section-4 #miami span img {
  height: 8px;
  padding-left: 4px;
}

.section-4 #belgium img {
  height: 100px;
  position: absolute;
  left: 42.6%;
  top: 27.9%;
}

.section-4 #belgium > div {
  position: absolute;
  left: 30.3%;
  top: 27.3%;
}

.section-4 #belgium > div > span {
  float: right;
}

.section-4 #italy img {
  height: 100px;
  position: absolute;
  left: 50.6%;
  top: 37.9%;
}

.section-4 #italy > div {
  position: absolute;
  left: 52.7%;
  top: 38.5%;
}

.section-4 #norway img {
  height: 57px;
  position: absolute;
  left: 49.6%;
  top: 14.9%;
}

.section-4 #norway > div {
  position: absolute;
  left: 51.5%;
  top: 14%;
}

.section-4 #estonia span {
  position: absolute;
  left: 53.3%;
  top: 25.2%;
}

.section-4 #estonia span img {
  height: 8px;
  padding-right: 4px;
}

.section-4 #china span {
  position: absolute;
  left: 76.9%;
  top: 40.2%;
}

.section-4 #china span img {
  height: 8px;
  padding-right: 4px;
}

.section-4 #south-africa span {
  position: absolute;
  left: 53.3%;
  top: 80.5%;
}

.section-4 #south-africa span img {
  height: 8px;
  padding-right: 4px;
}

.section-4 #spitsbergen span {
  position: absolute;
  left: 51.7%;
  top: 4.9%;
}

.section-4 #spitsbergen span img {
  height: 8px;
  padding-right: 4px;
}

.section-4 #iceland span {
  position: absolute;
  left: 34.7%;
  top: 18.5%;
}

.section-4 #iceland span img {
  height: 8px;
  padding-left: 4px;
}

.section-4 #new-zeland span {
  position: absolute;
  left: 83.6%;
  top: 89%;
}

.section-4 #new-zeland span img {
  height: 8px;
  padding-left: 4px;
}

.section-4 #sydney span {
  position: absolute;
  left: 81.7%;
  top: 82.3%;
}

.section-4 #sydney span img {
  height: 8px;
  padding-left: 4px;
}

.section-4 #san-francisco span {
  position: absolute;
  left: 9.1%;
  top: 38.3%;
}

.section-4 #san-francisco span img {
  height: 8px;
  padding-left: 45%;
}

/*map points positioning end*/

.section-5 {
  min-height: 550px;
  margin-top: 145px;
}

.section-5 > div {
  min-height: 550px;
}

.section-5 .left {
  padding: 0px;
  text-align: center;
  overflow: hidden;
}

.section-5 .left img {
  position: relative;
  left: 100%;
  margin-left: -200%;
}

.section-5 .right {
  background: linear-gradient(267deg, #149bc9 -10%, #0c497a 110%);
}

.section-5 h3 {
  color: #fff;
  padding-top: 7%;
  padding-left: 10%;
}

.section-5 .title {
  color: #fff;
  padding-top: 20px;
  padding-left: 10%;
  font-size: 48px;
}

.section-5 .title img {
  padding-right: 26px;
}

.section-5 .summary {
  margin-top: 20px;
  margin-bottom: 40px;
  font-size: 18px;
  color: #fff;
  padding-left: 10%;
}

.section-5 .button {
  margin-top: 30px;
  padding-left: 10%;
}

.section-5 .button .btn {
  border: 1px solid #fff;
}

.section-6 {
  padding-top: 100px;
  padding-bottom: 100px;
}

.section-6 h3 {
  margin-bottom: 50px;
}

.section-6 .title {
  font-size: 2.5em;
  margin-bottom: 40px;
}

.section-6 .content {
  font-size: 1.125em;
  margin-bottom: 70px;
}

.section-6 .clients-list .item {
  border-radius: 10px;
  border: 2px solid #dfebf6;
  max-width: 220px;
  height: 106px;
  padding: 17px;
  display: -ms-flexbox;
  display: flex;
}

.section-6 .clients-list .item img {
  width: auto;
  margin: auto;
  max-width: 100%;
  max-height: 100%;
  -ms-flex-item-align: center;
}

.section-6 .owl-theme .owl-nav.disabled + .owl-dots {
  margin-top: 40px;
}

.section-6 .owl-theme .owl-dots .owl-dot span {
  width: 14px;
  height: 14px;
  border: 4px solid #fff;
  background: #92b1cc;
}

.section-6 .owl-theme .owl-dots .owl-dot.active span,
.section-6 .owl-theme .owl-dots .owl-dot:hover span {
  background: #0097a7;
  width: 14px;
  height: 14px;
  border: 3px solid #cceaed;
}

.section-7 > div {
  display: -ms-flexbox;
  display: flex;
}

.section-7 .icon-quote {
  display: inline-block;
  background: url("/images/icon-quote.png");
}

.section-7 .icon-quote.icon-quote-lg {
  position: absolute;
  right: -18px;
  top: -49px;
  width: 250px;
  height: 184px;
  opacity: 0.1;
}

.section-7-left {
  /*min-height: 614px;*/
  color: #fff;
  background: linear-gradient(#009ece, #00bac3);
  float: left;
  overflow: hidden;
  position: relative;
}

.section-7-left .title {
  font-size: 1.875em;
  font-family: "baskerville_normalitalic", "Helvetica Neue", Helvetica, Arial, sans-serif;
}

.section-7-left .quote-header {
  display: -ms-flexbox;
  display: flex;
  margin: 40px 0;
}

.section-7-left .quote-header .icon-quote {
  width: 49px;
  height: 36px;
  background-size: 100%;
}

.section-7-left .quote-header .slide-quote-btn {
  width: 23px;
  height: 23px;
  margin: auto;
  background: url("/images/btn-quote-prev.png");
  border: none;
  outline: none;
}

.section-7-left .quote-header .slide-quote-btn + .slide-quote-btn {
  -moz-transform: rotate(180deg);
  -ms-transform: rotate(180deg);
  -o-transform: rotate(180deg);
  -webkit-transform: rotate(180deg);
  transform: rotate(180deg);
  margin-left: 11px;
}

.section-7-left .quote-header .line {
  -ms-flex-positive: 1;
  flex-grow: 1;
  margin: auto 15px;
  border-top: 1px solid rgba(255, 255, 255, 0.5);
}

.section-7-left .quote-content {
  font-size: 1.5em;
  margin-bottom: 45px;
}

.section-7-left .quote-author-wrp {
  font-style: italic;
  padding-right: 5px;
}

.section-7-left .quote-author-wrp .name {
  font-size: 1.125em;
  font-weight: 700;
}

.section-7-left > div {
  padding: 100px 265px 80px 200px;
}

.section-7-right {
  background: linear-gradient(-45deg, #a9dbe3 17%, #f5fdfe 68%);
  width: calc(100% - 64%);
  float: left;
  background-size: auto 100%;
  position: relative;
}

.section-7-right div {
  background-image: url("/images/img005.png");
  background-repeat: no-repeat;
  height: 263px;
  width: 263px;
  margin: -131px 0 0 -131px;
  position: absolute;
  top: 50%;
  left: 50%;
}

.section-8 {
  min-height: 425px;
  background-color: #ff9f1c;
}

.section-8 > div {
  /*width: 100%*/
  min-height: 500px;
  background: url(/images/lines.png) no-repeat;
  background-size: cover;
}

.section-8 .title {
  margin-top: -260px;
  font-size: 36px;
  color: #fff;
}

.section-8 .summary {
  margin-top: 20px;
  margin-bottom: 60px;
  font-size: 18px;
  color: #fff;
}

.section-8 .phones {
  margin-top: 60px;
  margin-left: -41px;
}

.section-8 .left-lines {
}

.section-8 .right-lines {
  margin-top: -425px;
  float: right;
}

@media (min-width: 768px) {
  .navbar-default {
    border: none;
  }

  .header-container {
    border: none;
  }
}

@media (min-width: 1200px) {
  .section-7-left {
    width: 64%;
  }
}

@media (max-width: 768px) {
  section h3 {
    font-size: 24px;
  }

  .section-2 .title {
    font-size: 36px;
  }

  .section-3 ul.list .text-bold {
    font-size: 16px;
  }

  .section-6 .title {
    font-size: 30px;
  }

  .section-7-left .title {
    font-size: 25px;
  }

  .section-7-left .quote-content {
    font-size: 18px;
  }

  .section-8 .title {
    font-size: 30px;
  }
}

@media (max-width: 990px) {
  .section-4 {
    font-size: 12px;
  }

  /*map points positioning begin*/

  .section-4 #belgium img {
    height: 80px;
  }

  .section-4 #norway img {
    height: 44px;
  }

  .section-4 #italy img {
    height: 80px;
  }

  /*map points positioning end*/
}

@media (max-width: 1200px) {
  .section-7-left {
    max-height: 614px;
  }

  .section-7-left > div {
    padding: 70px 20px 0px 20px;
  }

  .section-8 .phones {
    opacity: 0;
  }

  /*map points positioning begin*/

  .section-4 #canada span {
    left: 18.5%;
    top: 31.4%;
  }

  .section-4 #miami span {
    left: 19.5%;
    top: 46.9%;
  }

  .section-4 #belgium img {
    left: 41.6%;
    top: 26.9%;
  }

  .section-4 #belgium > div {
    position: absolute;
    left: 27.3%;
    top: 27.3%;
  }

  .section-4 #norway img {
    left: 49.5%;
    top: 14.1%;
  }

  .section-4 #estonia span {
    left: 53.2%;
    top: 24.5%;
  }

  .section-4 #china span {
    left: 76.8%;
    top: 40%;
  }

  .section-4 #south-africa span {
    left: 53.2%;
    top: 80.1%;
  }

  .section-4 #spitsbergen span {
    left: 51.6%;
    top: 4.5%;
  }

  .section-4 #iceland span {
    left: 33.4%;
    top: 18%;
  }

  .section-4 #new-zeland span {
    left: 81.3%;
    top: 88.7%;
  }

  .section-4 #sydney span {
    left: 80.3%;
    top: 81.8%;
  }

  .section-4 #san-francisco span {
    left: 8.3%;
    top: 36.9%;
  }

  /*map points positioning end*/
}

@media (max-width: 1350px) {
  .section-8 .phones {
    margin-left: -145px;
  }
}
