﻿.row.no-gutters {
  margin-right: 0;
  margin-left: 0;
}

.row.no-gutters > [class^="col-"],
.row.no-gutters > [class*="col-"] {
  padding-right: 0;
  padding-left: 0;
}

.zero-padding {
  padding: 0;
}

.zero-margin-hr {
  margin-left: 0;
  margin-right: 0;
}

.center-position {
  display: flex;
  align-items: center;
  justify-content: center;
}

.align-stretch {
  display: flex;
  align-items: stretch;
}

.carriers {
  position: relative;
}

.carriers .header {
  font-size: 48px;
  line-height: 48px;
  color: #006b71;
  margin-bottom: 75px;
}

body.apple-ios.modal-open {
  position: fixed;
  width: 100%;
}

.ls-item {
  background-repeat: no-repeat;
  background-position: center;
  content: "";
  position: relative;
  display: inline-block;
}

.s-item {
  position: relative;
  color: #003231;
  padding-left: 80px;
  margin-bottom: 35px;
}

.s-item:last-child {
  margin-bottom: 0;
}

.s-item .item-title {
  text-transform: uppercase;
  font-size: 24px;
}

.s-item .item-subtitle {
  font-size: 16px;
  line-height: 22px;
}

/* #region page benefits right button */

.benefits-btn {
  position: fixed;
  height: 65px;
  width: 180px;
  right: -70px;
  top: calc(30vh);
  background: #f1604b;
  border-top-left-radius: 33px;
  border-bottom-left-radius: 33px;
  z-index: 100;
  cursor: pointer;
  text-transform: uppercase;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  -webkit-transition-duration: 0.4s;
  transition-duration: 0.4s;
}

.benefits-btn:hover {
  right: 0;
}

.benefits-btn.hidden-benefits {
  right: -180px;
  /*display: none;*/
  -webkit-transition-duration: 0.4s;
  transition-duration: 0.4s;
}

.benefits-btn .btn-lbl {
  width: 115px;
  display: flex;
  align-items: center;
  font-size: 14px;
  text-transform: uppercase;
  color: #ffffff;
  padding-left: 25px;
}

.benefits-btn .btn-icon {
  background-image: url("/images/carriers/benefits-btn-icon.svg");
  background-position: center;
  background-repeat: no-repeat;
  width: 55px;
  height: 45px;
}

/* #endregion */

/* #region section 1 */
.carriers .section-1 {
  background: linear-gradient(180deg, #003231 0%, #006b71 100%);
  background-size: cover;
  height: 13.7em;
}

.carriers .section-1 .since {
  float: right;
  color: white;
  margin-top: -67px;
  padding-right: 10px;
  font-size: 36px;
  line-height: 32px;
}

.carriers .section-1 .since span {
  float: right;
  font-size: 18px;
}

.carriers .section-1 .title {
  background-image: linear-gradient(to top, #cfdaed, #fff);
  color: transparent;
  -webkit-background-clip: text;
  background-clip: text;
  margin-top: 60px;
  font-size: 48px;
}

.carriers .section-1 .subtitle {
  color: #fff;
  margin-top: 21px;
  text-transform: uppercase;
  font-size: 18px;
}

/* #endregion*/

/* #region section 2 */

.carriers .section-2 {
  padding-bottom: 100px;
}

.carriers .section-2 h3 {
  color: #4c5e72;
  margin: 0 0 50px;
}

.carriers .section-2 .carriers-list {
  font-size: 1em;
  margin-right: 3em;
  border: 1px solid #dfebf6;
  border-top: 0;
  border-left: 0;
}

.carriers .section-2 .carriers-list li {
  margin: 0;
  border: 0;
  border-top-width: 1px;
  border-left-width: 1px;
  border-color: #dfebf6;
  border-style: solid;
  width: 100%;
}

.carriers .section-2 .carriers-list li a {
  /*text-align: center;*/
  padding-left: 50px;
  color: #323f4e;
  font-size: 0.875em;
  font-weight: 700;
  text-transform: uppercase;
  border-radius: 0;
  border: 2px solid transparent;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  height: 100px;
  display: flex;
  align-items: center;
}

.carriers .section-2 .carriers-list li a:hover,
.carriers .section-2 .carriers-list li a:focus,
.carriers .section-2 .carriers-list > li.active > a,
.carriers .section-2 .carriers-list > li.active > a:focus,
.carriers .section-2 .carriers-list > li.active > a:hover {
  background: none;
  color: #323f4e;
}

.carriers .section-2 .carriers-list li a:hover {
  border: 2px solid #009ece;
  border-radius: 2px;
}

.carriers .section-2 .carriers-list li.active {
  border-top: 1px solid #dfebf6;
  border-left: 1px solid #dfebf6;
  background: linear-gradient(180deg, #009ece 0%, #003231 0.01%, #006b71 100%);
  box-shadow: 12px 16px 54px 10px rgba(53, 101, 103, 0.3);
  z-index: 1;
}

.carriers .section-2 .carriers-list li.active a,
.carriers .section-2 .carriers-list li.active a:hover,
.carriers .section-2 .carriers-list li.active a:focus {
  color: #fff;
}

.carriers .section-2 .carriers-list li.active a:hover {
  border-color: transparent;
}

.carriers .section-2 .carriers-list .carrier-text {
  display: inline-block;
  text-align: left;
  vertical-align: middle;
  word-break: break-word;
  font-size: 15px;
}

.carriers .section-2 .tab-content .list-header {
  font-weight: 600;
  margin-bottom: 10px;
}

.carriers .section-2 .tab-content .list-item-header {
  font-weight: 600;
  display: block;
}

.carriers .section-2 .title {
  font-size: 2.5em;
  border-left: 3px solid #f1604b;
  padding: 11px 50px 13px;
  margin-bottom: 30px;
  margin-top: 0;
  line-height: unset;
  color: #006b71;
  font-weight: 500;
}

.carriers .section-2 .summary {
  margin-bottom: 35px;
}

.carriers .section-2 .summary a:hover {
  text-decoration: underline;
}

.carriers .section-2 .list-title {
  font-weight: 700;
  margin-bottom: 15px;
}

.carriers .section-2 ul.list {
  margin-bottom: 50px;
  list-style-image: url("/images/icon-list-item-arrow.png");
  padding-left: 27px;
}

.carriers .section-2 ul.list li {
  padding-left: 12px;
  padding-bottom: 5px;
}

/* #region contact block */
.carriers .section-2 .contact-person {
  position: relative;
  margin-right: 3em;
  margin-top: 50px;
  padding: 50px;
  background: #e8f6f9;
}

.contact-person .contact-nav {
  position: absolute;
  top: 75px;
  left: 0;
  right: 0;
  display: flex;
  justify-content: space-between;
  padding: 0 20px;
}

.carriers .section-2 .owl-carousel .owl-item img {
  width: auto;
}

.section-2 .contact-nav .arr-icon {
  width: 12px;
  height: 22px;
  background-repeat: no-repeat;
  background-position: center;
  cursor: pointer;
}

.section-2 .contact-nav .arr-left {
  background-image: url("/images/carriers/contact-arr.svg");
}

.section-2 .contact-nav .arr-left:hover {
  background-image: url("/images/carriers/contact-arr-hover.svg");
  -webkit-transform: rotate(180deg);
  -moz-transform: rotate(180deg);
  -ms-transform: rotate(180deg);
  -o-transform: rotate(180deg);
  transform: rotate(180deg);
}

.section-2 .contact-nav .arr-right {
  background-image: url("/images/carriers/contact-arr.svg");
  -webkit-transform: rotate(180deg);
  -moz-transform: rotate(180deg);
  -ms-transform: rotate(180deg);
  -o-transform: rotate(180deg);
  transform: rotate(180deg);
}

.section-2 .contact-nav .arr-right:hover {
  background-image: url("/images/carriers/contact-arr-hover.svg");
  -webkit-transform: none;
  -moz-transform: none;
  -ms-transform: none;
  -o-transform: none;
  transform: none;
}

.contact-person .person-info {
  display: flex;
  justify-content: flex-start;
}

.person-info .person-img {
  width: 72px;
  height: 72px;
  border-radius: 50%;
  margin-right: 30px;
}

.person-data {
  color: #2f4351;
  font-size: 18px;
  line-height: 22px;
  overflow: hidden;
  word-break: break-word;
  text-overflow: ellipsis;
}

.contact-person .item {
  margin-bottom: 32px;
}

.contact-person .person-email,
.contact-person .person-skype,
.contact-person .person-phone {
  margin-top: 25px;
  cursor: pointer;
}

.carriers .breadcrumb-wrp {
  padding-left: 15px;
}

.carriers .section-2 .icon::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  display: inline-block;
  width: 22px;
  height: 22px;
  height: 100%;
  background-repeat: no-repeat;
  background-position: center;
}

.carriers .section-2 .person-email,
.carriers .section-2 .person-skype,
.carriers .section-2 .person-phone {
  position: relative;
  padding-left: 43px;
  display: flex;
  align-items: center;
  overflow: hidden;
  text-overflow: ellipsis;
  word-wrap: break-word;
  word-break: break-all;
}

.carriers .section-2 .icon-email::before {
  background-image: url("/images/carriers/icon-email.svg");
}

.carriers .section-2 .icon-skype::before {
  background-image: url("/images/carriers/icon-skype.svg");
}

.carriers .section-2 .icon-phone::before {
  background-image: url("/images/carriers/icon-phone.svg");
}

.carriers .section-2 .summary img {
  max-width: 100%;
  height: auto;
}

.hr-contact-btn {
  height: 45px;
  border-radius: 23px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  text-transform: uppercase;
  font-size: 14px;
  color: #ffffff;
}

.apply-btn {
  border-radius: 5px;
  background: #f1604b;
  margin-top: 18px;
}

.apply-btn:hover {
  background-color: #0097a7;
}

#hr-apply-modal .modal-header {
  border-bottom: none;
  padding: 25px;
}

#hr-apply-modal .modal-body {
  padding: 0 70px 70px 70px;
}

#hr-apply-modal .mdl-title {
  display: block;
  color: #323f4e;
  font-size: 24px;
  line-height: 24px;
  margin-bottom: 30px;
}

#hr-apply-modal .mdl-subtitle {
  display: block;
  color: #323f4e;
  font-size: 16px;
  line-height: 16px;
  margin-bottom: 60px;
}

#hr-apply-modal .icon-star {
  color: #ff6666;
}

/* #region apply form */

#hr-apply-modal .apply-form label {
  font-size: 0.875em;
  font-weight: 300;
  margin-bottom: 5px;
  white-space: nowrap;
  top: 35px;
  position: relative;
  transition: 0.3s;
  pointer-events: none;
}

#hr-apply-modal h4 {
  font-weight: 600;
}

#hr-apply-modal #position-header {
  word-break: break-word;
  text-transform: uppercase;
}

#hr-apply-modal .apply-form input,
#hr-apply-modal .apply-form textarea {
  width: 100%;
  border: none;
  padding: 10px 0;
  font-weight: 700;
  line-height: 1em;
  outline: none;
  resize: vertical;
}

#hr-apply-modal .apply-form .input-file {
  width: 0;
  height: 0;
  padding: 0;
  margin: 0;
  position: relative;
  display: none;
}

#hr-apply-modal .apply-form .attach-nav-icn {
  width: 21px;
  height: 21px;
  background-repeat: no-repeat;
  background-size: cover;
  position: absolute;
  right: 30px;
  top: 40px;
  cursor: pointer;
  z-index: 1;
}

#hr-apply-modal .apply-form .attach-file {
  background-image: url(/images/carriers/attach.svg);
}

#hr-apply-modal .apply-form .delete-file {
  background-image: url(/images/carriers/x-red.svg);
}

#hr-apply-modal .apply-form textarea {
  height: 80px;
}

#hr-apply-modal .apply-form .btn-submit {
  min-height: 41px;
  line-height: 1;
  margin-top: 20px;
}

#hr-apply-modal .apply-form .icon-forward {
  display: inline-block;
  width: 20px;
  height: 17px;
  background: url("/images/icon-forward.png") no-repeat 0 0;
}

/* #endregion */
.apply-with-btn {
  background-color: #2b6eab;
  margin-top: 30px;
}

.apply-with-btn:hover {
  background-color: #195083;
}

/* #endregion */

/* #region social block */

.carriers .section-2 .social-block {
  border-top: 1px solid #dfebf6;
  padding-top: 40px;
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

.social-block .social-lbl {
  display: inline-block;
  color: #92a1b2;
  font-size: 16px;
  line-height: 22px;
  margin-right: 20px;
  margin-top: 16px;
}

.social-link {
  margin-right: 10px;
}

.social-link:last-child {
  margin-right: 0;
}

.social-icon {
  display: inline-block;
  width: 30px;
  height: 30px;
  background-repeat: no-repeat;
  background-position: center;
  cursor: pointer;
}

.social-block .jssocials-share-logo {
  font-size: 1em !important;
}

.icon-facebook::before {
  background-image: url("/images/carriers/facebook.svg");
}

.icon-linkedin::before {
  background-image: url("/images/carriers/linkedin.svg");
}

.icon-google {
  background-image: url("/images/carriers/google.svg");
}

.carriers #social-network {
  padding-top: 100px;
  padding-bottom: 100px;
  background: linear-gradient(74deg, #003231 0%, #006b71 100%);
}

.carriers #social-network .pictures {
  position: relative;
}

.carriers #social-network .pictures img {
  border: 5px solid #fff;
  border-radius: 4px;
  background-repeat: no-repeat;
  background-size: cover;
  background-position: center;
  object-fit: cover;
}

.carriers #social-network .pictures .main-picture {
  position: relative;
  width: 91%;
  max-height: 24em;
  margin-left: -5%;
}

.carriers #social-network .pictures .bottom-right {
  position: absolute;
  width: 44%;
  bottom: -8%;
  max-height: 9em;
  right: 8%;
}

.carriers #social-network .pictures .bottom-left {
  position: absolute;
  width: 41%;
  bottom: -18%;
  max-height: 8em;
  left: -12%;
}

.carriers #social-network .pictures .left {
  position: absolute;
  width: 31%;
  max-height: 7em;
  left: -22%;
  bottom: 21%;
}

.carriers #social-network .description,
.carriers #social-network .under-line {
  color: #fff;
}

.carriers #social-network h2 {
  margin: 21px 0;
  font-size: 3em;
  letter-spacing: -0.01em;
}

.carriers #social-network .btn {
  width: 100%;
  min-width: unset;
  background-color: #fff;
  color: #003231;
  margin-top: 18px;
  text-transform: none;
  border-radius: 100px;
}

.carriers #social-network .btn:hover {
  background-color: #c6e6f0;
  color: #1694a7;
}

.carriers #social-network .btn span {
  font-size: 12pt;
  font-weight: 100;
  color: inherit;
}

.carriers #social-network .btn span::before {
  content: "";
  position: relative;
  top: 7px;
  left: -9px;
  display: inline-block;
  width: 25px;
  height: 25px;
  background-repeat: no-repeat;
  background-position: center;
  border-radius: 4px;
}

.carriers .section-odd.background {
  background: #f2f5f8;
  padding-top: 100px;
  padding-bottom: 100px;
}

.carriers .section-even.background {
  background-color: #ffffff;
  padding-top: 100px;
  padding-bottom: 100px;
}

.carriers .small-icon {
  background-repeat: no-repeat;
  background-size: 48px;
}

.carriers .big-icon {
  background-position: center;
  background-repeat: no-repeat;
}

@media (max-width: 1400px) {
  .carriers #social-network .pictures .main-picture {
    width: 87%;
    max-height: 23em;
    margin-left: 0%;
  }

  .carriers #social-network .pictures .bottom-right {
    width: 39%;
    bottom: -9%;
    max-height: 9em;
    right: 5%;
  }

  .carriers #social-network .pictures .bottom-left {
    width: 41%;
    bottom: -20%;
    max-height: 8em;
    left: -2%;
  }

  .carriers #social-network .pictures .left {
    width: 31%;
    max-height: 7em;
    left: -9%;
    bottom: 20%;
  }
}

@media (max-width: 1200px) {
  .carriers #social-network .pictures {
    position: static;
    margin-bottom: 100px;
  }

  .carriers #social-network .pictures .main-picture {
    margin-left: 9%;
    width: 77%;
  }

  .carriers #social-network .pictures .bottom-right {
    width: 34%;
    bottom: -6%;
    max-height: 14em;
    right: 4%;
  }

  .carriers #social-network .pictures .bottom-left {
    width: 27%;
    bottom: -12%;
    max-height: 8em;
    left: 4%;
  }

  .carriers #social-network .pictures .left {
    width: 22%;
    max-height: 8em;
    left: -1%;
    bottom: 24%;
  }

  .carriers #social-network .pictures img {
    border-width: 3px;
  }
}

@media (max-width: 992px) {
  #hr-apply-modal .btn {
    display: inline-block;
    position: absolute;
    float: none;
    top: 70px;
    left: 50%;
    transform: translateX(-50%);
  }

  #hr-apply-modal .g-recaptcha > div > div {
    position: absolute;
    left: 50%;
    transform: scale(0.77) translate(-64%, 0);
    -webkit-transform: scale(0.77) translate(-64%, 0);
  }

  .carriers .section-odd .row .relative-position {
    padding-left: 10px;
  }

  .services .section-odd .row .first-item {
    margin-top: 40px;
  }

  .services .section-odd .border-line {
    margin-top: 40px;
  }

  .section-odd .icon-work {
    width: 320px;
  }
}

@media (max-width: 768px) {
  .carriers .header {
    font-size: 36px;
    line-height: 36px;
    margin-bottom: 40px;
  }

  .carriers section .background,
  .carriers section .background {
    padding-top: 70px;
    padding-bottom: 70px;
  }

  .s-item .item-title {
    font-size: 20px;
  }

  .s-item .item-subtitle {
    font-size: 14px;
    line-height: 20px;
  }

  .carriers .section-1 {
    height: 18em;
  }

  .carriers .section-1 .since {
    margin-top: 30px;
  }

  .carriers .section-2 .carriers-list {
    margin-right: 0;
  }

  .carriers .section-2 .contact-person {
    margin-right: 0;
  }

  .carriers .section-2 .tab-content {
    padding-top: 30px;
  }

  .carriers .section-2 .title {
    font-size: 30px;
  }

  #hr-apply-modal .modal-body {
    padding: 0px 30px 150px 30px;
  }

  #hr-apply-modal .apply-form input,
  #hr-apply-modal .apply-form textarea {
    padding: 5px 0;
  }

  .carriers .section-2 .social-block {
    display: flex;
    flex-direction: column;
  }
}

@media (min-width: 768px) and (max-width: 992px) {
  .carriers .section-2 .contact-person {
    padding: 40px 35px;
  }

  .contact-person .contact-nav {
    padding: 0 10px;
    top: 60px;
  }

  .person-info .person-img {
    width: 60px;
    height: 60px;
    margin-right: 15px;
  }
}

@media (max-width: 576px) {
  .carriers .section-1 .since {
    display: none;
  }
}

.carriers #section-mobile .js-collapse {
  width: 100%;
  text-align: center;
  padding-left: 50px;
  color: #323f4e;
  font-size: 0.875em;
  font-weight: 700;
  text-transform: uppercase;
  border-radius: 0;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  height: 100px;
  display: flex;
  align-items: center;
  cursor: pointer;
}

.carriers #section-mobile .js-collapse:hover {
  border: 2px solid #009ece;
  border-radius: 2px;
}
.carriers #section-mobile .active .js-collapse:hover {
  border: 0;
}

.carriers #section-mobile .carriers-list {
  font-size: 1em;
  border: 1px solid #dfebf6;
  border-top: 0;
  border-left: 0;
}

@media (max-width: 767px) {
  #carriers {
    display: none;
  }
}

.carriers #section-mobile .carriers-list li {
  border: 0;
}

@media (max-width: 576px) {
  .carriers .section-1 {
    height: 11em;
  }

  .carriers .section-1 .title {
    font-size: 42px;
    margin-top: 20px;
  }

  .carriers .section-2 {
    padding-top: 20px;
  }

  .carriers .section-1 .subtitle {
    font-size: 16px;
  }
}
