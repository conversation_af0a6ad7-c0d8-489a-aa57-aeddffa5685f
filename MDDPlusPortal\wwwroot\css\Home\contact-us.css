/*pulse begin*/

.contact-us .pulse {
  /*margin: -138px 0px 0px 1095px;*/
  position: absolute;
  right: 707px;
  top: 113px;
  display: block;
  width: 14px;
  height: 14px;
  border-radius: 50%;
  background: #fff;
  cursor: pointer;
  box-shadow: 0 0 0 rgba(15, 186, 193, 0.4);
  animation: pulse 2s infinite;
}

.contact-us .pulse:hover {
  animation: none;
}

@-webkit-keyframes pulse {
  0% {
    -webkit-box-shadow: 0 0 0 0 rgba(15, 186, 193, 0.4);
  }
  70% {
    -webkit-box-shadow: 0 0 0 10px rgba(15, 186, 193, 0);
  }
  100% {
    -webkit-box-shadow: 0 0 0 0 rgba(15, 186, 193, 0);
  }
}
@keyframes pulse {
  0% {
    -moz-box-shadow: 0 0 0 0 rgba(15, 186, 193, 0.4);
    box-shadow: 0 0 0 0 rgba(15, 186, 193, 0.4);
  }
  70% {
    -moz-box-shadow: 0 0 0 100px rgba(15, 186, 193, 0);
    box-shadow: 0 0 0 100px rgba(15, 186, 193, 0);
  }
  100% {
    -moz-box-shadow: 0 0 0 0 rgba(15, 186, 193, 0);
    box-shadow: 0 0 0 0 rgba(15, 186, 193, 0);
  }
}

/*pulse end*/

.contact-us .tooltip-text {
  background-color: #ff5252;
  color: #fff;
  text-align: center;
  border-radius: 5px;
  position: relative;
  z-index: 1;
  bottom: 34px;
  right: 28px;
  font-size: 18px;
  padding-top: 6px;
  width: 70px;
  height: 35px;
  display: inline-block;
  font-weight: bold;
}

.contact-us .tooltip-text::after {
  content: "";
  position: absolute;
  top: 100%;
  left: 50%;
  margin-left: -5px;
  border-width: 5px;
  border-style: solid;
  border-color: #ff5252 transparent transparent transparent;
}

#exampleModalCenter #map {
  height: 100%;
}

#exampleModalCenter .popup-tip-anchor {
  height: 0;
  position: absolute;
  width: 200px;
}
#exampleModalCenter .popup-bubble-anchor {
  position: absolute;
  width: 100%;
  bottom: /* TIP_HEIGHT= */ 8px;
  left: 0;
}
#exampleModalCenter .popup-bubble-anchor::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  transform: translate(-50%, 0);
  width: 0;
  height: 0;
  border-left: 6px solid transparent;
  border-right: 6px solid transparent;
  border-top: /* TIP_HEIGHT= */ 8px solid #ff5252;
}
#exampleModalCenter .popup-bubble-content {
  position: absolute;
  top: 0;
  left: 0;
  transform: translate(-50%, -100%);
  background-color: #ff5252;
  padding: 5px;
  border-radius: 5px;
  color: white;
  font-size: 10pt;
  width: 128pt;
  overflow-y: auto;
  max-height: 90px;
}

#exampleModalCenter .modal-body .row {
  max-height: 420px;
  min-height: 420px;
  margin: 0 20px 20px 20px;
}

#exampleModalCenter .modal-body #map {
  max-height: inherit;
  min-height: inherit;
}

#exampleModalCenter .modal-title {
  display: inline-block;
}

#exampleModalCenter .close-btn {
  font-size: 24pt;
}

#exampleModalCenter .modal-header {
  border: none;
  margin-left: 20px;
}

#exampleModalCenter .modal {
  overflow-y: hidden !important;
}

.contact-us .section-1 {
  position: relative;
}

/* .contact-us .section-1 .gmnoprint */

.contact-us .section-1 > div {
  position: relative;
  z-index: 1;
  min-height: 70vh;
  max-height: 700px;
  width: 100%;
}

.contact-us .section-1 .map-holder {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

/* .contact-us .section-1 .map-holder::before {
  content: "";
  position: absolute;
  width: 100%;
  height: 23px;
  bottom: 0;
  left: 0;
  background: white;
  z-index: 1;
} */

.contact-us .section-1 .decorative-map {
  width: 100%;
  height: 100%;
}

.contact-us .section-1 .title {
  color: #fff;
  margin-top: 0;
  padding-top: 15px;
  padding-top: 7%;
  padding-left: 15%;
  font-size: 72px;
  font-weight: bold;
}

.contact-us .section-2 {
  position: relative;
  z-index: 2;
  margin-left: 15%;
  margin-right: 15%;
  margin-top: -300px;
  margin-bottom: 50px;
  border-radius: 20px;
  box-shadow: 0px 0px 40px rgba(150, 150, 150, 0.1);
}

.contact-us .section-2 > div {
  display: -ms-flexbox;
  display: flex;
}

.contact-us .section-2-left {
  background-color: #fff;
  border-radius: 20px 0px 0px 20px;
}

.contact-us .section-2-left > div {
  padding: 90px 10% 90px 10%;
}

.contact-us .section-2-left .title {
  font-size: 2.5em;
  margin-bottom: 15px;
}

.contact-us .section-2 .contact-us-form label {
  font-size: 0.875em;
  font-weight: 300;
  margin-bottom: 5px;
  white-space: nowrap;
  pointer-events: none;
  top: 35px;
  position: relative;
  transition: 0.3s;
}

.contact-us .section-2 .contact-us-form input,
.contact-us .section-2 .contact-us-form textarea {
  width: 100%;
  border: none;
  padding: 10px 0;
  font-weight: 700;
  line-height: 1em;
  outline: none;
  resize: vertical;
}

.contact-us .section-2 .contact-us-form textarea {
  height: 80px;
}

.contact-us .section-2 .contact-us-form .btn-submit {
  min-height: 41px;
  line-height: 1;
  margin: 20px 0px 20px 20px;
}

.contact-us .section-2 .contact-us-form .icon-forward {
  display: inline-block;
  width: 20px;
  height: 17px;
  background: url("/images/icon-forward.png") no-repeat 0 0;
}

.contact-us .section-2-right {
  position: relative;
  background: linear-gradient(0deg, #003231 0%, #006b71 100%);
  color: #fff;
  border-radius: 0px 20px 20px 0px;
}

.contact-us .section-2-right > div {
  /*padding: 110px 87px 0px 85px;*/
  padding: 90px 10% 10px 20%;
  padding-left: calc(50% - 100px);
  height: 100%;
  transition: padding 1s;
}

.contact-us .section-2-right > div.animate {
  padding-top: 90px;
  padding-bottom: 117px;
}

.contact-us .section-2 .title {
  font-size: 22.5pt;
  font-weight: 700;
  margin-bottom: 20px;
}

.contact-us .section-2-left .title {
  color: #006b71;
}

.contact-us .section-2 .subtitle {
  font-size: 16px;
  margin-bottom: 40px;
}

.contact-us .section-2 .content {
  margin-bottom: 40px;
}

.contact-us .section-2 .email-link {
  color: inherit;
}

.contact-us .section-2 .icon {
  padding-left: 40px;
  position: relative;
}

.contact-us .section-2 .icon::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  display: inline-block;
  width: 30px;
  height: 30px;
  background-size: 19px;
  background-repeat: no-repeat;
  background-position: center 5px;
}

.contact-us .section-2 .icon.icon-location::before {
  background-image: url("/images/icon-location.png");
}

.contact-us .section-2 .icon.icon-phone::before {
  background-image: url("/images/icon-phone.png");
}

.contact-us .section-2 .icon.icon-mail::before {
  background-image: url("/images/icon-mail.png");
}

.contact-us .section-2 .social-icon {
  display: inline-block;
  width: 34px;
  height: 33px;
  margin-right: 5px;
  position: relative;
  z-index: 1;
}

.contact-us footer {
  background: #fff;
}

.contact-us footer hr {
  border-top: 1px solid #ccc;
}

@media (min-width: 768px) {
  .contact-us .navbar-default {
    border: none;
  }

  .contact-us .header-container {
    border: none;
  }

  #exampleModalCenter .modal-dialog {
    margin: 200px auto;
  }
}

@media (min-width: 1200px) {
  #exampleModalCenter .modal-dialog {
    width: 1140px;
  }

  .contact-us .section-2-left {
    width: 64%;
  }

  .contact-us .section-2-right {
    width: calc(100% - 64%);
  }
}

@media (max-width: 1550px) and (min-width: 1200px) {
  .contact-us .section-2 .contact-us-form .btn-submit {
    width: 100%;
    line-height: 45px;
    margin: 110px 0px 20px 20px;
  }

  .section-2 .contact-frame_captcha-wrp {
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
  }
}

@media (max-width: 1200px) {
  .contact-us .pulse {
    width: 12px;
    height: 12px;
  }

  .contact-us .section-1 > div {
    min-height: 80vh;
  }

  .contact-us .tooltip-text {
    bottom: 35px;
    right: 29px;
  }

  .contact-us .section-1 .title {
    padding-left: 4%;
    font-size: 40pt;
  }

  .contact-us .section-2 {
    margin-left: 4%;
    margin-right: 4%;
    margin-bottom: 1100px;
  }

  .contact-us .section-2 .title {
    font-size: 22pt;
  }

  .contact-us .section-2 .contact-us-form input,
  .contact-us .section-2 .contact-us-form textarea {
    padding: 5px 0;
  }

  .contact-us .section-2 .contact-us-form textarea {
    height: 60px;
  }

  .contact-us .section-2-left {
    border-radius: 20px 20px 0px 0px;
    background-color: white;
  }

  .contact-us .section-2-right {
    border-radius: 0px 0px 20px 20px;
  }

  .contact-us .section-2-left > div {
    padding: 25px 5px 15px 5px;
  }

  .contact-us .section-2-right > div {
    padding: 70px 0px 80px 45px;
    padding-left: calc(50% - 100px);
  }

  .contact-us .section-2-right > div.animate {
    padding-top: 60px;
    padding-bottom: 80px;
  }

  .contact-us .section-2 > div {
    display: block;
  }
}

@media (max-width: 992px) {
  .contact-us .section-2 {
    margin-bottom: 33px;
  }
}

@media (max-width: 768px) {
  .contact-us .section-2-right > div {
    padding-left: 25px;
  }

  .contact-us .pulse {
    left: unset;
    right: 399px;
    top: 19%;
    width: 12px;
    height: 12px;
  }

  .contact-us section h3 {
    font-size: 24px;
  }

  .contact-us .section-1 {
    padding-top: 0px;
  }

  .contact-us .section-1 h2 {
    font-size: 24px;
  }

  .contact-us .section-1 > div {
    min-height: 70vh;
    background-position: 80% 0;
  }

  .contact-us .section-2 {
    margin-left: 0%;
    margin-right: 0%;
    margin-top: -245px;
    margin-bottom: 0px;
  }

  .contact-us .section-2-left {
    border-radius: 0px 0px 0px 0px;
  }

  .contact-us .section-2-right {
    border-radius: 0px 0px 0px 0px;
  }

  .contact-us .section-2 .contact-us-form .btn-submit {
    width: 100%;
    margin: 100px 0px 20px 20px;
    line-height: 45px;
  }

  .contact-us .section-2-left > div {
    height: calc(54.5em + 55px);
  }

  .contact-us .section-2-right .social {
    text-align: center;
    margin-left: calc(200px - 100%);
  }

  .contact-us .section-2-right::after {
    width: 266px;
    height: 121px;
  }
}

@media (max-width: 568px) {
  .contact-us .section-2-left > div {
    height: calc(54.5em + 55px);
  }

  .contact-us .section-1 .title {
    font-size: 35pt;
  }

  .contact-us .section-2 {
    margin-top: -245px;
  }

  .contact-us .section-1 > div {
    min-height: 70vh;
    background-position: 68% 0;
  }

  .contact-us .pulse {
    right: 42%;
  }
}

@media (max-height: 600px) {
  .contact-us .section-2 {
    margin-top: -160px;
  }
}
