.row.no-gutters {
  margin-right: 0;
  margin-left: 0;
}

.row.no-gutters > [class^="col-"],
.row.no-gutters > [class*=" col-"] {
  padding-right: 0;
  padding-left: 0;
}

.inner-portfolio .text img {
  max-width: 100%;
}

.inner-portfolio .header {
  background: linear-gradient(90deg, #006b71 0%, #003231 100%);
  height: 45em;
}

.inner-portfolio .header .project {
  position: relative;
}

.inner-portfolio .header .arrow-left {
  background-image: url(/images/portfolio/inner-project/mechanic-advisor/arrow.png);
  background-size: 42px 21px;
  width: 42px;
  height: 21px;
  transform: rotate(180deg);
}

.inner-portfolio .header .arrow-left-wrap {
  position: absolute;
  top: 40%;
  left: 2%;
}

.inner-portfolio .header .arrow-right {
  background-image: url(/images/portfolio/inner-project/mechanic-advisor/arrow.png);
  background-size: 42px 21px;
  width: 42px;
  height: 21px;
}

.inner-portfolio .header .arrow-right-wrap {
  position: absolute;
  top: 40%;
  right: 2%;
}

.inner-portfolio .header .arrow-right:hover {
  background-image: url(/images/portfolio/inner-project/mechanic-advisor/arrow-hover.png);
  background-size: 42px 21px;
  width: 42px;
  height: 21px;
  transform: rotate(180deg);
}

.inner-portfolio .header .arrow-left:hover {
  background-image: url(/images/portfolio/inner-project/mechanic-advisor/arrow-hover.png);
  background-size: 42px 21px;
  width: 42px;
  height: 21px;
  transform: rotate(360deg);
}

.inner-portfolio .header .category {
  font-size: 11.9pt;
  text-transform: uppercase;
  color: #ffffff;
  width: 60%;
}

.inner-portfolio .header .container {
  padding-top: 6%;
}

.inner-portfolio .header .project-title-container {
  width: 50%;
}

.inner-portfolio .header .project-title {
  font-weight: 600;
  font-size: 36pt;
  margin-top: 0.28em;
  margin-bottom: 0.5em;
  color: #ffffff;
  line-height: 1em;
  letter-spacing: 1pt;
}

.inner-portfolio .header .country {
  padding-top: 14.8px;
  color: #ffffff;
}

.inner-portfolio .header .country-category {
  padding-left: 9px;
  font-weight: 500;
  color: #ffffff;
}

.inner-portfolio .header .white-line {
  width: 3.8em;
  display: inline-block;
  margin-top: 2em;
  border-top: 3px solid #eee;
}

.inner-portfolio .header .URL {
  display: block;
  text-transform: uppercase;
  color: #ffffff;
  padding-top: 0.3em;
}

.inner-portfolio .header .mechanik-advisor.header-img {
  background-image: url(/images/portfolio/inner-project/mechanic-advisor/header-img.png);
  background-repeat: no-repeat;
  background-size: 535px 523px;
}

.inner-portfolio .header .condo-black-book.header-img {
  background-image: url(/images/portfolio/inner-project/condo-black-book/header-img.png);
  background-repeat: no-repeat;
  background-size: 535px 523px;
}

.inner-portfolio .header .header-img {
  width: 570px;
  height: 523px;
  position: absolute;
  right: 0%;
  top: -25%;
  margin-right: 31px;
  background-repeat: no-repeat;
  background-size: contain;
}

.inner-portfolio .description .text {
  position: relative;
  z-index: 1;
  border-radius: 15px;
  margin-top: -11em;
  background-color: #ffffff;
  font-size: 14pt;
  padding: 4.6em 5.2em;
  color: #003231;
  box-shadow: 0px -10px 31px 6px rgba(0, 0, 0, 0.15);
}

.inner-portfolio .device-background {
  background-repeat: no-repeat;
  background-size: contain;
  background-position-x: center;
  margin-top: 4.5%;
  margin-bottom: 4.5%;
}

.inner-portfolio .device-background.size {
  height: 28vw;
}

.inner-portfolio .device-images.margin {
  margin-top: 95px;
}

.inner-portfolio .solution.margin {
  margin-top: 4.5%;
}

.inner-portfolio .solution .under-line {
  text-transform: capitalize;
  margin-bottom: 50px;
}
.inner-portfolio .under-line {
  margin-bottom: 50px;
}

.inner-portfolio .solution .sub-section-header {
  color: #4c5e72;
  margin-top: 35px;
  margin-bottom: 0px;
}

.inner-portfolio .solution .text {
  color: #4c5e72;
  font-size: 14pt;
}

.inner-portfolio .list {
  list-style-image: url("/images/icon-list-item-arrow.png");
  padding-left: 27px;
}

.inner-portfolio .wide-img-video {
  position: relative;
  margin-top: 5%;
  height: 570px;
  z-index: 3;
  display: flex;
  align-items: center;
}

.inner-portfolio .wide-img-video .video-play-btn-wrp {
  color: #fff;
  text-transform: uppercase;
  font-size: 0.875em;
  font-weight: 700;
}

.inner-portfolio .wide-img-video .video-header {
  text-transform: none;
  font-size: 35pt;
  word-spacing: 3pt;
  line-height: 1.3;
}

.inner-portfolio .wide-img-video .image {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: -1;
}

.inner-portfolio .video-play-btn-wrp .btn {
  min-width: 102px;
  border: 2px solid #fff;
  margin-bottom: 14px;
  background: none;
  line-height: 1;
  margin-top: 23px;
  min-height: 51px;
  border-radius: 20px;
}

.inner-portfolio .video-play-btn-wrp .btn > span {
  display: inline-block;
  width: 0;
  height: 0;
  border-style: solid;
  border-width: 12px 0 12px 21px;
  border-color: transparent transparent transparent #ffffff;
  vertical-align: middle;
}

.inner-portfolio .wide-img-video .video-mobile {
  display: none;
}

.inner-portfolio .wide-img-video .image {
  object-fit: cover;
}

.inner-portfolio .wide-img-video .image-description {
  margin: 0 auto;
}

.inner-portfolio .wide-img-video .under-line {
  margin-top: 0px;
}

.inner-portfolio .technologies-list,
.technologies-list .row {
  margin-top: 4.5%;
}

.inner-portfolio .technologies-list .list {
  margin-bottom: 0.8px;
  font-size: 13.6pt;
}

.inner-portfolio .technologies-list .list > li {
  margin-bottom: 0.8px;
}

.inner-portfolio .video-section {
  background-color: #f2f5f8;
  margin-top: 110px;
}

.inner-portfolio .video-section .video-play-btn-wrp {
  text-align: center;
  color: #fff;
  text-transform: uppercase;
  font-size: 0.875em;
  font-weight: 700;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.inner-portfolio .video-section .video-play-btn-wrp .btn {
  min-width: 100px;
  border: 2px solid #fff;
  margin-bottom: 18px;
  background: none;
  line-height: 1;
}

.inner-portfolio .video-section .video-play-btn-wrp .btn > span {
  display: inline-block;
  width: 0;
  height: 0;
  border-style: solid;
  border-width: 12px 0 12px 21px;
  border-color: transparent transparent transparent #ffffff;
  vertical-align: middle;
}

.inner-portfolio .video-section .video-mobile {
  display: none;
}

.inner-portfolio .video-section-right {
  padding-left: 70px;
  padding-bottom: 40px;
}

.inner-portfolio .video-section .title {
  font-size: 36pt;
  color: #323f4e;
}

.inner-portfolio .video-section-right .content {
  margin-bottom: 45px;
}

.inner-portfolio .video-section .image {
  object-fit: cover;
  height: 750px;
}

.inner-portfolio .video-section .image-description {
  margin: 0 auto;
  padding: 18% 12.7% 0;
}

.inner-portfolio .video-section .text-wrap {
  margin-top: 2.2em;
  line-height: 19pt;
}

.inner-portfolio .iphone-advisor {
  margin-top: 150px;
}

.inner-portfolio .other-projects {
  margin-top: 7em;
  overflow: hidden;
}

.other-projects .title {
  text-align: center;
  margin: 0 auto;
  font-size: 31pt;
  width: 300pt;
  line-height: 45pt;
  letter-spacing: -0.5pt;
}

.inner-portfolio .breadcrumb-wrp {
  padding-left: 15px;
}

.inner-portfolio .other-projects .scroll-down {
  background-image: url("/images/portfolio/inner-project/mechanic-advisor/down-arrows.png");
  background-repeat: no-repeat;
  background-size: 23px 24px;
  width: 23px;
  height: 24px;
  margin: 25px auto;
  cursor: pointer;
}

.inner-portfolio .other-projects .no-gutters {
  padding-bottom: 0px;
  margin-bottom: -6px;
}

.inner-portfolio .other-projects .left-project {
  background: #003231;
  height: 30em;
}

.inner-portfolio .other-projects .right-project {
  background: #006b71;
  height: 30em;
}

.inner-portfolio .other-projects .left-project {
  color: #ffffff;
}

.inner-portfolio .other-projects .right-project {
  color: #ffffff;
}

.inner-portfolio .other-projects .left-project .image {
  background-repeat: no-repeat;
  background-size: 399px;
  background-position: center;
  width: 399px;
  height: 427px;
  margin: 2vh;
}

.inner-portfolio .other-projects .right-project .image {
  background-repeat: no-repeat;
  background-size: 399px;
  background-position: center;
  width: 399px;
  height: 427px;
  margin: 2vh;
}

.inner-portfolio .other-projects .btn {
  margin-top: 45px;
  border: 2px solid #ffffff;
  line-height: 38px;
  text-transform: uppercase;
  min-width: 171px;
  min-height: 34px;
  border-radius: 100px;
}

.inner-portfolio .other-projects .btn span {
  letter-spacing: 0.4px;
  font-size: 10pt;
  color: #ffffff;
  font-weight: 100;
}

.inner-portfolio .other-projects .left-project .row .col-sm-6 {
  position: relative;
}

.inner-portfolio .other-projects .left-project .description {
  position: absolute;
  text-align: right;
  max-width: 62%;
  top: 11vh;
  right: 10vh;
}

.inner-portfolio .other-projects .left-project .category {
  text-transform: uppercase;
  font-size: 10.5pt;
}

.inner-portfolio .other-projects .left-project .name {
  font-weight: 600;
  margin-top: 2pt;
  line-height: 27pt;
  margin-bottom: 0;
  font-size: 23pt;
}

.inner-portfolio .other-projects .right-project .description {
  position: absolute;
  text-align: left;
  max-width: 250px;
  top: 11vh;
  left: 10vh;
}

.inner-portfolio .other-projects .right-project .category {
  text-transform: uppercase;
  font-size: 10.5pt;
}

.inner-portfolio .other-projects .right-project .name {
  font-weight: 600;
  margin-top: 2pt;
  line-height: 27pt;
  margin-bottom: 0;
  font-size: 23pt;
}

.inner-portfolio .other-projects .right-project .btn {
}

.inner-portfolio .other-projects .right-project .btn span {
}

@media (max-width: 1400px) {
  .inner-portfolio .other-projects .col-lg-6 {
    float: none;
    width: 100%;
  }

  .inner-portfolio .other-projects .right-project .image {
    float: right;
    margin-right: 200px;
  }

  .inner-portfolio .other-projects .left-project .image {
    margin-left: 200px;
  }

  .inner-portfolio .other-projects .left-project .description {
    right: 200px;
  }

  .inner-portfolio .other-projects .right-project .description {
    left: 200px;
  }
}

@media (max-width: 1300px) {
  .inner-portfolio .header .arrow-right-wrap {
    position: absolute;
    top: 15%;
    right: 2%;
  }

  .inner-portfolio .header .arrow-left-wrap {
    position: absolute;
    top: 15%;
    right: 2%;
  }

  .inner-portfolio .other-projects .right-project .image {
    margin-right: 140px;
  }

  .inner-portfolio .other-projects .left-project .image {
    margin-left: 140px;
  }

  .inner-portfolio .other-projects .left-project .description {
    right: 140px;
  }

  .inner-portfolio .other-projects .right-project .description {
    left: 140px;
  }
}

@media (max-width: 1200px) {
  .inner-portfolio .project .project-title {
    font-size: 30pt;
  }

  .inner-portfolio .project .project-title {
    font-size: 30pt;
  }

  .inner-portfolio .project .header-img {
    background-size: contain;
    width: calc(570px * 0.9);
    height: calc(474px * 0.9);
    margin-right: 0;
    margin-top: 100px;
  }

  .inner-portfolio .arrow-left {
    display: none;
  }

  .inner-portfolio .arrow-right {
    display: none;
  }

  .inner-portfolio .other-projects .right-project .image {
    margin-right: 90px;
  }

  .inner-portfolio .other-projects .left-project .image {
    margin-left: 90px;
  }

  .inner-portfolio .other-projects .left-project .description {
    right: 90px;
  }

  .inner-portfolio .other-projects .right-project .description {
    left: 90px;
  }
}

@media (max-width: 992px) {
  .inner-portfolio .header .mechanik-advisor.header-img {
    display: none;
  }

  .inner-portfolio .header .condo-black-book.header-img {
    display: none;
  }

  .inner-portfolio .other-projects .left-project .image,
  .inner-portfolio .other-projects .right-project .image {
    width: 100%;
    margin: 0;
  }

  .inner-portfolio .other-projects .left-project .image,
  .inner-portfolio .other-projects .right-project .image {
    height: unset;
    background-size: contain;
    padding-bottom: 50%;
  }

  .inner-portfolio .other-projects .left-project .description,
  .inner-portfolio .other-projects .right-project .description {
    all: unset;
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 0 25px 50px 25px;
    text-align: center;
  }

  .inner-portfolio .other-projects .left-project .row,
  .inner-portfolio .other-projects .right-project .row {
    width: 100%;
  }

  .inner-portfolio .other-projects .left-project,
  .inner-portfolio .other-projects .right-project {
    height: unset;
    min-height: 300px;
    display: flex;
    align-items: center;
  }

  .inner-portfolio .iphone-advisor {
    margin-top: 50px;
  }
}

@media (max-width: 960px) {
  .inner-portfolio .device-background.size {
    height: 50vw;
  }
  .inner-portfolio .project .header-img {
    background-size: contain;
    width: calc(570px * 0.75);
    height: calc(474px * 0.75);
    margin-right: 0;
    margin-top: 175px;
  }
}

@media (max-width: 768px) {
  .inner-portfolio .other-projects .right-project .row {
    display: flex;
    flex-direction: column-reverse;
  }

  .inner-portfolio .other-projects .left-project,
  .inner-portfolio .other-projects .right-project {
    height: unset;
    min-height: 400px;
    display: flex;
    align-items: center;
    padding: 50px 0 0 0;
  }

  .inner-portfolio .description .text {
    box-shadow: 0px -37px 31px -2px rgba(0, 0, 0, 0.1);
  }

  .inner-portfolio .other-projects .left-project .name,
  .inner-portfolio .other-projects .right-project .name {
    font-size: 23px;
  }

  .inner-portfolio .wide-img-video .video-header {
    font-size: 33px;
  }
}

@media (max-width: 568px) {
  .inner-portfolio .header .category {
    width: 100%;
  }

  .inner-portfolio .device-background.size {
    height: 80vw;
  }

  .inner-portfolio .project .header-img {
    display: none !important;
  }

  .inner-portfolio .project .project-title-container {
    width: 100%;
  }

  .inner-portfolio .header {
    height: initial;
    padding-bottom: 2em;
  }

  .inner-portfolio .description .text {
    border-radius: 15px 15px 0 0;
    margin-top: -1em;
  }

  .inner-portfolio .description .container .text {
    padding: 15px 15px;
  }

  .inner-portfolio .other-projects {
    margin-top: 3em;
  }

  .inner-portfolio .other-projects .title {
    width: auto;
  }

  .inner-portfolio .other-projects .left-project .category,
  .inner-portfolio .other-projects .right-project .category,
  .inner-portfolio .other-projects .left-project .name,
  .inner-portfolio .other-projects .right-project .name {
    padding-left: 15px;
    padding-right: 15px;
  }
}

@media (max-width: 320px) {
  .inner-portfolio .other-projects .right-project .name,
  .inner-portfolio .other-projects .left-project .name {
    font-size: 18pt;
    line-height: 24pt;
  }
}
