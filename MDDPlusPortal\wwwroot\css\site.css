/*HELPERS: BEGIN*/
.relative {
  position: relative;
}
.sticky {
  position: sticky;
}
.static {
  position: static;
}

.text-bold {
  font-weight: 700;
}

.background-white {
  background-color: #fff;
}

.img-centered {
  margin: auto;
}

.full-width {
  width: 100%;
}
/*HELPERS: END*/

.under-line {
  color: #006b71;
  font-size: 24pt;
}

.under-line::after {
  content: "";
  display: block;
  width: 60px;
  height: 3px;
  background: #f1604b;
  margin-top: 15px;
}

.under-line-white::after {
  background-color: #fff;
}

.under-line-center::after {
  margin-left: auto;
  margin-right: auto;
}

.btn {
  min-width: 200px;
  min-height: 40px;
  border-radius: 5px;
  line-height: 40px;
  padding: 0;
  border: 0;
  color: #fff;
  text-transform: uppercase;
}

.btn > span {
  color: #fff;
  font-weight: 700;
}

.btn,
.btn:focus,
.btn:active,
.btn:focus:active {
  outline: none;
}

.btn-white {
  color: #26253e;
  background: white;
}

.btn-dark-green {
  color: #fff;
  background: #003231;
}

.btn-white:hover {
  background: #f9f9f9;
}

.btn-red {
  background: #f1604b;
}

.btn-blue {
  background: #00bcd4;
}

.btn-big {
  min-width: 230px;
  min-height: 45px;
  border-radius: 5px;
  line-height: 40px;
}

.btn-red-big {
  background: #f1604b;
  min-width: 230px;
  min-height: 45px;
  border-radius: 25px;
  line-height: 40px;
}

.btn-red:hover,
.btn-red-big:hover,
.btn-blue:hover,
.btn-blue-big:hover {
  background: #0097a7;
}

.btn-white {
  background: #fff;
}

.overflow-hidden {
  overflow: hidden;
}

.btn-white > span {
  color: #000;
}

/* MARGINS & PADDINGS */
.p-n {
  padding: 0;
}

.p-l {
  padding-left: 15px;
}

.p-r {
  padding-right: 15px;
}

.p-xxs {
  padding: 5px;
}

.p-xs {
  padding: 10px;
}

.p-sm {
  padding: 15px;
}

.p-m {
  padding: 20px;
}

.p-md {
  padding: 25px;
}

.p-lg {
  padding: 30px;
}

.p-xl {
  padding: 40px;
}

.p-b {
  padding-bottom: 50px;
}

.m-xxs {
  margin: 2px 4px;
}

.m-xs {
  margin: 5px;
}

.m-sm {
  margin: 10px;
}

.m {
  margin: 15px;
}

.m-md {
  margin: 20px;
}

.m-lg {
  margin: 30px;
}

.m-xl {
  margin: 50px;
}

.m-n {
  margin: 0 !important;
}

.m-l-none {
  margin-left: 0;
}

.m-l-xs {
  margin-left: 5px;
}

.m-l-sm {
  margin-left: 10px;
}

.m-l {
  margin-left: 15px;
}

.m-l-md {
  margin-left: 20px;
}

.m-l-lg {
  margin-left: 30px;
}

.m-l-xl {
  margin-left: 40px;
}

.m-l-n-xxs {
  margin-left: -1px;
}

.m-l-n-xs {
  margin-left: -5px;
}

.m-l-n-sm {
  margin-left: -10px;
}

.m-l-n {
  margin-left: -15px;
}

.m-l-n-md {
  margin-left: -20px;
}

.m-l-n-lg {
  margin-left: -30px;
}

.m-l-n-xl {
  margin-left: -40px;
}

.m-t-none {
  margin-top: 0;
}

.m-t-xxs {
  margin-top: 1px;
}

.m-t-xs {
  margin-top: 5px;
}

.m-t-sm {
  margin-top: 10px;
}

.m-t {
  margin-top: 15px;
}

.m-t-md {
  margin-top: 20px;
}

.m-t-lg {
  margin-top: 30px;
}

.m-t-xl {
  margin-top: 40px;
}

.m-t-n-xxs {
  margin-top: -1px;
}

.m-t-n-xs {
  margin-top: -5px;
}

.m-t-n-sm {
  margin-top: -10px;
}

.m-t-n {
  margin-top: -15px;
}

.m-t-n-md {
  margin-top: -20px;
}

.m-t-n-lg {
  margin-top: -30px;
}

.m-t-n-xl {
  margin-top: -40px;
}
.m-t-xxl {
  margin-top: 55px;
}

.m-r-none {
  margin-right: 0;
}

.m-r-xxs {
  margin-right: 1px;
}

.m-r-xs {
  margin-right: 5px;
}

.m-r-sm {
  margin-right: 10px;
}

.m-r {
  margin-right: 15px;
}

.m-r-md {
  margin-right: 20px;
}

.m-r-lg {
  margin-right: 30px;
}

.m-r-xl {
  margin-right: 40px;
}

.m-r-n-xxs {
  margin-right: -1px;
}

.m-r-n-xs {
  margin-right: -5px;
}

.m-r-n-sm {
  margin-right: -10px;
}

.m-r-n {
  margin-right: -15px;
}

.m-r-n-md {
  margin-right: -20px;
}

.m-r-n-lg {
  margin-right: -30px;
}

.m-r-n-xl {
  margin-right: -40px;
}

.m-b-none {
  margin-bottom: 0;
}

.m-b-xxs {
  margin-bottom: 1px;
}

.m-b-xs {
  margin-bottom: 5px;
}

.m-b-sm {
  margin-bottom: 10px;
}

.m-b {
  margin-bottom: 15px;
}

.m-b-md {
  margin-bottom: 20px;
}

.m-b-lg {
  margin-bottom: 30px;
}

.m-b-xl {
  margin-bottom: 40px;
}
.m-b-xxl {
  margin-bottom: 50px;
}

.m-b-n-xxs {
  margin-bottom: -1px;
}

.m-b-n-xs {
  margin-bottom: -5px;
}

.m-b-n-sm {
  margin-bottom: -10px;
}

.m-b-n {
  margin-bottom: -15px;
}

.m-b-n-md {
  margin-bottom: -20px;
}

.m-b-n-lg {
  margin-bottom: -30px;
}

.m-b-n-xl {
  margin-bottom: -40px;
}

.space-15 {
  margin: 15px 0;
}

.space-20 {
  margin: 20px 0;
}

.space-25 {
  margin: 25px 0;
}

.space-30 {
  margin: 30px 0;
}

.space-40 {
  margin: 40px 0;
}

.space-50 {
  margin: 50px 0;
}
