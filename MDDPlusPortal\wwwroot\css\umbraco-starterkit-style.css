@import url(https://fonts.googleapis.com/css?family=Montserrat|Source+Sans+Pro:400,700,300,600,600italic,400italic);
@import url(https://fonts.googleapis.com/css?family=Roboto+Mono:400,300,500|Lato:400,700,900);
@import url(https://fonts.googleapis.com/css?family=Playfair+Display:400,700italic,700,400italic|Noto+Sans:400,700);

*, :after, :before {
    box-sizing: border-box
}

body, html {
    -webkit-font-smoothing: antialiased;
    font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale
}

html {
    font-family: sans-serif;
    -ms-text-size-adjust: 100%;
    -webkit-text-size-adjust: 100%
}

body {
    margin: 0
}

article, aside, details, figcaption, figure, footer, header, hgroup, main, menu, nav, section, summary {
    display: block
}

audio, canvas, progress, video {
    display: inline-block;
    vertical-align: baseline
}

    audio:not([controls]) {
        display: none;
        height: 0
    }

[hidden], template {
    display: none
}

a {
    background-color: transparent
}

    a:active, a:hover {
        outline: 0
    }

abbr[title] {
    border-bottom: 1px dotted
}

b, strong {
    font-weight: 700
}

dfn {
    font-style: italic
}

h1 {
    font-size: 2em;
    margin: .67em 0
}

mark {
    background: #ff0;
    color: #000
}

small {
    font-size: 80%
}

sub, sup {
    font-size: 75%;
    line-height: 0;
    position: relative;
    vertical-align: baseline
}

sup {
    top: -.5em
}

sub {
    bottom: -.25em
}

img {
    border: 0
}

svg:not(:root) {
    overflow: hidden
}

figure {
    margin: 1em 40px
}

hr {
    box-sizing: content-box;
    height: 0
}

pre {
    overflow: auto
}

code, kbd, pre, samp {
    font-family: monospace,monospace;
    font-size: 1em
}

button, input, optgroup, select, textarea {
    color: inherit;
    font: inherit;
    margin: 0
}

button {
    overflow: visible
}

button, select {
    text-transform: none
}

button, html input[type=button], input[type=reset], input[type=submit] {
    -webkit-appearance: button;
    cursor: pointer
}

    button[disabled], html input[disabled] {
        cursor: default
    }

    button::-moz-focus-inner, input::-moz-focus-inner {
        border: 0;
        padding: 0
    }

input {
    line-height: normal
}

    input[type=checkbox], input[type=radio] {
        box-sizing: border-box;
        padding: 0
    }

    input[type=number]::-webkit-inner-spin-button, input[type=number]::-webkit-outer-spin-button {
        height: auto
    }

    input[type=search] {
        -webkit-appearance: textfield;
        box-sizing: content-box
    }

        input[type=search]::-webkit-search-cancel-button, input[type=search]::-webkit-search-decoration {
            -webkit-appearance: none
        }

fieldset {
    border: 1px solid silver;
    margin: 0 2px;
    padding: .35em .625em .75em
}

legend {
    border: 0;
    padding: 0
}

textarea {
    overflow: auto
}

optgroup {
    font-weight: 700
}

table {
    border-collapse: collapse;
    border-spacing: 0
}

td, th {
    padding: 0
}

.container {
    margin-right: auto;
    margin-left: auto;
    padding-left: 15px;
    padding-right: 15px
}

    .container:after, .container:before {
        content: " ";
        display: table
    }

    .container:after {
        clear: both
    }

.container-fluid {
    margin-right: auto;
    margin-left: auto;
    padding-left: 15px;
    padding-right: 15px
}

    .container-fluid:after, .container-fluid:before {
        content: " ";
        display: table
    }

    .container-fluid:after {
        clear: both
    }

.row {
    margin-left: -15px;
    margin-right: -15px
}

    .row:after, .row:before {
        content: " ";
        display: table
    }

    .row:after {
        clear: both
    }

.col-lg-1, .col-lg-2, .col-lg-3, .col-lg-4, .col-lg-5, .col-lg-6, .col-lg-7, .col-lg-8, .col-lg-9, .col-lg-10, .col-lg-11, .col-lg-12, .col-md-1, .col-md-2, .col-md-3, .col-md-4, .col-md-5, .col-md-6, .col-md-7, .col-md-8, .col-md-9, .col-md-10, .col-md-11, .col-md-12, .col-sm-1, .col-sm-2, .col-sm-3, .col-sm-4, .col-sm-5, .col-sm-6, .col-sm-7, .col-sm-8, .col-sm-9, .col-sm-10, .col-sm-11, .col-sm-12, .col-xs-1, .col-xs-2, .col-xs-3, .col-xs-4, .col-xs-5, .col-xs-6, .col-xs-7, .col-xs-8, .col-xs-9, .col-xs-10, .col-xs-11, .col-xs-12 {
    position: relative;
    min-height: 1px;
    padding-left: 15px;
    padding-right: 15px
}

.col-xs-1, .col-xs-2, .col-xs-3, .col-xs-4, .col-xs-5, .col-xs-6, .col-xs-7, .col-xs-8, .col-xs-9, .col-xs-10, .col-xs-11, .col-xs-12 {
    float: left
}

.col-xs-1 {
    width: 8.333333%
}

.col-xs-2 {
    width: 16.666667%
}

.col-xs-3 {
    width: 25%
}

.col-xs-4 {
    width: 33.333333%
}

.col-xs-5 {
    width: 41.666667%
}

.col-xs-6 {
    width: 50%
}

.col-xs-7 {
    width: 58.333333%
}

.col-xs-8 {
    width: 66.666667%
}

.col-xs-9 {
    width: 75%
}

.col-xs-10 {
    width: 83.333333%
}

.col-xs-11 {
    width: 91.666667%
}

.col-xs-12 {
    width: 100%
}

.col-xs-pull-0 {
    right: auto
}

.col-xs-pull-1 {
    right: 8.333333%
}

.col-xs-pull-2 {
    right: 16.666667%
}

.col-xs-pull-3 {
    right: 25%
}

.col-xs-pull-4 {
    right: 33.333333%
}

.col-xs-pull-5 {
    right: 41.666667%
}

.col-xs-pull-6 {
    right: 50%
}

.col-xs-pull-7 {
    right: 58.333333%
}

.col-xs-pull-8 {
    right: 66.666667%
}

.col-xs-pull-9 {
    right: 75%
}

.col-xs-pull-10 {
    right: 83.333333%
}

.col-xs-pull-11 {
    right: 91.666667%
}

.col-xs-pull-12 {
    right: 100%
}

.col-xs-push-0 {
    left: auto
}

.col-xs-push-1 {
    left: 8.333333%
}

.col-xs-push-2 {
    left: 16.666667%
}

.col-xs-push-3 {
    left: 25%
}

.col-xs-push-4 {
    left: 33.333333%
}

.col-xs-push-5 {
    left: 41.666667%
}

.col-xs-push-6 {
    left: 50%
}

.col-xs-push-7 {
    left: 58.333333%
}

.col-xs-push-8 {
    left: 66.666667%
}

.col-xs-push-9 {
    left: 75%
}

.col-xs-push-10 {
    left: 83.333333%
}

.col-xs-push-11 {
    left: 91.666667%
}

.col-xs-push-12 {
    left: 100%
}

.col-xs-offset-0 {
    margin-left: 0
}

.col-xs-offset-1 {
    margin-left: 8.333333%
}

.col-xs-offset-2 {
    margin-left: 16.666667%
}

.col-xs-offset-3 {
    margin-left: 25%
}

.col-xs-offset-4 {
    margin-left: 33.333333%
}

.col-xs-offset-5 {
    margin-left: 41.666667%
}

.col-xs-offset-6 {
    margin-left: 50%
}

.col-xs-offset-7 {
    margin-left: 58.333333%
}

.col-xs-offset-8 {
    margin-left: 66.666667%
}

.col-xs-offset-9 {
    margin-left: 75%
}

.col-xs-offset-10 {
    margin-left: 83.333333%
}

.col-xs-offset-11 {
    margin-left: 91.666667%
}

.col-xs-offset-12 {
    margin-left: 100%
}

fieldset {
    margin: 0;
    min-width: 0
}

fieldset, legend {
    padding: 0;
    border: 0
}

legend {
    display: block;
    width: 100%;
    margin-bottom: 20px;
    font-size: 21px;
    line-height: inherit;
    color: #333;
    border-bottom: 1px solid #e5e5e5
}

label {
    display: inline-block;
    max-width: 100%;
    margin-bottom: 5px;
    font-weight: 700
}

input[type=search] {
    box-sizing: border-box
}

input[type=checkbox], input[type=radio] {
    margin: 4px 0 0;
    margin-top: 1px\9;
    line-height: normal
}

input[type=file] {
    display: block
}

input[type=range] {
    display: block;
    width: 100%
}

select[multiple], select[size] {
    height: auto
}

input[type=checkbox]:focus, input[type=file]:focus, input[type=radio]:focus {
    outline: thin dotted;
    outline: 5px auto -webkit-focus-ring-color;
    outline-offset: -2px
}

output {
    padding-top: 7px
}

.form-control, output {
    display: block;
    font-size: 14px;
    line-height: 1.428571;
    color: #555
}

.form-control {
    width: 100%;
    height: 34px;
    padding: 6px 12px;
    background-color: #fff;
    background-image: none;
    border: 1px solid #ccc;
    border-radius: 4px;
    box-shadow: inset 0 1px 1px rgba(0,0,0,.075);
    transition: border-color .15s ease-in-out,box-shadow .15s ease-in-out
}

    .form-control:focus {
        border-color: #66afe9;
        outline: 0;
        box-shadow: inset 0 1px 1px rgba(0,0,0,.075),0 0 8px rgba(102,175,233,.6)
    }

    .form-control::-moz-placeholder {
        color: #999;
        opacity: 1
    }

    .form-control:-ms-input-placeholder {
        color: #999
    }

    .form-control::-webkit-input-placeholder {
        color: #999
    }

    .form-control::-ms-expand {
        border: 0;
        background-color: transparent
    }

    .form-control[disabled], .form-control[readonly], fieldset[disabled] .form-control {
        background-color: #eee;
        opacity: 1
    }

    .form-control[disabled], fieldset[disabled] .form-control {
        cursor: not-allowed
    }

textarea.form-control {
    height: auto
}

input[type=search] {
    -webkit-appearance: none
}

.form-group {
    margin-bottom: 15px
}

.checkbox, .radio {
    position: relative;
    display: block;
    margin-top: 10px;
    margin-bottom: 10px
}

    .checkbox label, .radio label {
        min-height: 20px;
        padding-left: 20px;
        margin-bottom: 0;
        font-weight: 400;
        cursor: pointer
    }

    .checkbox-inline input[type=checkbox], .checkbox input[type=checkbox], .radio-inline input[type=radio], .radio input[type=radio] {
        position: absolute;
        margin-left: -20px;
        margin-top: 4px\9
    }

    .checkbox + .checkbox, .radio + .radio {
        margin-top: -5px
    }

.checkbox-inline, .radio-inline {
    position: relative;
    display: inline-block;
    padding-left: 20px;
    margin-bottom: 0;
    vertical-align: middle;
    font-weight: 400;
    cursor: pointer
}

    .checkbox-inline + .checkbox-inline, .radio-inline + .radio-inline {
        margin-top: 0;
        margin-left: 10px
    }

    .checkbox-inline.disabled, .checkbox.disabled label, .radio-inline.disabled, .radio.disabled label, fieldset[disabled] .checkbox-inline, fieldset[disabled] .checkbox label, fieldset[disabled] .radio-inline, fieldset[disabled] .radio label, fieldset[disabled] input[type=checkbox], fieldset[disabled] input[type=radio], input[type=checkbox].disabled, input[type=checkbox][disabled], input[type=radio].disabled, input[type=radio][disabled] {
        cursor: not-allowed
    }

.form-control-static {
    padding-top: 7px;
    padding-bottom: 7px;
    margin-bottom: 0;
    min-height: 34px
}

    .form-control-static.input-lg, .form-control-static.input-sm, .input-group-lg > .form-control-static.form-control, .input-group-lg > .form-control-static.input-group-addon, .input-group-lg > .input-group-btn > .form-control-static.btn, .input-group-sm > .form-control-static.form-control, .input-group-sm > .form-control-static.input-group-addon, .input-group-sm > .input-group-btn > .form-control-static.btn {
        padding-left: 0;
        padding-right: 0
    }

.input-group-sm > .form-control, .input-group-sm > .input-group-addon, .input-group-sm > .input-group-btn > .btn, .input-sm {
    height: 30px;
    padding: 5px 10px;
    font-size: 12px;
    line-height: 1.5;
    border-radius: 3px
}

.input-group-sm > .input-group-btn > select.btn, .input-group-sm > select.form-control, .input-group-sm > select.input-group-addon, select.input-sm {
    height: 30px;
    line-height: 30px
}

.input-group-sm > .input-group-btn > select[multiple].btn, .input-group-sm > .input-group-btn > textarea.btn, .input-group-sm > select[multiple].form-control, .input-group-sm > select[multiple].input-group-addon, .input-group-sm > textarea.form-control, .input-group-sm > textarea.input-group-addon, select[multiple].input-sm, textarea.input-sm {
    height: auto
}

.form-group-sm .form-control {
    height: 30px;
    padding: 5px 10px;
    font-size: 12px;
    line-height: 1.5;
    border-radius: 3px
}

.form-group-sm select.form-control {
    height: 30px;
    line-height: 30px
}

.form-group-sm select[multiple].form-control, .form-group-sm textarea.form-control {
    height: auto
}

.form-group-sm .form-control-static {
    height: 30px;
    min-height: 32px;
    padding: 6px 10px;
    font-size: 12px;
    line-height: 1.5
}

.input-group-lg > .form-control, .input-group-lg > .input-group-addon, .input-group-lg > .input-group-btn > .btn, .input-lg {
    height: 46px;
    padding: 10px 16px;
    font-size: 18px;
    line-height: 1.333333;
    border-radius: 6px
}

.input-group-lg > .input-group-btn > select.btn, .input-group-lg > select.form-control, .input-group-lg > select.input-group-addon, select.input-lg {
    height: 46px;
    line-height: 46px
}

.input-group-lg > .input-group-btn > select[multiple].btn, .input-group-lg > .input-group-btn > textarea.btn, .input-group-lg > select[multiple].form-control, .input-group-lg > select[multiple].input-group-addon, .input-group-lg > textarea.form-control, .input-group-lg > textarea.input-group-addon, select[multiple].input-lg, textarea.input-lg {
    height: auto
}

.form-group-lg .form-control {
    height: 46px;
    padding: 10px 16px;
    font-size: 18px;
    line-height: 1.333333;
    border-radius: 6px
}

.form-group-lg select.form-control {
    height: 46px;
    line-height: 46px
}

.form-group-lg select[multiple].form-control, .form-group-lg textarea.form-control {
    height: auto
}

.form-group-lg .form-control-static {
    height: 46px;
    min-height: 38px;
    padding: 11px 16px;
    font-size: 18px;
    line-height: 1.333333
}

.has-feedback {
    position: relative
}

    .has-feedback .form-control {
        padding-right: 42.5px
    }

.form-control-feedback {
    position: absolute;
    top: 0;
    right: 0;
    z-index: 2;
    display: block;
    width: 34px;
    height: 34px;
    line-height: 34px;
    text-align: center;
    pointer-events: none
}

.form-group-lg .form-control + .form-control-feedback, .input-group-lg + .form-control-feedback, .input-group-lg > .form-control + .form-control-feedback, .input-group-lg > .input-group-addon + .form-control-feedback, .input-group-lg > .input-group-btn > .btn + .form-control-feedback, .input-lg + .form-control-feedback {
    width: 46px;
    height: 46px;
    line-height: 46px
}

.form-group-sm .form-control + .form-control-feedback, .input-group-sm + .form-control-feedback, .input-group-sm > .form-control + .form-control-feedback, .input-group-sm > .input-group-addon + .form-control-feedback, .input-group-sm > .input-group-btn > .btn + .form-control-feedback, .input-sm + .form-control-feedback {
    width: 30px;
    height: 30px;
    line-height: 30px
}

.has-success .checkbox, .has-success .checkbox-inline, .has-success.checkbox-inline label, .has-success.checkbox label, .has-success .control-label, .has-success .help-block, .has-success .radio, .has-success .radio-inline, .has-success.radio-inline label, .has-success.radio label {
    color: #3c763d
}

.has-success .form-control {
    border-color: #3c763d;
    box-shadow: inset 0 1px 1px rgba(0,0,0,.075)
}

    .has-success .form-control:focus {
        border-color: #2b542c;
        box-shadow: inset 0 1px 1px rgba(0,0,0,.075),0 0 6px #67b168
    }

.has-success .input-group-addon {
    color: #3c763d;
    border-color: #3c763d;
    background-color: #dff0d8
}

.has-success .form-control-feedback {
    color: #3c763d
}

.has-warning .checkbox, .has-warning .checkbox-inline, .has-warning.checkbox-inline label, .has-warning.checkbox label, .has-warning .control-label, .has-warning .help-block, .has-warning .radio, .has-warning .radio-inline, .has-warning.radio-inline label, .has-warning.radio label {
    color: #8a6d3b
}

.has-warning .form-control {
    border-color: #8a6d3b;
    box-shadow: inset 0 1px 1px rgba(0,0,0,.075)
}

    .has-warning .form-control:focus {
        border-color: #66512c;
        box-shadow: inset 0 1px 1px rgba(0,0,0,.075),0 0 6px #c0a16b
    }

.has-warning .input-group-addon {
    color: #8a6d3b;
    border-color: #8a6d3b;
    background-color: #fcf8e3
}

.has-warning .form-control-feedback {
    color: #8a6d3b
}

.has-error .checkbox, .has-error .checkbox-inline, .has-error.checkbox-inline label, .has-error.checkbox label, .has-error .control-label, .has-error .help-block, .has-error .radio, .has-error .radio-inline, .has-error.radio-inline label, .has-error.radio label {
    color: #a94442
}

.has-error .form-control {
    border-color: #a94442;
    box-shadow: inset 0 1px 1px rgba(0,0,0,.075)
}

    .has-error .form-control:focus {
        border-color: #843534;
        box-shadow: inset 0 1px 1px rgba(0,0,0,.075),0 0 6px #ce8483
    }

.has-error .input-group-addon {
    color: #a94442;
    border-color: #a94442;
    background-color: #f2dede
}

.has-error .form-control-feedback {
    color: #a94442
}

.has-feedback label ~ .form-control-feedback {
    top: 25px
}

.has-feedback label.sr-only ~ .form-control-feedback {
    top: 0
}

.help-block {
    display: block;
    margin-top: 5px;
    margin-bottom: 10px;
    color: #737373
}

.form-horizontal .checkbox, .form-horizontal .checkbox-inline, .form-horizontal .radio, .form-horizontal .radio-inline {
    margin-top: 0;
    margin-bottom: 0;
    padding-top: 7px
}

.form-horizontal .checkbox, .form-horizontal .radio {
    min-height: 27px
}

.form-horizontal .form-group {
    margin-left: -15px;
    margin-right: -15px
}

    .form-horizontal .form-group:after, .form-horizontal .form-group:before {
        content: " ";
        display: table
    }

    .form-horizontal .form-group:after {
        clear: both
    }

.form-horizontal .has-feedback .form-control-feedback {
    right: 15px
}

.input-group {
    position: relative;
    display: table;
    border-collapse: separate
}

    .input-group[class*=col-] {
        float: none;
        padding-left: 0;
        padding-right: 0
    }

    .input-group .form-control {
        position: relative;
        z-index: 2;
        float: left;
        width: 100%;
        margin-bottom: 0
    }

        .input-group .form-control:focus {
            z-index: 3
        }

    .input-group-addon, .input-group-btn, .input-group .form-control {
        display: table-cell
    }

        .input-group-addon:not(:first-child):not(:last-child), .input-group-btn:not(:first-child):not(:last-child), .input-group .form-control:not(:first-child):not(:last-child) {
            border-radius: 0
        }

.input-group-addon, .input-group-btn {
    width: 1%;
    white-space: nowrap;
    vertical-align: middle
}

.input-group-addon {
    padding: 6px 12px;
    font-size: 14px;
    font-weight: 400;
    line-height: 1;
    color: #555;
    text-align: center;
    background-color: #eee;
    border: 1px solid #ccc;
    border-radius: 4px
}

    .input-group-addon.input-sm, .input-group-sm > .input-group-addon, .input-group-sm > .input-group-btn > .input-group-addon.btn {
        padding: 5px 10px;
        font-size: 12px;
        border-radius: 3px
    }

    .input-group-addon.input-lg, .input-group-lg > .input-group-addon, .input-group-lg > .input-group-btn > .input-group-addon.btn {
        padding: 10px 16px;
        font-size: 18px;
        border-radius: 6px
    }

    .input-group-addon input[type=checkbox], .input-group-addon input[type=radio] {
        margin-top: 0
    }

    .input-group-addon:first-child, .input-group-btn:first-child > .btn, .input-group-btn:first-child > .btn-group > .btn, .input-group-btn:first-child > .dropdown-toggle, .input-group-btn:last-child > .btn-group:not(:last-child) > .btn, .input-group-btn:last-child > .btn:not(:last-child):not(.dropdown-toggle), .input-group .form-control:first-child {
        border-bottom-right-radius: 0;
        border-top-right-radius: 0
    }

    .input-group-addon:first-child {
        border-right: 0
    }

    .input-group-addon:last-child, .input-group-btn:first-child > .btn-group:not(:first-child) > .btn, .input-group-btn:first-child > .btn:not(:first-child), .input-group-btn:last-child > .btn, .input-group-btn:last-child > .btn-group > .btn, .input-group-btn:last-child > .dropdown-toggle, .input-group .form-control:last-child {
        border-bottom-left-radius: 0;
        border-top-left-radius: 0
    }

    .input-group-addon:last-child {
        border-left: 0
    }

.input-group-btn {
    font-size: 0;
    white-space: nowrap
}

    .input-group-btn, .input-group-btn > .btn {
        position: relative
    }

        .input-group-btn > .btn + .btn {
            margin-left: -1px
        }

        .input-group-btn > .btn:active, .input-group-btn > .btn:focus, .input-group-btn > .btn:hover {
            z-index: 2
        }

        .input-group-btn:first-child > .btn, .input-group-btn:first-child > .btn-group {
            margin-right: -1px
        }

        .input-group-btn:last-child > .btn, .input-group-btn:last-child > .btn-group {
            z-index: 2;
            margin-left: -1px
        }

.label {
    display: inline;
    padding: .2em .6em .3em;
    font-size: 75%;
    font-weight: 700;
    line-height: 1;
    color: #fff;
    text-align: center;
    white-space: nowrap;
    vertical-align: baseline;
    border-radius: .25em
}

    .label:empty {
        display: none
    }

.btn .label {
    position: relative;
    top: -1px
}

a.label:focus, a.label:hover {
    color: #fff;
    text-decoration: none;
    cursor: pointer
}

.label-default {
    background-color: #777
}

    .label-default[href]:focus, .label-default[href]:hover {
        background-color: #5e5e5e
    }

.label-primary {
    background-color: #337ab7
}

    .label-primary[href]:focus, .label-primary[href]:hover {
        background-color: #286090
    }

.label-success {
    background-color: #5cb85c
}

    .label-success[href]:focus, .label-success[href]:hover {
        background-color: #449d44
    }

.label-info {
    background-color: #5bc0de
}

    .label-info[href]:focus, .label-info[href]:hover {
        background-color: #31b0d5
    }

.label-warning {
    background-color: #f0ad4e
}

    .label-warning[href]:focus, .label-warning[href]:hover {
        background-color: #ec971f
    }

.label-danger {
    background-color: #d9534f
}

    .label-danger[href]:focus, .label-danger[href]:hover {
        background-color: #c9302c
    }

h1 {
    font-size: 2.2em
}

h2 {
    font-size: 1.6em;
    letter-spacing: 2px;
    text-transform: uppercase
}

.product-grid__item:hover body.theme-color-water .product-grid__item__name, .product-grid__item:hover body.theme-color-water .product-grid__item__price, body.theme-color-water .bg-main-color, body.theme-color-water .button--border--solid, body.theme-color-water .mobile-nav, body.theme-color-water .product-grid__item:hover .product-grid__item__name, body.theme-color-water .product-grid__item:hover .product-grid__item__price, body.theme-color-water .section--themed {
    background-color: #362bdc;
    color: #fff
}

.hamburger.lines:hover.active body.theme-color-water span:after, .hamburger.lines:hover.active body.theme-color-water span:before, .hamburger body.theme-color-water span, .hamburger body.theme-color-water span:after, .hamburger body.theme-color-water span:before, body.theme-color-water .bg-secondary-color, body.theme-color-water .hamburger.lines:hover.active span:after, body.theme-color-water .hamburger.lines:hover.active span:before, body.theme-color-water .hamburger span, body.theme-color-water .hamburger span:after, body.theme-color-water .hamburger span:before, body.theme-color-water .nav-link--home__text:after {
    background-color: #fff;
    color: #362bdc
}

.blogposts-centered body.theme-color-water .blogpost-title, body.theme-color-water .blogposts-centered .blogpost-title, body.theme-color-water .button--border, body.theme-color-water .font-main-color, body.theme-color-water .nav-link, body.theme-color-water .section__hero-content, body.theme-color-water footer a, footer body.theme-color-water a {
    color: #fff
}

article body.theme-color-water a, article body.theme-color-water a:visited, body.theme-color-water .employee-grid__item__email, body.theme-color-water .font-link-color, body.theme-color-water article a, body.theme-color-water article a:visited {
    color: #362bdc
}

body.theme-color-water--active.nav-link:visited, body.theme-color-water .border-main-color, body.theme-color-water .button--border, body.theme-color-water .nav-link--active, body.theme-color-water .nav-link:hover {
    border-color: #fff
}

.product-grid__item:hover body.theme-color-sun .product-grid__item__name, .product-grid__item:hover body.theme-color-sun .product-grid__item__price, body.theme-color-sun .bg-main-color, body.theme-color-sun .button--border--solid, body.theme-color-sun .mobile-nav, body.theme-color-sun .product-grid__item:hover .product-grid__item__name, body.theme-color-sun .product-grid__item:hover .product-grid__item__price, body.theme-color-sun .section--themed {
    background-color: #f8ea88;
    color: #000
}

.hamburger.lines:hover.active body.theme-color-sun span:after, .hamburger.lines:hover.active body.theme-color-sun span:before, .hamburger body.theme-color-sun span, .hamburger body.theme-color-sun span:after, .hamburger body.theme-color-sun span:before, body.theme-color-sun .bg-secondary-color, body.theme-color-sun .hamburger.lines:hover.active span:after, body.theme-color-sun .hamburger.lines:hover.active span:before, body.theme-color-sun .hamburger span, body.theme-color-sun .hamburger span:after, body.theme-color-sun .hamburger span:before, body.theme-color-sun .nav-link--home__text:after {
    background-color: #000;
    color: #f8ea88
}

.blogposts-centered body.theme-color-sun .blogpost-title, body.theme-color-sun .blogposts-centered .blogpost-title, body.theme-color-sun .button--border, body.theme-color-sun .font-main-color, body.theme-color-sun .nav-link, body.theme-color-sun .section__hero-content, body.theme-color-sun footer a, footer body.theme-color-sun a {
    color: #000
}

article body.theme-color-sun a, article body.theme-color-sun a:visited, body.theme-color-sun .employee-grid__item__email, body.theme-color-sun .font-link-color, body.theme-color-sun article a, body.theme-color-sun article a:visited {
    color: #b0a55b
}

body.theme-color-sun--active.nav-link:visited, body.theme-color-sun .border-main-color, body.theme-color-sun .button--border, body.theme-color-sun .nav-link--active, body.theme-color-sun .nav-link:hover {
    border-color: #000
}

.product-grid__item:hover body.theme-color-earth .product-grid__item__name, .product-grid__item:hover body.theme-color-earth .product-grid__item__price, body.theme-color-earth .bg-main-color, body.theme-color-earth .button--border--solid, body.theme-color-earth .mobile-nav, body.theme-color-earth .product-grid__item:hover .product-grid__item__name, body.theme-color-earth .product-grid__item:hover .product-grid__item__price, body.theme-color-earth .section--themed {
    background-color: #e7afa3;
    color: #000
}

.hamburger.lines:hover.active body.theme-color-earth span:after, .hamburger.lines:hover.active body.theme-color-earth span:before, .hamburger body.theme-color-earth span, .hamburger body.theme-color-earth span:after, .hamburger body.theme-color-earth span:before, body.theme-color-earth .bg-secondary-color, body.theme-color-earth .hamburger.lines:hover.active span:after, body.theme-color-earth .hamburger.lines:hover.active span:before, body.theme-color-earth .hamburger span, body.theme-color-earth .hamburger span:after, body.theme-color-earth .hamburger span:before, body.theme-color-earth .nav-link--home__text:after {
    background-color: #000;
    color: #e7afa3
}

.blogposts-centered body.theme-color-earth .blogpost-title, body.theme-color-earth .blogposts-centered .blogpost-title, body.theme-color-earth .button--border, body.theme-color-earth .font-main-color, body.theme-color-earth .nav-link, body.theme-color-earth .section__hero-content, body.theme-color-earth footer a, footer body.theme-color-earth a {
    color: #000
}

article body.theme-color-earth a, article body.theme-color-earth a:visited, body.theme-color-earth .employee-grid__item__email, body.theme-color-earth .font-link-color, body.theme-color-earth article a, body.theme-color-earth article a:visited {
    color: #90514c
}

body.theme-color-earth--active.nav-link:visited, body.theme-color-earth .border-main-color, body.theme-color-earth .button--border, body.theme-color-earth .nav-link--active, body.theme-color-earth .nav-link:hover {
    border-color: #000
}

.product-grid__item:hover body.theme-color-freaky .product-grid__item__name, .product-grid__item:hover body.theme-color-freaky .product-grid__item__price, body.theme-color-freaky .bg-main-color, body.theme-color-freaky .button--border--solid, body.theme-color-freaky .mobile-nav, body.theme-color-freaky .product-grid__item:hover .product-grid__item__name, body.theme-color-freaky .product-grid__item:hover .product-grid__item__price, body.theme-color-freaky .section--themed {
    background-color: #77f3d9;
    color: #000
}

.hamburger.lines:hover.active body.theme-color-freaky span:after, .hamburger.lines:hover.active body.theme-color-freaky span:before, .hamburger body.theme-color-freaky span, .hamburger body.theme-color-freaky span:after, .hamburger body.theme-color-freaky span:before, body.theme-color-freaky .bg-secondary-color, body.theme-color-freaky .hamburger.lines:hover.active span:after, body.theme-color-freaky .hamburger.lines:hover.active span:before, body.theme-color-freaky .hamburger span, body.theme-color-freaky .hamburger span:after, body.theme-color-freaky .hamburger span:before, body.theme-color-freaky .nav-link--home__text:after {
    background-color: #000;
    color: #77f3d9
}

.blogposts-centered body.theme-color-freaky .blogpost-title, article body.theme-color-freaky a, article body.theme-color-freaky a:visited, body.theme-color-freaky .blogposts-centered .blogpost-title, body.theme-color-freaky .button--border, body.theme-color-freaky .employee-grid__item__email, body.theme-color-freaky .font-link-color, body.theme-color-freaky .font-main-color, body.theme-color-freaky .nav-link, body.theme-color-freaky .section__hero-content, body.theme-color-freaky article a, body.theme-color-freaky article a:visited, body.theme-color-freaky footer a, footer body.theme-color-freaky a {
    color: #000
}

    body.theme-color-freaky--active.nav-link:visited, body.theme-color-freaky .border-main-color, body.theme-color-freaky .button--border, body.theme-color-freaky .nav-link--active, body.theme-color-freaky .nav-link:hover {
        border-color: #000
    }

.montserrat, body.theme-font-standard .nav-link.nav-link--home, body.theme-font-standard h1, body.theme-font-standard h2, body.theme-font-standard h3, body.theme-font-standard h4, body.theme-font-standard h5, body.theme-font-standard h6 {
    font-family: Montserrat,sans-serif
}

.source-sans-pro, body.theme-font-standard, body.theme-font-standard .nav-link {
    font-family: Source Sans Pro,sans-serif
}

    body.theme-font-standard p {
        line-height: 1.6em;
        margin-bottom: 30px
    }

.lato, body.theme-font-mono .nav-link.nav-link--home, body.theme-font-mono h1, body.theme-font-mono h2, body.theme-font-mono h3, body.theme-font-mono h4, body.theme-font-mono h5, body.theme-font-mono h6 {
    font-family: Lato,sans-serif
}

.roboto-mono, body.theme-font-mono, body.theme-font-mono .nav-link {
    font-family: Roboto Mono,monospace
}

body.theme-font-mono {
    font-weight: 400;
    font-size: 14px
}

    body.theme-font-mono .nav-link.nav-link--home, body.theme-font-mono h1, body.theme-font-mono h2, body.theme-font-mono h3, body.theme-font-mono h4, body.theme-font-mono h5, body.theme-font-mono h6 {
        font-weight: 900
    }

    body.theme-font-mono p {
        line-height: 1.6em;
        margin-bottom: 30px
    }

.playfair, body.theme-font-serif .nav-link.nav-link--home, body.theme-font-serif h1, body.theme-font-serif h2, body.theme-font-serif h3, body.theme-font-serif h4, body.theme-font-serif h5, body.theme-font-serif h6 {
    font-family: Playfair Display,serif
}

.noto, body.theme-font-serif, body.theme-font-serif .nav-link {
    font-family: Noto Sans,sans-serif
}

    body.theme-font-serif p {
        line-height: 1.6em;
        margin-bottom: 30px
    }

.product-image-container {
    display: -ms-flexbox;
    display: flex;
    -ms-flex-pack: center;
    justify-content: center;
    -ms-flex-align: start;
    align-items: flex-start
}

.product-image {
    max-width: 100%;
    max-height: 600px
}

.product-price {
    font-size: 30px;
    margin-bottom: 20px
}

.product-teaser {
    font-size: 18px;
    line-height: 1.6em;
    margin-bottom: 20px
}

.product-button {
    margin-bottom: 40px
}

.product-advantages {
    display: -ms-flexbox;
    display: flex;
    -ms-flex-direction: column;
    flex-direction: column
}

.product-advantage {
    padding: 15px 0;
    font-weight: 700;
    border-top: 1px solid #f2f2f2
}

    .product-advantage:last-child {
        border-bottom: 1px solid #f2f2f2
    }

.product-grid {
    display: -ms-flexbox;
    display: flex;
    -ms-flex-direction: row;
    flex-direction: row;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap;
    overflow: hidden
}

.product-grid__item {
    height: 300px;
    position: relative;
    overflow: hidden;
    -ms-flex: 1 1 100%;
    flex: 1 1 100%;
    max-width: 100%;
    background-size: cover;
    background-repeat: no-repeat;
    background-position: 50%
}

    .product-grid__item:hover {
        cursor: pointer
    }

.product-grid__item__overlay {
    position: absolute;
    bottom: 0;
    padding: 20px;
    width: 100%;
    display: -ms-flexbox;
    display: flex;
    -ms-flex-direction: column;
    flex-direction: column;
    -ms-flex-align: start;
    align-items: flex-start
}

.product-grid__item-image {
    max-width: 100%
}

.product-grid__item__name, .product-grid__item__price {
    color: #fff;
    text-transform: uppercase;
    font-size: 14px;
    margin-bottom: 5px;
    letter-spacing: 5px;
    background-color: rgba(0,0,0,.35);
    padding: 2px 5px;
    display: inline-block
}

.header {
    padding-top: 50px;
    padding-bottom: 50px;
    display: -ms-flexbox;
    display: flex;
    -ms-flex-align: center;
    align-items: center;
    position: absolute;
    width: 100%;
    z-index: 4
}

.header--fixed {
    padding-top: 20px;
    padding-bottom: 20px;
    background: #fff;
    transition: padding .2s cubic-bezier(.694,.0482,.335,1),background .2s cubic-bezier(.694,.0482,.335,1);
    left: 0;
    position: fixed
}

    .header--fixed .nav-bar {
        position: absolute;
        left: 50%;
        top: 50%;
        transform: translate(-50%,-50%)
    }

    .header--fixed .nav-link {
        color: #000 !important
    }

        .header--fixed .nav-link--active, .header--fixed .nav-link:hover {
            border-bottom-color: #000 !important
        }

    .header--fixed .hamburger span, .header--fixed .hamburger span:after, .header--fixed .hamburger span:before {
        background-color: #000 !important
    }

body.frontpage .header:not(.header--fixed) .nav-link--home__text:after {
    background-color: #fff
}

body.frontpage .header:not(.header--fixed) .nav-link {
    color: #fff
}

    body.frontpage .header:not(.header--fixed) .nav-link--active, body.frontpage .header:not(.header--fixed) .nav-link:hover {
        border-bottom-color: #fff
    }

body.frontpage .header:not(.header--fixed) .hamburger span, body.frontpage .header:not(.header--fixed) .hamburger span:after, body.frontpage .header:not(.header--fixed) .hamburger span:before {
    background-color: #fff
}

body.fixed .top-nav.-open {
    position: fixed;
    top: 0;
    bottom: 0;
    left: 0;
    transform: translate(0)
}

footer {
    padding-top: 60px;
    padding-bottom: 60px
}

.logo {
    padding-left: 20px
}

.logo-image {
    max-width: 180px;
    max-height: 100px;
    height: auto;
    margin-left: 20px;
    display: block
}

.nav-bar {
    display: -ms-inline-flexbox;
    display: inline-flex;
    -ms-flex-align: center;
    align-items: center;
    -ms-flex-direction: row;
    flex-direction: row
}

.nav-bar--list {
    -ms-flex-direction: column;
    flex-direction: column;
    -ms-flex-align: start;
    align-items: flex-start
}

.nav-bar--center, .nav-bar--list {
    display: -ms-flexbox;
    display: flex;
    -ms-flex-pack: center;
    justify-content: center
}

.nav-bar--air-bottom {
    margin-bottom: 40px
}

.nav-link, .nav-link:visited {
    text-decoration: none;
    margin: 10px 15px;
    font-size: 14px;
    text-transform: uppercase;
    font-weight: 600;
    letter-spacing: 2px;
    border-bottom: 2px solid transparent
}

.nav-link--air-bottom {
    margin-bottom: 5px
}

a.nav-link.nav-link--black {
    color: #000
}

    a.nav-link.nav-link--black:hover {
        border-color: #000
    }

.nav-link--black.nav-link--active {
    box-shadow: inset 0 -2px 0 #000
}

.nav-link--home {
    font-size: 24px;
    font-weight: 900;
    margin-right: 40px;
    color: #f4f4f0
}

    .nav-link--home:hover {
        box-shadow: none
    }

.nav-link--home__text {
    position: relative
}

    .nav-link--home__text:after {
        position: absolute;
        right: 0;
        display: block;
        content: "";
        height: 3px;
        width: 200%
    }

.section {
    padding: 90px 20px
}

.section--sand {
    background-color: #f4f4f0
}

.section--stroke-bottom {
    border-bottom: 1px solid #f2f2f2
}

.section--full-height {
    min-height: 100vh
}

.section--header {
    min-height: 320px;
    padding-top: 130px
}

.section--content-center {
    -ms-flex-align: center;
    align-items: center;
    text-align: center
}

.section--content-center, .section--content-center-bottom {
    display: -ms-flexbox;
    display: flex;
    -ms-flex-pack: center;
    justify-content: center
}

.section--content-center-bottom {
    -ms-flex-align: end;
    align-items: flex-end
}

.section--thick-border {
    border: 20px solid #fff
}

body.frontpage .section__hero-content {
    color: #fff
}

.section__hero-content {
    max-width: 760px;
    padding: 0 20px
}

.section__description {
    font-size: 18px;
    line-height: 1.5;
    margin-bottom: 60px
}

.background-image-full {
    background-repeat: no-repeat;
    background-size: cover;
    background-position: 50%
}

.overlay {
    position: relative
}

    .overlay:after {
        content: "";
        z-index: 0;
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%
    }

.overlay--dark:after {
    background: rgba(37,33,33,.6)
}

.overlay--light:after {
    background: hsla(0,0%,88%,.6)
}

.overlay * {
    position: relative;
    z-index: 1
}

.button, .button--small {
    display: -ms-inline-flexbox;
    display: inline-flex;
    max-width: 100%;
    padding: 10px 40px;
    text-align: center;
    text-decoration: none;
    letter-spacing: 2px;
    text-transform: uppercase;
    color: #fff;
    font-size: 16px;
    font-weight: 700;
    border: 2px solid #000;
    outline: none
}

    .button--small:hover, .button:hover {
        opacity: .7
    }

.button--small {
    padding: 8px 40px;
    font-size: 14px
}

.button--border--light {
    background: none;
    border-color: #fff;
    color: #fff
}

    .button--border--light:hover {
        background: #fff;
        color: #000;
        opacity: 1
    }

.button--border--light_solid {
    background: #fff;
    border-color: #fff;
    color: #000
}

    .button--border--light_solid:hover {
        opacity: .7
    }

.button--border--dark {
    background: none;
    border-color: #000;
    color: #000
}

    .button--border--dark:hover {
        background: #000;
        color: #fff;
        opacity: 1
    }

.button--border--dark_solid {
    background: #000;
    border-color: #000;
    color: #fff
}

    .button--border--dark_solid:hover {
        opacity: .7
    }

.button--border {
    background: none
}

    .button--border:hover {
        opacity: 1
    }

.button--border--solid {
    border-color: transparent
}

    .button--border--solid:hover {
        opacity: .7
    }

.blogposts {
    margin-left: auto;
    margin-right: auto
}

.blogpost {
    text-decoration: none;
    color: #000;
    display: block;
    padding: 30px 20px
}

    .blogpost.blogpost-with-image {
        display: -ms-flexbox;
        display: flex;
        -ms-flex-direction: column;
        flex-direction: column
    }

    .blogpost:last-child {
        margin-bottom: 60px
    }

    .blogpost:hover .blogpost-title {
        text-decoration: underline
    }

.blogpost-image {
    -ms-flex: 1 0 100%;
    flex: 1 0 100%;
    margin-left: 40px;
    border-radius: 50%
}

.blogpost-info {
    -ms-flex: 1 1 auto;
    flex: 1 1 auto
}

.blogpost-meta {
    display: -ms-flexbox;
    display: flex;
    -ms-flex-align: center;
    align-items: center;
    -ms-flex-pack: start;
    justify-content: flex-start;
    margin-bottom: 10px
}

.blogpost-date {
    display: block;
    opacity: 1;
    margin-right: 10px
}

.blogpost-cat {
    opacity: .5
}

.blogpost-image {
    height: 160px;
    width: 160px;
    background-size: cover;
    margin-left: auto;
    margin-right: auto;
    margin-top: 15px
}

.blogpost-title {
    font-size: 22px;
    letter-spacing: 1px;
    margin: 0 0 10px
}

.blogpost-excerpt {
    font-size: 16px;
    line-height: 1.5;
    opacity: .8
}

.blogpost-link {
    margin-top: 25px;
    display: -ms-inline-flexbox;
    display: inline-flex;
    padding-bottom: 2px;
    border-bottom: 2px solid rgba(0,0,0,.15);
    color: #000;
    text-decoration: none;
    font-size: 18px
}

    .blogpost-link:hover {
        border-bottom-color: #000
    }

.blogposts-centered {
    text-align: center
}

    .blogposts-centered .blogpost {
        margin: 30px 0;
        padding: 30px 30px 0
    }

        .blogposts-centered .blogpost:hover {
            background: none
        }

        .blogposts-centered .blogpost + .blogpost {
            border-top: 1px solid #f4f4f0
        }

    .blogposts-centered .blogpost-title {
        margin-bottom: 30px
    }

    .blogposts-centered .blogpost-excerpt {
        font-size: 18px
    }

    .blogposts-centered .blogpost-meta {
        -ms-flex-pack: center;
        justify-content: center
    }

    .blogposts-centered .blogpost-title {
        font-size: 30px;
        line-height: 1.3
    }

.testimonial {
    text-decoration: none;
    color: #000;
    display: block;
    background: #f4f4f0;
    margin-bottom: 30px
}

.testimonial-image {
    width: 100%;
    height: 200px;
    background-size: cover;
    background-position: 50%
}

.testimonial-content {
    padding: 30px
}

.testimonial-title {
    letter-spacing: 1px;
    margin: 0 0 10px
}

p.testimonial-text {
    margin-top: 0;
    margin-bottom: 0 !important
}

.testimonial-link {
    color: rgba(0,0,0,.3);
    text-decoration: none;
    display: inline-block;
    margin-top: 20px
}

    .testimonial-link:hover {
        color: #000
    }

.employee-grid {
    display: -ms-flexbox;
    display: flex;
    -ms-flex-direction: row;
    flex-direction: row;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap;
    overflow: hidden
}

.employee-grid__item {
    -ms-flex: 1 1 100%;
    flex: 1 1 100%;
    max-width: 100%;
    padding-top: 10px;
    padding-bottom: 10px
}

.employee-grid__item__image {
    height: 300px;
    background-size: cover;
    background-repeat: no-repeat;
    background-position: 50%
}

.employee-grid__item__details {
    padding: 30px;
    background: #f4f4f0
}

.employee-grid__item__name {
    color: #000;
    letter-spacing: 1px;
    margin-top: 0;
    margin-bottom: 5px
}

.employee-grid__item__email {
    text-decoration: none;
    margin-bottom: 15px;
    display: inline-block
}

.employee-grid__item__contact-item {
    color: rgba(0,0,0,.3);
    text-decoration: none;
    display: inline-block;
    margin-right: 10px
}

    .employee-grid__item__contact-item:hover {
        color: #000
    }

label {
    margin-bottom: 10px;
    font-size: 12px;
    text-transform: uppercase
}

.form-group {
    margin-bottom: 25px
}

.form-control {
    border-radius: 0;
    box-shadow: none;
    transition: none;
    padding: 20px 15px
}

    .form-control:focus {
        border-color: #362bdc;
        box-shadow: none
    }

.mobile-nav-handler {
    margin-left: auto;
    margin-right: 40px
}

.hamburger {
    position: relative;
    cursor: pointer;
    z-index: 5;
    width: 40px;
    height: 40px;
    display: -ms-flexbox;
    display: flex;
    -ms-flex-align: center;
    align-items: center
}

    .hamburger span, .hamburger span:after, .hamburger span:before {
        display: block;
        transition-property: all;
        transition-duration: .1s
    }

    .hamburger span {
        margin: 0 auto;
        position: relative
    }

        .hamburger span:after, .hamburger span:before {
            position: absolute;
            content: ''
        }

    .hamburger.lines span, .hamburger.lines span:after, .hamburger.lines span:before {
        width: 20px;
        height: 2px
    }

        .hamburger.lines span:before {
            transform: translateY(-5px)
        }

        .hamburger.lines span:after {
            transform: translateY(5px)
        }

    .hamburger.lines:hover span:before {
        transform: translateY(-7px)
    }

    .hamburger.lines:hover span:after {
        transform: translateY(7px)
    }

    .hamburger.lines:hover.active span:before {
        transform: rotate(45deg)
    }

    .hamburger.lines:hover.active span:after {
        transform: rotate(-45deg)
    }

    .hamburger.lines.active span {
        background-color: transparent !important
    }

        .hamburger.lines.active span:after, .hamburger.lines.active span:before {
            transform: translateY(0)
        }

        .hamburger.lines.active span:before {
            transform: rotate(45deg)
        }

        .hamburger.lines.active span:after {
            transform: rotate(-45deg)
        }

article {
    max-width: 700px;
    margin: 0 auto
}

    article a, article a:visited {
        text-decoration: none;
        text-decoration: underline
    }

        article a:hover, article a:visited:hover {
            text-decoration: none
        }

    article li, article p {
        font-weight: 400;
        font-style: normal;
        font-size: 18px;
        line-height: 1.5
    }

    article li {
        padding: 5px 0
    }

    article ol, article ul {
        margin: 20px;
        padding: 0
    }

    article img {
        max-width: 100%
    }

    article iframe, article img {
        margin: 0 0 20px
    }

.mobile-nav {
    width: 100%;
    height: auto;
    z-index: 4;
    left: 0;
    top: 0;
    right: 0;
    bottom: 0;
    display: -ms-flexbox;
    display: flex;
    position: fixed;
    -ms-flex-pack: center;
    justify-content: center;
    -ms-flex-align: center;
    align-items: center;
    opacity: 0;
    height: 0
}

.mobile-nav--open {
    opacity: 1;
    height: 100vh
}

.mobile-nav .nav-link {
    transition: transform .6s cubic-bezier(.23,1,.32,1);
    transform: translateY(999px);
    opacity: 0;
    font-size: 18px;
    margin: 15px
}

    .mobile-nav .nav-link:nth-child(1) {
        transition-delay: 60ms
    }

    .mobile-nav .nav-link:nth-child(2) {
        transition-delay: 80ms
    }

    .mobile-nav .nav-link:nth-child(3) {
        transition-delay: .1s
    }

    .mobile-nav .nav-link:nth-child(4) {
        transition-delay: .12s
    }

    .mobile-nav .nav-link:nth-child(5) {
        transition-delay: .14s
    }

    .mobile-nav .nav-link:nth-child(6) {
        transition-delay: .16s
    }

    .mobile-nav .nav-link:nth-child(7) {
        transition-delay: .18s
    }

    .mobile-nav .nav-link:nth-child(8) {
        transition-delay: .2s
    }

    .mobile-nav .nav-link:nth-child(9) {
        transition-delay: .22s
    }

    .mobile-nav .nav-link:nth-child(10) {
        transition-delay: .24s
    }

    .mobile-nav .nav-link:nth-child(11) {
        transition-delay: .26s
    }

.mobile-nav--open .nav-link {
    transform: translate(0);
    opacity: 1
}

.ta-center {
    text-align: center
}

.mw-640 {
    max-width: 640px
}

.ma-h-auto {
    margin-left: auto;
    margin-right: auto
}

.no-air {
    margin: 0
}

.no-scroll {
    overflow: hidden
}

@media screen and (-webkit-min-device-pixel-ratio:0) {
    input[type=date].form-control, input[type=datetime-local].form-control, input[type=month].form-control, input[type=time].form-control {
        line-height: 34px
    }

    .input-group-sm > .input-group-btn > input[type=date].btn, .input-group-sm > .input-group-btn > input[type=datetime-local].btn, .input-group-sm > .input-group-btn > input[type=month].btn, .input-group-sm > .input-group-btn > input[type=time].btn, .input-group-sm > input[type=date].form-control, .input-group-sm > input[type=date].input-group-addon, .input-group-sm > input[type=datetime-local].form-control, .input-group-sm > input[type=datetime-local].input-group-addon, .input-group-sm > input[type=month].form-control, .input-group-sm > input[type=month].input-group-addon, .input-group-sm > input[type=time].form-control, .input-group-sm > input[type=time].input-group-addon, .input-group-sm input[type=date], .input-group-sm input[type=datetime-local], .input-group-sm input[type=month], .input-group-sm input[type=time], input[type=date].input-sm, input[type=datetime-local].input-sm, input[type=month].input-sm, input[type=time].input-sm {
        line-height: 30px
    }

    .input-group-lg > .input-group-btn > input[type=date].btn, .input-group-lg > .input-group-btn > input[type=datetime-local].btn, .input-group-lg > .input-group-btn > input[type=month].btn, .input-group-lg > .input-group-btn > input[type=time].btn, .input-group-lg > input[type=date].form-control, .input-group-lg > input[type=date].input-group-addon, .input-group-lg > input[type=datetime-local].form-control, .input-group-lg > input[type=datetime-local].input-group-addon, .input-group-lg > input[type=month].form-control, .input-group-lg > input[type=month].input-group-addon, .input-group-lg > input[type=time].form-control, .input-group-lg > input[type=time].input-group-addon, .input-group-lg input[type=date], .input-group-lg input[type=datetime-local], .input-group-lg input[type=month], .input-group-lg input[type=time], input[type=date].input-lg, input[type=datetime-local].input-lg, input[type=month].input-lg, input[type=time].input-lg {
        line-height: 46px
    }
}

@media (min-width:768px) {
    .container {
        width: 750px
    }

    .col-sm-1, .col-sm-2, .col-sm-3, .col-sm-4, .col-sm-5, .col-sm-6, .col-sm-7, .col-sm-8, .col-sm-9, .col-sm-10, .col-sm-11, .col-sm-12 {
        float: left
    }

    .col-sm-1 {
        width: 8.333333%
    }

    .col-sm-2 {
        width: 16.666667%
    }

    .col-sm-3 {
        width: 25%
    }

    .col-sm-4 {
        width: 33.333333%
    }

    .col-sm-5 {
        width: 41.666667%
    }

    .col-sm-6 {
        width: 50%
    }

    .col-sm-7 {
        width: 58.333333%
    }

    .col-sm-8 {
        width: 66.666667%
    }

    .col-sm-9 {
        width: 75%
    }

    .col-sm-10 {
        width: 83.333333%
    }

    .col-sm-11 {
        width: 91.666667%
    }

    .col-sm-12 {
        width: 100%
    }

    .col-sm-pull-0 {
        right: auto
    }

    .col-sm-pull-1 {
        right: 8.333333%
    }

    .col-sm-pull-2 {
        right: 16.666667%
    }

    .col-sm-pull-3 {
        right: 25%
    }

    .col-sm-pull-4 {
        right: 33.333333%
    }

    .col-sm-pull-5 {
        right: 41.666667%
    }

    .col-sm-pull-6 {
        right: 50%
    }

    .col-sm-pull-7 {
        right: 58.333333%
    }

    .col-sm-pull-8 {
        right: 66.666667%
    }

    .col-sm-pull-9 {
        right: 75%
    }

    .col-sm-pull-10 {
        right: 83.333333%
    }

    .col-sm-pull-11 {
        right: 91.666667%
    }

    .col-sm-pull-12 {
        right: 100%
    }

    .col-sm-push-0 {
        left: auto
    }

    .col-sm-push-1 {
        left: 8.333333%
    }

    .col-sm-push-2 {
        left: 16.666667%
    }

    .col-sm-push-3 {
        left: 25%
    }

    .col-sm-push-4 {
        left: 33.333333%
    }

    .col-sm-push-5 {
        left: 41.666667%
    }

    .col-sm-push-6 {
        left: 50%
    }

    .col-sm-push-7 {
        left: 58.333333%
    }

    .col-sm-push-8 {
        left: 66.666667%
    }

    .col-sm-push-9 {
        left: 75%
    }

    .col-sm-push-10 {
        left: 83.333333%
    }

    .col-sm-push-11 {
        left: 91.666667%
    }

    .col-sm-push-12 {
        left: 100%
    }

    .col-sm-offset-0 {
        margin-left: 0
    }

    .col-sm-offset-1 {
        margin-left: 8.333333%
    }

    .col-sm-offset-2 {
        margin-left: 16.666667%
    }

    .col-sm-offset-3 {
        margin-left: 25%
    }

    .col-sm-offset-4 {
        margin-left: 33.333333%
    }

    .col-sm-offset-5 {
        margin-left: 41.666667%
    }

    .col-sm-offset-6 {
        margin-left: 50%
    }

    .col-sm-offset-7 {
        margin-left: 58.333333%
    }

    .col-sm-offset-8 {
        margin-left: 66.666667%
    }

    .col-sm-offset-9 {
        margin-left: 75%
    }

    .col-sm-offset-10 {
        margin-left: 83.333333%
    }

    .col-sm-offset-11 {
        margin-left: 91.666667%
    }

    .col-sm-offset-12 {
        margin-left: 100%
    }

    .form-inline .form-group {
        display: inline-block;
        margin-bottom: 0;
        vertical-align: middle
    }

    .form-inline .form-control {
        display: inline-block;
        width: auto;
        vertical-align: middle
    }

    .form-inline .form-control-static {
        display: inline-block
    }

    .form-inline .input-group {
        display: inline-table;
        vertical-align: middle
    }

        .form-inline .input-group .form-control, .form-inline .input-group .input-group-addon, .form-inline .input-group .input-group-btn {
            width: auto
        }

        .form-inline .input-group > .form-control {
            width: 100%
        }

    .form-inline .control-label {
        margin-bottom: 0;
        vertical-align: middle
    }

    .form-inline .checkbox, .form-inline .radio {
        display: inline-block;
        margin-top: 0;
        margin-bottom: 0;
        vertical-align: middle
    }

        .form-inline .checkbox label, .form-inline .radio label {
            padding-left: 0
        }

        .form-inline .checkbox input[type=checkbox], .form-inline .radio input[type=radio] {
            position: relative;
            margin-left: 0
        }

    .form-inline .has-feedback .form-control-feedback {
        top: 0
    }

    .form-horizontal .control-label {
        text-align: right;
        margin-bottom: 0;
        padding-top: 7px
    }

    .form-horizontal .form-group-lg .control-label {
        padding-top: 11px;
        font-size: 18px
    }

    .form-horizontal .form-group-sm .control-label {
        padding-top: 6px;
        font-size: 12px
    }

    .employee-grid__item, .product-grid__item {
        -ms-flex: 1 1 50%;
        flex: 1 1 50%;
        max-width: 50%
    }

    .employee-grid__item {
        padding: 10px
    }
}

.compat-msg {    
    color: #000;
    display: block;
    background: #f4f4f0;
    padding: 30px
}

@media (min-width:992px) {
    .container {
        width: 970px
    }

    .col-md-1, .col-md-2, .col-md-3, .col-md-4, .col-md-5, .col-md-6, .col-md-7, .col-md-8, .col-md-9, .col-md-10, .col-md-11, .col-md-12 {
        float: left
    }

    .col-md-1 {
        width: 8.333333%
    }

    .col-md-2 {
        width: 16.666667%
    }

    .col-md-3 {
        width: 25%
    }

    .col-md-4 {
        width: 33.333333%
    }

    .col-md-5 {
        width: 41.666667%
    }

    .col-md-6 {
        width: 50%
    }

    .col-md-7 {
        width: 58.333333%
    }

    .col-md-8 {
        width: 66.666667%
    }

    .col-md-9 {
        width: 75%
    }

    .col-md-10 {
        width: 83.333333%
    }

    .col-md-11 {
        width: 91.666667%
    }

    .col-md-12 {
        width: 100%
    }

    .col-md-pull-0 {
        right: auto
    }

    .col-md-pull-1 {
        right: 8.333333%
    }

    .col-md-pull-2 {
        right: 16.666667%
    }

    .col-md-pull-3 {
        right: 25%
    }

    .col-md-pull-4 {
        right: 33.333333%
    }

    .col-md-pull-5 {
        right: 41.666667%
    }

    .col-md-pull-6 {
        right: 50%
    }

    .col-md-pull-7 {
        right: 58.333333%
    }

    .col-md-pull-8 {
        right: 66.666667%
    }

    .col-md-pull-9 {
        right: 75%
    }

    .col-md-pull-10 {
        right: 83.333333%
    }

    .col-md-pull-11 {
        right: 91.666667%
    }

    .col-md-pull-12 {
        right: 100%
    }

    .col-md-push-0 {
        left: auto
    }

    .col-md-push-1 {
        left: 8.333333%
    }

    .col-md-push-2 {
        left: 16.666667%
    }

    .col-md-push-3 {
        left: 25%
    }

    .col-md-push-4 {
        left: 33.333333%
    }

    .col-md-push-5 {
        left: 41.666667%
    }

    .col-md-push-6 {
        left: 50%
    }

    .col-md-push-7 {
        left: 58.333333%
    }

    .col-md-push-8 {
        left: 66.666667%
    }

    .col-md-push-9 {
        left: 75%
    }

    .col-md-push-10 {
        left: 83.333333%
    }

    .col-md-push-11 {
        left: 91.666667%
    }

    .col-md-push-12 {
        left: 100%
    }

    .col-md-offset-0 {
        margin-left: 0
    }

    .col-md-offset-1 {
        margin-left: 8.333333%
    }

    .col-md-offset-2 {
        margin-left: 16.666667%
    }

    .col-md-offset-3 {
        margin-left: 25%
    }

    .col-md-offset-4 {
        margin-left: 33.333333%
    }

    .col-md-offset-5 {
        margin-left: 41.666667%
    }

    .col-md-offset-6 {
        margin-left: 50%
    }

    .col-md-offset-7 {
        margin-left: 58.333333%
    }

    .col-md-offset-8 {
        margin-left: 66.666667%
    }

    .col-md-offset-9 {
        margin-left: 75%
    }

    .col-md-offset-10 {
        margin-left: 83.333333%
    }

    .col-md-offset-11 {
        margin-left: 91.666667%
    }

    .col-md-offset-12 {
        margin-left: 100%
    }

    .nav-link, .nav-link:visited {
        margin-left: 15px;
        margin-right: 15px
    }

    .button, .button--small {
        padding: 10px 80px
    }

    .blogposts {
        max-width: 800px
    }

    .blogpost {
        padding: 30px;
        margin: 20px 0
    }

        .blogpost.blogpost-with-image {
            -ms-flex-direction: row;
            flex-direction: row
        }

    .blogpost-image {
        height: 160px;
        max-width: 160px;
        margin-top: 0;
        margin-left: 20px
    }

    .employee-grid {
        margin-left: 20px;
        margin-right: 20px
    }

    .mobile-nav-handler {
        display: none
    }
}

@media (min-width:1200px) {
    .container {
        width: 1170px
    }

    .col-lg-1, .col-lg-2, .col-lg-3, .col-lg-4, .col-lg-5, .col-lg-6, .col-lg-7, .col-lg-8, .col-lg-9, .col-lg-10, .col-lg-11, .col-lg-12 {
        float: left
    }

    .col-lg-1 {
        width: 8.333333%
    }

    .col-lg-2 {
        width: 16.666667%
    }

    .col-lg-3 {
        width: 25%
    }

    .col-lg-4 {
        width: 33.333333%
    }

    .col-lg-5 {
        width: 41.666667%
    }

    .col-lg-6 {
        width: 50%
    }

    .col-lg-7 {
        width: 58.333333%
    }

    .col-lg-8 {
        width: 66.666667%
    }

    .col-lg-9 {
        width: 75%
    }

    .col-lg-10 {
        width: 83.333333%
    }

    .col-lg-11 {
        width: 91.666667%
    }

    .col-lg-12 {
        width: 100%
    }

    .col-lg-pull-0 {
        right: auto
    }

    .col-lg-pull-1 {
        right: 8.333333%
    }

    .col-lg-pull-2 {
        right: 16.666667%
    }

    .col-lg-pull-3 {
        right: 25%
    }

    .col-lg-pull-4 {
        right: 33.333333%
    }

    .col-lg-pull-5 {
        right: 41.666667%
    }

    .col-lg-pull-6 {
        right: 50%
    }

    .col-lg-pull-7 {
        right: 58.333333%
    }

    .col-lg-pull-8 {
        right: 66.666667%
    }

    .col-lg-pull-9 {
        right: 75%
    }

    .col-lg-pull-10 {
        right: 83.333333%
    }

    .col-lg-pull-11 {
        right: 91.666667%
    }

    .col-lg-pull-12 {
        right: 100%
    }

    .col-lg-push-0 {
        left: auto
    }

    .col-lg-push-1 {
        left: 8.333333%
    }

    .col-lg-push-2 {
        left: 16.666667%
    }

    .col-lg-push-3 {
        left: 25%
    }

    .col-lg-push-4 {
        left: 33.333333%
    }

    .col-lg-push-5 {
        left: 41.666667%
    }

    .col-lg-push-6 {
        left: 50%
    }

    .col-lg-push-7 {
        left: 58.333333%
    }

    .col-lg-push-8 {
        left: 66.666667%
    }

    .col-lg-push-9 {
        left: 75%
    }

    .col-lg-push-10 {
        left: 83.333333%
    }

    .col-lg-push-11 {
        left: 91.666667%
    }

    .col-lg-push-12 {
        left: 100%
    }

    .col-lg-offset-0 {
        margin-left: 0
    }

    .col-lg-offset-1 {
        margin-left: 8.333333%
    }

    .col-lg-offset-2 {
        margin-left: 16.666667%
    }

    .col-lg-offset-3 {
        margin-left: 25%
    }

    .col-lg-offset-4 {
        margin-left: 33.333333%
    }

    .col-lg-offset-5 {
        margin-left: 41.666667%
    }

    .col-lg-offset-6 {
        margin-left: 50%
    }

    .col-lg-offset-7 {
        margin-left: 58.333333%
    }

    .col-lg-offset-8 {
        margin-left: 66.666667%
    }

    .col-lg-offset-9 {
        margin-left: 75%
    }

    .col-lg-offset-10 {
        margin-left: 83.333333%
    }

    .col-lg-offset-11 {
        margin-left: 91.666667%
    }

    .col-lg-offset-12 {
        margin-left: 100%
    }

    .employee-grid__item, .product-grid__item {
        -ms-flex: 1 1 33%;
        flex: 1 1 33%;
        max-width: 33%
    }

    .employee-grid__item {
        padding: 20px
    }
}

@media (max-width:992px) {
    .header .nav-bar {
        display: none
    }

    .nav-bar {
        -ms-flex-direction: column;
        flex-direction: column;
        -ms-flex-align: center;
        -ms-grid-row-align: center;
        align-items: center
    }
}