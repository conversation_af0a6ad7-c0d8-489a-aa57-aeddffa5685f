<?xml version="1.0" encoding="utf-8"?>
<!-- Generator: Adobe Illustrator 21.0.2, SVG Export Plug-In . SVG Version: 6.00 Build 0)  -->
<svg version="1.1" id="Layer_1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px"
	 viewBox="0 0 68 68" style="enable-background:new 0 0 68 68;" xml:space="preserve">
<style type="text/css">
	.st0{fill:#FDFEFD;stroke:#0F3846;stroke-width:1.8;stroke-linejoin:round;stroke-miterlimit:10;}
	.st1{fill:#0B98A8;stroke:#0F3846;stroke-width:1.8;stroke-linejoin:round;stroke-miterlimit:10;}
	.st2{fill:#0DBCD4;}
	.st3{fill:#DBF0F2;stroke:#0F3846;stroke-width:1.8;stroke-linejoin:round;stroke-miterlimit:10;}
	.st4{fill:#F05656;stroke:#0F3846;stroke-width:1.8;stroke-linejoin:round;stroke-miterlimit:10;}
	.st5{fill:#0E3947;}
</style>
<g>
	<polygon class="st0" points="43.1,32.9 47.7,32.9 47.7,28.4 43.1,28.4 	"/>
	<polygon class="st1" points="57.5,13.7 64.3,13.7 64.3,9.2 57.5,9.2 	"/>
	<polygon class="st1" points="50.5,67.1 67.1,67.1 67.1,13.7 50.5,13.7 	"/>
	<polygon class="st2" points="54.1,66.2 64.3,66.2 64.3,14.6 54.1,14.6 	"/>
	<polygon class="st0" points="23.8,67.1 51.1,67.1 51.1,33 23.8,33 	"/>
	<polygon class="st3" points="3.4,39.7 10.2,39.7 10.2,35.2 3.4,35.2 	"/>
	<polygon class="st1" points="32.9,8.8 32.9,0.8 19.3,0.8 19.3,8.8 14.8,8.8 14.8,39.4 23.8,39.4 23.8,32.6 37.5,32.6 37.5,8.8 	"/>
	<polygon class="st3" points="0.9,67.1 23.6,67.1 23.6,39.8 0.9,39.8 	"/>
	<polygon class="st4" points="14.8,67 23.8,67 23.8,44.3 14.8,44.3 	"/>
	<path class="st5" d="M28.4,67.1c-0.7,0-1.1-0.5-1.1-1.1V36.5c0-0.7,0.5-1.1,1.1-1.1c0.7,0,1.1,0.5,1.1,1.1V66
		C29.5,66.7,29.1,67.1,28.4,67.1z"/>
	<path class="st5" d="M32.9,67.1c-0.7,0-1.1-0.5-1.1-1.1V36.5c0-0.7,0.5-1.1,1.1-1.1c0.7,0,1.1,0.5,1.1,1.1V66
		C34.1,66.7,33.6,67.1,32.9,67.1z"/>
	<path class="st5" d="M37.5,67.1c-0.7,0-1.1-0.5-1.1-1.1V36.5c0-0.7,0.5-1.1,1.1-1.1c0.7,0,1.1,0.5,1.1,1.1V66
		C38.6,66.7,38.2,67.1,37.5,67.1z"/>
	<path class="st5" d="M42,67.1c-0.7,0-1.1-0.5-1.1-1.1V36.5c0-0.7,0.5-1.1,1.1-1.1c0.7,0,1.1,0.5,1.1,1.1V66
		C43.1,66.7,42.7,67.1,42,67.1z"/>
	<path class="st5" d="M46.6,67.1c-0.7,0-1.1-0.5-1.1-1.1V36.5c0-0.7,0.5-1.1,1.1-1.1c0.7,0,1.1,0.5,1.1,1.1V66
		C47.7,66.7,47.2,67.1,46.6,67.1z"/>
	<path class="st5" d="M22.8,50h-4.5c-0.7,0-1.1-0.5-1.1-1.1c0-0.7,0.5-1.1,1.1-1.1h4.5c0.7,0,1.1,0.5,1.1,1.1
		C24,49.5,23.5,50,22.8,50z"/>
	<path class="st5" d="M22.8,54.5h-4.5c-0.7,0-1.1-0.5-1.1-1.1s0.5-1.1,1.1-1.1h4.5c0.7,0,1.1,0.5,1.1,1.1S23.5,54.5,22.8,54.5z"/>
	<path class="st5" d="M22.8,59h-4.5c-0.7,0-1.1-0.5-1.1-1.1s0.5-1.1,1.1-1.1h4.5c0.7,0,1.1,0.5,1.1,1.1S23.5,59,22.8,59z"/>
	<path class="st5" d="M22.8,63.6h-4.5c-0.7,0-1.1-0.5-1.1-1.1c0-0.7,0.5-1.1,1.1-1.1h4.5c0.7,0,1.1,0.5,1.1,1.1
		C24,63.1,23.5,63.6,22.8,63.6z"/>
	<path class="st5" d="M45.4,28.5c-0.7,0-1.1-0.5-1.1-1.1v-2.3c0-0.7,0.5-1.1,1.1-1.1s1.1,0.5,1.1,1.1v2.3
		C46.6,28.1,46.1,28.5,45.4,28.5z"/>
	<path class="st5" d="M19.3,39.9c-0.7,0-1.1-0.5-1.1-1.1V12.6c0-0.7,0.5-1.1,1.1-1.1c0.7,0,1.1,0.5,1.1,1.1v26.1
		C20.4,39.4,20,39.9,19.3,39.9z"/>
	<path class="st5" d="M24.1,34.1c-0.7,0-1.1-0.5-1.1-1.1V12.6c0-0.7,0.5-1.1,1.1-1.1s1.1,0.5,1.1,1.1v20.3
		C25.2,33.6,24.8,34.1,24.1,34.1z"/>
	<path class="st5" d="M28.4,33.1c-0.7,0-1.1-0.5-1.1-1.1V12.6c0-0.7,0.5-1.1,1.1-1.1c0.7,0,1.1,0.5,1.1,1.1v19.3
		C29.5,32.6,29.1,33.1,28.4,33.1z"/>
	<path class="st5" d="M32.9,33.1c-0.7,0-1.1-0.5-1.1-1.1V12.6c0-0.7,0.5-1.1,1.1-1.1c0.7,0,1.1,0.5,1.1,1.1v19.3
		C34.1,32.6,33.6,33.1,32.9,33.1z"/>
	<path class="st5" d="M58.6,62.7c-0.7,0-1.1-0.5-1.1-1.1v-2.3c0-0.7,0.5-1.1,1.1-1.1c0.7,0,1.1,0.5,1.1,1.1v2.3
		C59.8,62.1,59.3,62.7,58.6,62.7z M58.6,55.9c-0.7,0-1.1-0.5-1.1-1.1v-2.3c0-0.7,0.5-1.1,1.1-1.1c0.7,0,1.1,0.5,1.1,1.1v2.3
		C59.8,55.3,59.3,55.9,58.6,55.9z M58.6,49.1c-0.7,0-1.1-0.5-1.1-1.1v-2.3c0-0.7,0.5-1.1,1.1-1.1c0.7,0,1.1,0.5,1.1,1.1v2.3
		C59.8,48.5,59.3,49.1,58.6,49.1z M58.6,42.2c-0.7,0-1.1-0.5-1.1-1.1v-2.3c0-0.7,0.5-1.1,1.1-1.1c0.7,0,1.1,0.5,1.1,1.1v2.3
		C59.8,41.7,59.3,42.2,58.6,42.2z M58.6,35.4c-0.7,0-1.1-0.5-1.1-1.1V32c0-0.7,0.5-1.1,1.1-1.1c0.7,0,1.1,0.5,1.1,1.1v2.3
		C59.8,34.9,59.3,35.4,58.6,35.4z M58.6,28.6c-0.7,0-1.1-0.5-1.1-1.1v-2.3c0-0.7,0.5-1.1,1.1-1.1c0.7,0,1.1,0.5,1.1,1.1v2.3
		C59.8,28,59.3,28.6,58.6,28.6z M58.6,21.8c-0.7,0-1.1-0.5-1.1-1.1v-2.3c0-0.7,0.5-1.1,1.1-1.1c0.7,0,1.1,0.5,1.1,1.1v2.3
		C59.8,21.2,59.3,21.8,58.6,21.8z"/>
	<path class="st5" d="M63.2,62.7c-0.7,0-1.1-0.5-1.1-1.1v-2.3c0-0.7,0.5-1.1,1.1-1.1s1.1,0.5,1.1,1.1v2.3
		C64.3,62.1,63.9,62.7,63.2,62.7z M63.2,55.9c-0.7,0-1.1-0.5-1.1-1.1v-2.3c0-0.7,0.5-1.1,1.1-1.1s1.1,0.5,1.1,1.1v2.3
		C64.3,55.3,63.9,55.9,63.2,55.9z M63.2,49.1c-0.7,0-1.1-0.5-1.1-1.1v-2.3c0-0.7,0.5-1.1,1.1-1.1s1.1,0.5,1.1,1.1v2.3
		C64.3,48.5,63.9,49.1,63.2,49.1z M63.2,42.2c-0.7,0-1.1-0.5-1.1-1.1v-2.3c0-0.7,0.5-1.1,1.1-1.1s1.1,0.5,1.1,1.1v2.3
		C64.3,41.7,63.9,42.2,63.2,42.2z M63.2,35.4c-0.7,0-1.1-0.5-1.1-1.1V32c0-0.7,0.5-1.1,1.1-1.1s1.1,0.5,1.1,1.1v2.3
		C64.3,34.9,63.9,35.4,63.2,35.4z M63.2,28.6c-0.7,0-1.1-0.5-1.1-1.1v-2.3c0-0.7,0.5-1.1,1.1-1.1s1.1,0.5,1.1,1.1v2.3
		C64.3,28,63.9,28.6,63.2,28.6z M63.2,21.8c-0.7,0-1.1-0.5-1.1-1.1v-2.3c0-0.7,0.5-1.1,1.1-1.1s1.1,0.5,1.1,1.1v2.3
		C64.3,21.2,63.9,21.8,63.2,21.8z"/>
	<path class="st5" d="M5.7,61.3c-0.7,0-1.1-0.5-1.1-1.1v-2.3c0-0.7,0.5-1.1,1.1-1.1s1.1,0.5,1.1,1.1v2.3C6.8,60.9,6.4,61.3,5.7,61.3
		z M5.7,54.5c-0.7,0-1.1-0.5-1.1-1.1v-2.3C4.5,50.4,5,50,5.7,50s1.1,0.5,1.1,1.1v2.3C6.8,54,6.4,54.5,5.7,54.5z M5.7,47.7
		c-0.7,0-1.1-0.5-1.1-1.1v-2.3c0-0.7,0.5-1.1,1.1-1.1s1.1,0.5,1.1,1.1v2.3C6.8,47.2,6.4,47.7,5.7,47.7z"/>
	<path class="st5" d="M10.2,61.3c-0.7,0-1.1-0.5-1.1-1.1v-2.3c0-0.7,0.5-1.1,1.1-1.1s1.1,0.5,1.1,1.1v2.3
		C11.4,60.9,10.9,61.3,10.2,61.3z M10.2,54.5c-0.7,0-1.1-0.5-1.1-1.1v-2.3c0-0.7,0.5-1.1,1.1-1.1s1.1,0.5,1.1,1.1v2.3
		C11.4,54,10.9,54.5,10.2,54.5z M10.2,47.7c-0.7,0-1.1-0.5-1.1-1.1v-2.3c0-0.7,0.5-1.1,1.1-1.1s1.1,0.5,1.1,1.1v2.3
		C11.4,47.2,10.9,47.7,10.2,47.7z"/>
	<path class="st5" d="M23.8,10.2c-0.7,0-1.1-0.5-1.1-1.1V5.7c0-0.7,0.5-1.1,1.1-1.1C24.5,4.5,25,5,25,5.7v3.4
		C25,9.8,24.5,10.2,23.8,10.2z"/>
	<path class="st5" d="M28.4,10.2c-0.7,0-1.1-0.5-1.1-1.1V5.7c0-0.7,0.5-1.1,1.1-1.1c0.7,0,1.1,0.5,1.1,1.1v3.4
		C29.5,9.8,29.1,10.2,28.4,10.2z"/>
</g>
</svg>
