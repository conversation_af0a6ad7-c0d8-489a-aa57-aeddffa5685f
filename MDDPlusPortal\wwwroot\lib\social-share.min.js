var SocialShare = { btns: [{ social: "share", link: "", iconClass: "fas fa-share-alt", content: " Share", class: "ss-btn ss-btn-share", color: "#444" }, { social: "facebook", link: "https://www.facebook.com/sharer.php?u=", iconClass: "fab fa-facebook-square", content: " Facebook", class: "ss-btn ss-btn-facebook", color: "#3b5998" }, { social: "twitter", link: "https://twitter.com/share?url=", iconClass: "fab fa-twitter", content: " Twitter", class: "ss-btn ss-btn-twitter", color: "#1da1f2" }, { social: "pinterest", link: "https://pinterest.com/pin/create/bookmarklet/?url=", iconClass: "fab fa-pinterest", content: " Pinterest", class: "ss-btn ss-btn-pinterest", color: "#bd081c" }, { social: "linkedin", link: "https://www.linkedin.com/shareArticle?url=", iconClass: "fab fa-linkedin-in", content: " LinkedIn", class: "ss-btn ss-btn-linkedin", color: "#007bb5" }, { social: "messenger", link: "fb-messenger://share/?link=", iconClass: "fab fa-facebook-messenger", content: " Messenger", class: "ss-btn ss-btn-messenger", color: "#0078FF" }, { social: "whatsapp", link: "https://wa.me/?text=", iconClass: "fab fa-whatsapp", content: " WhatsApp", class: "ss-btn ss-btn-whatsapp", color: "#25d366" }, { social: "viber", link: "viber://forward?text=", iconClass: "fab fa-viber", content: " Viber", class: "ss-btn ss-btn-viber", color: "#665CAC" }, { social: "telegram", link: "tg://msg?text=", iconClass: "fab fa-telegram", content: " Telegram", class: "ss-btn ss-btn-telegram", color: "#0088CC" }, { social: "tumblr", link: "https://www.tumblr.com/share/link?url=", iconClass: "fab fa-tumblr", content: " Tumblr", class: "ss-btn ss-btn-tumblr", color: "#35465d" }, { social: "reddit", link: "https://reddit.com/submit?url=", iconClass: "fab fa-reddit", content: " Reddit", class: "ss-btn ss-btn-reddit", color: "#ff4500" }, { social: "pocket", link: "https://getpocket.com/save?url=", iconClass: "fab fa-get-pocket", content: " Pocket", class: "ss-btn ss-btn-pocket", color: "#ee4056" }, { social: "email", link: "mailto:?body=", iconClass: "fas fa-envelope", content: " Email", class: "ss-btn ss-btn-email", color: "#f04a43" }, { social: "sms", link: "sms://?body=", iconClass: "fas fa-sms", content: " SMS", class: "ss-btn ss-btn-sms", color: "#1ba5e6" }], init: function (t) { SocialShare.boxs = document.querySelectorAll(t); for (var s = 0; s < SocialShare.boxs.length; s++)for (var e = 0; e < SocialShare.btns.length; e++) { var a = SocialShare.boxs[s].getAttribute("data-ss-social"), o = SocialShare.boxs[s].getAttribute("data-ss-link"), n = window.location.href; o && (n = o); var l = "", r = SocialShare.boxs[s].getAttribute("data-ss-" + SocialShare.btns[e].social); if (r) for (var i = r.split(","), c = 0; c < i.length; c++) { (u = i[c].split(":")).length > 1 && (l += "" == l ? "" : "&", l += u[0].trim() + "=" + encodeURIComponent(u[1].trim())) } if ("" != l && (l = (SocialShare.btns[e].link.includes("?") ? "&" : "?") + l), !a || a.toLowerCase().includes(SocialShare.btns[e].social)) { var b = document.createElement("a"); b.setAttribute("class", SocialShare.btns[e].class), b.setAttribute("href", SocialShare.btns[e].link + encodeURIComponent(n) + l), b.setAttribute("target", "_blank"), b.style.background = SocialShare.btns[e].color; var h = SocialShare.btns[e].iconClass, S = SocialShare.boxs[s].getAttribute("data-ss-icon-class"); if (S) for (var d = S.split(","), f = 0; f < d.length; f++) { var u; (u = d[f].split(":")).length > 1 && u[0].trim().toLowerCase() == SocialShare.btns[e].social && (h = u[1].trim()) } var m = document.createElement("i"); m.setAttribute("class", h); var k = !0, p = SocialShare.boxs[s].getAttribute("data-ss-icon"); p && "false" == p && (k = !1), k && b.appendChild(m); var g = !0, v = SocialShare.boxs[s].getAttribute("data-ss-content"); v && "false" == v && (g = !1), g && b.append(SocialShare.btns[e].content), SocialShare.boxs[s].appendChild(b) } } SocialShare.btnShares = document.querySelectorAll(".ss-btn-share"); for (s = 0; s < SocialShare.btnShares.length; s++) { n = decodeURIComponent(SocialShare.btnShares[s].getAttribute("href")); SocialShare.btnShares[s].removeAttribute("href"), SocialShare.btnShares[s].removeAttribute("target"), SocialShare.btnShares[s].setAttribute("data-ss-link", n), SocialShare.btnShares[s].addEventListener("click", function (t) { navigator.share ? navigator.share({ url: this.getAttribute("data-ss-link") }).then(() => { console.log("Thanks for sharing!") }).catch(console.error) : console.log("This brownser dont support native web share!") }) } }, createShareBox: function (t, s = null, e = null, a = !0, o = !0, n = !0) { for (var l = document.querySelectorAll(t), r = 0; r < l.length; r++)l[r].classList.contains("ss-box") || l[r].classList.add("ss-box"), s && l[r].setAttribute("data-ss-link", s), e && l[r].setAttribute("data-ss-social", e), a || l[r].setAttribute("data-ss-icon", a), o || l[r].setAttribute("data-ss-content", o), n && (l[r].innerHTML = ""); SocialShare.init(t) } }; window.addEventListener("load", function () { SocialShare.init(".ss-box") });